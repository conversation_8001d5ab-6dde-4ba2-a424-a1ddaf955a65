<!--
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023-04-20 19:49:22
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2024-01-15 00:17:00
 * @Description: 持仓Top10
-->
<template>
  <div class="home-poc-item poc-portfolio-top10">
    <div class="home-poc-item--header">
      TOP10持仓
      <el-form ref="elForm" :model="form">
        <jr-form-item-create :column="3" :data="configForm" :model="form" />
        <setDisplay
          :index-list="indexList"
          @setSessionColumns="setSessionColumns"
        />
      </el-form>
    </div>
    <div class="home-poc-item--body jr-decorated-table--body">
      <jr-table
        :columns="configTable.columns"
        :data-source="configTable.data"
        :loading="configTable.loading"
        :pagination="false"
        :on-change="handleQuery"
        border
      />
    </div>
  </div>
</template>

<script>
import { QueryPortfolioTop10, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, GetPortfolioList, getIndexInfo } from '@/api/home'
import setDisplay from './set-index.vue'

import { ConvertAmount } from '@jupiterweb/utils/common'
export default {
  components: {
    setDisplay
  },
  props: {
    params: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      tableList: [],
      indexList: [],
      form: {
        statType: '0',
        assetType: 'F01'
      },
      commonCols: [{
        title: '证券代码',
        prop: 'finprodMarketId',
        width: 90
      },
      {
        title: '证券名称',
        prop: 'finprodName',
        width: 120
      }],
      configForm: [],
      configTable: {
        loading: false,
        columns: [],
        data: [],
        marketValueTotal: 0,
        weightingMarketValueTotal: 0,
        pagination: {
          pageNo: 1,
          pageSize: 10,
          // limit: 15,
          pageSizeOptions: [15, 25, 50, 100],
          total: 0,
          showSizeChanger: true
        }
      }
    }
  },
  watch: {
    'params': {
      handler(v, o) {
        if (JSON.stringify(v) === JSON.stringify(o)) return
        if (v && v.portfolioId) {
          this.$set(this.configForm[1], 'options', [{ id: v.portfolioId, text: v.portfolioName }])
          Object.assign(this.form, { portfolioId: [v.portfolioId] })
          this.handleQuery()
        }
      }
    }
  },
  async created() {
    const self = this
    this.colRenders = {
      marketValue: (h, { row }) => {
        const precent = Math.min(100, (row.marketValue / self.configTable.marketValueTotal * 100).toFixed(2))
        return <div><span class={'bg-progress'} style={{ width: precent + '%' }}/><span class={'text'}>{ConvertAmount('1', row.marketValue)}</span></div>
      },
      weightingMarketValue: (h, { row }) => {
        const precent = Math.min(100, (row.weightingMarketValue / (self.configTable.weightingMarketValueTotal || 1) * 100).toFixed(2))
        return <div><span class='bg-progress' style={{ width: precent + '%' }} /><span class={'text'}>{ConvertAmount('1', row.weightingMarketValue)}</span></div>
      }
    }

    this.getParams()
    // 初始化
    this.init()
    // 把值传给列设置，部分呈选中的状态
    this.getIndexInfo()
  },
  methods: {
    async getParams() {
      const [assetTypeLists, portfolioList] = await Promise.all([
        GetComboboxList(['PINAN_ASSET_TYPE']),
        GetPortfolioList({
          length: 1,
          page: 1,
          order: 'PORTFOLIO_ID',
          sort: 'desc',
          search: 'JCPC01'
        })])
      this.configForm[2].options = assetTypeLists[0] || []

      if (portfolioList && portfolioList.length) {
        this.configForm && this.$set(this.configForm[1], 'options', portfolioList)
        Object.assign(this.form, { portfolioId: ['JCPC01'] })
      }
      this.$nextTick(() => {
        this.handleQuery()
      })
    },
    async getIndexInfo() {
      const { assetType: indexType } = this.form

      this.indexList = []

      if (!indexType) return

      const indexInfo = await getIndexInfo({ indexType }) || []

      if (indexInfo.length) {
        const allList = indexInfo[0].indexList || []
        const oriColumns = this.getSessionColumns().filter(o => allList.some(a => o.indexCode === a.indexCode))
        indexInfo[0].indexCode = indexType

        this.configTable.columns = this.commonCols.concat((oriColumns.length ? oriColumns : allList).filter(idx => idx.showFlag === 'Y').map(idx => ({
          prop: idx.indexCode,
          title: idx.indexName,
          render: this.colRenders[idx.indexCode],
          type: ['mGradeResult'].includes(idx.indexCode) ? undefined : 'amount',
          align: ['mGradeResult'].includes(idx.indexCode) ? 'center' : 'right'
        })))

        this.indexList = (indexInfo || []).map(a => {
          return {
            ...a,
            indexList: (a.indexList || []).map(aa => ({
              ...aa,
              ...oriColumns.find(o => aa.indexCode === o.indexCode),
              indexCode: aa.indexCode,
              indexName: aa.indexName
            }))
          }
        })
      }
    },
    setSessionColumns(list) {
      localStorage.setItem('portfolioTop10_' + this.form.assetType, JSON.stringify(list))
      this.configTable.columns = this.commonCols.concat(list.filter(idx => idx.showFlag === 'Y').map(idx => ({
        prop: idx.indexCode,
        title: idx.indexName,
        render: this.colRenders[idx.indexCode],
        type: ['mGradeResult'].includes(idx.indexCode) ? undefined : 'amount',
        align: ['mGradeResult'].includes(idx.indexCode) ? 'center' : 'right'
      })))
      Object.assign(this.indexList[0], {
        indexList: list
      })

      this.$nextTick(this.handleQuery)
    },
    getSessionColumns() {
      return JSON.parse(localStorage.getItem('portfolioTop10_' + this.form.assetType) || JSON.stringify([]))
    },
    init() {
      const self = this
      this.configForm = [{
        title: '',
        prop: 'statType',
        type: 'radio',
        required: true,
        options: [
          { value: '0', text: '全价市值' },
          { value: '1', text: '规模加权' }
        ],
        change() {
          self.handleQuery()
        }
      },
      {
        title: '',
        prop: 'portfolioId',
        type: 'remoteSelect',
        placeholder: '请选择组合',
        optionValue: 'id',
        options: [],
        multiple: true,
        api: '/invest/portfolio/ptloverview/PtlOverview001/getPortfolioId',
        change() {
          self.handleQuery()
        }
      },
      {
        title: '',
        prop: 'assetType',
        type: 'select',
        placeholder: '请选择资产类型',
        // multiple: true,
        optionValue: 'id',
        options: [],
        change() {
          self.getIndexInfo()
          self.handleQuery()
        }
      }]
    },
    // 查询方法
    async handleQuery() {
      //   查询校验???? 选的多会报错！！！
      this.configTable.loading = true
      const indexCodeList = this.configTable.columns.map(m => m.prop)
      const params = {
        page: '1',
        length: '15',
        ...this.form,
        indexCodeList
      }
      const res = await QueryPortfolioTop10(params)
      this.configTable.loading = false
      this.configTable.data = res
      if (indexCodeList.includes('marketValue')) {
        this.configTable.marketValueTotal = res.reduce((a, b) => a + (b.marketValue || 0), 0)
      }

      if (indexCodeList.includes('weightingMarketValue')) {
        this.configTable.weightingMarketValueTotal = res.reduce((a, b) => a + (b.weightingMarketValue || 0), 0)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import "./poc.scss";
</style>

<style lang="scss">
.poc-portfolio-top10 {
  .el-form {
    flex: 1;
    display: inline-flex;
    .jr-form-item {
      padding-left: 10px;
      padding-top: 3px;
      .el-radio {
        margin-right: 10px;
      }
    }
  }
  .form-item-create-component {
    line-height: 0;
    padding-right: 8px;
  }
  .home-poc-item--body {
    padding: 8px 10px;
    .bg-progress {
      background: #ff5151;
      position: absolute;
      height: 16px;
      right: 0;
    }
    .text {
      z-index: 1;
      position: relative;
    }
  }
}
</style>
