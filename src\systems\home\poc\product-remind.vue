<template>
  <div class="home-poc-item">
    <!-- <fullscreen v-on="{ ...$listeners }" />
    <span class="home-poc-item--header">产品端 (<font color="red">{{ totalCount }}</font>)</span> -->
    <slot />
    <div v-loading="queryDate && !remindCountMap.R1.length" class="home-poc-item--body">
      <div v-for="item in remindCountMap.R1" :key="item.remindName" class="content-item">
        <span class="icon" :style="'background-color: ' + item.bgColor">{{ item.abbr }}</span>
        <div class="text">
          <span>{{ item.remindName }}</span>
          <el-link :underline="false" type="primary" class="num" @click="openModal('R1', item)">{{ item.remindCount }}</el-link>
        </div>
      </div>
    </div>
    <modal-non-process
      :close-modal="() => remindModalConfig.visible = false"
      v-bind="{...remindModalConfig}"
    />
  </div>
</template>

<script>
// import fullscreen from './fullscreen'
import * as API from '@/api/home.js'
// 所有提醒项配置
const REMIND_STYLE_CONFIG = {
  R1: { // 产品端
    R1S1: ['募', '#4DABFF'],
    R1S2: ['息', '#34CACA'],
    R1S3: ['红', '#FC747B'],
    R1S4: ['赎', '#A3C436'],
    R1S5: ['期', '#F3B664'],
    R1S6: ['费', '#A476E2'],
    R1S7: ['净', '#A3C436']
  },
  R3: { // 回款及存续期确认
    R3S1: ['基 | 净', '#E6F7FF', '#ACDFFF'],
    R3S2: ['基 | 货', '#E6FFFB', '#9EEDE5'],
    R3S3: ['计 | 净', '#F6FFED', '#C6F0A6'],
    R3S4: ['货 | 基', '#FFF7E6', '#FFDDA6'],
    R3S5: ['债 | 续', '#FFF1F0', '#FFAEAA'],
    R3S6: ['债', '#FFF0F6', '#FFADD2'],
    R3S7: ['质', '#F9F0FF', '#DCBDF9'],
    R3S8: ['项 | 续', '#F0F5FF', '#C1D4FF'],
    R3S9: ['存 | 确', '#FFF2E8', '#FFCCB0'],
    R3S10: ['计 | 续', '#F6FFED', '#C9F0AB'],
    R3S12: ['计 | 货', '#FFF7E6', '#FFDDA6'],
    R3S13: ['存 | 续', '#FFF2E8', '#FFCCB0'],
    R3S14: ['质 | 续', '#F9F0FF', '#DCBDF9'],
    R3S15: ['买', '#FFF1F0', '#FFAEAA'],
    R3S16: ['债', '#FFF0F6', '#FFADD2']
  },
  R4: {
    R4S1: ['', 'remind-r4s1'],
    R4S2: ['', 'remind-r4s2']
  },
  R5: {
    R5S1: ['', 'remind-r5s1'],
    R5S2: ['', 'remind-r5s2'],
    R5S3: ['', 'remind-r5s3']
  },
  R7: { // 变更提醒
    R7S1: ['', 'remind-r7s1'],
    R7S2: ['', 'remind-r7s2']
  },
  R10: {
    R10S1: ['', 'remind-r10s1'],
    R10S2: ['', 'remind-r10s2']
  },
  R11: { // 分销缴款提醒
    R11S1: ['缴', '#FAEFFD']
  },
  R12: { // 标准券额度不足提醒
    R12S1: ['标', '#E8F3FF']
  },
  R13: { // ECIF异常数据提醒
    R13: ['ECIF', '#F0FAE9']
  },
  R14: { // 资管计划底层登记匹配异常
    R14S1: ['资', '#EBFAF8']
  }
}
export default {
  components: {
    // fullscreen
  },
  props: {
    date: {
      type: Number,
      default: null
    }
  },
  data() {
    return {
      queryDate: JSON.parse(sessionStorage.getItem('platDate')),
      // 初始化参数
      initParams: {},
      remindModalConfig: {
        visible: false,
        modalType: 'new',
        modalTypeName: '主动提醒详情',
        itemData: {},
        size: 'large',
        modalFooter: false,
        joinType: true,
        menuinfo: {
          componenturl: 'home/product/active-remind/active-table.vue'
        }
      },
      remindCountMap: {
        'R1': [] // 产品端
        // 'R2': [], // 315检查
        // 'R3': [], // 回款及存续期确认
        // 'R4': [], // 债券行权及覆盖
        // 'R5': [], // 风险提醒
        // 'R6': [], // 披露管理
        // 'R7': [], // 变更提醒
        // 'R9': [], // 公共数据变更
        // 'R10': [], // 债券上市流通
        // 'R11': [], // 分销缴款提醒
        // 'R12': [], // 标准券额度不足提醒
        // 'R13': [], // ECIF异常数据提醒
        // 'R14': [] // 资管计划底层登记匹配异常
      }
    }
  },
  computed: {
    totalCount() {
      return this.remindCountMap.R1.length ? this.remindCountMap.R1.reduce((a, b) => (a + b.remindCount), 0) : 0
    },
    getR2List() {
      const { remindCountMap: { R2 }} = this
      if (!R2.length) return []

      const newArr = []
      const sorted = R2.sort((a, b) => a.remindCode - b.remindCode)
      for (let i = 0; i < sorted.length / 3; i++) {
        newArr.push(sorted.slice(i * 3, i * 3 + 3))
      }
      return newArr.map(item => {
        const title = item[0].remindName.split('提前')[0]
        return {
          title,
          list: item.map(te => ({ ...te, remindName: te.remindName.replace(title, '') }))
        }
      })
    }
  },
  watch: {
    queryDate: {
      handler(newData) {
        newData && this.getRemindCount()
      },
      immediate: true
    }
  },
  created() {
    this.init()
  },
  methods: {
    async init() {
      this.initParams = await API.GetActiveRemindInit()
    },
    /**
     * 获取提醒数量
     */
    getRemindCount() {
      const self = this

      const { queryDate, remindCountMap } = self

      const generateList = (reminderType, list) => {
        const result = REMIND_STYLE_CONFIG[reminderType] ? list.map(l => {
          if (!l) return
          const cfg = REMIND_STYLE_CONFIG[reminderType][l.remindCode]
          return Object.assign(l, !cfg ? {} : {
            abbr: cfg[0],
            bgColor: cfg[1],
            bdColor: cfg[2]
          })
        }).filter(Boolean) : list
        self.$set(self.remindCountMap, reminderType, result)
      }
      Object.keys(remindCountMap).forEach(reminderType => {
        if (reminderType === 'R3') { // 回款及存续期确认
          // 债券F01、质押式回购F07、项目类F06、存款F05、计划类利率F03，计划类净值F04、计划类货币F09、基金/货币基金F02、借贷F12、买断式F10
          Promise.all(['F01', 'F07', 'F06', 'F05', 'F03', 'F04', 'F09', 'F02', 'F12', 'F10'].map(assetType => API.GetActiveRemindCount({ queryDate, reminderType, assetType }))).then(list => {
            generateList(reminderType, list.flat())
          }).catch()
        } else if (reminderType === 'R2') { // 315检查
          // 项目类F06、计划类利率F03
          Promise.all(['F06', 'F03'].map(assetType => API.GetActiveRemindCount({
            queryDate,
            reminderType,
            assetType
          }))).then(list => {
            generateList(reminderType, list.flat())
          }).catch()
        } else {
          API.GetActiveRemindCount({ queryDate, reminderType }).then((list = []) => {
            generateList(reminderType, list)
          })
        }
      })
    },
    openModal(topItem, subItem) {
      Object.assign(this.remindModalConfig, {
        visible: true,
        date: this.date,
        itemData: { queryDate: this.queryDate, mainCategory: topItem, smallCategory: subItem.remindCode }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
@import "./poc.scss";
.home-poc-item {
  display: flex;
  justify-content: center;
  align-items: center;
}
.home-poc-item--body {
  display: block;
  padding: 0 8px;
  margin-top: -16px;
  height: auto !important;
}
.content-item {
  width: calc(33.3% - 16px);
  background: #f5f7f9;
  flex-direction: row;
  display: inline-flex;
  padding: 16px 15px 16px 24px;
  height: 72px;
  margin-right: 8px;
  margin-left: 8px;
  margin-top: 16px;
  text-align: right;
  justify-content: space-between;
  .icon {
    font-size: 16px;
    display: inline-block;
    height: 40px;
    width: 40px;
    min-width: 40px;
    text-align: center;
    line-height: 40px;
    color: #fff;
    margin-right: 6px;
  }
  .text {
    display: inline-flex;
    flex-direction: column;
    span {
      white-space: nowrap;
    }
    .num {
      display: inline-block;
      font-size: 14px !important;
      font-weight: bold;
    }
  }
}
</style>
