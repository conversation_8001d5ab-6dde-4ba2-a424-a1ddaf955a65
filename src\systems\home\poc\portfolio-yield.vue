<!--
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023-04-20 19:49:22
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2023-05-12 09:43:46
 * @Description: 组合区间收益率
-->
<template>
  <div class="home-poc-item">
    <div class="home-poc-item--header">
      组合区间收益率
      <el-form :model="form" style="flex: 1;padding-left: 20%;">
        <jr-combobox v-model="form.range" :data="RANGE_LIST" @change="queryList" />
      </el-form>
    </div>
    <div class="home-poc-item--body">
      <el-tabs v-model="activeName" class="poc-portfolio-yield-tabs" type="border-card" :class="'is-' + activeName">
        <el-tab-pane name="up">
          <span slot="label"><i class="el-icon-top" /> {{ dataMap.upList.length }}</span>
          <div v-loading="loading" class="yield-base-info">
            {{ params.vDate }} - {{ params.mDate }} 上涨<span class="up"> {{ dataMap.upList.length }} </span>组
            <el-progress :percentage="(totalCount ? parseInt(dataMap.upList.length / totalCount * 100) : 0)" class="is-exception up" color="#ff0000" />
            <el-progress :percentage="(totalCount ? parseInt(dataMap.downList.length / totalCount * 100) : 0)" class="is-exception down" color="#008000" />
            <el-progress :percentage="(totalCount ? parseInt(dataMap.normalCount / totalCount * 100) : 0)" class="is-exception gray" color="#808080" />
          </div>
          <ul class="yield-data-list">
            <li v-for="item in dataMap.upList" :key="item.portfolioId">
              <label @click="handleParams(item)">{{ item.portfolioName || item.portfolioId }}</label>
              <span class="up">{{ (item.portfolioYield * 100).toFixed(2) }}%</span>
            </li>
          </ul>
        </el-tab-pane>
        <el-tab-pane name="down">
          <span slot="label"><i class="el-icon-bottom" /> {{ dataMap.downList.length }}</span>
          <div v-loading="loading" class="yield-base-info">
            {{ params.vDate }} - {{ params.mDate }} 下跌 <span class="down">{{ dataMap.downList.length }}</span> 组
            <el-progress :percentage="(totalCount ? parseInt(dataMap.upList.length / totalCount * 100) : 0)" class="is-exception up" color="#ff0000" />
            <el-progress :percentage="(totalCount ? parseInt(dataMap.downList.length / totalCount * 100) : 0)" class="is-exception down" color="#008000" />
            <el-progress :percentage="(totalCount ? parseInt(dataMap.normalCount / totalCount * 100) : 0)" class="is-exception gray" color="#808080" />
          </div>
          <ul class="yield-data-list">
            <li v-for="item in dataMap.downList" :key="item.portfolioId">
              <label @click="handleParams(item)">{{ item.portfolioName || item.portfolioId }}</label>
              <span class="down">{{ (item.portfolioYield * 100).toFixed(2) }}%</span>
            </li>
          </ul>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script>
import dayjs from 'dayjs'

import * as API from '@/api/home'
// 近一周、近一月、近三月、近半年、年初至今、近一年
const RANGE_LIST = [
  {
    text: '近一周',
    value: '01'
  },
  {
    text: '近一个月',
    value: '02'
  },
  {
    text: '近三个月',
    value: '03'
  },
  {
    text: '近半年',
    value: '04'
  },
  {
    text: '年初至今',
    value: '05'
  },
  {
    text: '近一年',
    value: '06'
  }
]
export default {
  data() {
    return {
      activeName: 'up',
      totalCount: 0,
      loading: false,
      dataMap: {
        normalCount: 0,
        upList: [],
        downList: []
      },
      RANGE_LIST,
      form: {
        range: '05'
      }
    }
  },
  computed: {
    params() {
      const { range } = this.form
      const [vDate, mDate] = this.convertDate(range)
      return {
        mDate, vDate
      }
    }
  },
  created() {
    this.queryList()
  },
  methods: {
    async queryList() {
      this.loading = true
      this.dataMap = await API.QueryPortfolioRate({ ...this.params })
      const { normalCount, downList, upList } = this.dataMap
      this.totalCount = upList.length + normalCount + downList.length
      this.loading = false
    },
    convertDate(range) {
      const format = 'YYYY-MM-DD'
      const sysDate = this.$store.getters.systemTime
      const plateDate = dayjs(sysDate).format(format)
      switch (range) {
        case '01':
          return [dayjs(sysDate).subtract(7, 'day').format(format), plateDate]
        case '02':
          return [dayjs(sysDate).subtract(30, 'day').format(format), plateDate]
        case '03':
          return [dayjs(sysDate).subtract(90, 'day').format(format), plateDate]
        case '04':
          return [dayjs(sysDate).subtract(180, 'day').format(format), plateDate]
        case '05':
          return [dayjs(new Date(new Date(sysDate).getFullYear(), 0, 1)).format(format), plateDate]
        case '06':
          return [dayjs(sysDate).subtract(365, 'day').format(format), plateDate]
      }
    },
    handleParams(item) {
      // 收益贡献：e02cb31f6a5f4ba7a28b0cddd34dc419
      // 持仓：7b2704057fe043d3b49742883ff888bc
      // 绩效考核：a5941f67330f4e2fbde96d6d8c5c01ff
      this.$emit('setTargetParams', {
        'a5941f67330f4e2fbde96d6d8c5c01ff': item,
        'e02cb31f6a5f4ba7a28b0cddd34dc419': item,
        '7b2704057fe043d3b49742883ff888bc': item
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@import "./poc.scss";
</style>

<style lang="scss">
$up: red;
$down: green;
$gray: gray;
.el-tabs--border-card.poc-portfolio-yield-tabs {
  border: none;
  .up {
    color: $up;
  }
  .down {
    color: $down;
  }
  .yield-base-info {
    padding-left: 12px;
    .el-progress.is-exception {
      margin-top: 4px;
      .el-progress__text {
        font-size: 12px !important;
      }
    }
    .el-progress.is-exception.up {
      margin-top: 8px;
    }
    .el-progress.is-exception.up .el-progress__text {
      color: $up;
    }
    .el-progress.is-exception.down .el-progress__text {
      color: $down;
    }
    .el-progress.is-exception.gray .el-progress__text {
      color: $gray;
    }
  }
  .yield-data-list {
    width: 100%;
    padding-top: 8px;
    li {
      line-height: 28px;
      display: flex;
      padding: 0 12px;
      justify-content: space-between;
      &:nth-child(2n) {
        background: #eee;
        border-top: 1px solid #e8e8e8;
        border-bottom: 1px solid #e8e8e8;
      }
      &:hover {
        background: var(--theme--color-light-9);
      }
      label {
        cursor: pointer;
      }
    }
  }
  &.is-up .el-tabs__active-bar {
    background-color: $up;
  }
  &.is-down .el-tabs__active-bar {
    background-color: $down;
  }
  .el-tabs__header {
    margin-bottom: 8px;
    border-bottom: none;
    .el-tabs__active-bar {
      bottom: none;
      top: 0;
    }
    .el-tabs__nav {
      width: 100%;
    }
    #tab-up {
      color: red !important;
      font-size: 16px;
      border-top: none;
      border-bottom: 2px solid transparent;
      margin-top: 0;
      width: 50%;
      text-align: center;
      &.is-active {
        border-top: none;
        border-bottom: 2px solid $up !important;
      }
      i{
        font-size: 14px;
      }
    }
    #tab-down {
      color: green !important;
      font-size: 16px;
      border-bottom: 2px solid transparent !important;;
      margin-top: 0;
      width: 50%;
      text-align: center;
      &.is-active {
        border-bottom: 2px solid $down !important;;
        border-top: none;
      }
      i{
        font-size: 14px;
      }
    }
  }
  .el-tabs__content {
    overflow: auto;
    padding: 0 8px;
  }
}
</style>
