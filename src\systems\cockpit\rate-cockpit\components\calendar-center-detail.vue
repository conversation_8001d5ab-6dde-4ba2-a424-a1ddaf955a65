<template>
  <el-dialog
    :visible.sync="dialogVisible"
    :width="px2vw(1120)"
    :before-close="handleClose"
    :modal-append-to-body="true"
    :append-to-body="true"
  >
    <div class="dialog">
      <div class="dialog-title">
        <span>日历</span>
        <jr-svg-icon icon-class="close" style="color: #ffffff;cursor: pointer;" @click.stop="handleClose"/>
      </div>
      <div class="dialog-content">
        <div class="dialog-content-header">
          <div class="dialog-content-header-date">
            <img src="@/assets/cockpit/canlendar.png" alt="" :style="{width: px2vw(24),height: px2vh(25)}">
            <span>{{ computedDate }}</span>
          </div>
          <div class="dialog-content-header-back" @click.stop="handleClose">
            <span>返回</span>
            <img src="@/assets/cockpit/back.png" alt="" :style="{width: px2vw(16),height: px2vh(16)}">
          </div>
        </div>
        <div class="dialog-content-table">
          <template v-for="(item,index) in legendArr">
            <div class="dialog-content-table-inner" :key="index"  v-if="item.tableData.length > 0">
              <div class="dialog-content-table-inner-title">
                <span :style="{backgroundColor: item.color}"/>
                <span :style="{color:item.color}">{{ item.text | titleFormat }}</span>
              </div>
              <div class="dialog-content-table-inner-tabContent">
                <el-table 
                  :data="item.tableData" 
                  style="width: 100%" 
                  :cell-style="{
                    borderBottom:'1px solid #F1D6BD',
                    color: 'rgba(0,0,0,0.9)'
                  }"
                  :header-cell-style="{
                    borderBottom:'1px solid #F1D6BD',
                    color: '#000000'
                  }"
                >
                  <template v-for="(column, cIndex) in item.columns">
                    <el-table-column 
                      v-if="column.format"
                      :key="cIndex"
                      :prop="column.props" 
                      :label="column.label">
                      <template slot-scope="scope">
                        <span>{{ column.formatFunc(scope.row[column.props]) }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column 
                      v-else
                      :key="cIndex"
                      :prop="column.props" 
                      :label="column.label">
                    </el-table-column>
                  </template>
                </el-table>
              </div>
            </div>
          </template>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import cockpitSelect from '../../components/cockpit-select.vue'
import { cockpitGetCalendarList } from "@/api/cockpit/cockpit"
import { ConvertAmount } from '@jupiterweb/utils/common.js'
import { px2vw, px2vh } from '../../utils/portcss'
import store from '@jupiterweb/store'
import moment from 'moment'
export default {
  name: 'CalendarCenterDate',
  components: {
    cockpitSelect
  },
  data() {
    return {
      dialogVisible: false,
      weekDays: ['一', '二', '三', '四', '五', '六', '日'],
      selectParams: {
        year: '',
        month: ''
      },
      legendArr:[
        {
          text:"债券付息兑付",
          color: "#5B8FF9",
          tableData:[],
          columns:[
            {
              props:'sInfoWindcode',
              label:'债券代码'
            },
            {
              props:'sInfoName',
              label:'债券简称'
            },
            {
              props:'bInfoIssuer',
              label:'发行人'
            },
            {
              props:'paymentDate',
              label:'支付日期',
              format: true,
              formatFunc: value => {
                return moment(value).format('YYYY-MM-DD')
              }
            },
            {
              props:'planPaySum',
              label:'应付现金流'
            },
            {
              props:'paymentStatus',
              label:'支付类型'
            }
          ]
        },
        {
          text:"我司新券发行",
          color: "#269A99",
          tableData:[],
          columns:[
            {
              props:'sInfoWindcode',
              label:'债券代码'
            },
            {
              props:'sInfoName',
              label:'债券简称'
            },
            {
              props:'bInfoIssuer',
              label:'发行人'
            },
            {
              props:'bondTypeName2',
              label:'债券类型'
            },
            {
              props:'term',
              label:'发行期限'
            },
            {
              props:'bIssueAmountact',
              label:'规模',
              format: true,
              formatFunc: value => {
                return ConvertAmount('HMU', value * 1, 1, 4) + '亿元'
              }
            },
            {
              props:'latestCouponrate',
              label:'票面利率'
            }
          ]
        },
        {
          text:"对标企业新券发行",
          color: "#E1B01E",
          tableData:[],
          columns:[
            {
              props:'sInfoWindcode',
              label:'债券代码'
            },
            {
              props:'sInfoName',
              label:'债券简称'
            },
            {
              props:'bInfoIssuer',
              label:'发行人'
            },
            {
              props:'bondTypeName2',
              label:'债券类型'
            },
            {
              props:'term',
              label:'发行期限'
            },
            {
              props:'bIssueAmountact',
              label:'规模',
              format: true,
              formatFunc: value => {
                return ConvertAmount('HMU', value * 1, 1, 4) + '亿元'
              }
            },
            {
              props:'latestCouponrate',
              label:'票面利率'
            }
          ]
        },
      ],
      date:'',
    }
  },
  filters:{
    titleFormat(val){
      if(store.getters.sysVersion === 'group' && val === '我司新券发行'){
        return '集团新券发行'
      }else{
        return val
      }
    }
  },
  watch: {
    "$store.getters.sysVersion":{
      deep: true,
      immediate: true,
      handler(){
        if(this.$store.getters.sysVersion === 'company'){
          this.legendArr = this.legendArr.map(item=>{
            item.columns = item.columns.filter(column=>column.props !== 'bInfoIssuer')
            return item
          })
        }
      }
    }
  },
  computed:{
    computedDate() {
      return moment(this.date).format('YYYY年MM月DD日')
    }
  },
  methods: {
    px2vw,
    px2vh,
    /**
     * 打开弹框
     */
    open(date) {
      this.dialogVisible = true
      this.date = date
      this.getInterDateDataApi()
    },
    /**
     * 关闭弹框
     */
    handleClose() {
      this.dialogVisible = false
      this.legendArr = this.legendArr.map(item=>{
        item.tableData = []
        return item
      })
    },
    /**
     * 获取指定日期数据
     */
    async getInterDateDataApi(){
      const data = await cockpitGetCalendarList(moment(this.date).format('YYYYMMDD'),'')
      this.legendArr = this.legendArr.map(item=>{
        if(data[item.text] instanceof Array && data[item.text].length > 0){
          item.tableData = data[item.text]
        }
        return item
      })
    },
  }
}
</script>

<style lang="scss" scoped>
@import '~assets/styles/portcss.scss';
::v-deep .el-dialog__header {
  display: none;
}
::v-deep .el-dialog__body {
  padding: 0px;
}
.dialog {
  width: 100%;
  height: vh(794);
  background: #FBFBF9;
  border-radius: 2px;
  &-title {
    width: 100%;
    height: vh(56);
    background-image: url('../../../../assets/cockpit/calendarBac.png');
    background-size: 100% 100%;
    border-radius: 2px 2px 0px 0px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px 25px;
    span {
      height: vh(28);
      font-family: MicrosoftYaHeiSemibold;
      font-size: vh(20);
      color: #ffffff;
      line-height: vh(28);
    }
  }
  &-content {
    padding: vh(14) vw(16) vh(10);
    width: 100%;
    height: vh(708);
    box-shadow: inset 0px 0px 48px 0px rgba(140, 214, 255, 0.04);
    backdrop-filter: blur(2px);
    &-header {
      width: 100%;
      height: vh(72);
      background: rgba(255,255,255,1);
      box-shadow: inset 0px -1px 0px 0px #EAE9E9;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: vh(16) vw(16);
      &-date{
        display: flex;
        gap: vw(10);
        height: vh(27);
        font-family: MicrosoftYaHeiSemibold;
        font-size: vh(20);
        color: rgba(0,0,0,0.9);
        line-height: vh(27);
        align-items: center;
        font-weight: 600;
      }
      &-back{
        height: vh(22);
        font-family: MicrosoftYaHei;
        font-size: vh(14);
        color: rgba(0,0,0,0.9);
        line-height: vh(22);
        display: flex;
        align-items: center;
        gap: vw(6);
        cursor: pointer;
      }
    }
    &-table {
      width: 100%;
      height: vh(636);
      background: rgba(255,255,255,1);
      overflow-y: scroll;
      margin-top: 1px;
      padding-bottom: vh(20);
      &-inner {
        width: 100%;
        &-title {
          width: 100%;
          height: vh(54);
          padding: vh(19) vw(0) vh(19) vw(16);
          display: flex;
          align-items: center;
          gap: vw(8);
          & > span:nth-of-type(1) {
            width: vw(3);
            height: vh(16);
            background-color: #5b8ff9;
          }
          & > span:nth-of-type(2) {
            height: vh(22);
            font-family: MicrosoftYaHeiSemibold;
            font-size: vh(16);
            color: #5b8ff9;
            line-height: vh(22);
          }
        }
        &-tabContent {
          width: 100%;
          padding: 0 vw(16);
        }
      }
    }
  }
}
</style>
