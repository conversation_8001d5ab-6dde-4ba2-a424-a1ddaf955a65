<template>
  <div>
    <jr-modal :visible="visible" :handle-cancel="handleCancel" :show-scale="false">
      <span class="title">{{ title }}</span>
      <span class="description">{{ description }}</span>
      <template v-slot:body>
        <div class="issuanceMaterials">
          <div class="issuanceMaterials-left">
            <div class="issuanceMaterials-left-title">
              <span>{{ title }}</span>
            </div>
            <div class="issuanceMaterials-left-content">
              <div
                v-for="(leftdata, index) in leftListData"
                :key="index"
                class="issuanceMaterials-left-content-item"
                :class="index === leftActiveIndex ? 'issuanceMaterials-left-content-active' : ''"
                @click.stop="leftClcik(index)"
              >
                <span>
                  <jr-svg-icon icon-class="profile" />
                </span>
                <span>{{ leftdata.category }}({{ leftdata.num }})</span>
              </div>
            </div>
          </div>
          <div class="issuanceMaterials-right">
            <div class="issuanceMaterials-right-content">
              <template v-if="rightListData.length > 0">
                <div
                  v-for="(rightData, index) in rightListData"
                  :key="index"
                  class="issuanceMaterials-right-content-item"
                >
                  <p v-if="rightData.down_able === 1">{{ rightData.n_info_title }}</p>
                  <p v-if="rightData.down_able === 0">
                    <span style="cursor: pointer; color: var(--theme--color)" @click.stop="downloadFile(rightData)">
                      {{ rightData.n_info_title }}
                    </span>
                  </p>
                  <p>
                    <span>{{ description }}</span>
                    <span>{{ activeCategory }}</span>
                  </p>
                  <span
                    v-if="rightData.down_able === 1"
                    class="issuanceMaterials-right-content-item-download"
                    @click.stop="downloadFile(rightData)"
                  >
                    <span>下载</span>
                    <span><jr-svg-icon icon-class="cloud-download" /></span>
                  </span>
                </div>
              </template>
              <template v-else>
                <jr-empty />
              </template>
            </div>
          </div>
        </div>
      </template>
      <template v-slot:footer>
        <div class="footer">
          <el-button type="primary" @click.stop="handleCancel">关闭</el-button>
        </div>
      </template>
    </jr-modal>
  </div>
</template>

<script>
import { GetListData } from '@jupiterweb/api/common/active-module-svr'
export default {
  data() {
    return {
      visible: false,
      title: '',
      description: '',
      leftActiveIndex: 0,
      windcode: '',
      leftListData: [],
      rightListData: [],
      filenum: ''
    }
  },
  computed: {
    activeCategory() {
      return this.leftListData[this.leftActiveIndex]?.category || ''
    }
  },
  methods: {
    /**
     * 弹框打开
     * @param {Object} selfTableParams 左侧自定义列配置获取参数
     * @param {Object} row 行内数据
     * @param {String} title 展示表格名称
     */
    open(selfTableParams, row, title) {
      this.visible = true
      this.title = title
      this.description = row.sInfoName
      this.leftActiveIndex = 0
      this.windcode = row.sInfoWindcode
      this.filenum = row.filenum
      this.getLeftListDataApi(selfTableParams)
    },
    /**
     * 左侧点击
     */
    leftClcik(index) {
      this.leftActiveIndex = index
      this.getRightListDataApi()
    },
    /**
     * 获取左侧数据列表
     * @param {Object} params 左侧自定义列配置获取参数
     */
    async getLeftListDataApi(params) {
      const data = await GetListData({
        params: {
          ...params,
          windcode: this.windcode
        },
        page: {
          pageNo: 1,
          pageSize: 99999
        }
      })
      this.leftListData = data?.pageInfo?.list || []
      this.getRightListDataApi()
    },
    /**
     * 获取右侧数据列表
     */
    async getRightListDataApi() {
      let num = this.leftListData[this.leftActiveIndex]?.num || 0
      num = 10
      if (num > 0) {
        const data = await GetListData({
          params: {
            ownedModuleid: '708631605142536192',
            ccid: 'ff6c2b5454ba4d4197371e17327b60e0',
            windcode: this.windcode,
            flag: this.leftListData[this.leftActiveIndex].categorycode,
            filenum: this.filenum
          },
          page: {
            pageNo: 1,
            pageSize: 99999
          }
        })
        this.rightListData = data?.pageInfo?.list || []
        console.log(this.rightListData)
      } else {
        this.rightListData = []
      }
    },
    /**
     * 文件下载
     */
    async downloadFile(fileInfo) {
      console.log(fileInfo)
      const fileName = fileInfo.n_info_title
      const fileUrl = fileInfo.n_info_annlink_new
      const downAble = fileInfo.down_able
      if (downAble === 0) {
        window.open(fileUrl, '_blank')
      } else {
        const response = await fetch(fileUrl)
        const blob = await response.blob() // 将响应转换为 Blob

        const a = document.createElement('a')
        a.href = URL.createObjectURL(blob)
        a.download = fileName
        document.body.appendChild(a)
        a.click()
        document.body.removeChild(a)
        URL.revokeObjectURL(a.href)
      }
    },
    /**
     * 弹框关闭
     */
    handleCancel() {
      this.visible = false
      // 重置初始化data数据
      Object.assign(this._data, this.$options.data())
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .jr-modal.el-dialog .platform-modal-content {
  padding: 0px;
}
::v-deep .jr-empty {
  margin: 0px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
::v-deep .jr-modal.el-dialog .el-dialog__footer {
  padding: 0px;
}
.title {
  height: 28px;
  font-size: var(--el-font-size-base);
  color: rgba(0, 0, 0, 0.85);
  line-height: 28px;
}
.description {
  height: 28px;
  margin-left: 16px;
  font-size: var(--el-font-size-base);
  color: rgba(0, 0, 0, 0.85);
  line-height: 28px;
}
.issuanceMaterials {
  width: 100%;
  height: 100%;
  display: flex;
  &-left {
    width: 19%;
    flex-shrink: 0;
    &-title {
      width: 100%;
      height: 36px;
      padding: 7px 28px 7px 20px;
      box-sizing: border-box;
      background: linear-gradient(180deg, #ffffff 0%, #f2f2f2 100%);
      span {
        height: 22px;
        font-size: var(--el-font-size-base);
        color: rgba(0, 0, 0, 0.9);
        line-height: 22px;
      }
    }
    &-content {
      padding: 4px 8px;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      gap: 4px;
      &-item {
        padding: 7px 0px 7px 12px;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        gap: 8px;
        color: #000000;
        cursor: pointer;
      }
      &-item:hover {
        color: var(--theme--color);
        background-color: rgba(255, 142, 43, 0.1);
      }
      &-active {
        color: var(--theme--color);
        background-color: rgba(255, 142, 43, 0.1);
      }
    }
  }
  &-right {
    width: 81%;
    height: 100%;
    padding: 16px;
    box-sizing: border-box;
    flex-shrink: 0;
    background-color: #f0f2f5;
    &-content {
      width: 100%;
      height: 100%;
      padding: 16px;
      box-sizing: border-box;
      background-color: #ffffff;
      overflow-y: scroll;
      &-item {
        width: 100%;
        min-width: 592px;
        background: linear-gradient(180deg, #fff6ee 0%, #ffffff 26%, #ffffff 100%);
        box-shadow: 2px 2px 8px 0px rgba(55, 90, 170, 0.04), -2px -2px 8px 0px rgba(170, 145, 55, 0.1);
        border-radius: 4px;
        border: 2px solid #ffffff;
        padding: 16px;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        position: relative;
        margin-bottom: 10px;
        gap: 19px;
        p {
          margin-bottom: 0px;
        }
        & > p:nth-of-type(1) {
          width: calc(100% - 60px);
          font-size: var(--el-font-size-base);
          color: rgba(0, 0, 0, 0.9);
          line-height: 22px;
          font-weight: 600;
        }
        & > p:nth-of-type(2) {
          display: flex;
          gap: 16px;
          & > span {
            height: 22px;
            font-size: var(--el-font-size-base);
            color: rgba(0, 0, 0, 0.6);
            line-height: 22px;
          }
        }
        &-download {
          position: absolute;
          right: 16px;
          top: 16px;
          color: var(--theme--color);
          display: flex;
          gap: 5px;
          align-items: center;
          cursor: pointer;
        }
      }
    }
  }
}
.footer {
  width: 100%;
  height: 56px;
  background: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  & > button {
    width: 88px;
    height: 32px;
    background: #ffffff;
    border-radius: 2px;
    border: 1px solid #cccccc;
    color: #000000;
  }
}
</style>
