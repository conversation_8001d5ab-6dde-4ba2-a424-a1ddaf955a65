<!--
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023-10-25 10:54:32
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2023-10-27 18:34:06
 * @Description: 策略收益走势
-->
<template>
  <article>
    <div class="header">
      <span class="title">策略收益走势</span>
      <div class="search-list">
        <!-- <label>组合：</label><jr-combobox v-model="indexType" :data="indexTypeList" />
        <label>指标：</label><jr-combobox v-model="status" :data="statusList" /> -->
        <label>日期：</label><el-date-picker v-model="rangeDate" type="daterange" />
        <el-radio-group v-model="combo" size="mini">
          <el-radio-button v-for="com in comboxList" :key="com" :label="com" />
        </el-radio-group>
      </div>
    </div>
    <section class="body">
      <echarts :options="chartOptions" />
    </section>
  </article>
</template>

<script>
import echarts from '@jupiterweb/components/echarts'
import { FormatDate } from '@jupiterweb/utils/common'
export default {
  components: { echarts },
  data() {
    const platDate = JSON.parse(sessionStorage.getItem('platDate'))
    // const base = []
    // const baseData = Array.from({ length: 15 }, (v, k) => 10 + k)
    // baseData.splice(3, 2)
    // baseData.splice(5, 2)
    // const strategy = []
    // const strategyData = Array.from({ length: 54 }, (v, k) => 28 + k)
    // strategyData.splice(3, 2)
    // strategyData.splice(8, 2)
    // strategyData.splice(5, 2)
    // strategyData.splice(19, 2)
    // strategyData.splice(18, 6)
    // strategyData.splice(12, 4)
    // strategyData.splice(22, 4)
    // strategyData.splice(14, 4)
    const getDateList = () => {
      const count = 365
      let i = 1
      const ret = []
      while (i <= count) {
        ret.push(FormatDate(new Date(platDate).getTime() + i * 24 * 3600000, 'yyyy-MM-dd'))
        // base.push(baseData[parseInt(Math.random() * 10)])
        // strategy.push(strategyData[parseInt(Math.random() * 25)])
        i++
      }
      return ret
    }
    const category = getDateList()
    return {
      height: null,
      rangeDate: [category[0], category.pop()],
      // indexType: '组合002',
      // indexTypeList: ['组合002', '组合003'].map(t => ({ text: t, value: t })),
      // status: '单位净值',
      // statusList: ['单位净值', '其他'].map(t => ({ text: t, value: t })),
      combo: '近1年',
      comboxList: ['近1周', '近1月', '近6月', '近1年', '全部'],
      chartOptions: {

        tooltip: {
          trigger: 'axis',
          axisPointer: { type: 'cross' }
        },
        grid: {
          right: 80,
          left: 80,
          top: 30,
          bottom: 70
        },
        dataZoom: [{
          'show': true,
          'height': 20,
          'xAxisIndex': [
            0
          ],
          bottom: 30,
          'start': 0,
          'end': 54,
          handleIcon: 'path://M306.1,413c0,2.2-1.8,4-4,4h-59.8c-2.2,0-4-1.8-4-4V200.8c0-2.2,1.8-4,4-4h59.8c2.2,0,4,1.8,4,4V413z',
          handleSize: '110%',
          handleStyle: {
            color: '#d3dee5'

          },
          textStyle: {
            color: '#fff' },
          borderColor: '#90979c'

        }, {
          'type': 'inside',
          'show': true,
          'height': 20,
          'start': 0,
          'end': 54
        }],
        legend: {
          show: true,
          bottom: 0,
          data: ['基准收益', '策略收益']
        },
        xAxis: [
          {
            type: 'category',
            axisTick: {
              alignWithLabel: true
            },
            data: [
              '2022/8/17',
              '2022/8/18',
              '2022/8/19',
              '2022/8/20',
              '2022/8/21',
              '2022/8/22',
              '2022/8/23',
              '2022/8/24',
              '2022/8/25',
              '2022/8/26',
              '2022/8/27',
              '2022/8/28',
              '2022/8/29',
              '2022/8/30',
              '2022/8/31',
              '2022/9/1',
              '2022/9/2',
              '2022/9/3',
              '2022/9/4',
              '2022/9/5',
              '2022/9/6',
              '2022/9/7',
              '2022/9/8',
              '2022/9/9',
              '2022/9/10',
              '2022/9/11',
              '2022/9/12',
              '2022/9/13',
              '2022/9/14',
              '2022/9/15',
              '2022/9/16',
              '2022/9/17',
              '2022/9/18',
              '2022/9/19',
              '2022/9/20',
              '2022/9/21',
              '2022/9/22',
              '2022/9/23',
              '2022/9/24',
              '2022/9/25',
              '2022/9/26',
              '2022/9/27',
              '2022/9/28',
              '2022/9/29',
              '2022/9/30',
              '2022/10/1',
              '2022/10/2',
              '2022/10/3',
              '2022/10/4',
              '2022/10/5',
              '2022/10/6',
              '2022/10/7',
              '2022/10/8',
              '2022/10/9'
            ]
          }
        ],
        yAxis: [
          {
            type: 'value',
            name: '',
            min: 0,
            position: 'left',
            axisLabel: {
              formatter: '{value} %'
            }
          }
        ],
        series: [
          {
            name: '基准收益',
            type: 'line',
            symbol: false,
            symbolSize: 0,
            data: [
              18,
              13,
              16,
              23,
              15,
              15,
              16,
              12,
              16,
              19,
              17,
              18,
              19,
              16,
              18,
              19,
              21,
              22,
              19,
              20,
              21,
              17,
              16,
              18,
              21,
              15,
              14,
              15,
              16,
              17,
              19,
              21,
              22,
              16,
              18,
              19,
              21,
              22,
              19,
              20,
              21,
              17,
              16,
              14,
              10,
              8,
              6,
              8,
              10,
              13,
              16,
              17,
              19,
              22
            ]
          },
          {
            name: '策略收益',
            type: 'line',
            symbol: false,
            symbolSize: 0,
            data: [
              38,
              33,
              36,
              43,
              35,
              35,
              36,
              37,
              41,
              44,
              42,
              38,
              49,
              46,
              48,
              49,
              51,
              52,
              39,
              60,
              61,
              57,
              56,
              58,
              61,
              55,
              54,
              55,
              56,
              57,
              39,
              41,
              42,
              50,
              52,
              53,
              55,
              56,
              53,
              54,
              57,
              53,
              52,
              54,
              51,
              51,
              40,
              42,
              44,
              49,
              47,
              51,
              45,
              50
            ]
          }
        ]
      }
    }
  }
}
</script>

<style lang="scss" scoped></style>
