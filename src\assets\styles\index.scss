// 对客服务样式
:root {
  --jr-navbar-height: 48px;
  --theme-mix-color: #856ffe;
  --theme-mix-color-light: rgba(133, 111, 254, 0.1);
  --jr-sidebar-active-submenu-bg-color: var(--theme-mix-color-light);
  --jr-sidebar-active-submenu-text-color: var(--theme-mix-color);
  --jr-sidebar-font-size: var(--el-font-size-large);
  --el-text-color-primary: rgba(0, 0, 0, 0.9);
  --el-padding-left-right: 16px;
  --jr-table-cell-padding: 10px var(--el-padding-left-right) 9px;
}
.jr-decorated-table--body {
  --jr-table-cell-padding: 9px var(--el-padding-left-right) 8px;
}
.jr-sidebar.is-type-menu-left {
  --jr-sidebar-text-color: var(--el-text-color-primary);
  --jr-sidebar-active-submenu-bg-color: var(--theme-mix-color-light);
  --jr-sidebar-active-submenu-text-color: var(--theme-mix-color);
  --jr-sidebar-font-size: var(--el-font-size-medium);
}
body {
  font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Helvetica, Arial, sans-serif !important;
  .jr-tags-view.tabs-view-container {
    --jr-tags-font-size: var(--el-font-size-base);
    --jr-tags-padding: 0 16px;
    --jr-tags-height: 38px;
    --jr-tags-margin: 4px;
    --jr-tags-body-padding: 4px 16px 16px;
  }
  .jr-tags-view.tabs-view-container .comm-tabs > .el-tabs__header .el-tabs__nav {
    .el-tabs__item {
      font-weight: 600;
      &.is-active {
        background-color: var(--el-fill-color-blank);
        box-shadow: inset 1px 0px 0px 0px transparent, inset -1px 0px 0px 0px transparent,
          inset 0px 4px 0px 0px var(--theme--color);
      }
    }
  }
  .jr-sidebar .menu-wrapper-container .el-scrollbar__view .navbar-logo-container.jr-navbar-logo {
    height: auto;
    line-height: normal;
    padding: 16px 24px 28px !important;
    .navbar-title {
      display: none;
    }
    .navbar-logo {
      width: 172px;
      height: 43px;
    }
  }
  .hideSidebar .jr-sidebar .menu-wrapper-container .el-scrollbar__view .navbar-logo-container.jr-navbar-logo {
    padding-left: 0 !important;
  }
  .collect-custom-tree-box {
    height: calc(100% - 43px - 28px - 16px);
    background: url('~@/assets/images/sidebar-bg.png') no-repeat bottom;
    background-size: inherit;

    .el-submenu__icon-arrow.el-icon-arrow-down {
      &::before {
        content: '';
        display: inline-block;
        width: 16px;
        height: 16px;
        background: url('~@/assets/images/personal/arrow-right.png') no-repeat center center;
      }
    }
    .el-submenu.is-opened > .el-submenu__title .el-submenu__icon-arrow {
      transform: rotateZ(-90deg);
    }
  }
  .jr-combobox {
    .el-input__inner {
      height: var(--el-component-size, 28px) !important;
    }
  }
  .theme-list-body + .drawer-item {
    display: none;
  }
  .el-cascader-menu,
  .el-autocomplete-suggestion li,
  .el-textarea__inner,
  .el-tabs__item:not(.is-active),
  .el-select-dropdown__item:not(.selected):not(.hover):not(.is-disabled) {
    color: rgba(0, 0, 0, 0.6) !important;
  }
  .jr-pagination .el-pagination {
    padding: 10px 0 16px;
  }

  .el-tooltip__popper {
    font-size: 12px !important;
  }
}

button,
input,
select,
textarea,
pre,
code,
label {
  font-family: 'Microsoft YaHei', sans-serif !important;
}

/*
样式调整
*/
body {
  .jr-table {
    // 表格行高36px 字号14px 表头文字加粗
    .el-table {
      th,
      tr,
      td {
        height: 36px !important;
      }

      .cell {
        height: 36px !important;
        line-height: 17px !important;
        font-size: var(--el-font-size-base) !important;
      }
    }
  }
}

.el-tabs__nav-wrap::after {
  height: 1px !important;
}
.ports-pc {
  min-width: 1280px !important;
  min-height: 720px !important;
  overflow: auto !important;
  font-size: var(--el-font-size-base) !important;
  padding: 4px 16px 16px 16px !important;
  background-color: rgba(0, 0, 0, 0) !important;
  > div {
    background-color: #fff !important;
  }

  // 表格行高36px 字号14px 表头文字加粗
  .el-table {
    th,
    tr,
    td {
      height: 36px !important;
    }

    .cell {
      height: 36px !important;
      line-height: 17px !important;
      font-size: var(--el-font-size-base) !important;
    }
  }

  // 全局字号默认14px（特殊处理）
  .el-tabs__item {
    font-size: var(--el-font-size-base) !important;
  }

  // 全局切换el-radio-group样式(模板：债市一览-利差分析-信用利差，中短期票据和城投债切换)
  .public-radio-group {
    .is-active {
      .el-radio-button__inner {
        border-left: none !important;
        height: 34px !important;
        box-sizing: border-box !important;
        background-color: var(--theme--color) !important;
      }
    }

    .el-radio-group {
      display: flex !important;
      align-items: center;
      gap: 8px;
      height: 34px !important;

      .el-radio-button {
        height: 34px !important;
      }

      .el-radio-button__inner {
        padding-left: 12px !important;
        padding-right: 12px !important;
        height: 34px !important;
        box-sizing: border-box !important;
        border-radius: 2px !important;
        line-height: 14px !important;
        font-size: var(--el-font-size-base) !important;
        background-color: #f4f4f4;
        border: none !important;
      }
    }
  }

  // 全局切换el-tabs样式(模板：债市一览-利差分析, 信用利差、发行利差、估值利差切换)
  .public-tabs-container {
    .el-tabs {
      width: 100%;
      height: 40px;
      padding: 0px 16px 0 16px !important;
      box-sizing: border-box;
      background-color: #ffffff;

      .el-tabs__item {
        font-size: var(--el-font-size-base) !important;
      }
    }

    .el-tabs__nav-wrap {
      overflow: visible !important;
    }

    .el-tabs__nav-wrap::after {
      content: '' !important;
      display: block !important;
      position: absolute !important;
      left: 0 !important;
      bottom: 0 !important;
      width: 100% !important;
      height: 1px !important;
      background-color: #e4e7ed !important;
      z-index: 1 !important;
    }
  }

  // 全局el-tabs__item样式（模板：我的债券-评级信息，tab栏）
  .public-el-tabs {
    ::v-deep .el-tabs__item {
      font-size: var(--el-font-size-base) !important;
    }
  }

  // 全局单行搜索栏样式(模板：我的债券-评级信息 查询栏)
  // 只设置了padding、下边框和背景色，其余样式自行调整
  .public-table-search-container {
    padding: 8px 16px !important;
    border-bottom: 1px solid #eae9e9 !important;
    background: #fff !important;
  }

  .public-table-search-container.long-form {
    padding: 8px 16px 0 !important;

    .el-form-item {
      margin-bottom: 8px;
    }
  }

  // 全局小标题容器(模板：我的债券-我司估值 我司新券行情)
  .public-title-container {
    padding-top: 8px !important;
    padding-bottom: 8px !important;
    font-family: MicrosoftYaHeiSemibold;
    font-size: var(--el-font-size-base);
    color: rgba(0, 0, 0, 0.85);
    line-height: 22px;
    text-align: left;
    font-style: normal;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 8px !important;
    background-color: #fff !important;
  }

  .public-title-container::before {
    content: '';
    display: inline-block !important;
    width: 3px;
    height: 16px !important;
    background-color: var(--theme--color);
    border-radius: 1px;
  }
}
.ports-pc.cockpit {
  background-color: transparent !important;
  > div {
    background-color: transparent !important;
  }
}

// 头部表单使用form组件样式统一规范化(模板：债市一览-发行查询)
.vertical-layout {
  &--top {
    // height: auto !important;
    min-height: 48px !important;
  }

  &--top-content {
    padding: 0 16px;

    .el-scrollbar__wrap {
      margin: 0 !important;
      overflow: auto;

      .el-form {
        padding-top: 8px;
        // flex-wrap: wrap;

        .el-form-item {
          width: calc(25% - 8px);
          margin-right: 8px;
          margin-bottom: 8px;

          .el-form-item__content {
            height: 32px;
            line-height: 32px;
          }
        }
      }
    }
  }
}

// 头部表单使用form组件样式统一规范化-低分辨率适配(模板：债市一览-发行查询)
@media screen and (max-width: 1600px) {
  .vertical-layout {
    &--top-content {
      .el-scrollbar__wrap {
        .el-form {
          .el-form-item {
            width: calc(33.33% - 8px);
          }
        }
      }
    }
  }
}
