<template>
  <div class="issuancePricing">
    <div class="public-tabs-container">
      <el-tabs v-model="tabActiveName" class="issuancePricing-tabs">
        <el-tab-pane label="实时定价" name="realTimePricing" />
        <el-tab-pane label="历史定价" name="historicalPricing" />
      </el-tabs>
    </div>
    <component :is="tabActiveName" />
  </div>
</template>

<script>
import realTimePricing from './components/realTimePricing.vue'
import historicalPricing from './components/historicalPricing.vue'
export default {
  name: 'IssuancePricing',
  components: {
    realTimePricing,
    historicalPricing
  },
  data() {
    return {
      tabActiveName: 'realTimePricing'
    }
  }
}
</script>

<style lang="scss" scoped>
.issuancePricing {
  position: relative;
  // &-tabs {
  //   width: 100%;
  //   height: 56px;
  //   padding: 12px 0px 0px 16px;
  //   box-sizing: border-box;
  //   background-color: #ffffff;
  //   position: sticky;
  //   top: 0px;
  //   left: 0px;
  //   z-index: 2;
  // }
}
</style>
