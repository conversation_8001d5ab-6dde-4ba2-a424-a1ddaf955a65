<template>
  <!-- 注册额度 -->
  <!--  公司债自定义列 6d4e0105257344f4b2307a910fffef48-->
  <!-- 协会债自定义列 a253d521b12440609efa6bab8f71cb16-->
  <div class="registration-limit">
    <div class="public-tabs-container">
      <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="协会债" name="first" />
        <el-tab-pane label="公司债" name="second" />
      </el-tabs>
    </div>

    <div class="public-table-search-container">
      <el-form :model="form" inline label-width="70px">
        <jr-form-item label="关键字">
          <el-input
            v-model="form.keyWord"
            style="height: 32px; margin-right: 14px"
            placeholder="请输入注册文件号/主承销商"
          />
        </jr-form-item>

        <jr-form-item label="项目类型">
          <jr-combobox
            v-model="form.projectType"
            style="height: 32px; margin-right: 14px"
            placeholder="请选择"
            clearable
            filterable
            :multiple="true"
            :collapse-tags="true"
            :data="activeName === 'first' ? bondTypeAssList : bondTypeComList"
            option-value="itemcode"
            option-label="cnname"
          />
        </jr-form-item>

        <jr-form-item label="到期日期" class="fix-date-style">
          <el-date-picker
            v-model="form.dateArray"
            style="height: 32px; margin-right: 14px"
            type="daterange"
            range-separator="至"
            unlink-panels
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="yyyy-MM-dd"
            class="date-picker"
          />
        </jr-form-item>

        <jr-form-item>
          <el-checkbox v-model="form.isExpired">含已到期</el-checkbox>
        </jr-form-item>

        <jr-form-item style="margin-left: 24px">
          <el-button type="primary" @click="submit">查询</el-button>
        </jr-form-item>
      </el-form>
    </div>
    <div style="height: calc(100% - 166px); background-color: #fff; display: flex; flex-direction: column">
      <jr-decorated-table
        v-if="activeName === 'first'"
        ref="table"
        class="registration-limit-table"
        style="flex: 1"
        stripe
        key="firstTable"
        :permitdetail="{
          ...permitdetail,
          export: { icon: 'upload', name: '导出', componenturl: 'export', btnkey: 'export' }
        }"
        :handleexport="exportData"
        :params="tableParams"
        custom-id="a253d521b12440609efa6bab8f71cb16"
        :menuinfo="menuinfo"
        :custom-render="customRender"
        :handle-registration-materials="handleregistrationMaterials"
        v-bind="{ ...$props }"
        @refreshed="callFn"
      />
      <jr-decorated-table
        v-else
        ref="table"
        class="registration-limit-table"
        style="flex: 1"
        stripe
        key="otherTable"
        :permitdetail="{
          ...permitdetail,
          export: { icon: 'upload', name: '导出', componenturl: 'export', btnkey: 'export' }
        }"
        :handleexport="exportData"
        :params="tableParams"
        custom-id="6d4e0105257344f4b2307a910fffef48"
        :menuinfo="menuinfo"
        :custom-render="customRender"
        :handle-registration-materials="handleregistrationMaterials"
        v-bind="{ ...$props }"
        @refreshed="callFn"
      />
      <decorTable v-if="false" />
      <span v-if="activeName === 'second'" class="registration-limit-tips">
        <jr-svg-icon icon-class="info-circle" />
        仅展示本企业部分注册通知书额度详情。
      </span>

      <span v-if="activeName === 'first'" class="registration-limit-tips-top">
        <jr-svg-icon icon-class="info-circle" />
        仅展示本企业部分注册通知书额度详情。
      </span>

      <span v-if="activeName === 'first'" class="registration-limit-tips">
        <jr-svg-icon icon-class="info-circle" />
        永续票据纳入中期票据品种展示。
      </span>
    </div>
    <issuanceMaterials ref="issuanceMaterials" />
  </div>
</template>
<script>
import moment from 'moment'
import { GetComboboxList } from '@/api/home'
import { registrationQuotaExport } from '@/api/reg/reg.js'
import issuanceMaterials from '@/systems/mine-bonds/group-bonds/dialogs/issuanceMaterials.vue'
import decorTable from '@/systems/mine-bonds/associated-enterprise/components/decorTable'
const BONDTYPE_COMPANY = 'BONDTYPE_COMPANY' // 项目类型字典项
const BONDTYPE_ASSOCIATION = 'BONDTYPE_ASSOCIATION' // 项目类型字典项
export default {
  components: {
    issuanceMaterials,
    decorTable
  },
  props: {
    menuinfo: {
      type: Object,
      default: () => ({})
    },
    // 权限相关
    permitdetail: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      bondTypeAssList: [],
      bondTypeComList: [], // 项目类型列表
      activeName: 'first',
      timelineData: {}, // 存储时间线数据
      customRender: {
        filenum: (h, { rowIndex, row }) => {
          return (
            <el-popover
              placement='top-start'
              trigger='click'
              class='custom-popover-item'
              onShow={() => {
                this.handlePopoverShow(rowIndex)
              }}
            >
              <span
                slot='reference'
                style='color:var(--theme--color);cursor:pointer;'
                popperClass='detail-popover'
                width='800'
                onClick={() => {
                  this.openPopover(row)
                }}
              >
                {row.filenum}
              </span>
              {row.objectId === this.compId ? (
                <decorTable customId={'befb38cb2c6d45fe883e0203bf79bb15'} filenum={row.filenum}></decorTable>
              ) : null}
            </el-popover>
          )
        }
      },
      form: {
        keyWord: '',
        projectType: [],
        dateArray: [], // 到期日期
        isExpired: false
      },
      companyCode: '013C0qYPAI', // 系统选择的公司code
      projectTypeList: [],
      tableParams: {
        companyCode: '', // 系统选择的公司code
        ccid: 'a253d521b12440609efa6bab8f71cb16',
        ownedModuleid: '1362426695289679872',
        isExpired: '0'
      },
      isExpanded: {}, // 记录每行的展开状态
      detailTableParams: {},
      columns: [], // 新增：存储详情表格的查询参数
      compId: null
    }
  },
  created() {
    console.log(this.menuinfo)

    this.getDictOptionsApi()
  },
  methods: {
    verifyPermissions(str) {
      if (this.menuinfo && Array.isArray(this.menuinfo.btnList) && this.menuinfo.btnList.length > 0) {
        const arr = this.menuinfo.btnList.filter((item) => {
          return item.btnnm === str && Object.prototype.hasOwnProperty.call(this.permitdetail, item.btnkey)
        })
        return Array.isArray(arr) && arr.length > 0
      } else {
        return false
      }
    },
    // 打开新的popver关闭之前的popver
    handlePopoverShow(index) {
      this.$nextTick(() => {
        const elList = document.querySelectorAll('.custom-popover-item')

        for (let i = 0; i < elList.length; i++) {
          const popoverInstance = elList[i].__vue__

          if (i !== index) {
            if (popoverInstance?.doClose) popoverInstance.doClose()
          }
        }
      })
    },
    async getDictOptionsApi() {
      const res = await GetComboboxList([BONDTYPE_ASSOCIATION, BONDTYPE_COMPANY])
      this.bondTypeAssList = res[BONDTYPE_ASSOCIATION]
      this.bondTypeComList = res[BONDTYPE_COMPANY]
    },
    handleregistrationMaterials(...args) {
      if (Array.isArray(args) && args.length >= 3) {
        const rowData = args[2]
        if (Object.prototype.hasOwnProperty.call(rowData, 'hasfile') && rowData.hasfile === 1) {
          this.openIssuanceMaterials(rowData)
        } else {
          this.$message.warning('暂无注册材料')
        }
      }
    },
    openPopover(row) {
      this.compId = row.objectId
    },
    callFn(data) {
      this.columns = data.config.columns
      this.sort = data.sort
      this.direction = data.direction
    },
    handleClick() {
      this.form = {
        keyWord: '',
        projectType: [],
        dateArray: [], // 到期日期
        isExpired: false
      }
      this.tableParams = {
        companyCode: '', // 系统选择的公司code
        ccid: this.activeName === 'first' ? 'a253d521b12440609efa6bab8f71cb16' : '6d4e0105257344f4b2307a910fffef48',
        ownedModuleid: '1362426695289679872',
        isExpired: '0'
      }
      this.submit()
    },
    // 查询导出之前处理参数
    deelWithParams() {
      const obj = {
        keyWord: this.form.keyWord,
        isExpired: this.form.isExpired ? '1' : '0'
      }
      if (Array.isArray(this.form.dateArray) && this.form.dateArray.length > 0) {
        obj.startDate = moment(this.form.dateArray[0]).format('YYYY-MM-DD')
        obj.endDate = moment(this.form.dateArray[1]).format('YYYY-MM-DD')
      } else {
        obj.startDate = ''
        obj.endDate = ''
      }
      if (Array.isArray(this.form.projectType) && this.form.projectType.length > 0) {
        obj.projectType = this.form.projectType
      } else {
        obj.projectType = null
      }

      return {
        ...this.tableParams,
        ...obj,
        webTime: new Date().getTime()
      }
    },
    submit() {
      this.tableParams = this.deelWithParams()
    },
    // 批量导出
    async exportData() {
      const params = {
        params: {
          ...this.deelWithParams(),
          pageInfo: {},
          filename: this.activeName === 'first' ? '协会债注册明细' : '公司债明细',
          column: this.columns,
          selectData: null,
          dataSource: this.activeName === 'first' ? '1' : '2'
        },
        page: {
          sort: this.sort,
          direction: this.direction
        }
      }
      await registrationQuotaExport(params)
    },
    async getComboboxList() {
      // RELATIONSHIP 待替换
      const data = await GetComboboxList(['RELATIONSHIP'])
      if (data && data.RELATIONSHIP) {
        this.projectTypeList = data.RELATIONSHIP
      }
    },
    /**
     * 打开注册资料弹框
     */
    openIssuanceMaterials(row) {
      const params = {
        ownedModuleid: '1362426695289679872',
        ccid: 'acb6ac768a6e433493dc60febcd9f972',
        filenum: row.filenum
      }
      this.$refs.issuanceMaterials.open(params, row, '注册材料')
    }
  }
}
</script>
<style lang="scss" scoped>
.fix-date-style {
  ::v-deep .el-form-item__content {
    position: relative;
    width: 308px;

    .el-date-editor {
      position: absolute;
      top: 0;
    }
  }
}

.registration-limit {
  position: relative;
  padding: 0 !important;
  margin: 8px 16px 0px 16px !important;
  background-color: #fff !important;

  ::v-deep .el-form-item__label {
    padding-top: 8px !important;
  }
  height: calc(100% - 24px);
  display: flex;
  flex-direction: column;

  &-tabs {
    ::v-deep .el-tabs__header {
      background: #fff;
      margin: 0;
      .el-tabs__nav-wrap::after {
        height: 2px !important;
      }
      .el-tabs__nav-scroll {
        margin-left: 16px;
      }
      .el-tabs__item {
        height: 56px;
        line-height: 56px;
        font-family: MicrosoftYaHeiSemibold;
        font-size: 20px;
        color: rgba(0, 0, 0, 0.6);
        text-align: left;
        font-style: normal;
      }
      .el-tabs__item.is-active {
        color: var(--theme--color) !important;
      }
    }
  }
  &-form {
    background: #fff;
    border-bottom: 1px solid #eae9e9;
    padding: 9px 0;

    ::v-deep .el-checkbox {
      .el-checkbox__input {
        margin-bottom: 6px;
      }
    }
  }
  &-table {
    background: #fff;
    .jr-decorated-table--header {
      display: none;
    }
    .jr-decorated-table--header-left {
      width: 100%;
      padding: 3px 16px;
    }
  }
  &-btn {
    // margin-top: 16px;
    background: #fff;
    padding: 11px 16px 7px 0;
    text-align: right;
  }
  &-tips {
    position: absolute;
    left: 32px;
    bottom: 22px;

    font-size: 14px;
    color: rgba(0, 0, 0, 0.6);
  }
}
.registration-limit-tips-top {
  position: absolute;
  left: 32px;
  bottom: 44px;

  font-size: 14px;
  color: rgba(0, 0, 0, 0.6);
}

.progress-popover {
  padding-left: 8px !important;
  font-size: 12px !important;
  .el-timeline {
    .el-timeline-item {
      padding-bottom: 15px;
    }
    .el-timeline-item__wrapper {
      padding-left: 15px !important;
    }
    .current-item {
      .el-timeline-item__wrapper {
        .el-timeline-item__timestamp,
        .el-timeline-item__content {
          color: var(--theme--color) !important;
        }
      }
    }
    .normal-item {
      .el-timeline-item__wrapper {
        .el-timeline-item__timestamp,
        .el-timeline-item__content {
          color: #bfbfbf !important;
        }
      }
    }
  }
}
</style>
