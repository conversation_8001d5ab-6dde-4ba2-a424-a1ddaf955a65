<template>
  <div class="useofRaisedFunds">
    <div class="public-tabs-container">
      <el-tabs v-model="tabActiveName">
        <el-tab-pane label="债券类" name="1" />
        <el-tab-pane label="非债券类" name="2" />
      </el-tabs>
    </div>
    <TableSelect
      :tab-active-name="tabActiveName"
      @setTableSelectParams="setTableSelectParams"
    />
    <div style="height: calc(100% - 132px); margin-top: 1px; background-color: #ffffff; position: relative">
      <jr-decorated-table
        ref="table"
        :params="{
          ownedModuleid: '708631605142536192',
          ...selectParams
        }"
        stripe
        style="height: 100%"
        :custom-id="tabActiveName === '1' ? 'f26c577592e640e3b99209f454e7a7c2' : '17ac5246304a4877b9976c550cd8ac4b'"
        :custom-render="customRender"
        :tab-active-name="tabActiveName"
        :modal-footer="false"
        :width="'58%'"
        :key="tabActiveName"
        :modal-title="tabActiveName === '1' ? '债券类' : '非债券类'"
        :handlecustom="customBtnClick"
        v-bind="{
          ...$attrs,
          ...$props
        }"
      />
      <p class="useofRaisedFunds-tips" @click.stop="test">
        <jr-svg-icon icon-class="info-circle" />
        <span>页面仅展示未到期数据，系统将会自动清除已到期数据</span>
      </p>
    </div>

    <table-editor v-if="false" />
  </div>
</template>

<script>
import tableEditor from './tableEditor.vue'
import TableSelect from './components/TableSelect.vue'
import { EventBus } from '../../redemption-manage/event-bus'

export default {
  components: {
    tableEditor,
    TableSelect
  },
  props: {
    // 权限相关
    permitdetail: {
      type: Object,
      default: () => ({})
    },
    menuinfo: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      tabActiveName: '1',
      id: '',
      customRender: {
        entName: (h, { rowIndex, row }) => {
          return (
            <el-popover
              placement='bottom-start'
              trigger='click'
              class='custom-popover-item'
              onShow={() => {
                this.handlePopoverShow(rowIndex)
              }}
              onHide={() => {
                this.setTableSelectParams({})
              }}
            >
              <span
                slot='reference'
                style='color:var(--theme--color);cursor:pointer;'
                onClick={() => {
                  this.openPopover(row)
                }}
              >
                {row.entName}
              </span>
              {row.id === this.id ? <tableEditor id={this.id} couldEdit={this.couldEdit}></tableEditor> : null}
            </el-popover>
          )
        }
      },
      selectParams: {},
      userInfo: {},
      outCompCode: ''
    }
  },
  async created() {
    this.outCompCode = this.getOutCompCode()
    console.log(this.outCompCode, 'outCompCode')
    EventBus.$on('refresh-use-of-raised-funds-list', () => {

      this.selectParams = { ...this.selectParams, webTime: new Date().getTime() }
    })
  },
  computed: {
    customId() {
      return this.tabActiveName === '1' ? 'f26c577592e640e3b99209f454e7a7c2' : '17ac5246304a4877b9976c550cd8ac4b'
    },
    couldEdit() {
      return Object.prototype.hasOwnProperty.call(this.permitdetail, 'plan')
    }
  },
  mounted() {
    this.$nextTick(() => {
      console.log(this.$refs)
    })
  },
  methods: {
    getOutCompCode() {
      if (!Object.hasOwnProperty.call(this.$store.getters, 'personInfo')) return ''
      if (!Object.hasOwnProperty.call(this.$store.getters.personInfo, 'outCompCode')) return ''
      return this.$store.getters.personInfo.outCompCode
    },
    getOutCompName(list) {
      if (!Array.isArray(list) || (Array.isArray(list) && list.length === 0)) return ''
      if (!Object.hasOwnProperty.call(this.$store.getters, 'userInfo')) return ''
      if (!Object.hasOwnProperty.call(this.$store.getters.userInfo, 'orgname')) return
    },
    test() {
      console.log(this.menuinfo)
      console.log(this.permitdetail)
    },
    /**
     * 获取数据详情
     */
    openPopover(row) {
      this.id = row.id
    },
    handlePopoverShow(index) {
      this.$nextTick(() => {
        const elList = document.querySelectorAll('.custom-popover-item')

        for (let i = 0; i < elList.length; i++) {
          const popoverInstance = elList[i].__vue__

          if (i !== index) {
            if (popoverInstance?.doClose) popoverInstance.doClose()
          }
        }
      })
    },
    /**
     * 设定查询参数
     */
    setTableSelectParams(data) {
      this.selectParams = { ...data, webTime: new Date().getTime(), ent_id: this.outCompCode }
    },
    /**
     * 自定义列头部弹框打开拦截
     */
    customBtnClick(...args) {
      this.$store.dispatch('tagsView/updateCurrentTab', {
        path: '/1361284917287215104',
        meta: { params: { id: 1 }, query: { id: 1 } }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .jr-decorated-table > div {
  padding-left: 16px;
  padding-right: 16px;
}
::v-deep .jr-decorated-table--top-search .jr-decorated-table--header-left {
  padding: 8px 0px !important;
}
::v-deep .jr-decorated-table--header-left button:nth-last-of-type(1) {
  background: #ffffff;
  color: #000;
  border: 1px solid #ccc;
}
.useofRaisedFunds {
  height: 100%;
  position: relative;
  &-tabs {
    width: 100%;
    height: 56px;
    padding: 12px 0px 0px 16px;
    box-sizing: border-box;
    background-color: #ffffff;
  }
  &-tips {
    display: flex;
    align-items: center;
    position: absolute;
    bottom: 4px;
    left: 32px;
    color: rgba(0, 0, 0, 0.6);
  }
}
</style>
