<template>
  <jr-modal
    modal-class="defined-modal"
    :handle-cancel="() => handleCancel()"
    :handle-ok="handleOk"
    :visible="visible"
    size="large"
    ok-text="完成"
  >
    <slot name="title">自定义列表自选</slot>
    <template v-slot:body>
      <div class="defined-modal-content">
        <div class="defined-modal-content-item">
          <div class="defined-modal-content-item-header">
            <div class="defined-modal-content-item-header-left">待选债券</div>
            <div class="defined-modal-content-item-header-right">
              已选择
              <span style="color: #f56c6c">{{ selectRows.length }}</span>
              条
            </div>
          </div>
          <div class="defined-modal-content-item-content jrtable">
            <div class="defined-modal-content-item-content-search">
              <el-input
                v-model="keyword"
                placeholder="债券代码/债券简称/发行人"
                suffix-icon="el-icon-search"
                style="height: 32px"
                clearable
                @input="handleSearchLeft"
              />
            </div>
            <div class="defined-modal-content-item-content-table">
              <jr-decorated-table
                ref="jrTable"
                :params="tableParams"
                custom-id="325a159d830a4c7b843cbf3a8197fd2a"
                v-bind="{ ...$props }"
                style="height: calc(100% - 75px); width: 100%"
                @handleSelectionChange="getSelectRows"
              />
            </div>
          </div>
          <div class="defined-modal-content-item-page" />
        </div>
        <div class="defined-modal-content-item">
          <div class="defined-modal-content-item-header">
            <div class="defined-modal-content-item-header-left">
              <span>已选债券</span>
              <span style="color: #e6a23c">
                <jr-svg-icon icon-class="info-circle" />
                最多50条
              </span>
            </div>
            <div class="defined-modal-content-item-header-right">
              <el-button type="text" style="height: 20px; background-color: #fff" @click="deleteAll">
                <jr-svg-icon icon-class="delete" />
                清空所选
              </el-button>
            </div>
          </div>
          <div class="defined-modal-content-item-content">
            <div class="defined-modal-content-item-content-search">
              <el-input
                v-model="rightKeyword"
                placeholder="债券代码/债券简称/发行人"
                suffix-icon="el-icon-search"
                style="height: 32px"
                clearable
                @input="getTableData"
              />
            </div>
            <div class="defined-modal-content-item-content-table">
              <jr-table :data-source="tableData" :height="591">
                <template v-slot:index>
                  <el-table-column type="index" width="80px" align="center" label="序号" />
                </template>
                <el-table-column prop="bondCode" label="债券代码" />
                <el-table-column prop="bondName" label="债券简称" />
                <el-table-column prop="bInfoIssuer" label="发行人" />
                <template v-slot:action>
                  <el-table-column label="操作" width="80" align="center">
                    <template slot-scope="scope">
                      <div class="table-action-box">
                        <el-popconfirm title="确定删除吗？" @confirm="handleDelete(scope.row)">
                          <jr-svg-icon slot="reference" icon-class="delete" title="删除" />
                        </el-popconfirm>
                      </div>
                    </template>
                  </el-table-column>
                </template>
              </jr-table>
            </div>
          </div>
          <div class="defined-modal-content-item-page" />
        </div>
      </div>
    </template>
  </jr-modal>
</template>
<script>
import { saveBatchCustomizeBond, querySelectedBondList, deleteCustomizeBond } from '@/api/bonds/bonds'
import { debounce } from 'lodash'
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    permitdetail: {
      type: Object,
      default: () => ({})
    },
    menuinfo: {
      type: Object,
      default: () => ({})
    },
    module: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      keyword: '',
      rightKeyword: '',
      tableData: [],
      allTableData: [],
      tableParams: {
        ownedModuleid: '1352315117735067648',
        module: this.module
      },
      selectRows: [],
      maxBondCount: 0
    }
  },

  created() {
    this.handleSearchLeft = debounce(this.handleSearchLeft, 500)
    this.getTableData = debounce(this.getTableData, 500)
    this.getSelectRows = debounce(this.getSelectRows, 500)
    this.getTableData()
  },
  methods: {
    getSelectRows(rows, listData) {
      this.selectRows = rows
    },
    getTableData() {
      querySelectedBondList({ inputvalue: this.rightKeyword, module: this.module }) // 企业id 暂时为空
        .then((res) => {
          console.log(res)
          this.tableData = res
          if (this.rightKeyword === '') {
            this.allTableData = res
            this.maxBondCount = this.tableData.length
          }
        })
    },

    handleSearchLeft() {
      this.tableParams = this.resetSearch({
        ...this.tableParams,
        inputvalue: this.keyword,
        module: this.module
      })
    },
    resetSearch(obj) {
      if (Object.hasOwnProperty.call(obj, 'searchFlag')) {
        delete obj['searchFlag']
      } else {
        obj['searchFlag'] = '1'
      }
      return obj
    },
    handleDelete(row) {
      deleteCustomizeBond([row]) // 企业id 暂时为空
        .then((res) => {
          this.keyword = ''
          this.rightKeyword = ''
          this.handleSearchLeft()
          this.getTableData()
        })
    },
    deleteAll() {
      if (Array.isArray(this.allTableData) && this.allTableData.length > 0) {
        deleteCustomizeBond(this.allTableData) // 企业id 暂时为空
          .then((res) => {
            this.keyword = ''
            this.rightKeyword = ''
            this.handleSearchLeft()
            this.getTableData()
          })
      } else {
        this.$message.warning('已选债券为空')
      }
    },
    handleOk() {
      if (Array.isArray(this.selectRows) && this.selectRows.length > 0) {
        if (Array.isArray(this.selectRows) && this.selectRows.length + this.maxBondCount > 50) {
          this.$message.warning('最多只能选择50条债券')
          return
        }

        const params = []
        this.selectRows.forEach((item) => {
          params.push({
            bondCode: item.sInfoWindcode,
            bondName: item.sInfoName,
            bInfoIssuer: item.bInfoIssuer,
            bInfoIssuercode: item.bInfoIssuercode,
            bIssueLastissue: item.bIssueLastissue,
            module: this.module
          })
        })
        saveBatchCustomizeBond(params) // 企业id 暂时为空
          .then((res) => {
            this.keyword = ''
            this.rightKeyword = ''
            this.handleSearchLeft()
            this.getTableData()
          })
      } else {
        this.$message.warning('请选择债券')
      }
    },
    handleCancel() {
      this.$emit('close')
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-form-item__content {
  margin-left: 0 !important;
}

::v-deep .is-center {
  .cell {
    .el-checkbox {
      width: 100% !important;
      height: 100% !important;
      align-items: center;
      display: flex;
      justify-content: center;
    }
  }
}

::v-deep .jr-decorated-table--header-left {
  display: none !important;
}

.jrtable {
  ::v-deep .el-table__body-wrapper {
    height: calc(100%) !important;
  }

  ::v-deep .jr-decorated-table--body {
    height: calc(100%) !important;
    .jr-table {
      height: calc(100%) !important;
      .el-table {
        height: calc(100%) !important;
      }
    }
  }

  ::v-deep .jr-pagination {
    position: absolute;
    right: 0;
    bottom: 5px;
  }

  ::v-deep .jr-decorated-table--body {
    padding-left: 0 !important;
    padding-right: 0 !important;
  }

  ::v-deep .is-scrolling-none {
    height: calc(100%) !important;
  }
}

.table-action-box {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
}

.defined-modal {
  &-content {
    display: flex;
    align-items: center;
    height: 100%;
    width: 100%;
    gap: 42px;
    // padding: 24px;

    &-item {
      height: 100%;
      flex: 1;
      flex-grow: 1;
      flex-basis: 0;
      min-width: 0;

      &-header {
        height: 30px;
        font-family: MicrosoftYaHeiSemibold;
        font-size: var(--el-font-size-base);
        color: rgba(0, 0, 0, 0.85);
        line-height: 30px;
        font-style: normal;
        display: flex;
        align-items: center;
        justify-content: space-between;

        &-left {
          text-align: left;
          display: flex;
          align-items: center;
          gap: 16px;
        }

        &-right {
          text-align: right;
        }
      }

      &-content {
        border-radius: 4px;
        border: 1px solid #cccccc;
        height: calc(100% - 74px);
        width: 100%;

        &-search {
          padding: 16px;
        }

        &-table {
          width: 100%;
          height: 100%;
          position: relative;
        }
      }

      &-page {
        width: 100%;
        height: 44px;
      }
    }
  }
}
</style>
