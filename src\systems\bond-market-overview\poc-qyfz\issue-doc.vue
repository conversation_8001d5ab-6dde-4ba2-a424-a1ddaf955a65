<template>
  <!-- POC专用 发行材料 -->
  <div class="issue-doc">
    <div class="issue-doc--left">
      <h3>发行材料</h3>
      <ul>
        <li :class="[active === 0 && 'active']" @click="active = 0"><jr-svg-icon icon-class="folder" />募集说明书（1）</li>
        <li :class="[active === 1 && 'active']" @click="active = 1"><jr-svg-icon icon-class="folder" />评级报告（0）</li>
        <li :class="[active === 2 && 'active']" @click="active = 2"><jr-svg-icon icon-class="folder" />法律意见书（0）</li>
        <li :class="[active === 3 && 'active']" @click="active = 3"><jr-svg-icon icon-class="folder" />财务报告（0）</li>
        <li :class="[active === 4 && 'active']" @click="active = 4"><jr-svg-icon icon-class="folder" />发行情况报告（0）</li>
      </ul>
    </div>
    <div class="issue-doc--right">
      <div v-if="active === 0" class="item-list">
        <div class="name">国药控股（中国）融资租赁有限公司2024年面向专业投资者公开发行公司债券（第三期）募集说明书
          <el-link @click="download('国药控股（中国）融资租赁有限公司2024年面向专业投资者公开发行公司债券（第三期）募集说明书')"><jr-svg-icon icon-class="download" />下载</el-link>
        </div>
        <div style="color: #ccc;">
          <span style="margin-right: 20px;">{{ itemData.zqjc || '--' }}</span> 募集说明书
        </div>
      </div>
      <jr-empty v-else />
    </div>
  </div>
</template>

<script>
import { ExportFn } from '@jupiterweb/utils/api'

export default {
  props: {
    itemData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      active: 0
    }
  },
  created() {
  },
  methods: {
    download(filename) {
      ExportFn('/config/public/download/abc.docx', undefined, undefined, undefined, filename + '.docx')
    }
  }
}
</script>

<style lang="scss" scoped>
.issue-doc {
  display: flex;
  width: 100%;
  height: 100%;
  &--left {
    width: 200px;
    height: 100%;
    background: #f5f5f5;
    h3 {
      font-size: 16px;
      font-weight: 500;
      padding: 10px;
      margin-bottom: 0;
    }
    ul {
      padding: 0;
      margin: 0;
      li {
        list-style: none;
        line-height: 30px;
        padding: 0 10px;
        display: flex;
        align-items: center;
        cursor: pointer;
        .jr-svg-icon {
          margin-right: 10px;
        }
        &.active {
          background-color: #fff;
        }
      }
    }
  }
  &--right {
    width: calc(100% - 200px);
    padding: 20px;
    flex: 1;
    height: 100%;
    border: 1px solid #e8e8e8;
    border-left: none;
  }
  .name {
    display: flex;
    justify-content: space-between;
    font-size: 14px;
  }
}
</style>
