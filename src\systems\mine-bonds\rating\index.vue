<!-- 发行明细 -->
<template>
  <div class="bondsDetail">
    <div class="public-table-search-container search-container">
      <div v-if="$store.getters.sysVersion === $dict.COMPANY_VER_group" class="search-item" style="display: flex; align-items: center;">
        <div class="label" style="width:auto; text-align: right; padding-right: 12px">发行人</div>
        <el-select
          v-model="searchForm.mainCaliber"
          placeholder="请选择"
          clearable
          filterable
          style="width: 285px"
          @change="queryList"
        >
          <el-option v-for="item in mainCaliberList" :key="item.value" :label="item.text" :value="item.value" />
        </el-select>
      </div>
      <div class="search-item" style="display: flex; align-items: center;">
        <div class="label" style="width: 100px; text-align: right; padding-right: 12px">评级日期</div>
        <el-date-picker
          v-model="searchForm.backTime"
          type="daterange"
          unlink-panels
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd"
          format="yyyy-MM-dd"
          style="width: 280px !important; height: 32px"
          clearable
          @change="dateChange"
        />
      </div>
      <div class="search-btn" style="margin-left: 25px;">
        <div class="btn-wrapper">
          <el-button type="primary" @click="queryData">查询</el-button>
        </div>
      </div>
    </div>
    <div class="bondsDetailChart">
      <div class="chartLeft">
        <div class="chartLeftTop">
          <div class="topTitle">主体评级</div>
          <div class="topRight">
            <div class="topRightItem">
              <jr-combobox
                v-model="value2"
                option-label="cnname"
                option-value="itemcode"
                style="width: 308px; height: 32px"
                :data="originalOptions"
                :multiple="true"
                clearable
                collapse-tags
                :popper-append-to-body="false"
                @change="handleAgencyChange"
              />
            </div>
            <div class="importBtn">
              <!-- <el-dropdown trigger="click">
                                <el-button style="color: rgba(0, 0, 0, 0.90);width: 92px;height: 28px;">
                                    <jr-svg-icon class="el-icon--left" icon-class="download" />
                                    导出<i class="el-icon-caret-bottom el-icon--right" />
                                </el-button>
                                <el-dropdown-menu slot="dropdown">
                                    <el-dropdown-item>导出为图片</el-dropdown-item>
                                </el-dropdown-menu>
                            </el-dropdown> -->
              <el-button @click="exportMainRatingChart">
                <jr-svg-icon icon-class="upload" />
                导出
              </el-button>
            </div>
          </div>
        </div>
        <div ref="chartLeftBottom" class="chartLeftBottom">
          <!-- 使用封装的评级图表组件 -->
          <RatingChart
            ref="mainRatingChart"
            :rating-data="ratingChartData"
            :legend-status="legendStatus"
            :tooltip-title="tooltipTitle"
            :rating-colors="ratingColors"
            @legend-click="handleLegendClick"
          />
        </div>
      </div>
      <div class="chartRight">
        <div class="chartRightTop">
          <div class="topTitle">中债隐含评级</div>
          <div class="importBtn">
            <el-button @click="exportImpliedRatingChart">
              <jr-svg-icon icon-class="upload" />
              导出
            </el-button>
          </div>
        </div>
        <div ref="chartRightBottom" class="chartRightBottom">
          <!-- 中债隐含评级图表 -->
          <RatingChart
            ref="impliedRatingChart"
            :rating-data="impliedRatingData"
            :legend-status="impliedLegendStatus"
            tooltip-title="中债估值隐含评级"
            :rating-colors="impliedRatingColors"
            chart-mode="timeline"
            :simple-mode="true"
            @legend-click="handleImpliedLegendClick"
          />
        </div>
      </div>
    </div>
    <div class="bazaar-tabs public-el-tabs">
      <el-tabs v-model="active" @tab-click="tabClick">
        <el-tab-pane label="主体评级" name="first">
          <div class="bond-details-list">
            <jr-decorated-table
              v-if="activeName === '主体评级表'"
              stripe
              :date="1662022042"
              :params="copyParams"
              custom-id="9784744570374c0abd53ea0e6e28d968"
              :row-click="clickFunc"
              :row-dbl-click="dblClickFunc"
              :default-page-size="10"
              style="height: 468px"
              :menuinfo="{ moduleid: 'xxxx' }"
              :initPagination="{
                pageSizeOptions: [10, 20, 50, 100]
              }"
              @handleSelectionChange="getSelectRows"
              @refreshed="callFn"
            />
          </div>
        </el-tab-pane>
        <el-tab-pane label="中债隐含评级" name="second" :lazy="true">
          <div class="bond-details-list">
            <jr-decorated-table
              v-if="activeName === '中债隐含评级表'"
              stripe
              :date="1662022042"
              :params="copyParams"
              custom-id="57c8079478ea415d84dff7de35939c2b"
              :row-click="clickFunc"
              :row-dbl-click="dblClickFunc"
              :default-page-size="10"
              :initPagination="{
                pageSizeOptions: [10, 20, 50, 100]
              }"
              style="height: 468px"
              :menuinfo="{ moduleid: 'xxxx' }"
              @handleSelectionChange="getSelectRows"
              @refreshed="callFn"
            />
            <span class="bond-details-tips">
              <jr-svg-icon icon-class="info-circle" />
              中债隐含评级来源于债券的隐含评级(剔除ABS、ABN)，也同时代表了主体的隐含评级。
            </span>
          </div>
        </el-tab-pane>
      </el-tabs>
      <div class="bazaar-tabs-btn" @click="exportBondTable">
        <jr-svg-icon icon-class="upload" />
      </div>
    </div>
  </div>
</template>

<script>
import RatingChart from './components/RatingChart.vue'
// import TemplateModule from '@/components/template-module/index.vue'
import {
  getBondRatingInfo,
  //   getBondIssuerInfo,
  getBondRatingAgencyInfo,
  //   exportBondRatingInfo,
  getImpliedRatingChartData,
  queryBondRatingAgency
} from '@/api/bonds/bonds'
import { exportExcelByCustomColumn } from '@/api/public/public'
// import { GetAllOrgan } from '@jupiterweb/api/config-platform/config-params'
import { mapGetters } from 'vuex'

export default {
  components: {
    RatingChart
    // TemplateModule
  },
  data() {
    return {
      active: 'first',
      searchForm: {
        mainCaliber: '',
        backTime: []
      },
      // 发行人
      mainCaliberList: [],
      // 评级机构
      originalOptions: [],
      // 中债隐含评级
      tableParams: {
        companyCode: '',
        endDate: '',
        startDate: '',
        ownedModuleid: '1352319892086145024'
      },
      copyParams: {
        companyCode: '',
        endDate: '',
        startDate: '',
        ownedModuleid: '1352319892086145024'
      },
      customOptions: {
        toolbox: {
          show: false
        }
      },

      // 图例状态(控制显示/隐藏)
      legendStatus: {},

      // 评级图表数据
      ratingChartData: [],

      // 中债隐含评级图表数据
      impliedRatingData: [],

      // 图表弹窗标题
      tooltipTitle: '',

      // 评级颜色配置
      ratingColors: {},

      options: [],
      value2: [],

      tableId: '9784744570374c0abd53ea0e6e28d968',
      columns: null,
      myChart: null,
      loading: false,

      // 中债隐含评级图表的图例状态和颜色
      impliedLegendStatus: {},
      impliedRatingColors: {},
      activeName: '主体评级表',
      selectRows: [],
      originalRatingChartData: null
    }
  },
  computed: {
    ...mapGetters(['currentBond']),
    bondInfo() {
      const { bondInfo = {}} = this.currentBond || {}
      return bondInfo
    },
    bondBaseInfo() {
      const { bondBaseInfo = {}} = this.bondInfo || {}
      return bondBaseInfo
    }
  },
  watch: {
    // 监听评级机构选择变化 - 已通过handleAgencyChange方法直接处理
    /*
    value2: {
      handler(newVal) {
        // 如果没有数据，不执行过滤
        if (!this.ratingChartData || this.ratingChartData.length === 0) return
        // 如果没有选择任何评级机构，显示全部数据
        if (!newVal || newVal.length === 0) {
          this.updateChartWithFullData()
          return
        }
        // 过滤数据，只保留选中的评级机构数据
        this.updateChartWithFilteredData(newVal)
      },
      deep: true
    }
    */
  },
  created() {
    // 初始化日期选择器为近三年
    const defaultRange = this.getDefaultDateRange()
    this.searchForm.backTime = [defaultRange.startDate, defaultRange.endDate]
    
    // 同时初始化tableParams的日期参数
    this.tableParams.startDate = defaultRange.startDate
    this.tableParams.endDate = defaultRange.endDate

    // this.init()
    this.getBondIssuerInfo()
    this.getBondRatingAgencyData()
    this.getRatingChartData()
    // 加载中债隐含评级数据
    this.getImpliedRatingChartData()
  },
  // 添加activated生命周期钩子，处理keep-alive缓存页面的重新激活
  activated() {
    // 当页面重新激活时，确保图表组件能正确重新渲染
    this.$nextTick(() => {
      // 延迟一点时间确保DOM完全恢复
      setTimeout(() => {
        // 如果图表组件存在，强制刷新图表
        if (this.$refs.mainRatingChart) {
          this.$refs.mainRatingChart.forceRefresh()
        }
        if (this.$refs.impliedRatingChart) {
          this.$refs.impliedRatingChart.forceRefresh()
        }

        // 如果没有数据，重新加载数据
        if ((!this.ratingChartData || this.ratingChartData.length === 0) &&
            (!this.impliedRatingData || this.impliedRatingData.length === 0)) {
          console.log('页面激活时重新加载数据')
          this.getRatingChartData()
          this.getImpliedRatingChartData()
        } else {
          console.log('页面激活时刷新图表显示')
        }
      }, 200)
    })
  },
  methods: {
    // 处理图例点击事件
    handleLegendClick(rating) {
      this.legendStatus[rating] = !this.legendStatus[rating]
    },

    // 获取默认的时间范围（近三年）
    getDefaultDateRange() {
      const today = new Date()
      const endDate = today.toISOString().split('T')[0] // 今天的日期，格式：yyyy-MM-dd
      today.setFullYear(today.getFullYear() - 3) // 三年前
      const startDate = today.toISOString().split('T')[0] // 三年前的日期
      return { startDate, endDate }
    },

    // 获取评级图表数据
    getRatingChartData() {
      this.copyParams = { ...this.tableParams }
      getBondRatingInfo(this.copyParams).then((res) => {
        if (res.length !== 0) {
          // 将所有评级机构的data数据合并到一个数组中
          let processedData = []

          // 遍历每个评级机构，提取其data数组中的对象
          res.forEach((agency) => {
            if (agency.data && Array.isArray(agency.data)) {
              processedData = [...processedData, ...agency.data]
            }
          })

          for (const i of processedData) {
            i.startDate = this.formatDate(i.startDate)
            i.endDate = this.formatDate(i.endDate)
          }

          // 将合并后的数据设置为ratingChartData
          this.ratingChartData = processedData

          // 重置原始数据
          this.originalRatingChartData = JSON.parse(JSON.stringify(processedData))

          // 更新图例状态，确保所有评级都有对应的显示状态
          const allRatings = [...new Set(processedData.map((item) => item.rating))]
          allRatings.forEach((rating) => {
            if (this.legendStatus[rating] === undefined) {
              this.$set(this.legendStatus, rating, true)
            }
          })

          // 更新评级颜色配置，确保所有评级都有对应的颜色
          // 注意：现在评级颜色会在RatingChart组件内部自动根据评级等级分配
          // 这里不再需要手动分配颜色，但保留ratingColors对象以维持兼容性
          allRatings.forEach((rating, index) => {
            if (!this.ratingColors[rating]) {
              this.$set(this.ratingColors, rating, '#000000') // 设置一个占位颜色
            }
          })

          // 从评级图表数据中提取评级机构数据
          this.getBondRatingAgencyData()

          // 如果之前已经选择了评级机构，应用过滤
          if (this.value2 && this.value2.length > 0) {
            this.$nextTick(() => {
              this.updateChartWithFilteredData(this.value2)
            })
          }
        } else {
          this.ratingChartData = []
          this.originalRatingChartData = null
          // 清空图例状态对象，确保没有数据时不显示图例
          this.legendStatus = {}
          // 清空评级机构选项
          this.originalOptions = []
          this.value2 = []
        }
      })
    },
    // 获取中债隐含评级图表数据
    getImpliedRatingChartData() {
      this.copyParams = { ...this.tableParams }
      // 调用获取中债隐含评级的API
      getImpliedRatingChartData(this.copyParams)
        .then((res) => {
          if (res && res.data) {
            this.processImpliedRatingData(res.data)
          } else {
            this.impliedRatingData = []
            // 清空图例状态对象，确保没有数据时不显示图例
            this.impliedLegendStatus = {}
          }
        })
        .catch((err) => {
          console.error('获取中债隐含评级数据失败:', err)
          this.impliedRatingData = []
          // 清空图例状态对象，确保没有数据时不显示图例
          this.impliedLegendStatus = {}
        })
    },
    getSelectRows(rows, listData) {
      this.selectRows = rows
    },
    callbackFn(data) {
      this.myChart = data
    },
    exportBondChart() {
      const exportOptions = {
        backgroundColor: '#ffffff',
        pixelRatio: 2 // 提高导出清晰度（可选）
      }
      // // 生成图片 URL 并触发下载
      const chart = this.myChart
      const imgUrl = chart.getDataURL(exportOptions)
      const link = document.createElement('a')
      link.href = imgUrl
      link.download = `中债隐含评级图`
      link.click()
    },
    callFn(data) {
      this.columns = data.config.columns
      this.sort = data.sort
      this.direction = data.direction
    },
    tabClick(tab, event) {
      if (tab.index === "0") {
        this.tableId = '9784744570374c0abd53ea0e6e28d968'
        this.activeName = '主体评级表'
      } else {
        this.tableId = '57c8079478ea415d84dff7de35939c2b'
        this.activeName = '中债隐含评级表'
      }
    },
    async exportBondTable() {
      console.log(this.columns)
      const params = {
        params: {
          pageInfo: {},
          filename: this.activeName,
          column: this.columns,
          selectData: Array.isArray(this.selectRows) && this.selectRows.length > 0 ? this.selectRows : null,
          dataSource: this.activeName === '主体评级表' ? '0' : '1',
          ccid: this.tableId,
          ownedModuleid: '1352319892086145024',
          ...this.tableParams
        },
        page: {
          sort: this.sort,
          direction: this.direction
        }
      }
      // return
      await exportExcelByCustomColumn(params)
    },
    // 发行人
    async getBondIssuerInfo() {
      const data = {
        text: ''
      }
      const res = await queryBondRatingAgency(data)
      this.mainCaliberList = res
      this.queryList(this.mainCaliberList[0].value)
      // return
      this.searchForm.mainCaliber = this.mainCaliberList[0].value
      this.tableParams.companyCode = this.mainCaliberList[0].value
      this.copyParams.companyCode = this.mainCaliberList[0].value
    },
    // 发行人变化
    async queryList(res) {
      // 不再调用getBondRatingAgencyData方法从接口获取数据
      this.tableParams.companyCode = res

      // 如果没有选择日期，使用默认时间范围
      if (!this.searchForm.backTime || !this.searchForm.backTime.length) {
        const defaultRange = this.getDefaultDateRange()
        this.tableParams.startDate = defaultRange.startDate
        this.tableParams.endDate = defaultRange.endDate
      }

      this.copyParams = { ...this.tableParams }

      // 更新图表弹窗标题为所选发行人名称
      const selectedIssuer = this.mainCaliberList.find((item) => item.value === res)
      if (selectedIssuer) {
        this.tooltipTitle = selectedIssuer.text
      }

      // 更新图表数据
      this.getRatingChartData()
      // 更新中债隐含评级图表数据
      this.getImpliedRatingChartData()
    },

    // 评级机构
    getBondRatingAgencyData(resParams) {
      // 如果传递了参数，使用旧的方式进行处理（仅在queryList方法调用时）
      if (resParams) {
        try {
          const params = {
            data: {
              clsno: resParams,
              cnname: '',
              effectflag: '',
              itemcode: ''
            },
            page: {
              direction: 'desc',
              pageNo: 1,
              pageSize: 10,
              sort: ''
            }
          }
          getBondRatingAgencyInfo(params)
            .then((res) => {
              if (res && res.list) {
                this.originalOptions = res.list
                // 默认全选所有评级机构
                this.value2 = this.originalOptions.map((item) => item.itemcode)
                this.$nextTick(() => {
                  // 强制更新视图
                  this.$forceUpdate()
                })
              } else {
                this.originalOptions = []
                this.value2 = []
              }
            })
            .catch((error) => {
              console.error('获取评级机构信息失败:', error)
              this.originalOptions = []
              this.value2 = []
            })
        } catch (error) {
          this.originalOptions = []
          this.value2 = []
        }
      } else {
        // 从评级图表数据中提取唯一的评级机构
        const agencies = [...new Set(this.ratingChartData.map((item) => item.agency))]

        // 格式化为下拉框所需的格式
        const formattedAgencies = agencies.map((agency) => ({
          itemcode: agency,
          cnname: agency
        }))

        // 更新评级机构下拉选项
        this.originalOptions = formattedAgencies

        // 默认全选所有评级机构
        this.value2 = this.originalOptions.map((item) => item.itemcode)

        // 强制更新视图
        this.$nextTick(() => {
          this.$forceUpdate()
        })
      }
    },
    // 日期改变
    dateChange(res) {
      if (res == null) {
        // 如果清空日期，使用默认时间范围（但不在UI上显示）
        const defaultRange = this.getDefaultDateRange()
        this.tableParams.startDate = defaultRange.startDate
        this.tableParams.endDate = defaultRange.endDate
        this.searchForm.backTime = []
      } else {
        this.tableParams.startDate = res[0]
        this.tableParams.endDate = res[1]
        this.searchForm.backTime = res
      }
      // 同步更新copyParams
      this.copyParams = { ...this.tableParams }

      // 更新主体评级图表数据
      this.getRatingChartData()
      // 更新中债隐含评级图表数据
      this.getImpliedRatingChartData()
    },
    // async init() {
    //   const res = await GetModuleData({ chartSeq: '988b10cd707a4b4695f32067e5fb0211' })
    // //   this.originalOptions = res.data
    // },
    queryData() {
      // 更新查询参数
      if (!this.searchForm.backTime || !this.searchForm.backTime.length) {
        // 不修改日期选择组件的显示，但在请求参数中使用默认时间范围
        const defaultRange = this.getDefaultDateRange()
        this.tableParams.startDate = defaultRange.startDate
        this.tableParams.endDate = defaultRange.endDate
      }
      this.copyParams = { ...this.tableParams }

      // 更新主体评级图表数据
      this.getRatingChartData()
      // 更新中债隐含评级图表数据
      this.getImpliedRatingChartData()
    },
    clickFunc() {
      // 表格行点击功能
    },
    dblClickFunc() {
      // 表格行双击功能
    },
    // 处理中债隐含评级图表的图例点击事件
    handleImpliedLegendClick(rating) {
      this.impliedLegendStatus[rating] = !this.impliedLegendStatus[rating]
    },

    // 处理中债隐含评级数据
    processImpliedRatingData(apiData) {
      // 检查是否有有效数据
      if (!apiData || !Array.isArray(apiData)) {
        return
      }

      // 转换数据格式
      const processedData = []
      const agency = '中债估值'

      // 按日期排序
      apiData.sort((a, b) => new Date(a.name) - new Date(b.name))

      // 获取所有唯一的日期
      const uniqueDates = [...new Set(apiData.map((item) => item.name))]

      // 处理数据
      uniqueDates.forEach((date, index) => {
        // 获取当前日期的所有评级
        const dateItems = apiData.filter((item) => item.name === date)

        // 如果有多个评级，取出现次数最多的那个
        let mostFrequentRating = null
        let maxCount = 0

        // 统计每个评级的出现次数
        const ratingCounts = {}
        dateItems.forEach((item) => {
          ratingCounts[item.rating] = (ratingCounts[item.rating] || 0) + 1
          if (ratingCounts[item.rating] > maxCount) {
            maxCount = ratingCounts[item.rating]
            mostFrequentRating = item.rating
          }
        })

        // 确定结束日期（如果不是最后一个日期）
        const endDate = index < uniqueDates.length - 1 ? uniqueDates[index + 1] : null

        processedData.push({
          agency,
          startDate: date,
          endDate,
          rating: mostFrequentRating
        })
      })

      for (const i of processedData) {
        i.startDate = this.formatDate(i.startDate)
        i.endDate = this.formatDate(i.endDate)
      }

      // 更新数据
      this.impliedRatingData = processedData

      // 更新图例
      this.updateImpliedLegendStatus()
    },

    // 更新中债隐含评级图例状态
    updateImpliedLegendStatus() {
      // 获取所有唯一的评级等级
      const uniqueRatings = [...new Set(this.impliedRatingData.map((item) => item.rating))]

      // 更新图例状态对象
      const newLegendStatus = {}
      uniqueRatings.forEach((rating) => {
        // 保留已有状态，如果是新的评级则默认显示
        newLegendStatus[rating] =
          this.impliedLegendStatus[rating] !== undefined ? this.impliedLegendStatus[rating] : true
      })

      this.impliedLegendStatus = newLegendStatus

      // 更新图例颜色
      const colors = ['#5AD8A6', '#5B8FF9', '#F6BD16', '#6DC8EC', '#945FB9', '#FF9845']
      const newRatingColors = {}

      uniqueRatings.forEach((rating, index) => {
        // 保留已有颜色，如果是新的评级则分配新颜色
        newRatingColors[rating] = this.impliedRatingColors[rating] || colors[index % colors.length]
      })

      this.impliedRatingColors = newRatingColors
    },
    // 导出主体评级图表
    exportMainRatingChart() {
      if (this.$refs.mainRatingChart) {
        this.$refs.mainRatingChart.exportChart('主体评级图表', 'jpg')
      } else {
        this.$message.warning('图表未加载完成，请稍后再试')
      }
    },

    // 导出中债隐含评级图表
    exportImpliedRatingChart() {
      if (this.$refs.impliedRatingChart) {
        this.$refs.impliedRatingChart.exportChart('中债隐含评级图表', 'jpg')
      } else {
        this.$message.warning('图表未加载完成，请稍后再试')
      }
    },

    // 用所有评级数据更新图表
    updateChartWithFullData() {
      // 保存原始评级图表数据的副本
      if (!this.originalRatingChartData) {
        // 深拷贝数据，避免引用问题
        this.originalRatingChartData = JSON.parse(JSON.stringify(this.ratingChartData))
      } else {
        // 恢复所有原始数据
        this.ratingChartData = JSON.parse(JSON.stringify(this.originalRatingChartData))
      }
    },

    // 用过滤后的数据更新图表
    updateChartWithFilteredData(selectedAgencies) {
      // 保存原始评级图表数据的副本（如果还没有）
      if (!this.originalRatingChartData) {
        // 深拷贝数据，避免引用问题
        this.originalRatingChartData = JSON.parse(JSON.stringify(this.ratingChartData))
      }

      // 过滤出选中的评级机构的数据
      const filteredData = this.originalRatingChartData.filter((item) => selectedAgencies.includes(item.agency))

      // 更新图表数据
      this.ratingChartData = filteredData
    },
    // 评级机构选择变化处理
    handleAgencyChange(value) {
      // 如果没有选择任何评级机构，清空图表和图例
      if (!value || value.length === 0) {
        // 清空图表数据但保留原始数据
        if (this.originalRatingChartData) {
          this.ratingChartData = []
          this.legendStatus = {}
        }
      } else if (this.originalRatingChartData) {
        // 有选择且有原始数据，应用过滤
        this.updateChartWithFilteredData(value)

        // 更新图例状态，只显示当前数据中的评级
        const currentRatings = [...new Set(this.ratingChartData.map((item) => item.rating))]

        // 先将所有图例设为不可见
        Object.keys(this.legendStatus).forEach((rating) => {
          this.$set(this.legendStatus, rating, false)
        })

        // 然后将当前数据中的评级设为可见
        currentRatings.forEach((rating) => {
          this.$set(this.legendStatus, rating, true)
        })
      }
    },
    formatDate(date) {
      if (date && date.length > 0) {
        const list = date.split('-')
        const newDate = list.join('/')
        return newDate
      }
      return ''
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-tabs__item {
  font-size: var(--el-font-size-base) !important;
  font-weight: bold;
}
</style>
<style lang="scss">
.bondsDetail {
  // height: 100%;
  display: flex;
  flex-direction: column;

  .search-container {
    display: flex;
    justify-content: flex-start;
    align-items: center;

    .formItem {
      display: flex;
      align-items: center;
      // width: 300px;
      padding-bottom: 10px;
      padding-left: 0;
    }

    .search-item {
      width: auto;
    }
  }

  .btn {
    display: inline-block;
    margin-left: 24px;
  }

  .searchForm {
    background: #fff;
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    min-height: 0;
  }

  .bondsDetailChart {
    height: 338px;
    background: #fff;
    padding: 10px;
    display: flex;
    flex-shrink: 0;

    .chartLeft {
      flex: 1;
      border: 1px solid #e0dfdf;
      margin-right: 15px;
      display: flex;
      flex-direction: column;

      .chartLeftTop {
        width: 100%;
        height: 50px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-right: 10px;

        .topTitle {
          font-style: normal;
          height: 17px;
          line-height: 17px;
          color: rgba(0, 0, 0, 0.85);
          font-size: var(--el-font-size-base) !important;
          border-left: 3px solid #ff8e2b;
          padding-left: 10px;
          margin-left: 15px;
          font-weight: bold;
        }

        .topRight {
          display: flex;
          align-items: center;

          .topRightItem {
            margin-right: 10px;
            width: 308px;
          }
        }
      }

      .chartLeftBottom {
        flex: 1;
        width: 100%;
        height: 100%;
        position: relative;
        /* 确保容器正确定位 */
        min-height: 200px;
        /* 设置最小高度以避免太小 */
        overflow: hidden;
        /* 防止溢出 */
      }
    }

    .chartRight {
      flex: 1;
      border: 1px solid #e0dfdf;
      display: flex;
      flex-direction: column;

      .chartRightTop {
        width: 100%;
        height: 50px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-right: 10px;

        .topTitle {
          font-style: normal;
          height: 17px;
          line-height: 17px;
          color: rgba(0, 0, 0, 0.85);
          font-size: var(--el-font-size-base) !important;
          border-left: 3px solid #ff8e2b;
          padding-left: 10px;
          margin-left: 15px;
          font-weight: bold;
        }
      }

      .chartRightBottom {
        flex: 1;
        width: 100%;
        height: 100%;
        position: relative;
        min-height: 200px;
        overflow: hidden;
      }
    }
  }

  .bazaar-tabs {
    background-color: #fff;
    padding: 0 10px 10px 10px;
    position: relative;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    /* 减去图表高度和搜索区域高度 */
    min-height: 0;
    // overflow: hidden;
    // ::v-deep .el-tabs__active-bar {
    //   width: 96px;
    // }
    .bond-details-list {
      padding: 0;
      background: #fff;
      flex-grow: 1;
      overflow-y: auto;
      min-height: 0;
      display: flex;
      flex-direction: column;

      .bondDetailsListTitle {
        font-size: var(--el-font-size-base) !important;
        color: #303133;
        font-weight: 700;
        padding-bottom: 6px;
        border-bottom: 1px solid #ebeef5;
        flex-shrink: 0;
      }

      .jr-decorated-table {
        height: 100%;
        flex-grow: 1;
        min-height: 0;
      }

      .bond-details-tips {
        position: absolute;
        left: 0px;
        bottom: 0px;
        font-size: var(--el-font-size-base) !important;
        color: rgba(0, 0, 0, 0.6);
      }
    }

    &-btn {
      position: absolute;
      top: 40px;
      right: 12px;
      width: 32px;
      height: 32px;
      line-height: 32px;
      text-align: center;
      border: 1px solid var(--el-border-color-button);
      transform: translateY(1px);

      .jr-svg-icon {
        fill: var(--theme--color);
        font-size: var(--el-font-size-medium);
      }

      &:hover {
        border-left-color: var(--theme--color-light-7);
        background: var(--theme--color-light-9);
        cursor: pointer;
      }
    }

    .jr-decorated-table--header {
      display: none;
    }

    .jr-decorated-table > div {
      padding: 0;
    }

    .el-tabs {
      flex-grow: 1;
      display: flex;
      flex-direction: column;
      // height: 100%;
    }

    .el-tabs__header {
      flex-shrink: 0;
    }

    .el-tabs__content {
      flex-grow: 1;
      min-height: 0;
      display: flex;
      flex-direction: column;
    }

    .el-tab-pane {
      flex-grow: 1;
      display: flex;
      flex-direction: column;
      padding-top: 32px;
    }

    .el-tabs__nav-wrap {
      overflow: visible !important;
    }

    .el-tabs__nav-wrap::after {
      content: '' !important;
      display: block !important;
      position: absolute !important;
      left: 0 !important;
      bottom: 0 !important;
      width: 100% !important;
      height: 1px !important;
      background-color: #e4e7ed !important;
      z-index: 1 !important;
    }
  }
}

@media screen and (max-width: 1440px) {
  .bondsDetail {
    .search-container {
      .search-item {
        width: 33%;
      }
    }
  }
}
</style>
