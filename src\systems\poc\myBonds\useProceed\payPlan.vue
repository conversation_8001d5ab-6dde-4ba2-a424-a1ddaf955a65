<template>
  <div class="payPlan">
    <jr-table-editor
      v-model="data.payPlanList"
      v-loading="loading"
      :columns="configColumns"
    />
  </div>
</template>

<script>
import { getModalProps, getModalComputed } from '@/systems/mixins'
export default {
  mixins: [getModalProps, getModalComputed],
  props: {
    itemData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      loading: false,
      configColumns: []
    }
  },
  created() {
    console.log(this.itemData)
    this.init()
  },
  methods: {
    init() {
      this.configColumns = [
        {
          title: '债券简称',
          prop: 'zqjc'
        },
        {
          title: '规模(亿)',
          prop: 'gm',
          type: 'hundredMillion'
        },
        {
          title: '偿还本金(元)',
          prop: 'chbj',
          type: 'amount'
        },
        {
          title: '偿还利息(元)',
          prop: 'chlx',
          type: 'amount'
        }
      ]
    },
    async preSaveHandel() {
      Object.assign(this.data, {
        ...this.itemData
      })
      return true
    }
  }
}
</script>
<style lang="scss">

</style>
