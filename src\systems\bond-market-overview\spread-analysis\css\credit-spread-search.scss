::v-deep .tooltip-text {
  max-width: 300px;
  white-space: nowrap; /* 禁止换行 */
  overflow: hidden; /* 隐藏溢出内容 */
  text-overflow: ellipsis; /* 显示省略号 */
}

.save-form {
  width: 793px;
  height: 348px;
  background: #f8f8f8;
  padding: 16px;

  &-list {
    height: 60px;
    margin-top: 16px;
    overflow: scroll;
    width: 100%;

    ::v-deep {
      display: flex !important;
      flex-direction: column;
      gap: 5px;
      width: 100%;
    }

    &-item {
      margin-top: 10px;
      height: 40px;
      width: 100%;
      padding-left: 16px;
      display: flex;
      align-items: center;
      background: rgba(0, 0, 0, 0.02);
      border: 1px solid #eae9e9;

      ::v-deep .el-checkbox__label {
        width: 100%;
      }

      ::v-deep .el-checkbox__inner {
        margin-bottom: 5px !important;
      }

      &-right {
        width: 48px;
        height: 16px;
        margin-left: auto;
        display: flex;
        align-items: center;
      }

      &-content {
        display: flex;
        align-items: center;

        &-left {
          height: 22px;
          display: flex;
          align-items: center;
          gap: 8px;

          &-title {
            height: 22px;
            font-family: MicrosoftYaHei;
            font-size: var(--el-font-size-medium);
            color: rgba(0, 0, 0, 0.9);
            line-height: 22px;
            text-align: left;
            font-style: normal;
          }
        }
      }
    }
  }

  &-contant {
    ::v-deep .el-form-item__content {
      margin-left: 0 !important;
      width: 100% !important;
      flex: 1 !important;
    }

    ::v-deep .el-cascader__search-input {
      display: none !important;
    }

    ::v-deep .el-cascader__tags {
      .el-tag--small {
        max-width: calc(100% - 70px) !important;
      }
    }
  }

  &-header {
    height: 22px;
    display: flex;
    align-items: center;
    gap: 8px;
    width: 100%;

    &-title {
      height: 22px;
      font-size: var(--el-font-size-medium);
      font-family: MicrosoftYaHeiSemibold;
      color: rgba(0, 0, 0, 0.9);
      line-height: 22px;
      text-align: left;
      font-style: normal;
    }

    &-end {
      margin-left: auto;
      display: flex;
      align-items: center;
      gap: 8px;
      width: 60px;

      height: 22px;
      font-family: MicrosoftYaHei;
      font-size: var(--el-font-size-base);
      color: #ff8e2b;
      line-height: 22px;
      text-align: left;
      font-style: normal;
    }
  }
}

.add {
  height: 33px;
  line-height: 33px;
  text-align: center;
  border: 1px dashed var(--theme--color, #00c1de);
  margin: 0;
  padding: 0;
  margin-top: 4px;
  cursor: pointer;
  color: var(--theme--color, #00c1de);
  i {
    margin-right: 5px;
    display: inline-block;
  }
}
.add:hover {
  color: #fff;
  background: var(--theme--color, #00c1de);
}