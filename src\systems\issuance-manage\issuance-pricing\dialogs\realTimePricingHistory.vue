<template>
  <jr-modal
    :visible="visible"
    :append-to-body="false"
    :modal-append-to-body="true"
    :handle-ok="handleCancel"
    :height="600"
    :handle-cancel="handleCancel"
    :has-footer="false"
  >
    <span class="modal-title">参考定价结果历史记录</span>
    <template v-slot:body>
      <div style="width: 100%;height: 100%;">
        <jr-decorated-table
          :params="{
            ownedModuleid:'708631605142536192'
          }"
          style="height: 544px;"
          custom-id="03cdcc0dd5904c14b9fdae284a48350d"
          v-bind="{
            ...$props
          }"
          :menuinfo="{}"
        />
      </div>
    </template>
  </jr-modal>
</template>

<script>
export default {
  data() {
    return {
      visible: false
    }
  },
  methods: {
    /**
     * 打开弹框
     */
    open() {
      this.visible = true
    },
    /**
     * 关闭弹框
     */
    handleCancel() {
      this.visible = false
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .jr-decorated-table--top-search .jr-decorated-table--header-left {
  height: 0px;
  padding: 0px !important;
}
.modal-title{
  font-family: MicrosoftYaHeiSemibold;
  font-size: var(--el-font-size-base);
  color: rgba(0,0,0,0.85);
  line-height: 28px;
  font-weight: 600;
}
</style>
