<template>
  <div class="setting-bond">
    <div class="list-unselected">
      <h3>
        待选债券
        <el-link>
          已选择
          <font color="#fb9c34" weight="bold">{{ selected.length }}</font>
          条
        </el-link>
      </h3>
      <el-input placeholder="债券代码/债券简称/发行人" suffix-icon="el-icon-search" />
      <jr-decorated-table
        ref="table"
        custom-id="5c005200f244469392ebc05476b42a3d"
        v-bind="{ ...$attrs, ...$props }"
        :style="'height: ' + (bodyHeight - 100) + 'px'"
        @handleSelectionChange="handleSelectionChange"
      />
    </div>
    <div class="list-selected jr-decorated-table--body">
      <h3>
        <span>
          已选债券
          <font color="#fb9c34">(上限50条)</font>
        </span>

        <el-link @click="handleRemove()">
          <jr-svg-icon icon-class="delete" />
          全部移除
        </el-link>
      </h3>
      <el-input placeholder="债券代码/债券简称/发行人" suffix-icon="el-icon-search" />
      <jr-table :data-source="selected" :columns="columns" border :height="bodyHeight - 100" />
    </div>
  </div>
</template>

<script>
export default {
  props: {
    bodyHeight: {
      type: Number,
      default: null
    },
    closeModal: {
      type: Function,
      default: () => {}
    }
  },
  data() {
    const self = this
    return {
      selected: [],
      columns: [
        {
          title: '债券代码',
          prop: 'zqdm'
        },
        {
          title: '债券简称',
          prop: 'zqjc'
        },
        {
          title: '发行人',
          prop: 'fxr'
        },
        {
          title: '操作',
          prop: 'action',
          align: 'center',
          width: 50,
          render: (h, { row }) => {
            return (
              <span class='table-action-box'>
                <el-tooltip content='删除'>
                  <jr-svg-icon icon-class='delete' onClick={self.handleRemove.bind(self, row)} />
                </el-tooltip>
              </span>
            )
          }
        }
      ]
    }
  },
  watch: {
    selected(val) {
      this.$emit('handleSelectionChange', val)
      this.$
    }
  },
  methods: {
    handleRemove(row) {
      const list = row ? [row] : this.selected
      list.forEach((r) => {
        this.$refs.table.$refs.tableBody.$refs.ActiveTable.$refs.jrTable.toggleRowSelection(r, false)
      })
    },
    handleSelectionChange(selection) {
      this.selected = selection
    },
    preSaveHandel() {
      this.msgSuccess('保存成功')
      this.closeModal()
      return false
    }
  }
}
</script>

<style lang="scss">
.setting-bond {
  display: flex;
  & > div {
    width: 49%;
    .jr-decorated-table--body {
      padding: 0 !important;
    }
    .jr-decorated-table--header {
      display: none !important;
    }
    &:last-child {
      margin-left: 2%;
    }
    h3 {
      display: flex;
      justify-content: space-between;
    }
  }
}
</style>
