<template>
  <div>
    <el-dialog title="公告详情" :visible.sync="visible" width="1120px" :before-close="handleCancel">
      <div class="dialog-content">
        <div class="dialog-text">{{ dialogText }}</div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  data() {
    return {
      visible: false,
      title: '',
      dialogText: ''
    }
  },
  computed: {},
  methods: {
    /**
     * 弹框打开
     * @param {Object} row 行内数据
     * @param {String} title 展示表格名称
     */
    open(val) {
      this.visible = true
      this.dialogText = val
    },
    /**
     * 弹框关闭
     */
    handleCancel() {
      this.visible = false
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-dialog__title {
  font-size: var(--el-font-size-extra-large);
  font-family: MicrosoftYaHeiSemibold;
  color: rgba($color: #000000, $alpha: 0.85);
  font-weight: bold;
}
::v-deep .el-dialog__body {
  padding: 0;

  .dialog-content {
    padding: 16px;
    background: #F0F2F5;

    .dialog-text {
      padding: 16px;
      height: 552px;
      background: url(../../../../assets/images/dialog-bg.png) no-repeat left bottom;
      background-size: 100% auto;
      background-color: #ffffff;
    }
  }
}
</style>
