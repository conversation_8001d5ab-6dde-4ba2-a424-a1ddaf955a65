<template>
  <div class="issuanceProductSelect">
    <div class="public-tabs-container">
      <el-tabs v-model="tabActiveName">
        <el-tab-pane label="债项综合成本" name="comprehensiveCostOfDebt" />
        <el-tab-pane label="融资参考" name="financingReference" style="color: rgba(0, 0, 0, 0.6)" />
      </el-tabs>
    </div>
    <component :is="tabActiveName" />
  </div>
</template>

<script>
import comprehensiveCostOfDebt from './components/comprehensiveCostOfDebt'
import financingReference from './components/financingReference'

export default {
  name: 'IssuanceProductSelect',
  components: {
    comprehensiveCostOfDebt,
    financingReference
  },
  props: {
    menuinfo: {
      type: Object,
      default: () => ({})
    }
  },
  created() {
    this.tabActiveName = this.menuinfo.meta.params.tabName || 'comprehensiveCostOfDebt'
  },
  data() {
    return {
      tabActiveName: 'comprehensiveCostOfDebt'
    }
  }
}
</script>

<style lang="scss" scoped>
.issuanceProductSelect {
  height: 100%;
  &-tabs {
    width: 100%;
    height: 56px;
    padding: 12px 0px 0px 16px;
    box-sizing: border-box;
    background-color: #ffffff;
  }
}
</style>
