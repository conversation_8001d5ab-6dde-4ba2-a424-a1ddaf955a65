<template>
  <div ref="chartContainer" :style="{ width: width, height: height }" class="chart-container" />
</template>

<script>
import * as echarts from 'echarts'

export default {
  name: 'ChartComponent',
  props: {
    // 图表配置项，必须传入的ECharts配置对象
    // 包含series、xAxis、yAxis等标准ECharts配置
    options: {
      type: Object,
      required: true
    },
    // 图表宽度，支持CSS单位(px/%)，默认100%宽度
    width: {
      type: String,
      default: '100%'
    },
    // 图表高度，支持CSS单位(px/%)，默认400px高度
    height: {
      type: String,
      default: '400px'
    }
  },
  data() {
    return {
      // ECharts实例对象
      chart: null,
      // 默认配置项，会被传入的options覆盖
      // 主要设置坐标轴分割线样式
      defaultOptions: {
        // x轴配置
        xAxis: {
          // 不显示x轴分割线
          splitLine: {
            show: false
          }
        },
        // y轴配置
        yAxis: {
          // 显示y轴分割线，样式为虚线
          splitLine: {
            show: true,
            lineStyle: {
              type: 'dashed', // 虚线样式
              color: '#eee' // 浅灰色
            }
          }
        }
      }
    }
  },
  watch: {
    options: {
      handler(newOptions) {
        this.updateChart(newOptions)
      },
      deep: true
    }
  },
  mounted() {
    this.initChart()
  },
  beforeDestroy() {
    if (this.chart) {
      this.chart.dispose()
      this.chart = null
    }
    window.removeEventListener('resize', this.resizeHandler)
  },
  methods: {
    // 初始化图表实例
    initChart() {
      this.chart = echarts.init(this.$refs.chartContainer)
      window.addEventListener('resize', this.resizeHandler)
      this.updateChart(this.options)
    },
    // 更新图表配置
    updateChart(options) {
      if (this.chart) {
        // 合并默认配置和用户配置
        const mergedOptions = this.mergeOptions(this.defaultOptions, options)
        // 使用新配置重绘图表
        this.chart.setOption(mergedOptions, true)
      }
    },
    // 深度合并配置项
    // @param defaultOpts - 默认配置
    // @param userOpts - 用户传入配置
    // @return 合并后的完整配置对象
    mergeOptions(defaultOpts, userOpts) {
      const result = JSON.parse(JSON.stringify(defaultOpts))

      // 处理xAxis配置
      if (userOpts.xAxis) {
        if (Array.isArray(userOpts.xAxis)) {
          result.xAxis = userOpts.xAxis.map(item => {
            return { ...result.xAxis, ...item, splitLine: { show: false }}
          })
        } else {
          result.xAxis = { ...result.xAxis, ...userOpts.xAxis, splitLine: { show: false }}
        }
      }

      // 处理yAxis配置
      if (userOpts.yAxis) {
        const defaultYAxis = result.yAxis
        if (Array.isArray(userOpts.yAxis)) {
          result.yAxis = userOpts.yAxis.map(item => {
            return {
              ...item,
              splitLine: {
                show: true,
                lineStyle: {
                  ...defaultYAxis.splitLine.lineStyle,
                  ...(item.splitLine?.lineStyle || {})
                }
              }
            }
          })
        } else {
          result.yAxis = {
            ...userOpts.yAxis,
            splitLine: {
              show: true,
              lineStyle: {
                ...defaultYAxis.splitLine.lineStyle,
                ...(userOpts.yAxis.splitLine?.lineStyle || {})
              }
            }
          }
        }
      }

      // 合并其他配置
      Object.keys(userOpts).forEach(key => {
        if (key !== 'xAxis' && key !== 'yAxis') {
          result[key] = userOpts[key]
        }
      })

      return result
    },
    // 窗口大小改变时重新调整图表尺寸
    resizeHandler() {
      if (this.chart) {
        this.chart.resize()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.chart-container {
	background-color: #fff;
	border-radius: 4px;
	box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
}
</style>
