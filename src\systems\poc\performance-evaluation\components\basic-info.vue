<template>
  <div>
    <el-row class="title">
      <el-col :span="24"><span>{{ data.PORTFOLIO_NAME }}</span></el-col>
    </el-row>
    <div class="basic-info">
      <div class="net-price">
        <el-row>
          <el-col :span="24"><span class="c-r b">{{ data.TDY_UNIT_VALUE | formatRate }}</span></el-col>
        </el-row>
        <el-row>
          <el-col :span="8"><span>单位净值</span></el-col>
          <el-col :span="5" class="a-r"><span class="c-r">{{ data.TDY_UNIT_VALUE_SUM | formatRate }}</span></el-col>
          <el-col :span="5" class="a-r"><span class="c-r">{{ data.TDY_ASSET_VALUE | formatAmount }}</span></el-col>
          <el-col :span="5" class="a-r"><span class="c-r">{{ data.TDY_ASSET_NETVALUE | formatAmount }}</span></el-col>
        </el-row>
        <el-row>
          <el-col :span="8"><span>{{ data.CDATE | formatDate }}</span></el-col>
          <el-col :span="5" class="a-r"><span>累计净值</span></el-col>
          <el-col :span="5" class="a-r"><span>总资产(万)</span></el-col>
          <el-col :span="5" class="a-r"><span>净资产(万)</span></el-col>
        </el-row>
      </div>
      <div class="yield">
        <el-row>
          <el-col :span="24" class="subtitle"><span>收益率</span></el-col>
        </el-row>
        <el-row>
          <el-col :span="8"><span :class="[Number(data.YIELD) > 0 && 'c-r']">{{ data.YIELD | formatRate2 }}</span></el-col>
          <el-col :span="8"><span :class="[Number(data.YIELD3M) > 0 && 'c-r']">{{ data.YIELD3M | formatRate2 }}</span></el-col>
          <el-col :span="8"><span :class="[Number(data.YIELD1Y) > 0 && 'c-r']">{{ data.YIELD1Y | formatRate2 }}</span></el-col>
        </el-row>
        <el-row>
          <el-col :span="8"><span>今年以来</span></el-col>
          <el-col :span="8"><span>近3月</span></el-col>
          <el-col :span="8"><span>近1年</span></el-col>
        </el-row>
      </div>
      <div class="annualized-rate">
        <el-row>
          <el-col :span="24" class="subtitle"><span>成立以来</span></el-col>
        </el-row>
        <el-row>
          <el-col :span="8"><span class="detail-text">{{ data.VOLATILITY | formatRate2 }}</span></el-col>
          <el-col :span="8"><span class="detail-text">{{ data.MAXWITHDRAW | formatRate }}</span></el-col>
          <el-col :span="8"><span class="detail-text">{{ data.SHARPE | formatRate }}</span></el-col>
        </el-row>
        <el-row>
          <el-col :span="8"><span>年化波动率</span></el-col>
          <el-col :span="8"><span>最大回撤</span></el-col>
          <el-col :span="8"><span>夏普比例</span></el-col>
        </el-row>
      </div>
      <div class="range">
        <el-row>
          <el-col :span="24" class="subtitle"><span>评价结果</span></el-col>
        </el-row>
        <el-row>
          <el-col :span="24"><span>{{ data.RATING || '--' }}</span></el-col>
        </el-row>
        <el-row>
          <el-col :span="24"><el-rate :value="Number(data.RATINGLEVEL)" disabled /></el-col>
        </el-row>
        <el-row>
          <el-col :span="24"><span>更新时间：{{ data.RATINGDATE | formatDate }}</span></el-col>
        </el-row>
      </div>
      <div class="detail">
        <el-row>
          <el-col :span="12">
            <label for="">投资经理：</label>
            <span>{{ data.PRODMANAGER || '--' }}</span>
          </el-col>
          <el-col :span="12">
            <label for="">成立日期：</label>
            <span>{{ data.VDATE | formatDate }}</span>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <label for="">投资性质：</label>
            <span>{{ filterDict(data.INVESTNATURE, 'INVEST_TYPE') }}</span>
          </el-col>
          <el-col :span="12">
            <label>到期日期：</label>
            <span>{{ data.MDATE | formatDate }}</span>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <label for="">开放类型：</label>
            <span>{{ filterDict(data.OPENTYPE, 'PTL_ISSUE_MODE') }}</span>
          </el-col>
          <el-col :span="12">
            <label for="">收益类型：</label>
            <span>{{ filterDict(data.PROFITTYPE, 'PROFIT_TYPE') }}</span>
          </el-col>
        </el-row>
      </div>
    </div>
  </div>
</template>

<script>
import { ConvertAmount, FormatDate, isNullOrUndefined } from 'jupiterweb/src/utils/common'
export default {
  filters: {
    formatDate(v) {
      return FormatDate(v, 'yyyy-MM-dd') || '--'
    },
    formatAmount(v) {
      return ConvertAmount('2', v, 10000) || '--'
    },
    formatRate(v) {
      return isNullOrUndefined(v) ? '--' : Number(v).toFixed(4)
    },
    formatRate2(v) {
      return isNullOrUndefined(v) ? '--' : Number(v * 100).toFixed(4)
    }
  },
  props: {
    data: {
      type: Object,
      default: () => ({})
    },
    dictList: {
      type: Array,
      default: () => []
    }
  },
  methods: {
    filterDict(v, key) {
      const index = ['INVEST_TYPE', 'PTL_ISSUE_MODE', 'PROFIT_TYPE'].findIndex(a => a === key)
      return this.dictList[index]?.find(a => a.id === v)?.text || '--'
    }
  }
}
</script>

<style lang="scss">

</style>
