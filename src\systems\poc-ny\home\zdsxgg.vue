<template>
  <!-- POC专用 -->
  <!-- 重大事项公告 -->
  <div class="poc-home">
    <h3>
      <span>重大事项公告</span>
      <el-link>更多</el-link>
    </h3>
    <div class="gg-list">
      <div v-for="(l,index) in list" :key="index">
        <div class="name">{{ l[0] }}</div>
        <div class="desc">
          <span>{{ l[1] }}</span>
          <span>{{ l[2] }}</span>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      list: [
        ['淄博市城市资产运营集团有限公司关于变更信用评级机构的公告', '淄博市城市资产运营集团有限公司', '2024-05-13'],
        [
          '平湖市国有资产控股集团有限公司关于董事，监事，总经理发生变动的公告',
          '平湖市国有资产控股集团有限公司',
          '2024-02-19'
        ],
        ['平湖市国有资产控股集团有限公司关于董事，监事发生变动的公告', '平湖市国有资产控股集团有限公司', '2023-11-16'],
        ['淄博市城市资产运营集团有限公司关于中介机构发生变更的公告', '淄博市城市资产运营集团有限公司', '2023-10-25'],
        ['淄博市城市资产运营集团有限公司关于变更审计机构的公告', '淄博市城市资产运营集团有限公司', '2023-10-24'],
        [
          '平湖市国有资产控股集团有限公司关于企业新增借款超过上年末净资产的20%的公告',
          '平湖市国有资产控股集团有限公司',
          '2023-07-07'
        ],
        ['淄博市城市资产运营集团有限公司关于无偿划转股权的公告', '淄博市城市资产运营集团有限公司', '2022-12-28']
      ]
    }
  }
}
</script>
<style lang="scss" scoped>
.gg-list {
  overflow: auto;
  &>div {
    margin-bottom: 20px;
  }
  .name {
    font-size: 14px;
    font-weight: 500;
    color: #222;
    line-height: 20px;
    margin-bottom: 10px;
  }
  .desc {
    font-size: 12px;
    font-weight: 400;
    line-height: 16px;
    color: #999;
    display: flex;
    justify-content: space-between;
  }
}
</style>
