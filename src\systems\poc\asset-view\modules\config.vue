<!--
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023-10-25 10:54:32
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2023-10-26 14:06:31
 * @Description: 资产配置
-->
<template>
  <article>
    <div class="header">
      <span class="title">资产配置</span>
      <el-radio-group v-model="combo" size="mini">
        <el-radio-button v-for="com in comboxList" :key="com" :label="com" />
      </el-radio-group>
    </div>
    <section class="body">
      <echarts :options="chartOptions" />
    </section>
  </article>
</template>

<script>
import echarts from '@jupiterweb/components/echarts'
export default {
  components: { echarts },
  data() {
    return {
      combo: '图',
      comboxList: ['图', '表'],
      chartOptions: {
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b} : {c} 亿元({d}%)'
        },
        series: [
          {
            name: '资产配置',
            type: 'pie',
            radius: '68%',
            center: ['50%', '50%'],
            clockwise: false,
            data: [
              {
                value: 75.31,
                name: '资产包'
              },
              {
                value: 20.37,
                name: '公募基金'
              },
              {
                value: 40.65,
                name: '计划类净值产品'
              },
              {
                value: 12.93,
                name: '直投债券'
              },
              {
                value: 8.79,
                name: '直投股票'
              },
              {
                value: 3.55,
                name: '其他'
              }
            ],
            label: {
              normal: {
                formatter: function(params) {
                  if (params.value !== 0) return params.data.name + '\r\n' + params.percent + '%'
                  else return ''
                },
                show: true
              }
            },
            itemStyle: {
              normal: {
                label: {
                  show: true,
                  position: 'outside'
                },
                labelLine: {
                  length: 30,
                  length2: 100,
                  show: true,
                  color: '#00ffff'
                }
              }
            }
          }
        ]
      }
    }
  }
}
</script>

<style lang="scss" scoped></style>
