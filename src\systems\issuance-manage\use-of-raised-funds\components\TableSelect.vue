<template>
  <div class="tableSelect">
    <div class="tableSelect-selectArea">
      <div class="tableSelect-selectArea-select">
        <div class="tableSelect-selectArea-select-custom">
          <label>企业名称</label>
          <div>
            <el-input v-model="selectParam.entName" placeholder="请输入内容" />
          </div>
        </div>
        <div v-if="tabActiveName === '1'" class="tableSelect-selectArea-select-custom">
          <label>存续期管理机构</label>
          <div>
            <el-input v-model="selectParam.manageOrg" placeholder="请输入内容" />
          </div>
        </div>
        <div v-if="tabActiveName === '2'" class="tableSelect-selectArea-select-custom">
          <label>借款银行</label>
          <div>
            <el-input v-model="selectParam.lead_underwriter" placeholder="请输入内容" />
          </div>
        </div>
        <div class="tableSelect-selectArea-select-custom">
          <label>融资类别</label>
          <jr-combobox
            v-model="selectParam.finCategory"
            style="width: 100%"
            :data="selectOptions.finCategoryOptions"
            :popper-append-to-body="false"
            :multiple="true"
            :disabled="tabActiveName === '1'"
            placeholder="请选择融资类别"
            collapse-tags
          />
        </div>
        <div class="tableSelect-selectArea-select-custom">
          <label>到期日期</label>
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            unlink-panels
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd"
          />
        </div>
      </div>
      <div class="tableSelect-buttons">
        <el-button type="primary" @click.stop="tableSelect">查询</el-button>
        <el-button @click.stop="resetTableSelect">重置</el-button>
      </div>
    </div>
  </div>
</template>
<script>
import { GetComboboxList } from '@/api/home'
const CATEGORY = 'CATEGORY' // 融资类别字典项
export default {
  props: {
    bondTypeOptions: {
      type: Array,
      default: () => []
    },
    tabActiveName: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      isLastissue: true, // 是否发行日期查询
      dateRange: [],
      selectParam: {
        entName: '',
        manageOrg: '',
        finCategory: '',
        dtStart: '',
        dtEnd: ''
      },
      selectOptions: {
        finCategoryOptions: []
      }
    }
  },
  watch: {
    tabActiveName: {
      immediate: true,
      handler() {
        // this.selectParam.finCategory = this.tabActiveName === '1' ? '债券业务' : ''
        this.resetTableSelect()
        if (this.tabActiveName === '1') {
          this.selectOptions.finCategoryOptions.push({
            text: '债券业务',
            id: '债券业务'
          })
        } else {
          this.selectOptions.finCategoryOptions = this.selectOptions.finCategoryOptions.filter((item) => {
            return item.id !== '债券业务'
          })
        }
      }
    }
  },
  created() {
    this.getDictDataApi()
  },
  methods: {
    /**
     * 表格确认查询
     */
    tableSelect() {
      if (this.dateRange.length) {
        this.selectParam.dtStart = this.dateRange[0]
        this.selectParam.dtEnd = this.dateRange[1]
      }
      this.$emit('setTableSelectParams', JSON.parse(JSON.stringify(this.selectParam)))
    },
    /**
     * 表格重置
     */
    resetTableSelect() {
      this.dateRange = []
      this.selectParam = {
        entName: '',
        manageOrg: '',
        finCategory: this.tabActiveName === '1' ? '债券业务' : '',
        dtStart: '',
        dtEnd: ''
      }
      this.tableSelect()
    },
    // 获取部分字典数据
    async getDictDataApi() {
      const data = await GetComboboxList([CATEGORY])
      this.selectOptions.finCategoryOptions = data[CATEGORY]
    }
  }
}
</script>

<style scoped lang="scss">
.tableSelect {
  position: relative;
  padding: 0 16px 8px;
  background-color: #ffffff;
  &-selectArea {
    box-sizing: border-box;
    display: flex;
    &-select {
      width: calc(100% - 144px);
      display: flex;
      flex-wrap: wrap;
      column-gap: 16px;
      row-gap: 8px;
      &-custom {
        width: calc((100% - 64px) / 4);
        display: flex;
        align-items: center;
        label {
          height: 32px;
          font-size: var(--el-font-size-base);
          color: rgba(0, 0, 0, 0.85);
          line-height: 32px;
          text-align: center;
          margin-right: 8px;
          flex-shrink: 0;
        }
        & > div {
          width: calc(100% - 56px);
        }
        &-icon {
          width: 13px;
          height: 13px;
          border-radius: 50%;
          border: 1px solid var(--theme--color);
          box-sizing: border-box;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
        }
        ::v-deep .el-input__inner {
          height: 32px !important;
          line-height: 32px;
        }
      }
    }
  }
  &-buttons {
    margin-left: auto;
    position: relative;
    & > button {
      // width: 88px;
      height: 32px;
      border-radius: 2px;
      box-sizing: border-box;
    }
    & > button:nth-of-type(1) {
      background: var(--theme--color);
    }
    & > button:nth-of-type(2) {
      border: 1px solid rgba(0, 0, 0, 0.12);
    }
    &-switch {
      position: absolute;
      right: 0px;
      cursor: pointer;
      width: 80px;
      height: 32px;
      line-height: 32px;
      border-radius: 2px;
      text-align: center;
      margin-bottom: 0px;
      color: var(--theme--color);
      font-size: var(--el-font-size-base);
    }
  }
}
::v-deep .el-range-editor--medium .el-range-separator {
  line-height: 26px;
}
::v-deep .el-range-editor--medium .el-range__icon {
  line-height: 26px;
}
::v-deep .el-select .el-input .el-select__caret {
  line-height: 32px;
}
::v-deep .el-range-editor .el-range__close-icon {
  line-height: 26px;
}

@media screen and (max-width: 1600px) {
  .tableSelect {
    &-selectArea {
      &-select {
        &-custom {
          width: calc((100% - 48px) / 3);
        }
      }
    }
  }
}
</style>
