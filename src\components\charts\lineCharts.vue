<!-- RatingChart.vue -->
<!-- 折线图组件：基于ECharts实现的可配置折线图
用于展示多系列数据的趋势变化 -->
<template>
  <!-- 图表容器，使用ref引用以便初始化ECharts实例 -->
  <div ref="chartContainer" class="rating-chart" />
</template>

<script>
export default {
  name: 'RatingChart',
  props: {
    // 图表数据系列配置
    seriesData: {
      type: Array,
      required: true
      // 格式: [{name: '系列名称', data: [值1, 值2, ...], color: '颜色值'}, ...]
      // name: 数据系列名称，将显示在图例中
      // data: 该系列的数据点数组，与xAxisData长度应保持一致
      // color: 该系列的颜色值，支持CSS颜色格式
      // 可选: extraOptions 对象，用于额外的系列配置项
    },
    // X轴数据配置
    xAxisData: {
      type: Array,
      required: true
      // 格式: ['类别1', '类别2', ...]
      // 用于定义X轴上的刻度标签
    },
    // Y轴数据配置
    yAxisData: {
      type: Array,
      required: true
      // 格式: ['类别1', '类别2', ...]
      // 用于定义Y轴上的刻度标签
    },
    // 图例数据配置
    legendData: {
      type: Array,
      default: () => []
      // 格式: [{name: '图例名称', color: '颜色值'}, ...]
      // name: 图例项名称
      // color: 图例项对应的颜色
    },
    // 图表高度配置
    height: {
      type: String,
      default: '100%'
      // 支持CSS高度值，如'100%'、'300px'等
    },
    // 图表宽度配置
    width: {
      type: String,
      default: '100%'
      // 支持CSS宽度值，如'100%'、'500px'等
    },
    // 自定义图表配置选项
    chartOptions: {
      type: Object,
      default: () => ({})
      // 可覆盖默认配置的ECharts选项对象
      // 支持所有ECharts配置项，会与默认配置合并
    }
  },
  data() {
    return {
      // ECharts实例对象
      chart: null
    }
  },
  watch: {
    // 监听seriesData变化，更新图表
    seriesData: {
      handler() {
        this.updateChart()
      },
      deep: true // 深度监听
    },
    // 监听xAxisData变化，更新图表
    xAxisData() {
      this.updateChart()
    },
    // 监听yAxisData变化，更新图表
    yAxisData() {
      this.updateChart()
    },
    // 监听legendData变化，更新图表
    legendData: {
      handler() {
        this.updateChart()
      },
      deep: true // 深度监听
    },
    // 监听chartOptions变化，更新图表
    chartOptions: {
      handler() {
        this.updateChart()
      },
      deep: true // 深度监听
    }
  },
  mounted() {
    // 组件挂载后初始化图表
    this.initChart()
    // 添加窗口大小变化监听
    this.handleResize()
  },
  beforeDestroy() {
    // 组件销毁前释放图表资源
    if (this.chart) {
      this.chart.dispose()
      this.chart = null
    }
    // 移除窗口大小变化监听器
    window.removeEventListener('resize', this.handleResize)
  },
  methods: {
    /**
		 * 初始化ECharts图表实例
		 * 创建图表并设置基础配置
		 */
    initChart() {
      // 检查ECharts是否已加载
      if (!window.echarts) {
        console.error('ECharts未加载')
        return
      }

      // 获取图表DOM容器
      const chartDom = this.$refs.chartContainer
      // 初始化ECharts实例
      this.chart = window.echarts.init(chartDom, null, {
        renderer: 'canvas', // 使用canvas渲染器
        useDirtyRect: false, // 不使用脏矩形优化
        devicePixelRatio: window.devicePixelRatio // 适配设备像素比
      })

      // 设置图表样式和数据
      this.updateChart()

      // 添加窗口大小变化监听，用于响应式调整图表大小
      window.addEventListener('resize', this.handleResize)
    },
    /**
		 * 处理窗口大小变化事件
		 * 调整图表大小以适应容器
		 */
    handleResize() {
      if (this.chart) {
        this.chart.resize()
      }
    },
    /**
		 * 更新图表配置和数据
		 * 根据props属性重新配置并渲染图表
		 */
    updateChart() {
      if (!this.chart) return

      // 处理图例数据配置
      const legendConfig = {
        top: '5px', // 图例顶部位置
        right: '20px', // 图例右侧位置
        itemWidth: 10, // 图例标记宽度
        itemHeight: 10, // 图例标记高度
        icon: 'circle', // 图例标记形状
        data: this.legendData.map(item => ({
          name: item.name,
          itemStyle: {
            color: item.color // 图例项颜色
          }
        }))
      }

      // 处理系列数据配置
      const seriesConfig = this.seriesData.map(item => ({
        name: item.name, // 系列名称
        type: 'line', // 图表类型为折线图
        symbol: 'circle', // 数据点形状
        symbolSize: 8, // 数据点大小
        data: item.data, // 系列数据
        itemStyle: {
          color: item.color // 数据点颜色
        },
        lineStyle: {
          color: item.color // 折线颜色
        },
        label: {
          show: false // 不显示数据标签
        },
        ...item.extraOptions // 合并额外的自定义配置项
      }))

      // 基础图表配置项
      const defaultOptions = {
        responsive: true, // 响应式
        maintainAspectRatio: false, // 不维持宽高比
        grid: {
          left: '10%', // 左边距
          right: '10%', // 右边距
          top: '15%', // 上边距
          bottom: '15%', // 下边距
          containLabel: true, // 包含坐标轴标签
          width: 'auto', // 宽度自适应
          height: 'auto' // 高度自适应
        },
        tooltip: {
          trigger: 'axis', // 触发类型-坐标轴触发
          backgroundColor: 'rgba(0, 0, 0, 0.60)', // 提示框背景色
          borderColor: '#ccc', // 提示框边框颜色
          borderWidth: 1, // 提示框边框宽度
          borderRadius: 8, // 提示框圆角
          padding: [20, 20], // 提示框内边距
          textStyle: {
            color: '#fff', // 提示框文本颜色
            fontSize: 14 // 提示框文本字号
          },
          axisPointer: {
            type: 'line', // 坐标轴指示器类型
            lineStyle: {
              color: '#ccc', // 指示线颜色
              width: 1, // 指示线宽度
              type: 'dashed' // 指示线类型
            }
          }
        },
        legend: legendConfig, // 图例配置
        xAxis: {
          type: 'category', // X轴类型-类目轴
          data: this.xAxisData, // X轴数据
          boundaryGap: true, // 坐标轴两端留白
          axisLine: {
            show: true, // 显示坐标轴线
            lineStyle: {
              color: '#000000', // 坐标轴线颜色
              width: 1 // 坐标轴线宽度
            }
          },
          axisTick: {
            show: true, // 显示坐标轴刻度
            alignWithLabel: true, // 刻度与标签对齐
            lineStyle: {
              color: '#000000', // 刻度线颜色
              width: 1 // 刻度线宽度
            }
          },
          axisLabel: {
            show: true, // 显示坐标轴标签
            interval: 0, // 标签显示间隔
            color: '#000000', // 标签颜色
            fontSize: 12 // 标签字号
          }
        },
        yAxis: {
          type: 'category', // Y轴类型-类目轴
          data: this.yAxisData, // Y轴数据
          axisLine: {
            show: false // 不显示坐标轴线
          },
          axisTick: {
            show: false // 不显示坐标轴刻度
          },
          axisLabel: {
            color: '#666', // 坐标轴标签颜色
            fontSize: 12 // 坐标轴标签字号
          },
          splitLine: {
            show: true, // 显示分隔线
            lineStyle: {
              color: '#e0e6f1', // 分隔线颜色
              type: 'solid' // 分隔线类型
            }
          }
        },
        series: seriesConfig // 系列配置
      }

      // 合并用户自定义配置与默认配置
      const options = this.mergeOptions(defaultOptions, this.chartOptions)

      // 设置图表选项并应用
      this.chart.setOption(options)
    },
    /**
		 * 合并配置选项
		 * @param {Object} defaultOpts - 默认配置选项
		 * @param {Object} customOpts - 自定义配置选项
		 * @return {Object} 合并后的配置选项
		 */
    mergeOptions(defaultOpts, customOpts) {
      // 实现简单的选项合并逻辑，确保主要配置节点正确合并
      return {
        ...defaultOpts,
        ...customOpts,
        grid: { ...defaultOpts.grid, ...customOpts.grid },
        tooltip: { ...defaultOpts.tooltip, ...customOpts.tooltip },
        legend: { ...defaultOpts.legend, ...customOpts.legend },
        xAxis: { ...defaultOpts.xAxis, ...customOpts.xAxis },
        yAxis: { ...defaultOpts.yAxis, ...customOpts.yAxis }
      }
    }
  }
}
</script>

<style scoped>
/* 图表容器样式 */
.rating-chart {
	width: 100%;
	height: 100%;
	min-height: 200px; /* 最小高度确保图表可见 */
}
</style>
