/**
 * 列表配置项
 */
export const tableConfig = {
  loading: false,
  sortColumn: null,
  sortOrder: null,
  columns: [],
  data: [],
  pagination: {
    pageNo: 1,
    pageSize: 10,
    pageSizeOptions: [10, 20, 30, 50],
    total: 0,
    showSizeChanger: true
  }
}

/**
 * 债券对标-债券信息列表字段
 */
export const bondTableColumns = [
  {
    prop: 'sinfoWindcode',
    title: '债券代码',
    width: '130',
    fixed: true
  },
  {
    prop: 'sinfoName',
    title: '债券简称',
    width: '160',
    sorter: true
  },
  {
    prop: 'cnbdCreditrating',
    title: '中债隐含评级',
    width: '145',
    sorter: true
  },
  {
    prop: 'term',
    title: '发行期限',
    width: '115',
    sorter: true
  },
  {
    prop: 'bissueAmountact',
    title: '规模',
    width: '120',
    sorter: true
  },
  {
    prop: 'latestCouponrate',
    title: '票面利率(%)',
    width: '140',
    sorter: true
  },
  {
    prop: 'exerciseValuation',
    title: '行权估值(%)',
    width: '140',
    sorter: true
  },
  {
    prop: 'maturityValuation',
    title: '到期估值(%)',
    width: '140',
    sorter: true
  },
  {
    prop: 'interestMargin',
    title: '票面与首次估值利差(BP)',
    width: '215',
    sorter: true
  },
  {
    prop: 'bondTypeName2',
    title: '债券类型',
    width: '150',
    sorter: true
  },
  {
    prop: 'productName',
    title: '创新专项品种',
    width: '160',
    sorter: true
  },
  {
    prop: 'bondRating',
    title: '债项评级',
    width: '115',
    sorter: true
  },
  {
    prop: 'sinfoExchmarketname',
    title: '交易市场',
    width: '160',
    sorter: true
  },
  {
    prop: 'bissueFirstissue',
    title: '发行起始日',
    width: '130',
    sorter: true
  },
  {
    prop: 'bissueLastissue',
    title: '发行截止日',
    width: '130',
    sorter: true
  },
  {
    prop: 'binfoCarrydate',
    title: '起息日期',
    width: '115',
    sorter: true
  },
  {
    prop: 'binfoListdate',
    title: '上市日期',
    width: '115',
    sorter: true
  },
  {
    prop: 'binfoMaturitydate',
    title: '到期日期',
    width: '115',
    sorter: true
  },
  {
    prop: 'binfoPaymentdate',
    title: '兑付日期',
    width: '115',
    sorter: true
  },
  {
    prop: 'binfoIssuer',
    title: '发行人',
    width: '250',
    sorter: true
  },
  {
    prop: 'isWindInvest',
    title: '是否wind城投分类',
    width: '160'
  },
  {
    prop: 'compProperty',
    title: '主体性质',
    width: '115',
    sorter: true
  },
  {
    prop: 'binfoCreditrating',
    title: '主体评级',
    width: '115',
    sorter: true
  },
  {
    prop: 'sinfoCompindName4',
    title: '行业',
    width: '130',
    sorter: true
  },
  {
    prop: 'province',
    title: '省',
    width: '160',
    sorter: true
  },
  {
    prop: 'city',
    title: '市',
    width: '130',
    sorter: true
  },
  {
    prop: 'district',
    title: '区县',
    width: '130',
    sorter: true
  },
  {
    prop: 'isGuarantor',
    title: '是否有担保',
    width: '130',
    sorter: true
  },
  {
    prop: 'guarantorlist',
    title: '担保人',
    width: '250',
    sorter: true
  },
  {
    prop: 'allLu',
    title: '主承销商',
    width: '250',
    sorter: true
  },
  {
    prop: 'leaderLu',
    title: '牵头主承销商',
    width: '250',
    sorter: true
  },
  {
    prop: 'jointLu',
    title: '联席主承销商',
    width: '250',
    sorter: true
  },
  {
    prop: 'leaderRate',
    title: '牵头承销占比(%)',
    width: '165',
    sorter: true
  },
  {
    prop: 'allLuDetail',
    title: '承销明细',
    width: '250'
  }
]
