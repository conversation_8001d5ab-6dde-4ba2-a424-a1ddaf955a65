<template> 
  <div class="bazaar" style="padding: 0 16px !important;">
    <jr-layout-horizontal>
      <template slot="left">
        <card-list ref="cardList" v-bind="{ isType, params }" :on-active="onActive" />
      </template>
      <template slot="right">
        <form-list v-bind="{ submit, reset, form }">
          <jr-form-item label="发行期限">
            <jr-checkbox-group
              v-model="form.bAnalCurveterm"
              :data="dictList"
              :min="0"
              :max="10"
              option-label="cnname"
              option-value="itemcode"
              @change="checkChange"
            />
          </jr-form-item>
        </form-list>
        <chart
          :options="chartOptions"
          :name="nameFn()"
        />
      </template>
    </jr-layout-horizontal>
  </div>
</template>

<script>
import formList from '../../components/form-list.vue'
import chart from '../../components/chart.vue'
import * as op from '../../components/chartParams'
import { date } from '@/utils/common'
import { GetInfoFn } from '@jupiterweb/utils/api'
import cardList from '../../components/card-list.vue'
export default {
  name: 'NationalDebt',
  provide() {
    return { parant: this }
  },
  components: {
    formList,
    chart,
    cardList
  },
  data() {
    return {
      paramsDatas: {},
      chartOptions: op.options,
      dictList: [],
      list: [],
      isType: '10Y',
      form: {
        bAnalCurveterm: ['10'],
        date: [date().subtract(1, 'y'), date().now()],
        radioDate: 12
      },
      params: { ccid: '98745c20df3747a1885c354f1ed85713', ownedModuleid: '708631605142536192', b_anal_curvename: '中债国开债收益率曲线' }
    }
  },
  created() {
    this.echartsData()
    this.getDictList()
  },
  mounted() {
  },
  methods: {
    async getDictList() {
      const data = await GetInfoFn('DICT', 'BOND_TERM_MARKET_CURVE_RATIO')
      this.dictList = data || []
    },
    // echarts  接口
    async echartsData() {
      this.paramsDatas = {
        ccid: '12ef221ca55f42e396528c6beecce30a', ownedModuleid: '708631605142536192',
        ...this.form, bAnalCurveName: '中债国开债收益率曲线'
      }
      const chartOptions = await op.getchartData(this.paramsDatas)
      this.chartOptions = chartOptions
    },
    onActive(item) {
      this.isType = item.bAnalCurveterm
      const itemcode = this.dictList.length > 0 ? this.dictList.find(k => k.cnname === this.isType).itemcode : ''
      this.form.bAnalCurveterm = itemcode ? [itemcode] : []
      this.echartsData()
    },
    submit() {
      this.echartsData()
    },
    reset() {
      this.echartsData()
      this.$refs.cardList.setCardInd('10Y')
    },
    // 发行期限值改变 清除卡片选中效果
    checkChange() {
      this.$nextTick(() => this.$refs.cardList.cardInd = null)
    },
    nameFn() {
      const d = this.form.date.join().replaceAll('-', '').replace(',', '-')
      return `国开债到期收益率_${d}`
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/assets/styles/market.scss'
</style>
