<template>
  <div class="bonds-detail-container">
    <!-- 顶部 Tabs -->
    <el-tabs v-model="activeTab" class="bonds-tabs">
      <el-tab-pane label="国债" name="national" />
      <el-tab-pane label="国开债" name="development" />
      <el-tab-pane label="利率债面板" name="interestRate" />
    </el-tabs>

    <div class="content-wrapper">
      <!-- 左侧栏 -->
      <div class="left-panel">
        <div
          v-for="item in bondTerms"
          :key="item.term"
          :class="['term-card', { active: item.term === selectedTerm }]"
          @click="selectedTerm = item.term"
        >
          <div class="term-label">{{ item.term }}</div>
          <div class="term-data">
            <div class="valuation">
              <span class="label">最新估值</span>
              <span class="value">{{ item.valuation }}</span>
            </div>
            <div class="change">
              <span class="label">涨跌BP</span>
              <span class="value" :class="getChangeClass(item.change)">{{ formatChange(item.change) }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧栏 -->
      <div class="right-panel">
        <div class="title">公开市场操作回笼投放分析</div>
        <div class="controls-section">
          <!-- 日期区间 -->
          <div class="control-row">
            <span class="control-label">日期区间</span>
            <el-radio-group v-model="dateRangeType" class="radio-group">
              <el-radio label="1y">近1年</el-radio>
              <el-radio label="6m">近6月</el-radio>
              <el-radio label="3m">近3月</el-radio>
              <el-radio label="1m">近1月</el-radio>
            </el-radio-group>
            <el-date-picker
              v-model="customDateRange"
              type="daterange"
              range-separator="~"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              class="date-picker"
              :picker-options="pickerOptions"
              value-format="yyyy-MM-dd"
            />
          </div>
          <!-- 发行期限 -->
          <div class="control-row">
            <span class="control-label">发行期限</span>
            <el-checkbox-group v-model="selectedIssueTerms" class="checkbox-group">
              <el-checkbox v-for="term in issueTerms" :key="term" :label="term">{{ term }}</el-checkbox>
            </el-checkbox-group>
            <el-tooltip content="发行期限最多选择10条" placement="top">
              <i class="el-icon-info info-icon" />
            </el-tooltip>
          </div>
          <!-- 操作按钮 -->
          <div class="control-row action-buttons">
            <el-button @click="resetFilters">重置</el-button>
            <el-button type="primary" @click="queryData">查询</el-button>
          </div>
        </div>

        <!-- 图表区域 -->
        <div class="chart-section">
          <div class="chart-header">
            <span class="chart-unit">单位：%</span>
            <el-button type="text" class="export-button" @click="exportData">
              <jr-svg-icon icon-class="upload" />
              导出
            </el-button>
          </div>
          <div ref="chartRef" class="chart-container" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'
// 假设 Element UI 是全局注册的

export default {
  name: 'BondsDetail',
  data() {
    const defaultDateRange = [new Date(2023, 9, 13), new Date(2024, 9, 13)] // 月份从 0 开始
    return {
      activeTab: 'interestRate',
      selectedTerm: '5Y',
      dateRangeType: '1y',
      defaultDateRange: defaultDateRange, // 保留用于重置逻辑
      customDateRange: defaultDateRange, // 初始化为默认值
      selectedIssueTerms: ['5Y'],
      chartInstance: null, // ECharts 实例
      bondTerms: [
        // 在 data 中默认就是响应式的
        { term: '1Y', valuation: '2.0010', change: +0.75 },
        { term: '3Y', valuation: '2.0000', change: +0.02 },
        { term: '5Y', valuation: '2.0000', change: +0.61 },
        { term: '7Y', valuation: '2.0010', change: +0.8 },
        { term: '10Y', valuation: '2.0000', change: +0.8 },
        { term: '15Y', valuation: '2.1900', change: +0.05 }
      ],
      issueTerms: [
        '1M',
        '2M',
        '3M',
        '6M',
        '9M',
        '1Y',
        '2Y',
        '3Y',
        '4Y',
        '5Y',
        '6Y',
        '7Y',
        '8Y',
        '9Y',
        '10Y',
        '15Y',
        '20Y',
        '30Y',
        '40Y',
        '50Y'
      ],
      // el-date-picker 在 Vue 2 中的 pickerOptions
      pickerOptions: {
        // 按需添加快捷选项或其他配置
        // defaultTime: ['00:00:00', '23:59:59'] // 示例
      }
    }
  },
  computed: {
    chartData() {
      // 这个计算属性理想情况下应根据筛选条件获取真实数据
      // 现在返回模拟数据
      // 使用 this.selectedIssueTerms, this.dateRangeType, this.customDateRange 来决定数据
      return {
        '5Y': {
          dates: [
            '2023/11/30',
            '2023/12/15',
            '2023/12/25',
            '2024/01/05',
            '2024/01/17',
            '2024/01/25',
            '2024/02/08',
            '2024/02/20',
            '2024/03/08',
            '2024/03/20',
            '2024/04/02',
            '2024/04/15',
            '2024/04/26',
            '2024/05/10',
            '2024/05/22',
            '2024/06/05',
            '2024/06/17',
            '2024/07/01',
            '2024/07/10',
            '2024/07/25',
            '2024/08/02',
            '2024/08/15',
            '2024/08/28',
            '2024/09/06'
          ],
          values: [
            2.75, 2.8, 2.8145, 2.78, 1.8111, 2.15, 2.3, 2.45, 2.25, 2.35, 2.2, 2.28, 2.15, 2.22, 2.18, 2.1, 2.15, 2.05,
            2.08, 2.0, 2.03, 1.98, 1.95, 2.0
          ]
        }
        // ... 其他期限的数据
      }
    }
  },
  watch: {
    dateRangeType(newType) {
      if (newType !== 'custom') {
        this.customDateRange = null // 选择预定义范围时清除自定义范围
        // 可选：立即获取数据 this.queryData();
      } else if (!this.customDateRange) {
        // 切换到自定义，但尚未选择范围，重置为默认值
        this.customDateRange = [...this.defaultDateRange] // 使用扩展运算符避免直接修改默认值数组
      }
    },
    customDateRange(newRange) {
      if (newRange && newRange[0] && newRange[1]) {
        // this.dateRangeType = 'custom'; // 如果希望选择日期自动切换单选按钮，取消此行注释
        // 可选：立即获取数据 this.queryData();
      }
    },
    selectedIssueTerms: {
      handler() {
        this.queryData()
      },
      deep: true // 监听数组内部变化需要 deep: true
    }
  },
  mounted() {
    this.$nextTick(() => {
      // 确保 DOM 准备就绪
      this.initChart()
    })
  },
  beforeDestroy() {
    // Vue 2 生命周期钩子
    // 移除事件监听器
    window.removeEventListener('resize', this.resizeChart)
    // 销毁 ECharts 实例
    if (this.chartInstance) {
      this.chartInstance.dispose()
      this.chartInstance = null
    }
  },
  methods: {
    formatChange(change) {
      return (change > 0 ? '+' : '') + change.toFixed(2)
    },

    getChangeClass(change) {
      return change > 0 ? 'positive' : change < 0 ? 'negative' : 'zero'
    },

    resetFilters() {
      this.dateRangeType = '1y'
      this.customDateRange = [...this.defaultDateRange] // 重置为默认值，使用扩展运算符
      this.selectedIssueTerms = ['5Y']
      this.queryData() // 重置后重新查询数据
    },

    queryData() {
      console.log('查询数据条件:', {
        dateRangeType: this.dateRangeType,
        customDateRange: this.customDateRange, // 发送到 API 前可能需要格式化 (例如转为 'YYYY-MM-DD' 字符串)
        selectedIssueTerms: this.selectedIssueTerms
      })
      // 在此添加实际的 API 获取逻辑
      // 成功后：如果需要，更新 data 中的属性，然后调用 updateChart
      this.updateChart()
    },

    exportData() {
      console.log('导出数据...')
      // 在此添加导出逻辑
    },

    initChart() {
      const chartDom = this.$refs.chartRef
      if (chartDom) {
        // 如果已存在实例，先销毁
        if (this.chartInstance) {
          this.chartInstance.dispose()
        }
        this.chartInstance = echarts.init(chartDom)
        this.updateChart() // 初始绘制
        window.addEventListener('resize', this.resizeChart)
      } else {
        console.error('未找到图表容器引用')
      }
    },

    updateChart() {
      if (!this.chartInstance) return

      // 使用计算属性 this.chartData
      const currentChartData = this.chartData

      const seriesData = this.selectedIssueTerms
        .map((term) => {
          const data = currentChartData[term]
          if (!data) return null

          let minVal = Infinity
          let maxVal = -Infinity
          // let minIndex = -1
          // let maxIndex = -1
          data.values.forEach((val, index) => {
            if (val < minVal) {
              minVal = val
              // minIndex = index
            }
            if (val > maxVal) {
              maxVal = val
              // maxIndex = index
            }
          })

          return {
            name: term,
            type: 'line',
            symbol: 'circle',
            symbolSize: 2,
            smooth: false,
            data: data.values,
            lineStyle: { width: 1.5 },
            markPoint: {
              data: [
                { type: 'max', name: '最大值', value: maxVal, itemStyle: { color: '#FF4500' }}, // 橙红色最大值标记
                {
                  type: 'min',
                  name: '最小值',
                  value: minVal,
                  itemStyle: { color: '#32CD32' },
                  label: { position: 'bottom' }
                } // 绿色最小值标记，标签在下方
              ],
              label: {
                formatter: (params) => params.value.toFixed(4), // 格式化标签值
                position: 'top', // 默认标签位置
                fontSize: 10,
                color: '#333'
              },
              symbol: 'circle',
              symbolSize: 6
            },
            emphasis: { lineStyle: { width: 2 }}
          }
        })
        .filter((s) => s !== null)

      const option = {
        // ECharts 配置项
        tooltip: {
          trigger: 'axis',
          formatter: (params) => {
            if (!params || params.length === 0) return ''
            let tooltipStr = params[0].axisValue + '<br/>' // 日期
            params.forEach((item) => {
              tooltipStr += `<span style="display:inline-block;margin-right:5px;border-radius:10px;width:9px;height:9px;background-color:${item.color};"></span>`
              tooltipStr +=
                item.seriesName +
                ': ' +
                (typeof item.value === 'number' ? item.value.toFixed(4) : item.value) +
                '%<br/>'
            })
            return tooltipStr
          }
        },
        legend: {
          data: this.selectedIssueTerms,
          bottom: 10,
          itemWidth: 10,
          itemHeight: 10,
          icon: 'circle'
        },
        grid: { left: '50px', right: '30px', top: '40px', bottom: '60px' },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          // 使用第一个选中期限的数据日期作为 X 轴，假设所有系列共享相同日期
          data: currentChartData[this.selectedIssueTerms[0]]?.dates || [],
          axisLine: { show: true, lineStyle: { color: '#ccc' }},
          axisTick: { show: false },
          axisLabel: { color: '#666' }
        },
        yAxis: {
          type: 'value',
          min: 1.6,
          max: 3.0,
          interval: 0.2,
          axisLabel: { formatter: '{value}', color: '#666' },
          splitLine: { show: true, lineStyle: { color: '#e8e8e8', type: 'dashed' }}
        },
        series: seriesData,
        color: ['#4e7af6'] // 单系列线条颜色
      }

      this.chartInstance.setOption(option)
    },

    resizeChart() {
      if (this.chartInstance) {
        this.chartInstance.resize()
      }
    }
  }
}
</script>

<style scoped lang="scss">
.bonds-detail-container {
  padding: 20px;
  background-color: #f5f7fa; // 页面背景色
  min-height: 100vh; // 确保容器至少和视口一样高
}

.bonds-tabs {
  margin-bottom: 20px;
  :deep(.el-tabs__header) {
    margin-bottom: 0; // 移除 tab header 底部 margin
  }
  :deep(.el-tabs__nav-wrap::after) {
    // tab 下方的线
    height: 1px;
    background-color: #e4e7ed;
  }
  :deep(.el-tabs__item) {
    // tab 标签样式
    font-size: 14px;
    color: #606266;
    &.is-active {
      color: #fa8c16; // 激活状态橙色
      font-weight: 500;
    }
  }
  :deep(.el-tabs__active-bar) {
    background-color: #fa8c16; // 激活指示器橙色
  }
}

.content-wrapper {
  display: flex;
  gap: 20px; // 左右栏间距
}

.left-panel {
  width: 200px; // 左侧栏宽度
  display: flex;
  flex-direction: column;
  gap: 15px; // 卡片间距
}

.term-card {
  background-color: #fff;
  border-radius: 4px;
  padding: 15px;
  display: flex;
  align-items: center;
  cursor: pointer;
  border: 1px solid #e8e8e8;
  transition: all 0.3s ease;
  position: relative; // 用于边框高亮

  &:hover {
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  }

  &.active {
    background-color: #ede7f6; // 淡紫色背景
    border-color: #ede7f6; // 边框颜色与背景一致
    // 使用伪元素创建左侧紫色边框
    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 0;
      bottom: 0;
      width: 3px;
      background-color: #673ab7; // 紫色边框
      border-top-left-radius: 4px; // 匹配卡片圆角
      border-bottom-left-radius: 4px;
    }
    .term-label {
      color: #673ab7; // 标签变紫
      font-weight: bold;
    }
  }

  .term-label {
    font-size: 18px;
    font-weight: bold;
    color: #333;
    width: 40px; // 固定宽度方便对齐
    margin-right: 15px;
    text-align: center;
    position: relative; // 用于竖线
    // 使用伪元素创建竖线
    &::after {
      content: '';
      position: absolute;
      right: -8px; // 调整标签和数据之间的位置
      top: 50%;
      transform: translateY(-50%);
      height: 60%; // 调整线条高度
      width: 1px;
      background-color: #e8e8e8; // 线条颜色
    }
  }

  // 如果活动卡片有线条，隐藏或更改颜色
  &.active .term-label::after {
    background-color: #dcd8e1; // 活动卡片上稍暗的线条？或者用 display:none 移除
  }

  .term-data {
    font-size: 12px;
    line-height: 1.6;
    flex-grow: 1;
    padding-left: 8px; // 添加内边距以与竖线分隔

    .valuation,
    .change {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 4px; // 在估值和变动之间添加小间距
      &:last-child {
        margin-bottom: 0;
      }
    }

    .label {
      color: #999;
    }

    .value {
      font-weight: 500;
      color: #333;
      font-family: 'Arial', sans-serif;
      &.positive {
        color: #f56c6c; // 红色
      }
      &.negative {
        color: #67c23a; // 绿色
      }
      &.zero {
        color: #909399; // 灰色
      }
    }
    .valuation .value {
      font-size: 14px;
      font-weight: bold;
    }
    .change .value {
      font-size: 13px;
    }
  }
}

.right-panel {
  flex-grow: 1;
  background-color: #fff;
  border-radius: 4px;
  padding: 20px;
  border: 1px solid #e8e8e8;
  display: flex;
  flex-direction: column;
}

.title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 20px;
  color: #333;
}

.controls-section {
  background-color: #f9f9f9;
  padding: 15px;
  border-radius: 4px;
  margin-bottom: 20px;
  border: 1px solid #eee;

  .control-row {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    &:last-child {
      margin-bottom: 0;
    }
  }

  .control-label {
    font-size: 14px;
    color: #666;
    width: 70px;
    margin-right: 10px;
    white-space: nowrap;
    text-align: right; // 标签文本右对齐
  }

  .radio-group {
    margin-right: 20px;
    :deep(.el-radio__label) {
      font-size: 13px;
    }
    // 匹配图片中的单选按钮样式（橙色选中）
    :deep(.el-radio__input.is-checked .el-radio__inner) {
      border-color: #fa8c16;
      background: #fa8c16;
    }
    :deep(.el-radio__input.is-checked + .el-radio__label) {
      color: #fa8c16;
    }
  }

  .date-picker {
    width: 260px; // 调整宽度以匹配图片
    :deep(.el-input__inner) {
      font-size: 13px;
    }
    :deep(.el-range-separator) {
      width: auto;
      padding: 0 2px;
    } // 调整分隔符宽度
  }

  .checkbox-group {
    display: flex;
    flex-wrap: wrap;
    gap: 5px 15px;
    flex-grow: 1;
    :deep(.el-checkbox__label) {
      font-size: 13px;
    }
    // 匹配图片中的复选框样式（橙色选中）
    :deep(.el-checkbox__input.is-checked .el-checkbox__inner),
    :deep(.el-checkbox__input.is-indeterminate .el-checkbox__inner) {
      background-color: #fa8c16;
      border-color: #fa8c16;
    }
    :deep(.el-checkbox__input.is-checked + .el-checkbox__label) {
      color: #fa8c16;
    }
    :deep(.el-checkbox__inner) {
      // 使复选框变为方形
      border-radius: 2px;
    }
    :deep(.el-checkbox__inner::after) {
      // 选中标记样式
      border-width: 1.5px;
    }
  }
  .info-icon {
    margin-left: 8px;
    color: #aaa; // 图标使用更浅的灰色
    cursor: pointer;
    font-size: 16px; // 调整图标大小
  }

  .action-buttons {
    margin-top: 5px;
    padding-left: 80px; // 与标签上方的控件对齐
    gap: 10px;
    display: flex; // 确保按钮在一行
    :deep(.el-button) {
      font-size: 13px;
      padding: 7px 15px; // 轻微调整内边距
      border-radius: 3px; // 轻微的边框圆角
    }
    :deep(.el-button--default) {
      // 重置按钮样式
      color: #666;
      border-color: #dcdfe6;
      &:hover {
        color: #fa8c16;
        border-color: #fde2cc;
        background-color: #fef6ef;
      }
    }
    :deep(.el-button--primary) {
      // 查询按钮样式
      background-color: #fa8c16;
      border-color: #fa8c16;
      &:hover {
        background-color: #ffa940;
        border-color: #ffa940;
      }
    }
  }
}

.chart-section {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  min-height: 350px;
}
.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}
.chart-unit {
  font-size: 12px;
  color: #999;
  // 根据最终 ECharts grid.left 确定的 Y 轴标签宽度调整外边距
  margin-left: 50px; // 初始猜测值，按需调整
}
.export-button {
  font-size: 13px;
  color: #409eff;
  border: none; // 移除文本按钮边框
  background: transparent;
  padding: 0; // 移除文本按钮内边距
  &:hover {
    color: #66b1ff;
  }
  i {
    // Vue 2 Element UI 图标样式
    margin-right: 4px;
    font-size: 14px; // 调整图标大小
  }
}
.chart-container {
  width: 100%;
  height: 100%;
  flex-grow: 1;
}

// 响应式样式保持不变
@media (max-width: 1200px) {
  .content-wrapper {
    flex-direction: column;
  }
  .left-panel {
    width: 100%;
    flex-direction: row; // 水平排列
    flex-wrap: wrap; // 允许换行
    justify-content: center; // 居中显示
  }
  .term-card {
    width: calc(33% - 10px); // 每行显示3个
    margin-bottom: 15px; // Add bottom margin for wrapped items
  }
  .right-panel {
    width: 100%;
  }
}
@media (max-width: 768px) {
  .term-card {
    width: calc(50% - 10px); // 每行显示2个
  }
  .controls-section .checkbox-group {
    justify-content: flex-start;
  }
  .controls-section .action-buttons {
    padding-left: 0; // 移动设备上取消缩进
    justify-content: flex-end; // 按钮靠右
  }
  .left-panel {
    gap: 10px; // Reduce gap on smaller screens
  }
  .term-card {
    width: calc(50% - 5px); // Adjust width for reduced gap
  }
}
</style>
