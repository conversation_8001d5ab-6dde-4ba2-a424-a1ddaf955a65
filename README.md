## 开发

* 开发工具：VsCode(建议)
* 格式化工具： Prettier - Code formatter(VsCode插件)

```bash
# 进入项目目录
cd 【项目目录】

# 设置私服地址（内网，保证能访问公司内网）
npm config set registry http://**************:8081/repository/npm-group/

# node-sass 安装失败时，可以尝试先执行
npm config set sass_binary_site https://npm.taobao.org/mirrors/node-sass/

# 安装依赖
npm install

# 项目目录下新建文件 .env.local(后端服务地址，可参考.env.example),文件内容：
PROXY_TARGET = 'http://192.168.xx.xx:xxxx/'

# 启动服务
npm run start
```
浏览器访问 http://localhost:9100 (会自动打开)


## 发布

```bash
# 构建生产环境

# 场景一：后端没有上下文或者分开部署,执行:
npm run build

# 场景二：跟后端项目一体化部署，放在后端resource下,需要配置后端项目名/上下文
方式1：
  修改根目录下package.json文件中参数homepage 修改为对应项目名，比如： /jupiter

方式2: (jupiter@4.0.4及以后版本支持， xxxx对应后端项目名，比如：jupiter)
  npm run build --project=xxxx
```
# 删除并构建

rimraf ./node_modules ./package-lock.json && npm i && npm run build

# config.js特殊配置说明
* `BASE_URL`: '/jupiter/', // 服务端路径，nginx部署不需要写ip端口
* `TIMEOUT`: 300, // 全局前端接口超时时间：（单位：秒）
* `DARK_THEME`: true, // 是否开启深色主题，true-开启，false-关闭
* `THEME_LIST`: [{ name: 'xxxxx', color: '#xxxxxx' }], // 行内定制主题,可新增、同名或同色覆盖
* `DEFAULT_THEME`: '#409eff' // 默认主题色，需是系统默认或上面定制中的一种，系统默认参考：[拂晓蓝-#409eff,蔚蓝-#00c1de,紫蓝-#7865e3,极客蓝-#3e59f2,明青-#13c2c2,明绿-#79ac43,薄暮-#6384c8,绛紫-#9254de,深橙-#ff7700,金盏花-#fb9c34,日暮-#fb5e28,火山-#ff5456,雅典黑-#4e5777]