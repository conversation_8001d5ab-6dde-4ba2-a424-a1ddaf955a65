/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2024-06-25 15:02:52
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2025-04-27 11:06:28
 * @Description: 描述
 */

import {
  GetInfoFn, UpdateFn,
  ExportFn
} from '@jupiterweb/utils/api'

// 初始化接口
export const init = (params = {}) => GetInfoFn('/framework/wf/home/<USER>/init', params)
// 批量审批校验
export const CheckMutiApproval = (params = {}) => GetInfoFn('/framework/wf/home/<USER>/checkMutiApproval', params)
// 批量审批
export const MutiApproval = (params = {}) => GetInfoFn('/framework/wf/home/<USER>/mutiApproval', params)

// 主动提醒
export const GetActiveRemindInit = () => GetInfoFn('/invest/remind/PaActiveRemind001/init')
// 主动提醒条数
export const GetActiveRemindCount = (params) => GetInfoFn('/invest/remind/PaActiveRemind001/searchCount', params)
// 获取主动提醒详情
export const GetRemindDetailData = (params) => GetInfoFn('/invest/remind/PaActiveRemind002/selramind', params)

// 主动提醒详情
export const GetActiveRemindDetailInit = () => GetInfoFn('/invest/remind/PaActiveRemind002/init')
// 关闭提醒
export const CloseRemind = (params) => GetInfoFn('/invest/remind/PaActiveRemind002/closeRemind', params)
// 查询资产编号
export const SearchFinprodId = (params) => GetInfoFn('/invest/remind/PaActiveRemind001/searchFinprodId', params)
// 债券覆盖check
export const BondCoverCheck = (params) => GetInfoFn('/invest/remind/PaActiveRemind002/coverBondCheck', params)
// 债券覆盖
export const BondCover = (params, cb) => UpdateFn('/invest/remind/PaActiveRemind002/coverBond', params, cb)

// POC组合区间收益率
export const QueryPortfolioRate = (params) => GetInfoFn('/cockpit/portfolioRate', params)
// 持仓Top10
export const QueryPortfolioTop10 = (params) => GetInfoFn('/cockpit/topPtlPosition', params)
// 字典
export const GetComboboxList = (params) => GetInfoFn('DICT', params)

// 查询组合
export const GetPortfolioList = (params) => GetInfoFn('/invest/portfolio/ptlmanagementinfo/PtlManagementInfo001/selectPortfolioId', params)

// 可选指标-树结构
export const getIndexInfo = params => GetInfoFn('/cockpit/indexInfo', params)

// 模块导出excel
export const exportExcelByModule = params => ExportFn('/web/common/exportExcelByModule', params)

// 获取用户信息
export const getUserInfo = params => GetInfoFn('/system/common/userInfo', params)

// 获取用户版本权限
export const getUserVersionPermission = params => GetInfoFn('/system/common/module/list', params)

// 根据债券代码查询对应债券承销商
export const queryLeadUnderwriter = params => GetInfoFn('/common/queryBondInfo/queryLeadUnderwriter', params)

// 公共模块-区域经济地区选项
export const regionDict = params => GetInfoFn('/web/common/regionDict', params)
