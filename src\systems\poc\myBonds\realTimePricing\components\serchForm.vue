<template>
  <div class="realTimePricingSearch">
    <div class="searchForm">
      <el-form :model="searchForm" label-width="90px">
        <jr-form-item label="发行人" class="formItem">
          <el-input
            v-model="searchForm.fxr"
            disabled
          />
        </jr-form-item>
        <br>
        <jr-form-item label="发行期限" style="width: 500px; display: inline-block;">
          <el-radio-group v-model="searchForm.fxqx" size="mini">
            <el-radio-button
              v-for="item in fxqxList"
              :key="item.value"
              :label="item.value"
            >{{ item.text }}</el-radio-button>
          </el-radio-group>
        </jr-form-item>
        <jr-form-item label="发行方式" class="formItem">
          <el-radio-group v-model="searchForm.fxfs">
            <el-radio :label="0">公募</el-radio>
            <el-radio :label="1">私募</el-radio>
          </el-radio-group>
        </jr-form-item>
        <jr-form-item label="是否可续期" class="formItem">
          <el-radio-group v-model="searchForm.sfkxq">
            <el-radio :label="0">不可续期</el-radio>
            <el-radio :label="1">可续期</el-radio>
          </el-radio-group>
        </jr-form-item>
        <el-button type="primary" class="btn">计算</el-button>
      </el-form>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      modalConfig: {
        isVisible: false,
        itemData: {}
      },
      searchForm: {
        fxr: '河北顺德投资集团有限公司',
        fxqx: '1Y',
        fxfs: 0,
        sfkxq: 0
      },
      fxqxList: [
        { value: '3M', text: '3M' },
        { value: '6M', text: '6M' },
        { value: '9M', text: '9M' },
        { value: '1Y', text: '1Y' },
        { value: '2Y', text: '2Y' },
        { value: '3Y', text: '3Y' },
        { value: '5Y', text: '5Y' },
        { value: '7Y', text: '7Y' }
      ]
    }
  },
  methods: {
  }
}
</script>
<style lang="scss">
.realTimePricingSearch {
    margin-top: 10px;
    padding: 6px 10px 6px 10px;
    background: #fff;
    .radio {
        display: inline-block;
        margin-left: 30px;
    }
    .el-table {
        height: 300px !important;
    }
    .formItem {
        display: inline-block;
        width: 300px
    }
    .btn {
        display: inline-block;
        margin-left: 10px;
    }
    .searchForm {
        padding: 10px 0 10px 0;
        background: #fff;
        margin-bottom: 10px;
        .el-tag {
            margin: 10px;
        }
    }
    .content {
        display: flex;
        .leftContent {
            flex: 1;
        }
        .rightContent {
            flex: 1;
        }
    }
}
</style>

