<template>
  <jr-modal
    modal-class="defined-modal"
    :handle-cancel="() => handleCancel()"
    :handle-ok="handleOk"
    :visible="visible"
    size="large"
    ok-text="完成"
  >
    <slot name="title">自定义列表自选</slot>
    <template v-slot:body>
      <div class="defined-modal-content">
        <div class="defined-modal-content-item">
          <div class="defined-modal-content-item-header">
            <div class="defined-modal-content-item-header-left">待选债券</div>
            <div class="defined-modal-content-item-header-right">
              已选择
              <span style="color: #f56c6c">{{ tableData.length }}</span>
              条
            </div>
          </div>
          <div class="defined-modal-content-item-content jrtable">
            <div class="defined-modal-content-item-content-search">
              <el-input
                v-model="keyword"
                placeholder="债券代码/债券简称/发行人"
                suffix-icon="el-icon-search"
                style="height: 32px"
                clearable
                @input="handleSearchLeft"
              />
            </div>
            <div class="defined-modal-content-item-content-table">
              <jr-table
                :data-source="leftTableData"
                ref="leftTable"
                :height="591"
                muti-select
                row-key="uuid"
                fit
                @selection-change="selectLeftRows"
              >
                <el-table-column prop="sinfoWindcode" label="债券代码" />
                <el-table-column prop="sinfoName" label="债券简称" />
                <el-table-column prop="binfoIssuer" label="发行人" />
              </jr-table>
            </div>
          </div>
          <div class="defined-modal-content-item-page">
            <jr-pagination
              :total="pageControl.total"
              :page-sizes="pageControl.pageSizes"
              :page.sync="pageControl.page"
              :limit.sync="pageControl.pageSize"
              :auto-scroll="false"
              layout="total, sizes, prev, pager, next, jumper"
              @pagination="getLeftTableData"
            />
          </div>
        </div>
        <div class="defined-modal-content-item">
          <div class="defined-modal-content-item-header">
            <div class="defined-modal-content-item-header-left">
              <span>已选债券</span>
              <span style="color: #e6a23c">
                <jr-svg-icon icon-class="info-circle" />
                最多50条
              </span>
            </div>
            <div class="defined-modal-content-item-header-right">
              <el-button type="text" style="height: 20px; background-color: #fff" @click="deleteAll">
                <jr-svg-icon icon-class="delete" />
                清空所选
              </el-button>
            </div>
          </div>
          <div class="defined-modal-content-item-content">
            <div class="defined-modal-content-item-content-search">
              <el-input
                v-model="rightKeyword"
                placeholder="债券代码/债券简称/发行人"
                suffix-icon="el-icon-search"
                style="height: 32px"
                clearable
                @input="handleSearchRight"
              />
            </div>
            <div class="defined-modal-content-item-content-table">
              <jr-table :data-source="tableData" :height="591" fit>
                <template v-slot:index>
                  <el-table-column type="index" width="80px" align="center" label="序号" />
                </template>
                <el-table-column prop="sinfoWindcode" label="债券代码" />
                <el-table-column prop="sinfoName" label="债券简称" />
                <el-table-column prop="binfoIssuer" label="发行人" />
                <template v-slot:action>
                  <el-table-column label="操作" width="80" align="center">
                    <template slot-scope="scope">
                      <div class="table-action-box">
                        <el-popconfirm title="确定删除吗？" @confirm="handleDelete(scope.row)">
                          <jr-svg-icon slot="reference" icon-class="delete" title="删除" />
                        </el-popconfirm>
                      </div>
                    </template>
                  </el-table-column>
                </template>
              </jr-table>
            </div>
          </div>
          <div class="defined-modal-content-item-page" />
        </div>
      </div>
      <div class="footerBac"></div>
    </template>
  </jr-modal>
</template>
<script>
import { saveBatchCustomizeBond, querySelectedBondList, queryCustomizeBondForAll } from '@/api/bonds/bonds'
import { debounce } from 'lodash'
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    permitdetail: {
      type: Object,
      default: () => ({})
    },
    menuinfo: {
      type: Object,
      default: () => ({})
    },
    module: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      keyword: '',
      rightKeyword: '',
      tableData: [],
      allTableData: [],
      leftTableData: [],
      leftTableDataALL: [],
      selectRows: [],
      maxBondCount: 0,
      pageControl: {
        total: 0,
        pageSizes: [20, 30, 40, 50, 60, 70],
        pageSize: 20,
        page: 1
      }
    }
  },

  async created() {
    this.handleSearchLeft = debounce(this.handleSearchLeft, 500)
    this.getTableData = debounce(this.getTableData, 500)
    this.refreshLeftTableSelect = debounce(this.refreshLeftTableSelect, 100)

    await this.getTableData()
  },
  methods: {
    getLeftTableData() {
      const data = [...this.leftTableDataALL]
      this.pageControl.total = data.length
      const start = this.pageControl.pageSize * (this.pageControl.page - 1)
      const end = start + this.pageControl.pageSize
      this.leftTableData = data.slice(start, end)
      this.refreshLeftTableSelect()
    },
    async searchLeftTableData() {
      const data = await queryCustomizeBondForAll({ module: this.module })
      console.log(data)
      this.leftTableDataALL = [...data]
      this.getLeftTableData()
    },
    refreshLeftTableSelect() {
      this.$nextTick(() => {
        const eleTableRef = this.$refs.leftTable?.$children[0]
        const selectRowIds = this.tableData.map((item) => item.sinfoWindcode)
        eleTableRef?.clearSelection()
        this.leftTableData.forEach((row) => {
          if (selectRowIds.includes(row.sinfoWindcode)) {
            eleTableRef?.toggleRowSelection(row, true)
          }
        })
      })
    },
    selectLeftRows(e) {
      if (!Array.isArray(e) || e.length === 0) return false
      if (this.tableData.length < 50) {
        const times = 50 - this.tableData.length
        for (let index = 0; index < e.length; index++) {
          const item = e[index]

          if (index <= times - 1) {
            if (!this.tableData.find((item1) => item1.sinfoWindcode === item.sinfoWindcode)) {
              this.tableData.push(item)
              this.allTableData.push(item)
            }
          } else {
            this.refreshLeftTableSelect()
            break
          }
        }
      } else {
        this.$message.warning('最多只能选择50条数据')
        // this.cleanLeftTableSelect()
        return false
      }
    },
    async getTableData() {
      const res = await querySelectedBondList({ inputvalue: '', module: this.module })
      console.log(res)
      this.tableData = [...res]
      this.allTableData = [...res]
      this.maxBondCount = this.tableData.length
      this.searchLeftTableData()
    },

    handleSearchLeft() {
      const data = [...this.leftTableDataALL].filter(
        (item) =>
          item.sinfoWindcode.includes(this.keyword) ||
          item.sinfoName.includes(this.keyword) ||
          item.binfoIssuer.includes(this.keyword)
      )
      this.pageControl.total = this.leftTableDataALL.length
      const start = this.pageControl.pageSize * (this.pageControl.page - 1)
      const end = start + this.pageControl.pageSize
      this.leftTableData = data.slice(start, end)
      this.refreshLeftTableSelect()
    },
    handleSearchRight() {
      const data = [...this.allTableData]
      this.tableData = data.filter(
        (item) =>
          item.sinfoWindcode.includes(this.rightKeyword) ||
          item.sinfoName.includes(this.rightKeyword) ||
          item.binfoIssuer.includes(this.rightKeyword)
      )
    },
    resetSearch(obj) {
      if (Object.hasOwnProperty.call(obj, 'searchFlag')) {
        delete obj['searchFlag']
      } else {
        obj['searchFlag'] = '1'
      }
      return obj
    },
    handleDelete(row) {
      console.log(row)
      this.leftTableDataALL.push(row)
      this.handleSearchLeft()
      this.tableData = this.tableData.filter((item) => {
        return JSON.stringify(item) !== JSON.stringify(row)
      })
      this.refreshLeftTableSelect()
    },
    deleteAll() {
      this.tableData = []
      this.refreshLeftTableSelect()
    },
    handleOk() {
      const params = []
      if (this.tableData.length > 0) {
        this.tableData.forEach((item) => {
          params.push({
            bondCode: item.sinfoWindcode,
            bondName: item.sinfoName,
            binfoIssuer: item.binfoIssuer,
            binfoIssuercode: item.binfoIssuercode,
            bIssueLastissue: item.bissueLastissue,
            module: this.module
          })
        })
      }

      saveBatchCustomizeBond({ voList: params, module: this.module }) // 企业id 暂时为空
        .then((res) => {
          this.keyword = ''
          this.rightKeyword = ''
          this.handleCancel()
        })
    },
    handleCancel() {
      this.keyword = ''
      this.rightKeyword = ''
      this.$emit('close')
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-form-item__content {
  margin-left: 0 !important;
}

::v-deep .jr-modal {
  .el-dialog__footer {
    border-top: none !important;
    background: none !important;
  }
}

.footerBac {
  width: 100%;
  height: 207px;
  background-image: url(../../../../assets/images/bondEntryBac.png);
  background-size: 100% 100%;
  position: fixed;
  bottom: 0px;
  left: 0px;
  z-index: 0;
}

::v-deep .is-center {
  .cell {
    .el-checkbox {
      width: 100% !important;
      height: 100% !important;
      align-items: center;
      display: flex;
      justify-content: center;
    }
  }
}

.jrtable {
  ::v-deep .el-table__body-wrapper {
    height: calc(100%) !important;
  }

  ::v-deep .jr-decorated-table--body {
    height: calc(100%) !important;
    .jr-table {
      height: calc(100%) !important;
      .el-table {
        height: calc(100%) !important;
      }
    }
  }
  ::v-deep .jr-decorated-table--body {
    padding-left: 0 !important;
    padding-right: 0 !important;
  }

  ::v-deep .is-scrolling-none {
    height: calc(100%) !important;
  }
}

.table-action-box {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
}

.defined-modal {
  &-content {
    display: flex;
    align-items: center;
    height: 100%;
    width: 100%;
    gap: 42px;
    // padding: 24px;

    &-item {
      height: 100%;
      flex: 1;
      flex-grow: 1;
      flex-basis: 0;
      min-width: 0;

      &-header {
        height: 30px;
        font-family: MicrosoftYaHeiSemibold;
        font-size: var(--el-font-size-base);
        color: rgba(0, 0, 0, 0.85);
        line-height: 30px;
        font-style: normal;
        display: flex;
        align-items: center;
        justify-content: space-between;

        &-left {
          text-align: left;
          display: flex;
          align-items: center;
          gap: 16px;
        }

        &-right {
          text-align: right;
        }
      }

      &-content {
        border-radius: 4px;
        border: 1px solid #cccccc;
        height: calc(100% - 74px);
        width: 100%;
        overflow: hidden;

        &-search {
          padding: 16px;
        }

        &-table {
          width: 100%;
          height: 100%;
          position: relative;
        }
      }

      &-page {
        width: 100%;
        height: 44px;
        position: relative;
        z-index: 999999999;
      }
    }
  }
}
</style>
