<!-- 债券持仓分析 -> 持仓趋势明细 -->
<template>
  <div class="home-poc-item has-fullscreen plt-base-info trend-detail-page">
    <div class="home-poc-item--header float-left">持仓趋势明细
      <fullscreen v-on="{ ...$listeners }" />

      <el-form :model="form" style="flex: 1; position: absolute; top: 8px; right: 140px;">
        <jr-form-item-create :data="cols" :model="form" :column="1" style="line-height: 0;" />
      </el-form>
    </div>

    <jr-decorated-table
      custom-id="faf7f34d522a4b219b92ca99c7219613"
      v-bind="{
        ...$attrs,
        params: queryParams,
        noPagination: true,
        menuinfo: {
          pageId: 'PtlPositionAnalysis001',
          btnList: [{
            btnPosition: 'HEAD',
            btnkey: 'export',
            btnnm: '导出',
            componenturl: 'export',
            effectflag: 'E',
            moduleid: 'PtlPositionAnalysis001_002_001',
            moduletype: 'btn',
            orde: 4,
            parameter: JSON.stringify({ noSelect: true }),
            permitflag: 'C',
            permittag: '02',
            showflag: 'Y',
            tmFlag: 'E'
          }]
        },
      }"
    />
  </div>
</template>

<script>
const format = 'YYYY-MM-DD'
import dayjs from 'dayjs'
import { getInit } from '@/systems/mixins'
import fullscreen from '../../common/fullscreen'

export default {
  components: {
    fullscreen
  },
  mixins: [getInit('/invest/portfolio/bondposanalysis/BondPosAnalysis001')],
  props: {
    params: {
      type: Object,
      default: null
    }
  },
  data() {
    const sysDate = this.$store.getters.systemTime
    const plateDate = dayjs(sysDate).format(format)

    return {
      sysDate,
      plateDate,
      cols: [],
      form: {
        timeLength: '01'
      }
    }
  },
  computed: {
    queryParams() {
      const { timeLength } = this.form
      const [vDate, mDate] = this.convertDate(timeLength)
      return { vDate, mDate, ...this.params }
    }
  },
  created() {
    this.init()
  },
  methods: {
    init() {
      const { TIME_LENGTH } = this.getInit

      this.cols = [{
        type: 'select',
        prop: 'timeLength',
        options: TIME_LENGTH,
        optionValue: 'id',
        clearable: false
      }]
    },
    convertDate(timeLength) {
      const sysDate = { ...this.params }.mDate || this.$store.getters.systemTime
      const plateDate = dayjs(sysDate).format(format)

      switch (timeLength) {
        case '04':
          return [dayjs(sysDate).subtract(7, 'day').format(format), plateDate]
        case '03':
          return [dayjs(sysDate).subtract(30, 'day').format(format), plateDate]
        case '02':
          return [dayjs(sysDate).subtract(180, 'day').format(format), plateDate]
        case '01':
          return [dayjs(sysDate).subtract(365, 'day').format(format), plateDate]
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import "../../common/poc.scss";
</style>

<style lang="scss">
.trend-detail-page {
  .el-form {
    .el-form-item__content {
      line-height: 0 !important;
    }
  }
}
</style>
