import * as echarts from 'echarts'
// 获取 echarts 内部维护的所有实例
function getAllEChartsInstances(id) {
  if (!id) return []
  const instances = []

  // echarts 内部通过 echarts._charts 维护所有实例
  if (echarts && echarts.getInstanceByDom) {
    const containers = document.getElementById(`pane-${id}`).querySelectorAll('[_echarts_instance_]')
    containers.forEach((container) => {
      const instance = echarts.getInstanceByDom(container)
      if (instance) {
        instances.push(instance)
      }
    })
  }

  return instances
}

// 触发所有图表 resize
function resizeAllECharts(id) {
  const instances = getAllEChartsInstances(id)
  if ((Array.isArray(instances) && instances.length === 0) || !Array.isArray(instances)) return

  instances.forEach((instance) => {
    try {
      instance.resize()
    } catch (e) {
      console.error('Resize chart error:', e)
    }
  })
}

export { getAllEChartsInstances, resizeAllECharts }
