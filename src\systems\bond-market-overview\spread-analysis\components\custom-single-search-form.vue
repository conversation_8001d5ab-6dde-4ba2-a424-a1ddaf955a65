<template>
  <div class="list-item">
    <el-checkbox v-model="localChecked" @change="emitCheckStatus" :disabled="!(localValue && localOtherValue)">
      <div class="list-item-label">
        <div class="list-item-label-icon">
          <div class="list-item-label-icon-left" :style="{ background: iconcolor }" />
          <div class="list-item-label-icon-center" :style="{ borderColor: iconcolor }" />
          <div class="list-item-label-icon-right" :style="{ background: iconcolor }" />
        </div>
        <span class="list-item-label-text">
          {{ label }}{{ subtext }}
          <el-tooltip v-if="showtooltip" placement="right" effect="dark">
            <jr-svg-icon icon-class="info-circle" />
            <div slot="content" v-html="tiptext" style="max-width: 300px" />
          </el-tooltip>
        </span>
      </div>
    </el-checkbox>

    <div style="display: flex; align-items: center; gap: 8px">
      <jr-combobox
        v-model="localValue"
        class="list-item-right"
        style="width: 124px; height: 32px"
        :data="selectlist"
        clearable
        option-value="itemcode"
        option-label="cnname"
        placeholder="请选择内容"
        @change="emitCheckStatus"
      />

      <jr-combobox
        v-model="localOtherValue"
        class="list-item-other"
        style="width: 124px; height: 32px"
        :data="otherselectlist"
        clearable
        option-value="itemcode"
        option-label="cnname"
        placeholder="请选择内容"
        @change="emitCheckStatus"
      />
    </div>

    <div class="list-item-action">
      <jr-svg-icon icon-class="plus-circle" color="var(--theme--color)" @click="addItem" />

      <jr-svg-icon icon-class="delete" color="var(--theme--color)" @click="deleteItem" />
    </div>
  </div>
</template>

<script>
export default {
  props: {
    selectlist: {
      type: Array,
      default() {
        return []
      }
    },
    otherselectlist: {
      type: Array,
      default() {
        return []
      }
    },
    checked: {
      type: Boolean,
      default() {
        return false
      }
    },
    iconcolor: {
      type: String,
      default() {
        return '#409EFF'
      }
    },
    label: {
      type: String,
      default() {
        return ''
      }
    },
    subtext: {
      type: String,
      default() {
        return ''
      }
    },
    showtooltip: {
      type: Boolean,
      default() {
        return false
      }
    },
    tiptext: {
      type: String,
      default() {
        return ''
      }
    },
    value: {
      type: String,
      default() {
        return ''
      }
    },
    othervalue: {
      type: String,
      default() {
        return ''
      }
    },
    index: {
      type: Number,
      default() {
        return 0
      }
    },
    liststr: {
      type: String,
      default() {
        return ''
      }
    }
  },
  data() {
    return {}
  },
  methods: {
    emitCheckStatus() {
      this.$emit('change')
    },
    addItem() {
      this.$emit('add', this.liststr)
    },
    deleteItem() {
      this.$emit('delete', this.liststr, this.index)
    }
  },
  computed: {
    localOtherValue: {
      get() {
        return this.othervalue
      },

      set(value) {
        this.$emit('update:othervalue', value)
      }
    },
    localValue: {
      get() {
        return this.value
      },

      set(value) {
        this.$emit('update:value', value)
      }
    },
    localChecked: {
      get() {
        return this.checked
      },

      set(value) {
        this.$emit('update:checked', value)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import '../css/custom-single-search-form-temporary.scss';
</style>
