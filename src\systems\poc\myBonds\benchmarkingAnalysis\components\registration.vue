
<template>
  <div class="info">
    <div v-for="item in basicInfoList" :key="item.id" class="info-div">
      <div class="info-content">
        <div class="info-item title">{{ item.name }}</div>
        <div class="info-item lastItem" :title="item.value">{{ item.value }}</div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      basicInfoList: [
        { name: '企业名称', value: '淄博市城市资产运营集团有限公司', id: '01' },
        { name: '统一社会信用代码', value: '91370300755424389Q', id: '02' },
        { name: '登记机关', value: '淄博市市铸细问管理局', id: '03' },
        { name: '法定代表人', value: '刘丙强', id: '11' },
        { name: '注册资本', value: '610,686.00万元', id: '04' },
        { name: '实缴资本', value: '548,810.00万元', id: '05' },
        { name: '营业期限', value: '2003-07-16 至无固定期限', id: '06' },
        { name: '成立时间', value: '2003-07-16', id: '07' },
        { name: '营业状态', value: '存续', id: '08' },
        { name: '注册地址', value: '山东省周村区颈贴区撤放路754号访病尊恳L区3#缺桌楼', id: '09' },
        { name: '经营范围', value: `
        以自有资金从事投资活动；自有资金投资的资产管理服务；土地整治服务；陆地管道运输；专用设备修理；仪器仪表销售；
        技术服务、技术开发、技术咨询、技术交流、技术转让、技术推广；停车场服务；住房租赁；非居住房地产租赁；
        金属矿石销售；化工产品销售（不含许可类化工产品）；煤炭及制品销售；机械设备销售；机械零件、零部件销售；
        金属材料销售；水泥制品销售；石灰和石膏销售；建筑材料销售；金属制品销售。
        （除依法须经批准的项目外，凭营业执照依法自主开展经营活动）
        许可项目：建设工程施工；房地产开发经营；非煤矿山矿产资源开采；天然水收集与分配；
        自来水生产与供应；热力生产和供应；特种设备设计。
        （依法须经批准的项目，经相关部门批准后方可开展经营活动，具体经营项目以相关部门批准文件或许可证件为准）
      `, id: '10'
        }
      ]
    }
  }
}
</script>

<style lang='scss' scoped>
    .info {
        display: flex;
        flex-wrap: wrap;
        .info-div:nth-child(-n+10){
            flex: 0 0 50%;
        }
        .info-div:last-child {
            width: 100%;
            .title {
                flex: 1;
                padding-right: 0px;
                padding-left: 3px;
            }
            .lastItem {
                flex: 3;
            }
        }
        &-title {
            line-height: 28px;
            border: var(--zoom, 1px) solid #dddfe4;
            padding-left: 10px;
            font-weight: 700;
        }
        &-content {
            display: flex;
            .title {
                background-color: #f7f7f7;
            }
        }
        &-item {
            // flex: 1;
            width: 100%;
            line-height: 28px;
            border: var(--zoom, 1px) solid #dddfe4;
            display: inline-block;
            justify-content: center;
            align-items: center;
            padding: 5px;
            border-right: none;
            text-align: center;
            overflow: hidden; /* 超出部分隐藏 */
            text-overflow: ellipsis; /* 使用省略号表示文本溢出 */
            white-space: nowrap; /* 确保文本在一行内显示 */
        }
        .lastItem {
            border-right: var(--zoom, 1px) solid #dddfe4;
        }
    }
</style>
