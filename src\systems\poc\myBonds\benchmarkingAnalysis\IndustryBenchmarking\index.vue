<!-- 行业对标 -->
<template>
  <div class="myBenchmark">
    <EnterpriseConfiguration />
    <BondBenchmarking />
    <FinancialBenchmarking />
  </div>
</template>

<script>
import EnterpriseConfiguration from '../components/enterpriseConfiguration.vue'
import BondBenchmarking from '../components/bondBenchmarking.vue'
import FinancialBenchmarking from '../components/financialBenchmarking.vue'
export default {
  components: { EnterpriseConfiguration, BondBenchmarking, FinancialBenchmarking },
  data() {
    return {

    }
  },
  methods: {

  }
}
</script>
<style lang="scss">
.myBenchmark{
    height: 100%;
}
</style>

