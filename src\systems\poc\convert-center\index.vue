<!--
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023-10-24 17:11:49
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2023-10-26 14:23:13
 * @Description: 配置转化中心
-->
<template>
  <div class="poc-convert-center">
    <div class="top">
      <PocView />
      <PocScene />
    </div>
    <div class="middle">
      <PocProcess />
    </div>
    <div class="bottom">
      <PocConverted />
      <PocUnconvert />
    </div>
  </div>
</template>

<script>
import PocView from './modules/view'
import PocScene from './modules/scene'
import PocProcess from './modules/process'
import PocUnconvert from './modules/unconvert'
import PocConverted from './modules/converted'
export default {
  components: {
    PocView,
    PocUnconvert,
    PocProcess,
    PocConverted,
    PocScene
  }
}
</script>

<style lang="scss">
.poc-convert-center {
  height: 100%;
  overflow: auto;
  display: flex;
  width: 100%;
  flex-direction: column;
  article {
    padding: 0 16px;
  }
  .header {
    border-bottom: 1px solid #E6E6E6;
    height: 30px;
    display: flex;
    justify-content: space-between;
    .el-radio-group {
      margin-top: 3px;
      margin-left: 6px;
      .el-radio-button--mini {
        .el-radio-button__inner {
          padding: 4px 10px;
        }
      }
    }
    .el-date-editor {
      width: 200px !important;
    }
    .el-range-editor--medium.el-input__inner {
      height: 22px !important;
      line-height: 20px !important;
    }
    .el-range-editor--medium .el-range__icon {
      line-height: 16px !important;
    }
    .search-list {
      padding-top: 3px;
      .jr-combobox {
        width: 90px;
        .el-input__inner {
          height: 22px!important;
        }
      }
      &>label {
        margin-left: 6px;
      }
      .jr-svg-icon {
        margin-left: 6px;
        cursor: pointer;
      }

      .el-radio-group {
        margin-top: 0;
        vertical-align: super;
      }
    }
    .title {
      line-height: 29px;
      display: inline-block;
      font-size: 14px;
      color: #333;
      border-top: 1px solid var(--theme--color);
    }
  }
  .body {
    height: calc(100% - 30px);
  }
  .top {
    height: 300px;
    display: flex;
    & > article {
      width: calc(60% - 4px);
      background: #fff;
      margin-right: 8px;
      &:last-child {
        width: calc(40% - 4px);
        margin-right: 0;
      }
    }
  }
  .middle {
    height: 180px;
    min-height: 180px;
    display: flex;
    margin-top: 8px;
    & > article {
      width: 100%;
      background: #fff;
    }
  }
  .bottom {
    height: calc(100% - 480px);
    min-height: 380px;
    margin-top: 8px;
    display: flex;
    & > article {
      width: calc(50% - 4px);
      background: #fff;
      margin-right: 8px;
      &:last-child {
        margin-right: 0;
      }
    }
  }
}
</style>
