<template>
  <!-- POC专用 -->
  <!-- 注册进度 -->
  <jr-decorated-table
    :custom-id="
      menuinfo.moduleid === '1316422716046700544'
        ? '7e1fb86ed34e4a31a333550ddfacb5d0'
        : 'b9e1cb881930442e98cd7aa8d463ecf8'
    "
    v-bind="{ ...$attrs, ...$props, menuinfo, customRender }"
  />
</template>
<script>
export default {
  props: {
    menuinfo: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      customRender: {
        xmzt: (h, { row }) => {
          return (
            <el-popover placement='bottom' width='200' trigger='click'>
              <el-timeline>
                <el-timeline-item timestamp='2024-09-29' placement='top' color='var(--theme--color)'>
                  反馈中（第3次）
                </el-timeline-item>
                <el-timeline-item timestamp='2024-09-13' placement='top'>
                  反馈中（第2次）
                </el-timeline-item>
                <el-timeline-item timestamp='2024-08-30' placement='top'>
                  反馈中
                </el-timeline-item>
                <el-timeline-item timestamp='2024-08-20' placement='top'>
                  预评中
                </el-timeline-item>
              </el-timeline>
              <el-link type='primary' slot='reference'>
                {row.xmzt}
              </el-link>
            </el-popover>
          )
        }
      }
    }
  }
}
</script>
