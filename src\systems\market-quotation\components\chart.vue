<template>
  <div class="template-module-chart">
    <el-dropdown @command="handleCommand" @visible-change="visibleChange">
      <ws-button class="el-dropdown-link" :loading="loading">
        <jr-svg-icon icon-class="upload" /> 导出 <i :class="visible ? 'el-icon-caret-top' : 'el-icon-caret-bottom' " />
      </ws-button>
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item command="tp"><jr-svg-icon icon-class="picture" /> 导出图片</el-dropdown-item>
        <el-dropdown-item command="bg"><jr-svg-icon icon-class="table" /> 导出表格</el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>
    <echarts ref="echarts" v-bind="{ options, styles }" />
  </div>
</template>
<script>
import echarts from '@jupiterweb/components/echarts'
import wsButton from '@/components/ws-button'
import { ExportFn } from '@jupiterweb/utils/api'
export default {
  name: 'Echarts1',
  inject: ['parant'],
  components: {
    echarts,
    wsButton
  },
  props: {
    time: {
      type: Number,
      default: 0
    },
    options: {
      type: Object,
      default: () => ({})
    },
    styles: {
      type: Object,
      default: () => ({ width: '100%', height: '500px' })
    },
    name: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      loading: false,
      visible: false
    }
  },
  methods: {
    handleCommand(v) {
      if (v === 'tp') { // 下载图片
        const exportOptions = {
          backgroundColor: '#ffffff',
          pixelRatio: 2 // 提高导出清晰度（可选）
        }
        // // 生成图片 URL 并触发下载
        const chart = this.$refs.echarts.myChart
        const imgUrl = chart.getDataURL(exportOptions)
        const link = document.createElement('a')
        link.href = imgUrl
        link.download = `${this.name}.png`
        link.click()
      } else {
        this.loading = true
        const form = this.parant.paramsDatas
        const params = {
          ...form,
          tradeDtStart: form.tradeDtStart || (form.date && form.date.length > 0 ? form.date[0] : ''),
          tradeDtEnd: form.tradeDtEnd || (form.date && form.date.length > 0 ? form.date[1] : '')
        }
        const url = this.parant.downFileUrl || '/marketdata/market/exportMarket'
        ExportFn(url, params, () => {
          this.loading = false
        })
      }
    },
    // 下拉菜单显示和隐藏
    visibleChange(v) {
      this.visible = v
    }
  }
}
</script>

<style lang="scss" scoped>
.template-module-chart{
  position: relative;
  .el-dropdown{
    position: absolute;
    right: 32px;
    top: 16px;
    z-index: 1;
    .el-dropdown-link{
      cursor: pointer;
      font-size: 14px;
      color: #333333;
    }
  }
}
</style>
