<template>
  <div class="tableSelect">
    <div class="tableSelect-selectArea">
      <div class="tableSelect-selectArea-select">
        <div class="tableSelect-selectArea-select-custom">
          <label>统计维度</label>
          <div>
            <jr-combobox
              v-model="selectParam.flag"
              style="width: 100%; height: 32px"
              :data="selectOptions.statisticalDimensionOptions"
              :popper-append-to-body="false"
              placeholder="请选择统计维度"
            />
          </div>
        </div>
        <div class="tableSelect-selectArea-select-custom">
          <label style="display: flex; align-items: center; justify-content: center">
            <span class="tableSelect-selectArea-select-custom-icon">
              <jr-svg-icon
                icon-class="swap"
                style="font-size: var(--el-font-size-base); color: var(--theme--color)"
                @click.stop="toggleLastIssue"
              />
            </span>
            <span style="font-size: var(--el-font-size-base); width: 60px; flex-shrink: 0; margin-right: 20px">
              {{ isLastissue ? '发行日期' : '到期日期' }}
            </span>
          </label>
          <div>
            <el-date-picker
              v-if="isLastissue"
              v-model="selectParam.lastissue"
              type="daterange"
              range-separator="至"
              unlink-panels
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            />
            <el-date-picker
              v-else
              v-model="selectParam.maturitydate"
              type="daterange"
              range-separator="至"
              unlink-panels
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            />
          </div>
        </div>
        <div class="tableSelect-selectArea-select-custom">
          <label>状态</label>
          <jr-combobox
            v-model="selectParam.issueState"
            style="width: 100%"
            :data="selectOptions.statusOptions"
            :popper-append-to-body="false"
            :multiple="true"
            placeholder="请选择状态"
          />
        </div>
        <div v-show="expand" class="tableSelect-selectArea-select-custom">
          <label>债券类型</label>
          <SelectAutoSetCollapseTages
            style="width: 100%"
            :options="bondTypeOptions"
            placeholder="请选择债券类型"
            @emitConfirmData="getBondTypes"
          />
        </div>
        <div v-show="expand" v-sysversion="'group'" class="tableSelect-selectArea-select-custom">
          <label>发行人</label>
          <jr-combobox
            v-model="selectParam.issuers"
            style="width: 100%"
            :data="selectOptions.issuerListOptions"
            :popper-append-to-body="false"
            placeholder="请选择发行人"
            option-label="text"
            option-value="value"
            :multiple="true"
            collapseTags
          />
        </div>
        <div v-show="expand" class="tableSelect-selectArea-select-custom">
          <label>剩余期限</label>
          <SelectAutoSetCollapseTages
            mode="all"
            style="width: 100%"
            :options="selectOptions.remainPeriodOptions"
            placeholder="请选择剩余期限"
            @emitConfirmData="getRemainPeriod"
          />
        </div>
        <div v-show="expand" class="tableSelect-selectArea-select-custom">
          <label>债券简称</label>
          <el-autocomplete
            v-model="selectParam.windcode"
            :fetch-suggestions="querySearchAsync"
            placeholder="请输入内容"
            value-key="text"
          />
        </div>
      </div>
      <div class="tableSelect-buttons" :style="expand ? { justifyContent: 'space-between' } : {}">
        <div>
          <el-button type="primary" @click.stop="tableSelect">查询</el-button>
          <el-button @click.stop="resetTableSelect">重置</el-button>
        </div>
        <p
          v-sysversion="'group'"
          class="tableSelect-buttons-switch"
          @click.stop="toggleExpand"
        >
          {{ expand ? '&#8743; 收起' : '&#8744; 展开' }}
        </p>
      </div>
    </div>
  </div>
</template>
<script>
import SelectAutoSet from '@/components/selectAutoSet'
import SelectAutoSetCollapseTages from '@/components/selectAutoSetCollapseTages'
import { queryBondShortName } from '@/api/bonds/bonds'
import { getIssuerList } from '@/api/public/public'
import { GetComboboxList } from '@/api/home'
const MONTH_CYCLE = 'MONTH_CYCLE' // 剩余期限字典值
export default {
  components: {
    SelectAutoSet,
    SelectAutoSetCollapseTages
  },
  props: {
    bondTypeOptions: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      isLastissue: true, // 是否发行日期查询
      expand: true, // 展开收起状态
      selectParam: {
        flag: '2',
        lastissue: [],
        maturitydate: [],
        issueState: ['存续'],
        bondtypes: [],
        issuers: [],
        remainTerm: [],
        windcode: ''
      },
      selectOptions: {
        statisticalDimensionOptions: [
          { text: '以到期计', value: '2' },
          { text: '以行权计', value: '1' }
        ],
        statusOptions: [
          { text: '存续', value: '存续' },
          { text: '到期', value: '到期' },
          { text: '预发行', value: '预发行' },
          { text: '发行失败', value: '发行失败' }
        ],
        remainPeriodOptions: [],
        issuerListOptions: []
      }
    }
  },
  created() {
    this.getRemainPeriodOptionsApi()
    this.getIssuerListApi()
  },
  methods: {
    /**
     * 获取剩余期限下拉项
     */
    async getRemainPeriodOptionsApi() {
      const data = await GetComboboxList([MONTH_CYCLE])
      this.selectOptions.remainPeriodOptions = data[MONTH_CYCLE].map((item) => {
        return {
          label: item.text,
          value: item.value
        }
      })
    },
    /**
     * 获取发行人下拉项
     */
    async getIssuerListApi() {
      const data = await getIssuerList({})
      this.selectOptions.issuerListOptions = data
    },
    /**
     * 表格确认查询
     */
    tableSelect() {
      this.$emit('setTableSelectParams', JSON.parse(JSON.stringify(this.selectParam)))
    },
    /**
     * 表格重置
     */
    resetTableSelect() {
      this.selectParam = {
        flag: '1',
        lastissue: [],
        maturitydate: [],
        issueState: [],
        bondtypes: [],
        issuers: [],
        remainTerm: [],
        windcode: ''
      }
      this.tableSelect()
    },
    /**
     * 切换展开收起状态
     */
    toggleExpand() {
      this.expand = !this.expand
    },
    /**
     * 从自定义组件获取债券类型数据
     */
    getBondTypes(data) {
      this.selectParam.bondtypes = data
    },
    /**
     * 从自定义组件获取剩余期限数据
     */
    getRemainPeriod(data) {
      this.selectParam.remainTerm = data
    },
    /**
     * 切换发行日期/到期日期
     */
    toggleLastIssue() {
      if (this.isLastissue) {
        this.selectParam.lastissue = []
      } else {
        this.selectParam.maturitydate = []
      }
      this.isLastissue = !this.isLastissue
    },
    /**
     * 债券简称远程搜索
     */
    querySearchAsync(queryString, cb) {
      if (queryString) {
        queryBondShortName({
          text: queryString
        }).then((data) => {
          if (data && Object.keys(data).length) {
            cb(data)
          } else {
            cb([])
          }
        })
      } else {
        cb([])
      }
    }
  }
}
</script>

<style scoped lang="scss">
.tableSelect {
  position: relative;
  &-selectArea {
    box-sizing: border-box;
    display: flex;
    &-select {
      width: calc(100% - 148px);
      display: flex;
      flex-wrap: wrap;
      column-gap: 24px;
      row-gap: 8px;
      &-custom {
        width: calc((100% - 60px) / 3);
        display: flex;
        label {
          width: 56px;
          height: 32px;
          font-size: var(--el-font-size-base);
          color: rgba(0, 0, 0, 0.85);
          line-height: 32px;
          text-align: center;
          margin-right: 8px;
          flex-shrink: 0;
        }
        & > div {
          width: calc(100% - 56px);
        }
        &-icon {
          width: 13px;
          height: 13px;
          border-radius: 50%;
          border: 1px solid var(--theme--color);
          box-sizing: border-box;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
        }
        ::v-deep .el-input__inner {
          height: 32px !important;
          line-height: 32px;
        }
      }
    }
  }
  &-buttons {
    margin-left: 14px;
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    & > button {
      height: 32px;
      border-radius: 2px;
      box-sizing: border-box;
    }
    & > button:nth-of-type(1) {
      background: var(--theme--color);
    }
    & > button:nth-of-type(2) {
      border: 1px solid rgba(0, 0, 0, 0.12);
    }
    &-switch {
      // position: absolute;
      // right: 0px;
      cursor: pointer;
      width: 80px;
      height: 32px;
      line-height: 32px;
      border-radius: 2px;
      text-align: center;
      margin-bottom: 0px;
      color: var(--theme--color);
    }
  }
}
::v-deep .el-range-editor--medium .el-range-separator {
  line-height: 26px;
}
::v-deep .el-range-editor--medium .el-range__icon {
  line-height: 26px;
}
::v-deep .el-select .el-input .el-select__caret {
  line-height: 32px;
}
::v-deep .el-range-editor .el-range__close-icon {
  line-height: 26px;
}
</style>
