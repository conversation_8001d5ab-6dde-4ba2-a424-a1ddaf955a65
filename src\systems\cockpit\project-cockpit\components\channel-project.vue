<template>
  <div class="channel-project">
    <div class="channel-project-select">
      <cockpitSelect 
        :style="{ width: px2vw(175), flexShrink: 0 }" 
        :options="projects" 
        :defaultValue="defaultValue"
        valueKey="label"
        @change="selectChange" 
        type="deep"
      />
    </div>
    <div :style="{ width: '100%', height: px2vh(315),marginTop: px2vh(33) }">
      <el-carousel 
        trigger="click" 
        style="width: 100%; height: 100%"
        :autoplay="false"
        ref="carousel"
        @change="carouselChange"
        >
        <el-carousel-item v-for="(carousel, cIndex) in projects" :key="cIndex" style="width: 100%; height: 100%">
          <echarts :options="carousel.options" style="width: 100%; height: 100%" />
        </el-carousel-item>
      </el-carousel>
    </div>
  </div>
</template>

<script>
import cockpitSelect from '../../components/cockpit-select.vue'
import { cockpitGetChannelProjectList } from '@/api/cockpit/cockpit.js'
import echarts from '@jupiterweb/components/echarts'
import * as echartsInstance from 'echarts'
import { px2vw, px2vh } from '../../utils/portcss'
export default {
  name: 'ChannelProject',
  components: {
    cockpitSelect,
    echarts
  },
  data() {
    return {
      projects: [],
      options: {},
      defaultValue: []
    }
  },
  created() {
    this.getChannelProjectDataApi()
  },
  methods: {
    px2vw,
    px2vh,
    /**
     * 获取通道中项目数据
     */
    async getChannelProjectDataApi() {
      const data = await cockpitGetChannelProjectList()
      for (let key in data) {
        this.projects.push({
          label: key,
          value: data[key]
        })
      }
      this.projects = this.projects.map(item=>{
        item.options = this.initChart(item.value)
        return item
      })
      this.defaultValue = this.projects[0]?.label || ''
    },
    /**
     * 项目选择
     */
    selectChange(data) {
      const index = this.projects.findIndex(item=>item.label === data)
      this.$refs.carousel.setActiveItem(index)
    },
    /**
     * 走马灯切换
     */
    carouselChange(index){
      this.defaultValue = this.projects[index]?.label
    },
    /**
     * 生成图表
     */
    initChart(data) {
      const seriesData = []
      for(let i = 0;i < data.length - 1;i++){
        seriesData.push([
          i,
          data[i].anndate,
          data[i + 1].anndate
        ])
      }
      let xData = []
      data.forEach(item=>{
        if(!xData.includes(item.anndate)){
          xData.push(item.anndate)
        }
      })
      return {
        grid: {
          left: '6%',
          right: '10%',
          top: '0%',
          bottom: '20%',
          containLabel: true
        },
        xAxis: {
          // type: 'time',
          data: xData,
          boundaryGap: false,
          axisLabel: {
            color: 'rgba(255,255,255,0.7)',
            fontSize: 12
          },
          axisLine: {
            lineStyle: {
              color: '#015192'
            }
          },
          axisTick: {
            show: false,
            alignWithLabel: true,
            lineStyle: {
              color: '#ffffff'
            }
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: 'rgba(255,255,255,0.1)',
              type: 'dashed'
            }
          }
        },
        yAxis: {
          type: 'category',
          data: data.filter((_,index)=>index < data.length - 1).map(item=>item.progress),
          axisLabel: {
            color: 'rgba(255,255,255,0.7)',
            fontSize: 12
          },
          axisLine: {
            lineStyle: {
              color: '#015192'
            }
          },
          axisTick: {
            show: false,
            lineStyle: {
              color: '#ffffff'
            }
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: 'rgba(255,255,255,0.1)',
              type: 'dashed'
            }
          }
        },
        toolbox: {
          show: false,
        },
        series: [
          {
            type: 'custom',
            renderItem: function (params, api) {
              var categoryIndex = api.value(0)
              var start = api.coord([api.value(1), categoryIndex])
              var end = api.coord([api.value(2), categoryIndex])
              var height = api.size([0, 1])[1] * 0.6

              var rectShape = echartsInstance.graphic.clipRectByRect(
                {
                  x: start[0],
                  y: start[1] - height / 2,
                  width: end[0] - start[0],
                  height: height
                },
                {
                  x: params.coordSys.x,
                  y: params.coordSys.y,
                  width: params.coordSys.width,
                  height: params.coordSys.height
                }
              )

              return (
                rectShape && {
                  type: 'rect',
                  transition: ['shape'],
                  shape: rectShape,
                  style: {
                    fill: new echartsInstance.graphic.LinearGradient(0, 0, 1, 0, [
                      { offset: 0, color: '#5B8FF9' }, // 起始颜色
                      { offset: 1, color: '#94C2FD' } // 结束颜色
                    ]),
                    stroke: '#1d4ed8',
                    lineWidth: 1
                  }
                }
              )
            },
            encode: {
              x: [1, 2],
              y: 0
            },
            data: seriesData
          }
        ],
        tooltip: {
          trigger: 'item',
          backgroundColor: 'rgba(0,0,0,0.8)',
          textStyle: {
            color: '#ffffff'
          },
          // formatter: function (params) {
          //   var data = params.data
          //   var categoryName = this.option.yAxis.data[data[0]]
          //   var startTime = echartsInstance.format.formatTime('MM-dd hh:mm', data[1])
          //   var endTime = echartsInstance.format.formatTime('MM-dd hh:mm', data[2])
          //   return categoryName + '<br/>' + '开始: ' + startTime + '<br/>' + '结束: ' + endTime
          // }
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~assets/styles/portcss.scss';
.channel-project {
  width: 100%;
  height: 100%;
  margin-top: vh(16);
  &-select {
    width: 100%;
    height: vh(32);
    padding-left: vw(23);
    padding-right: vw(9);
    display: flex;
    justify-content: flex-end;
  }
}
/*指示器*/
::v-deep .el-carousel__button{
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #CCC;
}
::v-deep .is-active .el-carousel__button{
  background-color: var(--theme--color);
}
::v-deep .el-carousel__arrow{
  top: 35%;
}
</style>
