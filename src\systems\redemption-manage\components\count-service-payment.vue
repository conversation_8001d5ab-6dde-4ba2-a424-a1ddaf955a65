<template>
  <div class="zj-content service-payment-modal">
    <UpdateModal :close-modal="closeModal" :item-data="itemData" :visible="isShowUpdateModal" />
  </div>
</template>

<script>
import UpdateModal from './count-modal.vue'
export default {
  name: 'CountServicePayment',
  components: { UpdateModal },
  props: {
    permitdetail: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  data() {
    return {
      form: {
        test: ''
      },
      itemData: null,
      isShowUpdateModal: true
    }
  },
  mounted() {},
  methods: {
    // 提交
    handleQuerySubmit() {},

    // 关闭弹窗
    closeModal(isSuccess) {
      this.isShowUpdateModal = false
      this.itemData = null
      isSuccess && this.getlistData()
    }
  }
}
</script>

<style></style>
