<!-- 债项付息兑付 -->
<template>
  <div class="denominatedBonds">
    <jr-decorated-table
      style="height: 100%"
      custom-id="3226e4a888144782ad7258b13a403461"
      :custom-render="customRender"
      v-bind="{...$attrs, ...$props}"
    />
    <jr-modal
      :modal-class="'denominatedBonds-modal'"
      :visible="visible"
      size="large"
      :has-footer="false"
      :handle-cancel="closeModal"
    >
      <slot
        name="title"
      >查看</slot>
      <template v-slot:body>
        <jr-decorated-table
          style="height: 100%"
          custom-id="cb2815bf48b246439d652ffaaccacda5"
          v-bind="{...$attrs, ...$props}"
        />
        <div class="denominatedBonds-info">
          {{ rowData.zqjc + '  发行日期：' + '2024-10-10' + '  应付总计：' + 100000 + '  已付：' + '2000000' + '  未付：' + '3000000' }}
        </div>
      </template>
    </jr-modal>
  </div>
</template>

<script>
export default {
  data() {
    return {
      customRender: {
        zqjc: (h, { row }) => {
          return <el-link type='primary' onClick={this.rowClick.bind(this, row)}>{row.zqjc}</el-link>
        }
      },
      rowData: {},
      visible: false
    }
  },
  methods: {
    rowClick(row) {
      this.rowData = row
      this.visible = true
    },
    closeModal() {
      this.visible = false
    }
  }
}
</script>
<style lang="scss">
.denominatedBonds{
    height: 100%;
}
.denominatedBonds-info {
      position: absolute;
      bottom: 40px;
    }
</style>
