<template>
  <div class="page-market-rate--term">
    <div class="page-market-rate--term-title">
      <span>类型</span>
      <el-button type="text" @click="form.termSelected = []"><jr-svg-icon icon-class="sync" /> 重置</el-button>
    </div>
    <div class="page-market-rate--term-content">
      <jr-checkbox-group v-model="form.termSelected" :data="termList" />
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      termList: [{
        text: '加权平均',
        value: '加权平均'
      }, {
        text: '大型金融机构',
        value: '大型金融机构'
      }, {
        text: '中型金融机构',
        value: '中型金融机构'
      }, {
        text: '小型金融机构',
        value: '小型金融机构'
      }],
      form: {
        termSelected: ['加权平均']
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.page-market-rate--term {
  width: 300px;
  height: 100%;
  padding: 0 12px;
  background-color: #f5f5f5;
  overflow: auto;
  .page-market-rate--term-title {
    display: flex;
    line-height: 40px;
    justify-content: space-between;
    align-items: center;
    span {
      font-size: 14px;
    }
  }
  .color-warning {
    color: var(--el-theme-color-warning);
  }
  .page-market-rate--term-content {
    height: calc(100% - 50px);
    width: 100%;
    min-width: 268px;
    overflow-x: hidden;
    ::v-deep.el-checkbox {
      display: inline-flex;
      flex-direction: row-reverse;
      justify-content: space-between;
      background: #fff;
      margin: 0 10px 10px 0px;
      padding: 6px 10px;
      width: 124px;
      align-items: center;
    }
    ::v-deep .jr-checkbox-group > label:nth-child(2n) {
      margin-right: 0;
    }
  }
}
</style>
