<template>
  <div class="bazaar national-rate" style="padding: 0 16px !important;">
    <h3>
      <span title=""><jr-svg-icon icon-class="exclamation-circle" />涨跌BP=(实时-前一日日终)</span>
    </h3>
    <jr-table :data-source="tableData" :stripe="false" :height="400">
      <el-table-column
        v-for="item in columnData"
        :key="item.prop"
        :prop="item.prop"
        :label="item.fieldName"
        width="132"
      >
        <template slot-scope="scope">
          <div v-if="item.prop === 'curveName'" class="curveName size-color">{{ scope.row.curveName }}</div>
          <div v-else :class="{'props-col': true, 'no': bAnalYieldBp(scope.row, item) == 0, 'hot': bAnalYieldBp(scope.row, item) > 0, 'fall': bAnalYieldBp(scope.row, item) < 0}">
            <p class="size-color">{{ bA<PERSON><PERSON>ield(scope.row, item) || '-' }}</p>
            <span>{{ bAnalYieldBp(scope.row, item) ? bAnalYieldBp(scope.row, item) + 'BP' : '-' }} <i v-if="bAnalYieldBp(scope.row, item)" :class="{'el-icon-caret-bottom': bAnalYieldBp(scope.row, item) < 0, 'el-icon-caret-top': bAnalYieldBp(scope.row, item) > 0}" /></span>
          </div>
        </template>
      </el-table-column>
    </jr-table>
  </div>
</template>

<script>
import { panel } from '@/api/poc/performance-evaluation'
export default {
  data() {
    return {
      tableData: [],
      columnData: []
    }
  },
  created() {
    this.marketRatePanel()
  },
  methods: {
    bAnalYield(row, item) {
      return row[`bAnalYield-${item.prop}`]
    },
    bAnalYieldBp(row, item) {
      return row[`bAnalYieldBP-${item.prop}`]
    },
    async marketRatePanel() {
      const data = await panel({
        panelType: 'market'
      }, '/marketdata/market/marketRatePanel')
      if (data) {
        this.columnData = data.titleList
        this.tableData = data.dataList
      }
    }
  }
}
</script>

<style lang="scss">
@import '@/assets/styles/national-rate.scss'
</style>
