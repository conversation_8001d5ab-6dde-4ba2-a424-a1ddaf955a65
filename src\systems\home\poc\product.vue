<template>
  <!-- 产品概览 -->
  <div class="home-poc-item has-fullscreen">
    <div class="home-poc-item--header float-left">产品概览 <fullscreen v-on="{ ...$listeners }" /></div>
    <jr-decorated-table
      custom-id="0703c58f79974302845aa0c6965c1ee8"
      v-bind="{ ...$attrs, defaultExpandAll: true, noPagination: true, params: {}, menuinfo: { pageId: 'Product'} }"
      :row-click="handleParams"
      @refreshed="queryEnd"
    />
  </div>
</template>

<script>
import fullscreen from './fullscreen'
export default {
  components: {
    fullscreen
  },
  props: {
    detail: {
      type: Object,
      default: () => ({})
    }
  },
  methods: {
    queryEnd(ins) {
      ins.listData && ins.listData[0] && this.handleParams(ins.listData[0].children[0], true)
    },
    handleParams(item, isFirst) {
      const p = {
        portfolioId: item.finprodMarketId,
        portfolioName: item.finprodAbbr
      }
      // 收益贡献：e02cb31f6a5f4ba7a28b0cddd34dc419
      // 持仓：7b2704057fe043d3b49742883ff888bc
      this.$emit('setTargetParams', isFirst === true ? {
        'e02cb31f6a5f4ba7a28b0cddd34dc419': p,
        '7b2704057fe043d3b49742883ff888bc': p
      } : {
        'a5941f67330f4e2fbde96d6d8c5c01ff': p,
        'e02cb31f6a5f4ba7a28b0cddd34dc419': p,
        '7b2704057fe043d3b49742883ff888bc': p
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@import "./poc.scss";
</style>
