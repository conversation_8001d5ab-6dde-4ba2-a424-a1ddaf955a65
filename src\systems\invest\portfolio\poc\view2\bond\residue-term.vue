<!-- 债券持仓分析 -> 久期&平均剩余期限 -->
<template>
  <div class="home-poc-item residue-term-page">
    <div class="home-poc-item--header"><el-button type="text" @click="changeType(1)">久期</el-button><span>|</span><el-button type="text" @click="changeType(2)">平均剩余期限</el-button>
      <el-form :model="form" style="flex: 1;padding-left: 20%;padding-right: 29px; margin-top: -1px;">
        <jr-form-item-create :data="cols" :model="form" :column="3" style="line-height: 2;" />
      </el-form>
      <fullscreen @fullscreen="fullscreen" />
    </div>

    <TemplateModule
      ref="TemplateModule"
      class="home-poc-item--body"
      :chart-seq="chartSeq"
      chart-type="LINE"
      style="padding-top: 10px;"
      :params="queryParams"
    />
  </div>
</template>

<script>
const format = 'YYYY-MM-DD'
import dayjs from 'dayjs'
import { getInit } from '@/systems/mixins'
import fullscreen from '../../common/fullscreen'
import TemplateModule from '@jupiterweb/components/template-module'

export default {
  components: {
    fullscreen,
    TemplateModule
  },
  mixins: [getInit('/invest/portfolio/bondposanalysis/BondPosAnalysis001')],
  props: {
    params: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      chartSeq: '460f0afd6e934f2a9ee84b66eb9bb69f',
      cols: [],
      form: {
        timeLength: '01'
      }
    }
  },
  computed: {
    queryParams() {
      const { timeLength } = this.form
      const [vDate, mDate] = this.convertDate(timeLength)
      return { vDate, mDate, ...this.params }
    }
  },
  created() {
    this.init()
  },
  methods: {
    init() {
      const { TIME_LENGTH } = this.getInit

      this.cols = [{
        type: 'select',
        prop: 'timeLength',
        options: TIME_LENGTH,
        optionValue: 'id',
        clearable: false
      }]
    },
    convertDate(timeLength) {
      const sysDate = { ...this.params }.mDate || this.$store.getters.systemTime
      const plateDate = dayjs(sysDate).format(format)

      switch (timeLength) {
        case '04':
          return [dayjs(sysDate).subtract(7, 'day').format(format), plateDate]
        case '03':
          return [dayjs(sysDate).subtract(30, 'day').format(format), plateDate]
        case '02':
          return [dayjs(sysDate).subtract(180, 'day').format(format), plateDate]
        case '01':
          return [dayjs(sysDate).subtract(365, 'day').format(format), plateDate]
      }
    },
    changeType(type) {
      this.chartSeq = type === 1 ? '460f0afd6e934f2a9ee84b66eb9bb69f' : 'f271791f2ad2435eb8fcd429ac809fdf'
    },
    fullscreen(v) {
      this.$emit('fullscreen', v)

      const { chartInstance } = this.$refs.TemplateModule.$children[0]
      chartInstance && this.$nextTick(chartInstance.resize)
    }
  }
}
</script>

<style lang="scss" scoped>
@import "../../common/poc.scss";
</style>

<style lang="scss">
.residue-term-page {
  .home-poc-item--header {
    height: 43px !important;
    line-height: 43px !important;

    span {
      display: inline-block;
    }

    .el-button.el-button--text {
      height: 40px !important;
      line-height: 40px !important;
      font-size: 13px !important;
      padding: 0 5px !important;
    }
  }
}
</style>

