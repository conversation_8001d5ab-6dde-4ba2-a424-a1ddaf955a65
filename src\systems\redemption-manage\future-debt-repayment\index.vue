<template>
  <div class="future-debt">
    <div class="future-debt-search-public public-table-search-container">
      <el-form inline :model="publicForm" label-width="90" class="future-debt-search-public-form">
        <jr-form-item v-if="$store.getters.sysVersion === $dict.COMPANY_VER_group" label="企业名称">
          <el-autocomplete
            v-model="publicForm.compName"
            style="width: 100%"
            placeholder="请输入企业全称"
            clearable
            remote
            :fetch-suggestions="remoteSearch"
            :loading="loading"
            @clear="handleClear"
            @select="handleSelect"
          />
        </jr-form-item>

        <jr-form-item>
          <div solt="label">
            <el-popover
              placement="top-start"
              trigger="hover"
              content="当前页面数据图表和列表均以追湖时间点计算本金、利息等信息。"
            >
              <span slot="reference" style="padding-right: 12px">追溯时间点</span>
            </el-popover>
          </div>
          <div class="future-debt-search-public-form-date">
            <el-date-picker
              v-model="publicForm.customDate"
              style="height: 32px; width: 100%"
              type="date"
              placeholder="请选择日期"
              format="yyyy-MM-dd"
              class="date-picker"
            />
            <div @click="setToday">今</div>
          </div>
        </jr-form-item>

        <el-popover placement="bottom-start" trigger="click">
          <div class="future-debt-search-public-form-yearQuarter" slot="reference">
            <jr-svg-icon icon-class="date" color="#FF8E2B" />
          </div>
          <el-cascader-panel v-model="selectedValues" :options="cascaderOptions" @change="handleChangeYearQuarter" />
        </el-popover>

        <div class="future-debt-search-public-form-actions">
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
        </div>
      </el-form>
    </div>

    <div class="future-debt-chart">
      <div class="future-debt-chart-title public-title-container" style="padding: 0 16px">
        <div class="future-debt-chart-title-text">未来偿债现金流数据图</div>
        <div class="future-debt-chart-title-radios">
          <jr-radio-group v-model="chartsForm.range" :data="rangeList" />
        </div>
      </div>
      <div class="future-debt-chart-search public-table-search-container">
        <el-form inline :model="chartsForm" label-width="90" class="future-debt-search-public-form">
          <jr-form-item label="统计维度">
            <jr-combobox
              v-model="chartsForm.statDimension"
              style="height: 32px"
              placeholder="请选择"
              clearable
              filterable
              :data="[
                {
                  itemcode: 1,
                  cnname: '以行权价'
                },
                {
                  itemcode: 2,
                  cnname: '以到期计'
                }
              ]"
              option-value="itemcode"
              option-label="cnname"
            />
          </jr-form-item>

          <jr-form-item label="债券查询">
            <el-autocomplete
              v-model="chartsForm.windname"
              :fetch-suggestions="querySearchAsync"
              placeholder="请输入债券"
              clearable
              style="height: 32px"
              value-key="text"
              @select="handleChangeBond"
            />
          </jr-form-item>

          <div class="future-debt-search-public-form-actions">
            <el-button type="primary" @click="handleSearch">查询</el-button>
          </div>

          <div class="future-debt-search-public-form-end">
            <el-radio-group v-model="chartsForm.queryFlag">
              <el-radio-button label="1">
                <jr-svg-icon icon-class="future01" color="#fff" />
              </el-radio-button>
              <el-radio-button label="2">
                <jr-svg-icon icon-class="future02" color="#000" />
              </el-radio-button>
            </el-radio-group>
            <el-button style="height: 32px">
              <jr-svg-icon class="el-icon--left" icon-class="upload" />
              导出
            </el-button>
          </div>
        </el-form>
      </div>
      <div style="padding: 8px 16px">
        <div class="future-debt-chart-tips">
          <div>
            2025年01月31日至2025年08月31日，应付现金流总额：
            <span>{{ ConvertAmount('HMU', amount, 1, 4) }}</span>
            亿元，其中到期债券：
            <span>{{ totalcount }}</span>
            只
          </div>
        </div>
      </div>

      <div class="future-debt-chart-content">
        <futureDebtChart :chartdata="chartOptions" :height="500" title="导出文件名" />
      </div>
    </div>

    <div class="future-debt-table">
      <div class="future-debt-table-title public-title-container" style="padding: 0 16px">未来偿债现金流明细</div>
      <div class="future-debt-table-search public-table-search-container">
        <el-form inline :model="chartsForm" label-width="90" class="future-debt-search-public-form">
          <jr-form-item label="支付日期">
            <el-date-picker
              v-model="form.customDateRange"
              style="height: 32px"
              type="daterange"
              range-separator="至"
              unlink-panels
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="yyyy-MM-dd"
              class="date-picker"
            />
          </jr-form-item>

          <jr-form-item label="债券类型">
            <SelectAutoSetCollapseTages
              style="width: 100%"
              :options="bondTypeOptions"
              placeholder="请选择债券类型"
              :maxVisibleTags="1"
              @emitConfirmData="getBondTypes"
            />
          </jr-form-item>

          <jr-form-item label="剩余期限">
            <SelectAutoSetCollapseTages
              mode="all"
              style="width: 100%"
              :options="remainPeriodOptions"
              placeholder="请选择剩余期限"
              :maxVisibleTags="2"
              @emitConfirmData="getRemainPeriod"
            />
          </jr-form-item>

          <jr-form-item label="是否赎回">
            <jr-combobox
              v-model="form.is_callable"
              style="height: 32px"
              placeholder="请选择"
              clearable
              filterable
              :data="[
                {
                  itemcode: 'Y',
                  cnname: '是'
                },
                {
                  itemcode: 'N',
                  cnname: '否'
                }
              ]"
              option-value="itemcode"
              option-label="cnname"
            />
          </jr-form-item>
          <div class="future-debt-search-public-form-actions">
            <el-button type="primary" @click="handleTableSearchParams">查询</el-button>
          </div>
        </el-form>
      </div>
      <div class="future-debt-table-content">
        <jr-decorated-table
          ref="jrTable"
          :params="tableParams"
          style="height: 516px"
          custom-id="6b3ed11203b745adadd25d3221e057b8"
          :menuinfo="menuinfo"
          :default-page-size="10"
          :initPagination="{
            pageSizeOptions: [10, 20, 50, 100]
          }"
          v-bind="{ ...$attrs, ...$props }"
        />
      </div>
    </div>
  </div>
</template>

<script>
import { debounce } from 'lodash'
import moment from 'moment'
import futureDebtChart from '../components/future-debt-chart.vue'
import { queryBondShortName } from '@/api/bonds/bonds'
import { ConvertAmount } from '@jupiterweb/utils/common.js'
import { issuanceQueryBondTypeList } from '@/api/issuance/issuance'
import { GetComboboxList } from '@/api/home'
import SelectAutoSetCollapseTages from '@/components/selectAutoSetCollapseTages'
const MONTH_CYCLE = 'MONTH_CYCLE' // 剩余期限字典值
import {
  futureCashFlowQueryAnalyseInfo,
  futureCashFlowQueryPrincipalAndInterestInfo
} from '@/api/redemption-manage/redemption-manage'
export default {
  components: { futureDebtChart, SelectAutoSetCollapseTages },
  props: {
    menuinfo: {
      type: Object,
      default: () => ({})
    },
    permitdetail: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      publicForm: {
        customDate: '',
        yearQuarter: ''
      },
      tableParams: {},
      loading: false,
      options: [],
      cascaderOptions: [],
      chartsForm: {
        range: '2',
        customDate: null,
        queryFlag: '1',
        statDimension: 1
      },
      rangeList: [],
      form: {
        customDateRange: []
      },
      chartOptions: {},
      amount: 0,
      totalcount: 0,
      remainPeriodOptions: [],
      bondTypeOptions: [],
      selectedValues: []
    }
  },
  created() {
    this.getRemainPeriodOptionsApi()
    this.getBondTypeOptionsApi()
    this.debouncedRemoteSearch = debounce(this.rawRemoteSearch, 500)

    this.setToday()
    if (this.publicForm.customDate) {
      this.rangeList = [
        {
          label: '未来5年及以后',
          value: 1
        },
        {
          label: '未来一年',
          value: 2
        },
        {
          label: '未来半年',
          value: 3
        }
      ]
      this.chartsForm.range = 2
    }

    this.initCascaderOptions()
    this.form.customDateRange = this.getDefaultPayDateRange()
    this.getChartData([], [], [])
    this.handleSearch()
  },
  computed: {
    retrospectiveTime() {
      return this.publicForm.customDate
    }
  },
  watch: {
    // 追溯时间点不可为空
    retrospectiveTime(newVal) {
      this.selectedValues = [
        new Date(this.publicForm.customDate).getFullYear(),
        moment(new Date(this.publicForm.customDate)).format('YYYY-MM-DD')
      ]
      if (!newVal) {
        this.setToday()
      }
    }
  },
  methods: {
    ConvertAmount,
    getBondTypes(data) {
      this.form.bondType = data
    },
    getRemainPeriod(data) {
      this.form.remainTerm = data
    },
    handleChangeBond(value) {
      console.log(value, 'value')
      if (value && Object.hasOwnProperty.call(value, 'value')) {
        this.chartsForm.windCode = value.value
      }
    },
    getChartRadioSearch(type) {
      switch (type) {
        case 1:
          return this.publicForm.customDate
        case 2:
          return [this.publicForm.customDate, this.ddMonthsSafely(this.publicForm.customDate, 12)]
        case 3:
          return [this.publicForm.customDate, this.ddMonthsSafely(this.publicForm.customDate, 6)]
      }
    },
    ddMonthsSafely(inputDate, months) {
      const date = new Date(inputDate)
      const originalDate = date.getDate()

      date.setMonth(date.getMonth() + months)

      // 处理跨月时日期不一致的情况（如1月31日加1个月）
      if (date.getDate() !== originalDate) {
        date.setDate(0) // 设置为上个月的最后一天
      }

      return moment(new Date(date)).format('YYYY-MM-DD')
    },
    initCascaderOptions() {
      const nowYear = moment(+new Date()).format('YYYY')
      for (let i = 5; i > 0; i--) {
        const year = nowYear * 1 - i
        this.cascaderOptions.push({
          value: year,
          label: year + '年',
          children: [
            {
              value: year + '-03-31',
              label: year + '-03-31'
            },
            {
              value: year + '-06-30',
              label: year + '-06-30'
            },
            {
              value: year + '-09-30',
              label: year + '-09-30'
            },
            {
              value: year + '-12-31',
              label: year + '-12-31'
            }
          ]
        })
      }
    },
    getChartData(xAxisData, seriesData, legend) {
      if (Array.isArray(seriesData) && seriesData.length > 0) {
        this.chartOptions = {
          xAxis: {
            type: 'category',
            data: xAxisData
          },
          toolbox: { show: false },
          legend: {
            data: legend,
            orient: 'horizontal',
            top: 0,
            left: 'center',
            type: 'scroll', // 设置为可滚动
            pageIconColor: '#000', // 分页箭头颜色
            pageIconInactiveColor: '#333', // 禁用时分页箭头颜色
            pageTextStyle: {
              color: '#000' // 页码文字颜色
            },
            textStyle: {
              color: '#000' // 设置文字颜色为深灰色
            },
            pageButtonItemGap: 5, // 分页按钮与图例项的间隔
            pageButtonGap: 10, // 分页按钮与图例组件外框的间隔
            pageButtonPosition: 'end', // 分页按钮位置
            pageFormatter: '{current}/{total}', // 页码显示格式
            padding: 5 // 图例内边距
          },
          grid: {
            top: 72,
            left: 36,
            bottom: 36,
            right: 36
          },
          yAxis: {
            type: 'value'
          },
          series: seriesData
        }
      } else {
        this.chartOptions = {
          title: {
            text: '暂无数据',
            left: 'center',
            top: 'center',
            textStyle: {
              color: '#999',
              fontSize: 14
            }
          },
          graphic: [
            {
              type: 'image',
              style: {
                width: 100,
                height: 100
              },
              left: 'center',
              top: '40%'
            }
          ],
          series: [] // 空数组表示无数据
        }
      }
    },
    setToday() {
      const today = new Date()
      today.setHours(0, 0, 0, 0)
      this.publicForm.customDate = moment(new Date(today)).format('YYYY-MM-DD')
    },
    querySearchAsync(queryString, cb) {
      if (queryString) {
        queryBondShortName({
          text: queryString
        }).then((data) => {
          if (data && Object.keys(data).length) {
            cb(data)
          } else {
            cb([])
          }
        })
      } else {
        cb([])
      }
    },
    handleTableSearchParams() {
      console.log('自定义列查询')
      this.chartsForm.customDate = this.getChartRadioSearch(this.chartsForm.range)
      const obj = {
        startTime: Array.isArray(this.chartsForm.customDate)
          ? moment(new Date(this.chartsForm.customDate[0])).format('YYYYMMDD')
          : moment(new Date(this.chartsForm.customDate)).format('YYYYMMDD'),
        endTime: Array.isArray(this.chartsForm.customDate)
          ? moment(new Date(this.chartsForm.customDate[1])).format('YYYYMMDD')
          : null,
        bondType: this.form.bondType,
        remainTerm: this.form.remainTerm,
        is_callable: this.form.is_callable,
        windCode: this.chartsForm.windCode || '',
        payStartTime:
          this.form.customDateRange.length > 0 ? moment(new Date(this.form.customDateRange[0])).format('YYYYMMDD') : '',
        payEndTime:
          this.form.customDateRange.length > 0 ? moment(new Date(this.form.customDateRange[1])).format('YYYYMMDD') : ''
      }

      this.tableParams = {
        ...obj,
        ...this.tableParams,
        webTime: new Date().getTime()
      }
    },
    handleSearch() {
      console.log(this.publicForm, 'publicForm')
      console.log(this.chartsForm, 'chartsForm')
      this.chartsForm.customDate = this.getChartRadioSearch(this.chartsForm.range)
      const params = {
        authOrgIds: [],
        bondType: this.form.bondType,
        endTime: Array.isArray(this.chartsForm.customDate)
          ? moment(new Date(this.chartsForm.customDate[1])).format('YYYYMMDD')
          : null,
        isRedemption: this.form.isRedemption || null,
        issuerCode: '',
        queryFlag: this.chartsForm.queryFlag || null,
        remainTerm: this.form.remainTerm,
        startTime: Array.isArray(this.chartsForm.customDate)
          ? moment(new Date(this.chartsForm.customDate[0])).format('YYYYMMDD')
          : moment(new Date(this.chartsForm.customDate)).format('YYYYMMDD'),
        statDimension: this.chartsForm.statDimension || null,
        windCode: this.chartsForm.windCode || ''
      }
      futureCashFlowQueryPrincipalAndInterestInfo(params).then((res) => {
        console.log(res, 'res')
        if (Array.isArray(res) && res.length > 0) {
          const { xAxisData, series, legend } =
            this.chartsForm.queryFlag === '2'
              ? this.processLargeBondData(res, {
                  defaultBondType: ''
                })
              : this.processStackedWithTotal(res, {
                  defaultBondType: ''
                })
          console.log(xAxisData, series, legend)

          this.getChartData(xAxisData, series, legend)
        } else {
          this.$message({
            type: 'warning',
            message: '暂无数据'
          })
        }
      })
      futureCashFlowQueryAnalyseInfo(params).then((res) => {
        if (Object.hasOwnProperty.call(res, 'amount')) {
          this.amount = res.amount || 0
        }
        if (Object.hasOwnProperty.call(res, 'totalcount')) {
          this.totalcount = res.totalcount || 0
        }
      })
    },
    handleReset() {
      this.publicForm = {
        customDate: '',
        yearQuarter: ''
      }
      this.setToday()
      this.handleSearch()
    },
    getQuarterDateRange(year, quarter) {
      const months = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31]
      const isLeapYear = (y) => (y % 4 === 0 && y % 100 !== 0) || y % 400 === 0

      if (isLeapYear(year)) {
        months[1] = 29 // 闰年2月有29天
      }

      const startMonth = Math.ceil(quarter - 0.25) * 3 - 2 // 计算开始月份
      const endMonth = Math.ceil(quarter) * 3 // 计算结束月份

      const startDate = new Date(year, startMonth - 1, 1) // 创建开始日期对象
      const endDate = new Date(year, endMonth - 1, months[endMonth - 1]) // 创建结束日期对象

      // 处理12月的情况，将结束日期设置为下一年的1月1日的前一天
      if (endMonth === 13) {
        endDate.setFullYear(year + 1)
        endDate.setMonth(0) // 0表示1月
        endDate.setDate(0) // 设置为0会自动调整为上一个月的最后一天
      }

      return [moment(new Date(startDate)).format('YYYY-MM-DD'), moment(new Date(endDate)).format('YYYY-MM-DD')]
    },

    handleChangeYearQuarter(value) {
      console.log(value)
      if (Array.isArray(value) && value.length > 0) {
        this.publicForm.customDate = value[1]
      }
    },
    // 设置搜索日期为今天
    setToday() {
      this.publicForm.customDate = moment(new Date()).format('YYYY-MM-DD')
    },
    getDefaultPayDateRange() {
      const currentDate = new Date()
      const currentYear = currentDate.getFullYear()
      const currentMonth = currentDate.getMonth() // 0-11

      // 计算结束日期（明年当前月-1月的最后一天）
      let endYear = currentYear + 1
      let endMonth = currentMonth - 1

      // 处理跨年情况（如果当前月是1月，则前一个月是上一年的12月）
      if (endMonth < 0) {
        endMonth = 11 // 12月
        endYear -= 1
      }

      // 获取结束月份的最后一天
      const endDate = new Date(endYear, endMonth + 1, 0) // 下个月的第0天就是上个月的最后一天

      return [moment(new Date(currentDate)).format('YYYY-MM-DD'), moment(new Date(endDate)).format('YYYY-MM-DD')]
    },
    // 企业名称搜索
    remoteSearch(query, cb) {
      if (!query) return cb([])
      this.loading = true
      this.debouncedRemoteSearch(query, cb)
    },
    rawRemoteSearch(query, cb) {
      const compId = '502036608'
      queryCompName(query, compId) // 企业id 暂时为空
        .then((res) => {
          // 转换数据格式，确保每个选项有value字段
          const suggestions = res.map((item) => ({
            value: item.compName, // 显示文本
            ...item // 保留其他字段
          }))
          cb(suggestions)
        })
        .finally(() => {
          this.loading = false
        })
    },
    handleSelect(value) {
      if (value) {
        // 保存选中的数据
        this.publicForm.compId = value.compId
        this.publicForm.compName = value.compName
      }
    },
    // 企业名称清空回调
    handleClear() {
      // 清空选中的数据
      this.publicForm.compId = ''
      this.publicForm.compName = ''
    },
    /**
     * 获取剩余期限下拉项
     */
    async getRemainPeriodOptionsApi() {
      const data = await GetComboboxList([MONTH_CYCLE])
      this.remainPeriodOptions = data[MONTH_CYCLE].map((item) => {
        return {
          label: item.text,
          value: item.value
        }
      })
    },
    /**
     * 获取债券类型下拉项参数
     */
    async getBondTypeOptionsApi() {
      const data = await issuanceQueryBondTypeList()
      this.bondTypeOptions = data.reduce((pre, current) => {
        if (current) {
          const index = pre.findIndex((item) => item.value === current.bondTypeCode)
          if (index === -1) {
            pre.push({
              label: current.bondTypeName,
              value: current.bondTypeCode,
              children: []
            })
          } else {
            pre[index].children.push({
              label: current.bondTypeName2,
              value: current.bondTypeCode2
            })
          }
        }
        return pre
      }, [])
    },
    transformBondDataAdvanced(rawData, { defaultBondType = '' } = {}) {
      // 1. 初始化数据结构
      const dateMap = new Map() // { payDate -> { bondType -> { type -> amount } } }
      const bondTypeSet = new Set() // 所有bondType（包括null处理后的值）
      const typeSet = new Set() // 所有type类型

      // 2. 数据聚合
      rawData.forEach((item) => {
        const { payDate, bondType = null, type, amount } = item
        const normalizedBondType = bondType || defaultBondType
        const normalizedType = type || '其他'

        // 记录存在的类型
        bondTypeSet.add(normalizedBondType)
        typeSet.add(normalizedType)

        // 初始化数据结构
        if (!dateMap.has(payDate)) {
          dateMap.set(payDate, new Map())
        }
        const bondTypeMap = dateMap.get(payDate)

        if (!bondTypeMap.has(normalizedBondType)) {
          bondTypeMap.set(normalizedBondType, new Map())
        }
        const typeMap = bondTypeMap.get(normalizedBondType)

        // 累加金额
        const currentAmount = typeMap.get(normalizedType) || 0
        typeMap.set(normalizedType, currentAmount + amount)
      })

      // 3. 生成X轴数据（按日期排序）
      const xAxisData = Array.from(dateMap.keys()).sort((a, b) => {
        return new Date(a) - new Date(b)
      })

      // 4. 动态生成series（按bondType和type组合）
      const series = []
      const typeList = Array.from(typeSet) // 所有type类型（用于legend）

      Array.from(bondTypeSet).forEach((bondType) => {
        typeList.forEach((type) => {
          series.push({
            name: `${bondType ? bondType + '-' : ''}${type}`,
            type: 'bar',
            stack: bondType, // 同bondType堆叠
            data: xAxisData.map((date) => {
              return dateMap.get(date)?.get(bondType)?.get(type) || 0
            }),
            barMinHeight: 50, // 最小高度（像素）
            barWidth: 32,
            itemStyle: {
              color: type === '本金' ? '#5B8FF9' : '#5AD8A6',
              borderColor: '#fff',
              borderWidth: 1
            }
          })
        })
      })

      return { xAxisData, series, legend: typeList }
    },

    processLargeBondData(data, { defaultBondType = '' } = {}) {
      // 1. 使用Map进行高效聚合
      const dateBondMap = new Map()
      const dateSet = new Set()
      const bondTypeSet = new Set()
      const typeSet = new Set(['本金', '利息']) // 强制包含主要类型
      // 颜色辅助函数
      function lightenColor(hex, percent) {
        // 实现颜色变浅逻辑（略）
        return hex
      }

      data.forEach(({ payDate, bondType = null, type, amount }) => {
        const normBondType = bondType || defaultBondType
        const normType = typeSet.has(type) ? type : '其他'

        dateSet.add(payDate)
        bondTypeSet.add(normBondType)
        typeSet.add(normType)

        if (!dateBondMap.has(payDate)) {
          dateBondMap.set(payDate, new Map())
        }
        const bondMap = dateBondMap.get(payDate)

        if (!bondMap.has(normBondType)) {
          bondMap.set(normBondType, new Map())
        }
        const typeMap = bondMap.get(normBondType)

        typeMap.set(normType, (typeMap.get(normType) || 0) + amount)
      })

      // 2. 生成排序后的xAxis数据
      const xAxisData = [...dateSet].sort((a, b) => new Date(a) - new Date(b))
      console.log('dateBondMap:', dateBondMap)

      // 3. 计算每个bondType在每个日期的总值
      const bondTypeTotals = new Map()
      bondTypeSet.forEach((bondType) => {
        const dateTotalMap = new Map()
        xAxisData.forEach((date) => {
          const types = dateBondMap.get(date)?.get(bondType) || new Map()
          dateTotalMap.set(
            date,
            [...types.values()].reduce((sum, v) => sum + v, 0)
          )
        })
        bondTypeTotals.set(bondType, dateTotalMap)
      })

      // 4. 生成series数据
      const bondTypes = [...bondTypeSet]
      const series = []

      // 颜色生成器（每个bondType分配主色）
      const colorGen = (() => {
        const colors = ['#5470C6', '#91CC75', '#FAC858', '#EE6666', '#73C0DE']
        const map = new Map()
        return (bondType, isPrincipal) => {
          if (!map.has(bondType)) map.set(bondType, colors[map.size % colors.length])
          return isPrincipal ? map.get(bondType) : lightenColor(map.get(bondType), 30)
        }
      })()

      // 生成堆叠系列
      bondTypes.forEach((bondType) => {
        ;['本金', '利息'].forEach((type) => {
          series.push({
            name: `${bondType ? bondType + '-' : ''}${type}`,
            type: 'bar',
            stack: bondType, // 按bondType分组堆叠
            data: xAxisData.map((date) => dateBondMap.get(date)?.get(bondType)?.get(type) || 0),
            itemStyle: {
              color: colorGen(bondType, type === '本金'),
              borderColor: '#fff',
              borderWidth: 1
            },
            barWidth: 32,
            barMinHeight: 2
          })
        })

        // 添加透明总值系列（不显示在图例）
        series.push({
          name: `${bondType}-总值`,
          type: 'bar',
          stack: bondType,
          data: xAxisData.map((date) => bondTypeTotals.get(bondType).get(date)),
          itemStyle: { color: 'transparent' },
          label: {
            show: true,
            position: 'bottom',
            offset: [0, -18],
            formatter: '{c}',
            fontSize: 12,
            color: '#333'
          },
          legendHoverLink: false,
          silent: true
        })
      })

      return { xAxisData, series, bondTypes }
    },
    processStackedWithTotal(data, { defaultBondType = '' } = {}) {
      // 1. 数据聚合
      const dateMap = new Map() // { payDate => { type => amount } }
      const dateSet = new Set()
      const typeSet = new Set(['本金', '利息']) // 确保固定顺序

      // 第一次遍历：聚合数据
      data.forEach((item) => {
        const { payDate, type, amount } = item
        dateSet.add(payDate)
        typeSet.add(type)

        if (!dateMap.has(payDate)) {
          dateMap.set(payDate, new Map())
        }
        const typeMap = dateMap.get(payDate)
        typeMap.set(type, (typeMap.get(type) || 0) + amount)
      })

      // 2. 生成排序后的x轴数据
      const xAxisData = [...dateSet].sort((a, b) => new Date(a) - new Date(b))

      // 3. 计算每个日期的总值（用于顶部显示）
      const dateTotals = new Map()
      xAxisData.forEach((date) => {
        const typeMap = dateMap.get(date) || new Map()
        dateTotals.set(
          date,
          [...typeMap.values()].reduce((sum, val) => sum + val, 0)
        )
      })

      // 4. 生成堆叠series
      const series = [...typeSet].map((type) => ({
        name: `${defaultBondType ? defaultBondType + '-' : ''}${type}`,
        type: 'bar',
        stack: 'total',
        data: xAxisData.map((date) => dateMap.get(date)?.get(type) || 0),
        itemStyle: {
          color: type === '本金' ? '#5B8FF9' : '#5AD8A6',
          borderColor: '#fff',
          borderWidth: 1
        },
        label: {
          show: false // 不显示单个堆叠项的值
        },
        barWidth: 32,
        barMinHeight: 2
      }))

      // 5. 添加总值series（透明柱体用于显示顶部标签）
      const totalSeries = {
        name: '总值',
        type: 'bar',
        stack: 'total',
        data: xAxisData.map((date) => dateTotals.get(date)),
        itemStyle: {
          color: 'transparent' // 透明柱体
        },
        label: {
          show: true,
          position: 'bottom',
          offset: [0, -18], // 关键控制参数
          formatter: (params) => {
            const total = params.value
            return total > 0 ? total.toFixed(2) : ''
          },
          fontSize: 12,
          color: '#333'
        },
        barWidth: 32,
        emphasis: { disabled: true },
        legendHoverLink: false,
        silent: true // 禁用所有交互
      }

      return { xAxisData, series: [...series, totalSeries], legend: Array.from(typeSet) }
    }
  }
}
</script>

<style scoped lang="scss">
.future-debt {
  width: 100%;
  min-height: 100%;
  background-color: #fff;

  .future-debt-search-public {
    width: 100%;

    &-form {
      display: flex;
      align-items: center;
      gap: 16px;

      ::v-deep .el-form-item {
        display: flex;
        align-items: center;
        margin: 0 !important;

        .el-form-item__label {
          flex-shrink: 0;
          display: flex !important;
          align-items: center;
          padding-top: 0 !important;
        }

        .el-form-item__content {
          flex: 1 !important;
          flex-shrink: 0;
          display: flex !important;
          align-items: center;
          .el-date-editor {
            flex: 1 !important;
            flex-shrink: 0;
            width: auto !important;
          }
        }
      }

      &-date {
        display: flex;
        align-items: center;
        height: 32px;
        background: #ffffff;
        border-radius: 2px;
        border: 1px solid #cccccc;
        position: relative;
        padding-right: 2px;

        ::v-deep .el-date-editor {
          height: 30px !important;
          .el-input__inner {
            border: none !important;
            height: 30px !important;
          }
        }

        div {
          width: 28px;
          height: 28px;
          background: #ff8e2b;
          border-radius: 2px;
          line-height: 28px;
          font-family: MicrosoftYaHei;
          font-size: var(--el-font-size-base);
          color: #ffffff;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }

      &-yearQuarter {
        width: 32px;
        height: 32px;
        background-color: rgba(255, 142, 43, 0.1);
        border-radius: 2px;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      &-actions {
        display: flex;
        align-items: center;
      }

      &-end {
        display: flex;
        align-items: center;
        margin-left: auto;
        gap: 8px;

        ::v-deep .el-radio-button__inner {
          height: 32px !important;
          padding-top: 0 !important;
          padding-bottom: 0 !important;
          padding-left: 8px !important;
          padding-right: 8px !important;

          display: flex;
          align-items: center;
        }

        ::v-deep .radiogroup {
          height: 32px !important;
          .el-radio-button {
            height: 32px !important;
          }
        }
      }
    }
  }

  &-chart {
    width: 100%;

    &-title {
      display: flex;
      align-items: center;
      gap: 16px;

      &-text {
        height: 22px;
        font-family: MicrosoftYaHeiSemibold;
        font-size: var(--el-font-size-base);
        color: rgba(0, 0, 0, 0.85);
        line-height: 22px;
        text-align: left;
        font-style: normal;
      }

      ::v-deep .el-radio {
        min-width: 0 !important;
      }
    }

    &-search {
      padding: 16px;
    }

    &-tips {
      padding: 12px 16px;
      width: 100%;
      height: 46px;
      background: #fef4ee;
      border-radius: 2px;
      div {
        font-family: MicrosoftYaHei;
        font-size: var(--el-font-size-base);
        color: rgba(0, 0, 0, 0.9);
        line-height: 22px;
        text-align: left;
        font-style: normal;

        span {
          color: #ff8e2b;
        }
      }
    }
  }

  &-table {
    &-title {
      padding: 24px 0;
      font-family: MicrosoftYaHeiSemibold;
      font-size: var(--el-font-size-base);
      color: rgba(0, 0, 0, 0.85);
      line-height: 22px;
      text-align: left;
      font-style: normal;
    }

    &-content {
      width: 100%;
      // height: 500px;
      margin-top: 16px;

      ::v-deep .jr-decorated-table--header {
        display: none !important;
      }
    }
  }
}
</style>
