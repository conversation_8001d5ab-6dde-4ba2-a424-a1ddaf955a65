<!-- 组合归因分析 -> 债券归因 -->
<template>
  <div class="home-poc-item has-fullscreen ascribe-bond-page">
    <div class="home-poc-item--header float-left">债券归因 <fullscreen v-on="{ ...$listeners }" /></div>

    <div style="height: 50%; margin-top: 45px;">
      <echarts :options="options" :styles="{ height: '100%' }" />
    </div>

    <jr-decorated-table
      style="height: calc(50% - 45px);"
      custom-id="dbc79ac895144a63ac6155828a740237"
      v-bind="{
        ...$attrs,
        params,
        noPagination: true,
        menuinfo: {
          pageId: 'PtlAttributeAnalysis001',
          btnList: [{
            btnPosition: 'HEAD',
            btnkey: 'export',
            btnnm: '导出',
            componenturl: 'export',
            effectflag: 'E',
            moduleid: 'PtlAttributeAnalysis001_002_001',
            moduletype: 'btn',
            orde: 4,
            parameter: JSON.stringify({ noSelect: true }),
            permitflag: 'C',
            permittag: '02',
            showflag: 'Y',
            tmFlag: 'E'
          }]
        }
      }"
      @refreshed="queryListEnd"
    />
  </div>
</template>

<script>
import echarts from '@jupiterweb/components/echarts'
import fullscreen from '../../common/fullscreen'
import { ConvertAmount, isNullOrUndefined } from '@jupiterweb/utils/common'

export default {
  components: {
    echarts,
    fullscreen
  },
  props: {
    params: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      options: {},
      tableData: []
    }
  },
  methods: {
    getChartOptions(data = []) {
      const item = { ...data.find(v => v.assetType === '合计') }
      const convert = v => '\n' + (!isNullOrUndefined(item[v]) ? `（${ConvertAmount('rate', item[v])}%）` : '--')

      data = [{
        name: `债券总收益率${convert('bondETotalRate')}`,
        label: { // 节点文本样式
          normal: {
            offset: [0, 0]
          }
        },
        children: [
          {
            name: `收入效应${convert('incomeEff')}`
          },
          {
            name: `国债效应${convert('nationalDebtEff')}`,
            children: [
              {
                name: `基础变动效应${convert('baseChangeEff')}`
              },
              {
                name: `期限结构变动效应${convert('termChangeEff')}`
              }
            ]
          },
          {
            name: `利益效应${convert('interestGapEff')}`
          },
          {
            name: `择券效应${convert('selectBondEff')}`
          }
        ]
      }]
      // console.log('--x--：', x)
      // console.log('--series--：', series)

      return {
        // tooltip: {
        //   trigger: 'item',
        //   triggerOn: 'mousemove'
        // },
        toolbox: {
          show: true,
          right: '12px',
          feature: {
            saveAsImage: { show: true }
          }
        },
        series: [{
          type: 'tree',
          id: 0,
          name: 'tree1',
          data,
          width: '100%',
          top: '10px',
          left: 0,
          bottom: '20%',
          avoidLabelOverlap: true, // 防止标签重叠
          roam: false, // 移动+缩放  'scale' 或 'zoom'：只能够缩放。 'move' 或 'pan'：只能够平移。
          scaleLimit: { // 缩放比例
            min: 0.7, // 最小的缩放值
            max: 4 // 最大的缩放值
          },
          layout: 'orthogonal', // 树图布局，orthogonal水平垂直方向，radial径向布局 是指以根节点为圆心，每一层节点为环，一层层向外
          orient: 'TB', // 树形方向  TB为上下结构  LR为左右结构
          // nodePadding: 100, // 结点间距 （发现没用）
          // layerPadding: 30, // 连接线长度 （发现没用）
          symbol(a, { dataIndex }) {
            return dataIndex > 1 ? 'triangle' : false
          }, // 图形形状  rect方形  roundRect圆角 emptyCircle圆形 circle实心圆
          symbolSize: 12, // 状态大小
          symbolRotate: 180,
          edgeShape: 'polyline', // 线条类型  curve曲线
          initialTreeDepth: 2, // 初始展开的层级
          expandAndCollapse: true, // 子树折叠和展开的交互，默认打开
          lineStyle: { // 结构线条样式
            width: 0.7,
            color: '#1E9FFF',
            type: 'broken'
          },
          label: { // 节点文本样式
            normal: {
              backgroundColor: '#81c5f7',
              position: 'bottom',
              verticalAlign: 'middle', // 文字垂直对齐方式
              align: 'center',
              borderColor: '#1E9FFF',
              color: '#fff',
              borderWidth: 1,
              borderRadius: 5,
              padding: 5,
              height: 30,
              width: 140,
              offset: [0, 20], // 节点文字与圆圈之间的距离
              fontSize: 14,
              // 节点文本阴影
              shadowBlur: 10,
              shadowColor: 'rgba(0,0,0,0.25)',
              shadowOffsetX: 0,
              shadowOffsetY: 2
            }
          },
          leaves: { // 叶子节点文本样式
            label: {
              // backgroundColor: '#fff',
              // color: '#333',
              // position: 'bottom',
              // rotate: 0, // 标签旋转。
              // verticalAlign: 'middle',
              // align: 'center',
              // 文本框内文字超过6个字折行
              /* formatter: function(val) {
              let strs = val.name.split(''); //字符串数组
              let str = ''
              for(let i = 0, s; s = strs[i++];) { //遍历字符串数组
                str += s;
                if(!(i % 6)) str += '\n'; //按需要求余，目前是一个字换一行
              }
              return str
              }, */
              // 或者
              overflow: 'break', // break为文字折行，  truncate为文字超出部分省略号显示
              lineOverflow: 'truncate' // 文字超出高度后 直接截取
            }
          }
        }]
      }
    },
    queryListEnd(ins) {
      this.options = this.getChartOptions(ins.listData)
      // if (this.isFirst) {
      //   this.isFirst = false
      //   const { searchDate, portfolioId = '' } = ins.getCommonParams('searchForm')
      //   this.handlequery({ searchDate, portfolioId })
      // }
    }
  }
}
</script>

<style lang="scss" scoped>
@import "../../common/poc.scss";
</style>

<style lang="scss">
.ascribe-bond-page {
  .home-poc-item--header {
    .home-poc-item--fullscreen {
      border-left: 1px solid #DCDFE6 !important;
    }
  }

  &.home-poc-item.has-fullscreen {
    .jr-decorated-table--header {
      padding-right: 10px !important;
    }
  }
}
</style>
