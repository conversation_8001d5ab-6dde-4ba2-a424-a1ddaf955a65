<template>
  <div class="page-market-rate">
    <Yield />
    <div class="flex-row">
      <Chart style="width: calc(100% - 300px);" />
      <Term />
    </div>
  </div>
</template>

<script>
import Yield from './modules/yield.vue'
import Chart from './modules/chart.vue'
import Term from './modules/term.vue'
export default {
  components: {
    Yield,
    Chart,
    Term
  }
}
</script>

<style lang="scss">
.page-market-rate {
  height: 100%;
  width: 100%;
  background-color: #fff;
  .flex-row {
    display: flex;
    width: 100%;
    height: calc(100% - 170px);
  }
}
</style>
