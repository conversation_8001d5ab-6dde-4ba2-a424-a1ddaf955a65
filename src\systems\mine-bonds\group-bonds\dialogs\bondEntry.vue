<template>
  <div>
    <jr-modal
      :visible="visible"
      :handle-cancel="handleCancel"
      :has-footer="false"
      v-bind="steps === '1' ? {} : { height: 680 }"
    >
      <span class="modalTitle">债券录入</span>
      <template v-slot:body>
        <div
          class="modalBody"
          :style="{
            padding: `${steps === '1' ? '28px 0px 0px 88px' : '28px 0px 0px'}`
          }"
        >
          <div class="modalBody-modalSteps">
            <span
              class="modalBody-modalSteps-circle"
              :class="steps === '1' ? 'modalBody-modalSteps-active' : ''"
              :style="{ borderColor: 'var(--theme--color)' }"
            >
              <span v-show="steps === '1'">1</span>
              <span v-show="steps !== '1'">
                <jr-svg-icon icon-class="check" style="color: var(--theme--color)" />
              </span>
            </span>
            <span class="modalBody-modalSteps-text" style="margin-right: 4px">第1步</span>
            <span
              class="modalBody-modalSteps-line"
              :style="steps === '2' ? 'background-color:var(--theme--color)' : ''"
            />
            <span
              class="modalBody-modalSteps-circle"
              :class="steps === '2' ? 'modalBody-modalSteps-active' : ''"
              style="margin-left: 24px"
            >
              2
            </span>
            <span class="modalBody-modalSteps-text">第2步</span>
          </div>
          <el-form v-show="steps === '1'" ref="form" class="modal-form" :model="form">
            <jr-form-item-create
              ref="formCreate"
              :data="fields"
              :model="form"
              :prop-path="''"
              :disabled="false"
              :column="2"
            />
          </el-form>
          <div v-show="steps === '1'" class="modalBody-modalForm-back" />
          <div v-show="steps === '1'" class="modalBody-modalForm-submitButtons">
            <el-button @click.stop="handleCancel">取消</el-button>
            <el-button type="primary" :loading="submitLoading" @click.stop="submit">下一步</el-button>
          </div>
          <el-form v-show="steps === '2'" style="width: 100%; height: 400px">
            <jr-table-editor
              ref="editor"
              v-model="tableData"
              class="modalBody-tableArea"
              :columns="columns"
              :show-delete="false"
              :hiden-add-row="true"
              style="height: 100%; overflow-y: scroll"
            />
          </el-form>
          <div v-if="steps === '2'" class="modalBody-footer">
            <div class="modalBody-footer-pagination">
              <span>
                <jr-svg-icon icon-class="info-circle" style="font-size: 13px; height: 19px; line-height: 19px" />
                <span>债项付息兑付列表（不区分以行权计与以到期计）；优先以万得公告兑付计划为准</span>
              </span>
              <jr-pagination
                :total="form.cashFlowList.length"
                :page-sizes="[5, 10, 15, 20]"
                :page.sync="pageInfo.pageNo"
                :limit.sync="pageInfo.pageSize"
                :auto-scroll="false"
                :background="true"
                :simple-pager="false"
              />
            </div>
            <div class="modalBody-footer-submitButtons">
              <el-button @click.stop="handleCancel">取消</el-button>
              <el-button v-if="steps === '2'" type="primary" @click.stop="goBack">上一步</el-button>
              <el-button type="primary" :loading="submitLoading" @click.stop="submit">保存</el-button>
            </div>
          </div>
        </div>
      </template>
    </jr-modal>
  </div>
</template>

<script>
const REPAY_TIMES = 'REPAY_TIMES' // 付息频率字典项
const REPAY_TYPE = 'REPAY_TYPE' // 还本方式字典项
const INTEREST_TYPE = 'INTEREST_TYPE' // 计息方式字典项
const INTEREST_PAY_TYPE = 'INTEREST_PAY_TYPE' // 息票方式字典项
const INTEREST_RATE_TYPE = 'INTEREST_RATE_TYPE' // 利率类型字典项
import {
  queryBondRepayCalculator,
  saveBondDetail,
  queryCompanyAuthInfo,
  initBondDetailCashFlow
} from '@/api/bonds/bonds'
import { GetComboboxList } from '@/api/home'
import moment from 'moment'
export default {
  props: {
    bondTypeOptions: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      visible: false,
      fields: [],
      form: {
        isInright: '0',
        bInfoIssueprice: 100,
        cashFlowList: [],
        bInfoPaymenttype: '72471',
        bInfoInteresttype: '72468',
        bInfoCoupon: '72479',
        paymentType: '1'
      },
      columns: [
        {
          title: '支付日期',
          prop: 'paymentDate',
          disabled: true
        },
        {
          title: '票面利率',
          prop: 'couponrate',
          type: 'number',
          precision: 4,
          disabled: true
        },
        {
          title: '余额(亿)',
          prop: 'balance',
          disabled: true,
          type: 'amount'
        },
        {
          title: '兑付类型',
          prop: 'type',
          disabled: true
        },
        {
          title: '应付利息',
          prop: 'interestAmount',
          disabled: true,
          type: 'amount'
        },
        {
          title: '应付本金',
          prop: 'principalAmount',
          type: 'amount',
          disabled: this.principalAmountDisabled,
          change: () => {
            console.log(this)
            this.refreshTableData()
          }
        },
        {
          title: '应付现金流',
          prop: 'cashFlowAmount',
          disabled: true,
          type: 'amount'
        }
      ],
      tableData: [],
      pageInfo: {
        pageNo: 1,
        pageSize: 20
      },
      steps: '1',
      submitLoading: false
    }
  },
  watch: {
    'bondTypeOptions'() {
      this.setFieldsOptions('债券类型', this.bondTypeOptions)
    },
    'form.cashFlowList': {
      deep: true,
      immediate: true,
      handler(val) {
        this.getTableData()
      }
    },
    'pageInfo': {
      deep: true,
      handler() {
        this.getTableData()
      }
    }
  },
  computed: {
    principalAmountDisabled() {
      if (
        this.form.bInfoPaymenttype === '72471' &&
        this.form.bInfoCoupon === '72479' &&
        this.form.bInfoInteresttype === '72468' &&
        this.form.paymentType === '1'
      ) {
        return true
      } else {
        return false
      }
    }
  },
  created() {
    this.getDictOptionsApi()
    this.getCompanyAuthInfo()
    this.init()
  },
  methods: {
    async refreshTableData() {
      const params = JSON.parse(JSON.stringify(this.form))
      const data = await queryBondRepayCalculator(params)
      console.log(data)
      if (Array.isArray(data)) {
        this.form.cashFlowList = data
      }
    },
    // 获取发行时主体评级
    async getCompanyAuthInfo() {
      const res = await queryCompanyAuthInfo()
      if (Array.isArray(res) && res.length > 0) {
        this.form.bInfoCreditrating = res[0].rating || ''
        this.form.bInfoCreditratingagency = res[0].ratingName || ''
      }
    },
    /**
     * 初始化表单数据
     */
    init() {
      const fields = [
        {
          title: '债券代码',
          prop: 'sInfoCode',
          type: 'text',
          required: true,
          trigger: 'change',
          placeholder: '请输入债券代码及后缀'
        },
        {
          title: '债券简称',
          prop: 'sInfoName',
          type: 'text',
          required: true,
          trigger: 'change',
          placeholder: '请输入债券简称'
        },
        {
          title: '债券全称',
          prop: 'bInfoFullname',
          type: 'text',
          placeholder: '请输入债券全称'
        },
        {
          title: '债券类型',
          prop: 'bondTypeName',
          type: 'cascader',
          options: [],
          required: true,
          trigger: 'change',
          props: {
            label: 'label',
            value: 'value'
          },
          uiProps: {
            showAllLevels: false
          }
        },
        {
          title: '规模(亿)',
          prop: 'bIssueAmountact',
          type: 'number',
          required: true,
          trigger: 'change',
          precision: 4,
          placeholder: '请输入规模',
          uiProps: {
            min: 0
          }
        },
        {
          title: '票面利率(%)',
          prop: 'latestCouponrate',
          type: 'number',
          required: true,
          trigger: 'change',
          precision: 4,
          placeholder: '请输入票面利率',
          uiProps: {
            min: 0
          }
        },
        {
          title: '起息日期',
          prop: 'bInfoCarrydate',
          type: 'date',
          required: true,
          trigger: 'change',
          uiProps: {
            valueFormat: 'yyyyMMdd'
          },
          placeholder: '请选择起息日期'
        },
        {
          title: '到期日期',
          prop: 'bInfoMaturitydate',
          type: 'date',
          required: true,
          trigger: 'change',
          uiProps: {
            valueFormat: 'yyyyMMdd'
          },
          placeholder: '请选择到期日期'
        },
        {
          title: '',
          prop: 'isInright',
          type: 'checkbox',
          class: 'debtWithRights',
          falseLabel: '0',
          trueLabel: '1',
          append: () => {
            return <span style='height:18px;line-height:26px;margin-left:10px'>含权债</span>
          }
        },
        {
          title: '发行价格(元)',
          prop: 'bInfoIssueprice',
          type: 'number',
          precision: 2,
          placeholder: '请输入发行价格(元)',
          uiProps: {
            min: 0
          }
        },
        {
          title: '余额(亿)',
          prop: 'bInfoOutstandingbalance',
          type: 'number',
          required: true,
          trigger: 'change',
          placeholder: '请输入余额(亿)',
          uiProps: {
            min: 0
          }
        },
        {
          title: '发行起始日',
          prop: 'bIssueFirstissue',
          type: 'date',
          uiProps: {
            valueFormat: 'yyyyMMdd'
          },
          placeholder: '请选择发行起始日'
        },
        {
          title: '发行截止日',
          prop: 'bIssueLastissue',
          type: 'date',
          uiProps: {
            valueFormat: 'yyyyMMdd'
          },
          placeholder: '请选择发行截止日'
        },
        {
          title: '公告日',
          prop: 'bIssueAnnouncement',
          type: 'date',
          uiProps: {
            valueFormat: 'yyyyMMdd'
          },
          placeholder: '请选择公告日'
        },
        {
          title: '上市日期',
          prop: 'bInfoListdate',
          type: 'date',
          uiProps: {
            valueFormat: 'yyyyMMdd'
          },
          placeholder: '请选择上市日期'
        },
        {
          title: '付息频率',
          prop: 'bInfoInterestfrequency',
          placeholder: '请选择付息频率',
          type: 'select',
          options: [],
          required: true,
          showCode: false,
          trigger: 'change'
        },
        {
          title: '付息日说明',
          prop: 'bInfoCoupondatetxt',
          type: 'text',
          placeholder: '请输入付息日说明'
        },
        {
          prop: 'paymentType',
          placeholder: '请选择还本方式',
          type: 'select',
          showCode: false,
          options: [],
          required: true,
          trigger: 'change',
          paymentTypeappend: () => {
            return (
              <span>
                <span>还本方式</span>
                <el-tooltip
                  content='分期还本测算采用按期数平均'
                  placement='right'
                  effect='light'
                  style='margin-top:5px;margin-left:3px;'
                >
                  <jr-svg-icon iconClass='info-circle' />
                </el-tooltip>
              </span>
            )
          }
        },
        {
          title: '兑付日',
          prop: 'bInfoPaymentdate',
          type: 'date',
          uiProps: {
            valueFormat: 'yyyyMMdd'
          },
          placeholder: '请选择兑付日',
          required: true,
          trigger: 'change'
        },
        {
          title: '计息方式',
          prop: 'bInfoPaymenttype',
          placeholder: '请选择计息方式',
          type: 'select',
          options: [],
          required: true,
          showCode: false,
          trigger: 'change'
        },
        {
          title: '利率类型',
          prop: 'bInfoInteresttype',
          placeholder: '请选择利率类型',
          type: 'select',
          options: [],
          required: true,
          showCode: false,
          trigger: 'change'
        },
        {
          title: '息票品种',
          prop: 'bInfoCoupon',
          placeholder: '请选择息票品种',
          type: 'select',
          options: [],
          required: true,
          showCode: false,
          trigger: 'change',
          disabled: true
        },
        {
          title: '利率说明',
          prop: 'bInfoCoupontxt',
          type: 'text',
          placeholder: '请输入利率说明'
        },
        {
          title: '主承销商',
          prop: 'allLu',
          type: 'text',
          placeholder: '请输入主承销商'
        },
        {
          title: '联系主承销商',
          prop: 'jointLu',
          type: 'text',
          placeholder: '联系主承销商'
        },
        {
          title: '存续期管理机构',
          prop: 'managementAgency',
          type: 'text',
          placeholder: '请输入存续期管理机构'
        },
        {
          title: '薄记管理人',
          prop: 'bookAgency',
          type: 'text',
          placeholder: '请输入薄记管理人'
        },
        {
          title: '发行时债项评级',
          prop: 'bondratingConversion',
          type: 'text',
          placeholder: '请输入发行时债项评级'
        },
        {
          title: '发行时主体评级',
          prop: 'bInfoCreditrating',
          type: 'text',
          placeholder: '请输入发行时主体评级',
          disabled: true
        },
        {
          title: '发行人委托评级机构',
          prop: 'bInfoCreditratingagency',
          type: 'text',
          placeholder: '请输入发行人委托评级机构',
          disabled: true
        },
        {
          title: '担保人',
          prop: 'bInfoGuarantor',
          type: 'textarea',
          placeholder: '请输入担保人',
          class: 'bInfoGuarantor',
          uiProps: {
            showWordLimit: true,
            maxlength: 1000
          }
        },
        {
          prop: 'registerFileNumber',
          type: 'text',
          placeholder: '请输入注册文号',
          registerFileNumberprepend: () => {
            return (
              <div style='display:flex;align-items:center;gap:5px;'>
                <span>注册文号</span>
                <el-tooltip content='如中市协注【2025】MTN001' placement='right' effect='light' style='margin-top:2px'>
                  <jr-svg-icon iconClass='info-circle' />
                </el-tooltip>
              </div>
            )
          }
        }
      ]
      this.fields = fields.map((item) => {
        if (item.class) {
          item.class = item.class + ' bondEntry'
        } else {
          item.class = 'bondEntry'
        }
        return item
      })
    },
    /**
     * 弹框打开
     */
    open() {
      this.visible = true
    },
    /**
     * 弹框关闭
     */
    handleCancel() {
      this.visible = false
      this.goBack()
      Object.assign(this._data.form, this.$options.data().form)
    },
    /**
     * 获取付息频率、还本方式、计息方式下拉项
     */
    async getDictOptionsApi() {
      const data = await GetComboboxList([
        REPAY_TIMES,
        REPAY_TYPE,
        INTEREST_TYPE,
        INTEREST_PAY_TYPE,
        INTEREST_RATE_TYPE
      ])
      const formatObj = {
        付息频率: 'REPAY_TIMES',
        还本方式: 'REPAY_TYPE',
        计息方式: 'INTEREST_TYPE',
        息票品种: 'INTEREST_PAY_TYPE',
        利率类型: 'INTEREST_RATE_TYPE'
      }
      for (const key in formatObj) {
        this.setFieldsOptions(key, data[formatObj[key]])
      }
    },
    /**
     * 设置options数据
     * @param {String} title 匹配title
     * @param {Array} options 数据
     */
    setFieldsOptions(title, options) {
      this.fields = this.fields.map((item) => {
        if (item.title === title) {
          item.options = options
        }
        if (item.prop === 'paymentType' && title === '还本方式') {
          item.options = options
        }
        return item
      })
    },
    /**
     * 获取tableData数据
     */
    getTableData() {
      const startIndex = (this.pageInfo.pageNo - 1) * this.pageInfo.pageSize
      const endIndex = startIndex + this.pageInfo.pageSize
      this.tableData = this.form.cashFlowList.slice(startIndex, endIndex)
    },
    /**
     * 返回上一步
     */
    goBack() {
      this.steps = '1'
      this.form.cashFlowList = []
    },
    formatterDateString(str) {
      return str.replace(/^(\d{4})(\d{2})(\d{2})$/, '$1-$2-$3')
    },
    /**
     * 表单提交，根据当前步数提供对应操作
     */
    async submit() {
      const params = JSON.parse(JSON.stringify(this.form))
      if (this.steps === '1') {
        this.$refs.form.validate(async (valid) => {
          if (valid) {
            let validateResult = true
            if (
              +new Date(this.formatterDateString(params.bInfoMaturitydate)) <
              +new Date(this.formatterDateString(params.bInfoCarrydate))
            ) {
              this.$message.warning('到期日期不能小于起息日期')
              validateResult = false
            }

            if (
              params.bIssueFirstissue &&
              params.bIssueLastissue &&
              +new Date(this.formatterDateString(params.bIssueLastissue)) <
                +new Date(this.formatterDateString(params.bIssueFirstissue))
            ) {
              this.$message.warning('发行截止日不能小于发行起始日')
              validateResult = false
            }

            if (validateResult) {
              this.submitLoading = true
              const data = await initBondDetailCashFlow(params)
              if (data && Object.keys(data).length) {
                this.form.cashFlowList = data
                this.steps = '2'
              }
              this.submitLoading = false

              this.columns[5].disabled = this.principalAmountDisabled
            }
          }
        })
      } else {
        this.submitLoading = true
        params.cashFlowList = params.cashFlowList.map((item) => {
          item.paymentDate = moment(item.paymentDate).format('YYYYMMDD')
          return item
        })
        const res = await saveBondDetail(params)
        console.log(res)
        if (res === 'Y') {
          this.handleCancel()
        }
        this.submitLoading = false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .jr-modal.el-dialog .platform-modal-content {
  padding: 0px;
}
::v-deep .el-form-item__content {
  width: calc(100% - 128px);
}
::v-deep .el-form-item {
  width: calc((100% - 18px) / 2);
}
::v-deep .modalBody-modalForm-formRow .jr-formatted-input.el-input--medium .el-input__inner {
  text-align: left;
}
::v-deep .bondEntry {
  margin: 24px 0px 0px !important;
}
::v-deep .jr-modal.el-dialog {
  width: 58% !important;
}

::v-deep .cell {
  .el-form-item {
    height: 36px !important;

    .el-form-item__content {
      height: 36px !important;

      .el-input{
        height: 36px !important;
        .el-input__inner{
          height: 36px !important;
        }
      }
    }
  }
}
.modalTitle {
  height: 28px;
  font-family: MicrosoftYaHeiSemibold;
  font-size: var(--el-font-size-base);
  color: rgba(0, 0, 0, 0.85);
  line-height: 28px;
}
.modalBody {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding-top: 28px;
  box-sizing: border-box;
  &-modalSteps {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    &-circle {
      width: 24px;
      height: 24px;
      text-align: center;
      line-height: 24px;
      border-radius: 50%;
      border: 1px solid #cccccc;
    }
    &-active {
      background-color: var(--theme--color);
      color: #ffffff;
      border: none;
    }
    &-text {
      height: 24px;
      font-size: var(--el-font-size-base);
      color: rgba(0, 0, 0, 0.9);
      line-height: 24px;
      margin-left: 8px;
    }
    &-line {
      width: 262px;
      height: 1px;
      background-color: #cccccc;
    }
  }
  &-modalForm {
    &-back {
      width: 100%;
      height: 207px;
      background-image: url('../../../../assets/images/bondEntryBac.png');
      background-size: 100% 100%;
      position: fixed;
      bottom: 0px;
      left: 0px;
    }
    &-submitButtons {
      flex-shrink: 0;
      width: calc(100% - 86px);
      height: 56px;
      padding: 12px 0px;
      display: flex;
      justify-content: center;
      margin-top: 78px;
      & > button {
        width: 88px;
        height: 32px;
        border-radius: 2px;
      }
    }
  }
  &-tableArea {
    width: 100%;
    margin-top: 28px;
    flex: 1;
    padding: 0px 24px 0px;
    box-sizing: border-box;
  }
  &-footer {
    flex-shrink: 0;
    width: 100%;
    height: 208px;
    background-image: url('../../../../assets/images/bondEntryBac.png');
    background-size: 100% 100%;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    box-sizing: border-box;
    padding: 0px 24px 12px;
    box-sizing: border-box;
    align-self: flex-end;
    flex-shrink: 0;
    &-pagination {
      display: flex;
      justify-content: space-between;
      & > span {
        display: flex;
        align-items: flex-start;
        gap: 6px;
        span {
          width: 322px;
          height: 38px;
          font-size: var(--el-font-size-base);
          color: rgba(0, 0, 0, 0.6);
          line-height: 19px;
        }
      }
    }
    &-submitButtons {
      display: flex;
      align-self: center;
      margin-top: 28px;
      & > button {
        width: 88px;
        height: 32px;
        border-radius: 2px;
      }
    }
  }
}
.modal-form {
  height: calc(100% - 182px) !important;
  overflow-y: scroll;
  margin-top: 24px;
  padding-right: 86px;
}
</style>
<style lang="scss">
.bInfoGuarantor {
  width: 100% !important;
  flex-shrink: 0;
  .el-form-item__label {
    width: 14% !important;
    margin-left: 1% !important;
  }
  .el-form-item__content {
    width: 84% !important;
  }
}
.debtWithRights {
  .el-form-item__content {
    margin-left: 30% !important;
  }
}
</style>
