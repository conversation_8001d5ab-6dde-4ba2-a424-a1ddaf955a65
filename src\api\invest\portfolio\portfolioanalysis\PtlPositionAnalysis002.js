import { GetListInfo, ExportFn } from '@jupiterweb/utils/api'

export const getFindPage = params => GetListInfo('/invest/portfolio/ptlpositionanalysis/PtlPositionAnalysis002/findPage', params)
// 导出接口
export const indexExport = params => ExportFn('/invest/portfolio/ptlpositionanalysis/PtlPositionAnalysis002/export', params)

// 可选指标-树结构
export const getAllTreeNode = params => GetListInfo('/invest/portfolio/ptlpositionanalysis/PtlPositionAnalysis003/getAllTreeNode', params)

// 保存列配置
export const savaTableCols = params => GetListInfo('/invest/portfolio/ptlpositionanalysis/PtlPositionAnalysis003/savaTableCols', params)

// 生成新的列
export const getTableCols = params => GetListInfo('/invest/portfolio/ptlpositionanalysis/PtlPositionAnalysis002/getTableCols', params)
