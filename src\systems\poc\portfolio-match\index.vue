<!--
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023-10-14 09:50:04
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2025-01-09 20:49:45
 * @Description: 组合自动化匹配
-->
<template>
  <div class="poc-portfolio-match">
    <el-button type="primary" class="top-button">匹配步骤</el-button>
    <el-tabs type="card">
      <el-tab-pane label="组合自动化匹配">
        <jr-layout-vertical :height="bodyHeight * 0.5 + 20">
          <div slot="top">
            <jr-layout-horizontal :width="width * 0.4">
              <template slot="left">
                <span class="title">待匹配产品</span>
                <jr-layout-vertical :height="300">
                  <flat-index slot="top" :query="() => {}" :reset="() => {}" :pagination="configTable.pagination" :has-refresh="false">
                    <template v-slot:form>
                      <el-form ref="elForm" :model="configTable.formData">
                        <jr-form-item-create :column="3" :data="configTable.formConfig" :model="configTable.formData" />
                      </el-form>
                    </template>

                    <template slot="table-list">
                      <jr-table
                        :height="180"
                        muti-select
                        :columns="configTable.columns"
                        :data-source="configTable.data"
                        :loading="configTable.loading"
                        :pagination="configTable.pagination"
                        :on-change="()=> {}"
                        border
                      >
                        <template v-slot:index>
                          <el-table-column
                            type="index"
                            width="50px"
                            align="center"
                            :label="InitialMessage('common.columns.index')"
                          >
                            <template slot-scope="scope">
                              <span>{{ (configTable.pagination.pageNo - 1) * configTable.pagination.pageSize + scope.$index +1 }}</span>
                            </template>
                          </el-table-column>
                        </template>
                      </jr-table>
                    </template>
                  </flat-index>
                  <el-tabs slot="bottom" type="card">
                    <el-tab-pane label="收益分析">
                      <el-form style="padding-right: 10px;">
                        <jr-form-item-create :data="profitFormConfig" :model="{}" :column="2" disabled />
                      </el-form>
                    </el-tab-pane>
                    <el-tab-pane label="投向分析" />
                    <el-tab-pane label="投向分析(单资产)" />
                  </el-tabs>
                </jr-layout-vertical>
              </template>
              <template slot="right">
                <span class="title" :style="'left: ' + (width * 0.4 + 20) + 'px'">可选资产信息</span>
                <flat-index slot="top" :query="() => {}" :reset="() => {}" :pagination="configTableRight.pagination" :has-refresh="false">
                  <template v-slot:form>
                    <el-form ref="elForm" :model="configTableRight.formData">
                      <jr-form-item-create :column="2" :data="configTableRight.formConfig" :model="configTableRight.formData" />
                    </el-form>
                  </template>
                  <template slot="query-button">
                    <el-button type="primary">资产筛选</el-button>
                    <div style="white-space: nowrap;direction: rtl;margin-top: 4px;">
                      <el-button type="primary" style="margin-left: 10px;">删除模拟资产</el-button>
                      <el-button type="primary">匹配资产</el-button>
                      <el-button type="primary">模拟资产录入</el-button>
                      <el-button type="primary">查询</el-button>
                    </div>
                  </template>
                  <template slot="table-list">
                    <jr-table
                      :height="350"
                      muti-select
                      :columns="configTableRight.columns"
                      :data-source="configTableRight.data"
                      :loading="configTableRight.loading"
                      :pagination="configTableRight.pagination"
                      :on-change="()=> {}"
                      border
                    >
                      <template v-slot:index>
                        <el-table-column
                          type="index"
                          width="50px"
                          align="center"
                          :label="InitialMessage('common.columns.index')"
                        >
                          <template slot-scope="scope">
                            <span>{{ (configTableRight.pagination.pageNo - 1) * configTableRight.pagination.pageSize + scope.$index +1 }}</span>
                          </template>
                        </el-table-column>
                      </template>
                    </jr-table>
                  </template>
                </flat-index>
              </template>
            </jr-layout-horizontal>
          </div>
          <template slot="bottom">
            <div class="op-button-box">
              <el-button type="primary" plain>风险试算</el-button>
              <el-button type="primary" plain>匹配方案提交</el-button>
              <el-button type="primary" plain>收益测算</el-button>
              <el-button type="primary" plain>查询匹配结果</el-button>
              <el-button type="primary" plain>Metlab自动匹配</el-button>
              <el-button type="primary" plain>Matlab参数维护</el-button>
              <el-button type="primary" plain class="float-right">卖出</el-button>
            </div>
            <jr-table
              :columns="bottomColumns"
              :data-source="configTableRight.data"
              :loading="configTableRight.loading"
              :pagination="configTableRight.pagination"
              :on-change="()=> {}"
              border
            >
              <template v-slot:index>
                <el-table-column
                  type="index"
                  width="50px"
                  align="center"
                  :label="InitialMessage('common.columns.index')"
                >
                  <template slot-scope="scope">
                    <span>{{ (configTableRight.pagination.pageNo - 1) * configTableRight.pagination.pageSize + scope.$index +1 }}</span>
                  </template>
                </el-table-column>
              </template>
            </jr-table>
          </template>
        </jr-layout-vertical>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
export default {
  data() {
    return {
      width: document.body.clientWidth,
      bodyHeight: document.body.clientHeight,
      // 表格数据左侧
      configTable: {
        loading: false,
        columns: [
          {
            title: '产品代码',
            prop: 'code'
          },
          {
            title: '产品名称',
            prop: 'name'
          },
          {
            title: '产品总份额',
            prop: 'amount'
          },
          {
            title: '可用资金',
            prop: 'amount2'
          },
          {
            title: '到期日',
            prop: 'tenThousand'
          },
          {
            title: '预期收益率',
            prop: 'yield'
          },
          {
            title: '产品类型',
            prop: 'prodType'
          }
        ],
        formData: {
          date: JSON.parse(sessionStorage.getItem('platDate')),
          isChange: '01'
        },
        formConfig: [
          {
            title: '查询日',
            prop: 'date',
            type: 'date',
            disabled: true
          }, {
            title: '产品类型',
            prop: 'prodType',
            type: 'select',
            options: []
          }, {
            title: '是否变动',
            prop: 'isChange',
            type: 'select',
            options: [{
              text: '是',
              value: '01'
            }, {
              text: '否',
              value: '02'
            }]
          }, {
            title: '产品代码',
            prop: 'code',
            type: 'text'
          }, {
            title: '可用资金',
            prop: 'amount',
            class: 'w-350',
            type: 'amountInputRange'
          }
        ],
        data: [],
        pagination: {
          pageNo: 1,
          pageSize: window.screen.height > 1000 ? 25 : 15,
          pageSizeOptions: [15, 25, 50, 100],
          total: 0,
          showSizeChanger: true
        }
      },
      configTableRight: {
        loading: false,
        columns: [
          {
            title: '匹配面额(万)',
            prop: 'code'
          },
          {
            title: '匹配金额(万)',
            prop: 'name'
          },
          {
            title: '收益率(%)',
            prop: 'amount'
          },
          {
            title: '税后收益率(%)',
            prop: 'amount2'
          },
          {
            title: '到期日',
            prop: 'tenThousand'
          },
          {
            title: '资产代码',
            prop: 'yield'
          },
          {
            title: '资产名称',
            prop: 'prodType'
          },
          {
            title: '剩余面额(万)',
            prop: 'code2'
          },
          {
            title: '可用金额(万)',
            prop: 'name2'
          }
        ],
        formData: {
          rangeDate: [JSON.parse(sessionStorage.getItem('platDate')), JSON.parse(sessionStorage.getItem('platDate'))],
          isChange: '01'
        },
        formConfig: [
          {
            title: '资产代码',
            prop: 'prodType',
            type: 'select',
            options: []
          },
          {
            title: '资产来源',
            prop: 'prodType',
            type: 'select',
            options: []
          },
          {
            title: '到期日',
            prop: 'rangeDate',
            type: 'rangeDate',
            disabled: true
          }
        ],
        data: [],
        pagination: {
          pageNo: 1,
          pageSize: window.screen.height > 1000 ? 25 : 15,
          pageSizeOptions: [15, 25, 50, 100],
          total: 0,
          showSizeChanger: true
        }
      },
      profitFormConfig: [
        {
          title: '投资收益率',
          type: 'rate',
          prop: 'yield'
        },
        {
          title: '净投资收益率',
          type: 'rate2',
          prop: 'netyield'
        },
        {
          title: '总投资收益',
          type: 'text',
          prop: 'profit'
        },
        {
          title: '净总投资收益',
          type: 'text',
          prop: 'netyield'
        },
        {
          title: '银行端收入',
          type: 'text',
          prop: 'income'
        },
        {
          title: '净银行端收入',
          type: 'text',
          prop: 'income2'
        },
        {
          title: '收益缺口',
          type: 'text',
          prop: 'profit3'
        },
        {
          title: '推荐再投资收益率',
          type: 'rate',
          prop: 'yield33'
        }
      ],
      bottomColumns: [
        {
          title: '资产来源',
          prop: 'field1'
        }, {
          title: '资产一级类型',
          prop: 'field2'
        }, {
          title: '资产二级类型',
          prop: 'field3'
        }, {
          title: '资产三级类型',
          prop: 'field4'
        }, {
          title: '业务类型',
          prop: 'field5'
        }, {
          title: '底层资产类型',
          prop: 'field6'
        }, {
          title: '资产代码',
          prop: 'field7'
        }, {
          title: '资产名称',
          prop: 'field8'
        }, {
          title: '投资类型',
          prop: 'field9'
        }, {
          title: '交易账户',
          prop: 'field10'
        }, {
          title: '投资币种',
          prop: 'field11'
        }, {
          title: '真是持仓面额(万)',
          prop: 'field12'
        }, {
          title: '久期差',
          prop: 'field13'
        }, {
          title: '收益率(%)',
          prop: 'field14'
        }, {
          title: '税后收益率(%)',
          prop: 'field15'
        }, {
          title: '模拟买入面额(万)',
          prop: 'field16'
        }
      ]
    }
  }
}
</script>

<style lang="scss">
.poc-portfolio-match {
  height: 100%;
  .w-350 {
    width: 350px !important;
  }
  .jr-decorated-table--top-search .jr-decorated-table--header-top {
    margin-bottom: 0;
  }
  &>.el-tabs {
    height: 100%;
    &>.el-tabs__content {
      height: calc(100% - 28px);
      transform: translate(0);
      &>.el-tab-pane {
        padding-top: 10px;
        &>.vertical-layout {
          border: 1px solid #e8e8e8;
        }
      }
    }
  }
  // .title {
  //   position: fixed;
  //   background: #fff;
  //   padding: 0 8px;
  //   z-index: 1;
  //   top: 0px;
  //   left: 10px;
  //   font-size: 14px;
  // }
  .jr-decorated-table__flat-search-footer {
    padding-left: 10px;
    text-align: right;
    .el-button--default {
      display: none;
    }
  }
  .jr-decorated-table--header-left,
  .jr-decorated-table--header-right {
    display: none;
  }
  .top-button {
    position: absolute;
    top: 6px;
    left: 156px;
    z-index: 1;
  }
  .op-button-box {
    padding-left: 100px;
    border-bottom: 1px solid #e8e8e8;
    padding-bottom: 10px;
  }
  .el-tabs__header {
    margin-bottom: 0;
  }
  .el-tabs__header,
  .el-tabs__content {
    background: #fff;
  }
}
@media screen and (max-width: 1600px) {
  .poc-portfolio-match .jr-form-item.w-350 {
    width: 250px !important;
  }
}
</style>
