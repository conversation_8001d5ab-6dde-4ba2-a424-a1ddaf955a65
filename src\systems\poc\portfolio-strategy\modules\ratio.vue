<!--
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023-10-25 10:54:32
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2023-10-27 18:34:59
 * @Description: 策略配置比例
-->
<template>
  <article>
    <div class="header">
      <span class="title">策略配置比例</span>
      <el-radio-group v-model="combo" size="mini">
        <el-radio-button v-for="com in comboxList" :key="com" :label="com" />
      </el-radio-group>
    </div>
    <section class="body">
      <echarts :options="chartOptions" />
    </section>
  </article>
</template>

<script>
import echarts from '@jupiterweb/components/echarts'
export default {
  components: { echarts },
  data() {
    return {
      combo: '图',
      comboxList: ['图', '表'],
      chartOptions: {
        title: [{
          text: '{name|' + 199.99 + '}\n{val|亿元}',
          top: 'center',
          left: 'center',
          textStyle: {
            rich: {
              name: {
                fontSize: 28,
                fontWeight: 'normal',
                color: '#333333',
                padding: [10, 0]
              },
              val: {
                fontSize: 14,
                fontWeight: 'bold',
                color: '#666666'
              }
            }
          }
        }],
        grid: {
          top: 20,
          bottom: 0
        },
        series: [
          {
            name: '资产配置',
            type: 'pie',
            radius: ['45%', '60%'],
            center: ['50%', '50%'],
            avoidLabelOverlap: true,
            clockwise: false,
            data: [
              {
                value: 8.52,
                name: '直投股票'
              },
              {
                value: 1.24,
                name: '直投债券'
              },
              {
                value: 4.46,
                name: '现金管理产品'
              },
              {
                value: 70.73,
                name: '固收类产品(非现金管理)'
              },
              {
                value: 74.10,
                name: '混合类产品'
              },
              {
                value: 1.83,
                name: '权益类产品'
              },
              {
                value: 3.33,
                name: '商品及衍生品类产品'
              }
            ],
            label: {
              normal: {
                formatter: params => {
                  return (
                    '{icon|●}{name|' + params.name + '}{value|' + params.percent + '%      ' +
                        params.value + '}'
                  )
                },
                padding: [0, -180, 15, -100],
                rich: {
                  icon: {
                    fontSize: 12
                  },
                  name: {
                    fontSize: 12,
                    padding: [0, 10, 0, 4],
                    color: '#666666'
                  },
                  value: {
                    fontSize: 12,
                    fontWeight: 'bold',
                    color: '#333333'
                  }
                }
              }
            },
            itemStyle: {
              normal: {
                label: {
                  show: true,
                  position: 'outside'
                },
                labelLine: {
                  length: 20,
                  length2: 200,
                  show: true,
                  color: '#00ffff'
                }
              }
            }
          }
        ]
      }
    }
  }
}
</script>

<style lang="scss" scoped></style>
