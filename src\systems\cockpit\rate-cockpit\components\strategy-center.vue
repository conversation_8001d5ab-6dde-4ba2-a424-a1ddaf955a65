<template>
  <div class="strategyCenter">
    <cockpitHeader title="策略中心" :style="{ width: px2vw(374), height: px2vh(42) }" type="light" />
    <cockpitTabs v-model="activeTab" :tabs="tabs" :style="{ marginTop: px2vh(15) }" @handleMore="redirectToMenu" />
    <div class="strategyCenter-select">
      <cockpitSelect
        v-model="selectParams.compProperty"
        :style="{ width: px2vw(142), flexShrink: 0 }"
        labelKey="text"
        valueKey="text"
        :options="compPropertyOptions"
        :default-value="compPropertyDefaultValue"
        :multiple="true"
      />
      <cockpitSelect
        v-model="selectParams.creditratingConversion"
        :style="{ width: px2vw(110), flexShrink: 0 }"
        labelKey="text"
        :options="creditratingConversionOptions"
        :default-value="creditratingConversionDefaultValue"
        :multiple="true"
      />
      <SelectAutoSet
        :style="{ width: px2vw(174), flexShrink: 0 }"
        :options="proviceOptions"
        labelKey="cname"
        valueKey="id"
        mode="all"
        @emitConfirmData="getProviceData"
      />
    </div>
    <div class="strategyCenter-cardArea">
      <el-carousel trigger="click" style="width: 100%; height: 100%">
        <el-carousel-item v-for="(carousel, cIndex) in carouselData" :key="cIndex">
          <div class="strategyCenter-cardArea-cardContent">
            <p v-for="(bond, index) in carousel" :key="index">
              <span :style="{width:bondTypeWidth}">
                <span>{{ bond.bondTypeName2 }}</span>
                <img src="@/assets/cockpit/cockpit_arrow.png" alt="" :style="{width:px2vw(16),height:px2vh(17)}">
              </span>
              <span :class="`color${cIndex % 2 === 0 ? 'A' : 'B'}${index}`" :style="getCarouselStyle(bond)">{{ bond.rateavg }}%</span>
              <span>{{ bond.numb }}</span>
            </p>
          </div>
        </el-carousel-item>
      </el-carousel>
    </div>
  </div>
</template>

<script>
import cockpitHeader from '../../components/cockpit-header.vue'
import cockpitTabs from '../../components/cockpit-tabs.vue'
import cockpitSelect from '../../components/cockpit-select.vue'
import { cockpitGetStrategyList } from '@/api/cockpit/cockpit'
import SelectAutoSet from '@/components/selectAutoSet'
import { GetComboboxList } from "@/api/home"
import { regionLink } from "@/api/public/public"
import { px2vw, px2vh } from '../../utils/portcss'
const COMPPROPERTY = "COMPPROPERTY" // 主体性质字典项
const ISSUERCREDITRATING = "ISSUERCREDITRATING"
export default {
  name: 'StrategyCenter',
  components: {
    cockpitHeader,
    cockpitTabs,
    cockpitSelect,
    SelectAutoSet
  },
  data() {
    return {
      tabs: [
        {
          tabName: '近期市场发行',
          type: '01',
          menuId:'1352314542536605696'
        },
        {
          tabName: '对标企业近期发行',
          type: '02',
          menuId:'1352315027146489856'
        }
      ],
      activeTab: {},
      carouselData: [],
      bondTypeWidth: "0",
      selectParams:{
        compProperty:[],
        creditratingConversion:[],
        provinceCode:[],
        cityCode:[],
        districtCode:[]
      },
      compPropertyOptions:[],
      creditratingConversionOptions:[],
      proviceOptions: [],
      colorArr:[],
      creditratingConversionDefaultValue:[],
      compPropertyDefaultValue:[],
    }
  },
  props: {
    companyList: {
      type: Array,
      default: () => []
    }
  },
  watch: {
    activeTab: {
      deep: true,
      handler(newVal,oldVal) {
        if(Object.keys(oldVal).length){
          this.getCockpitStrategyDataApi()
        }
      }
    },
    selectParams: {
      deep: true,
      handler() {
        this.getCockpitStrategyDataApi()
      }
    },
    companyList: {
      deep: true,
      handler(newVal,oldVal) {
        this.selectParams.creditratingConversion = newVal.map(item=>item.rating)
        this.creditratingConversionDefaultValue = newVal.map(item=>item.rating)
        this.selectParams.compProperty = newVal.map(item=>item.compProperty)
        this.compPropertyDefaultValue = newVal.map(item=>item.compProperty)
        this.getCockpitStrategyDataApi()
      }
    }
  },
  created(){
    this.getDictList()
    this.getRegionList()
  },
  methods: {
    px2vw,
    px2vh,
    /**
     * 获取策略中心数据
     */
    async getCockpitStrategyDataApi() {
      const data = await cockpitGetStrategyList(this.activeTab.type, {...this.selectParams})
      this.bondTypeWidth = this.getMaxBondTypeNameWidth(data)
      const baseRateavg = data[0]?.rateavg || 0
      let tempArr = []
      this.carouselData = data.reduce((pre, current, index) => {
        this.$set(current,'width',current.rateavg / baseRateavg)
        tempArr.push(current)
        if (tempArr.length >= 5) {
          pre.push(tempArr)
          tempArr = []
        }
        if (index === data.length - 1 && tempArr.length > 0) {
          pre.push(tempArr)
          tempArr = []
        }
        return pre
      }, [])
    },
    /**
     * 获取字典相关数据
     */
    async getDictList() {
      const data = await GetComboboxList([COMPPROPERTY, ISSUERCREDITRATING])
      this.compPropertyOptions = data[COMPPROPERTY]
      this.creditratingConversionOptions = data[ISSUERCREDITRATING]
    },
    /**
     * 获取省市区下拉项
     */
    async getRegionList() {
      const data = await regionLink()
      this.proviceOptions = this.formatAreaList(data)
    },
    /**
     * 辅助函数：格式化省市区列表
     */
    formatAreaList(areaList) {
      let arr = []
      arr = JSON.parse(JSON.stringify(areaList))

      const idMapping = arr.reduce((acc, el, i) => {
        acc[el.id] = i
        return acc
      }, {})

      const root = []
      arr.forEach((el) => {
        // 判断根节点
        if (!el.pid) {
          root.push(el)
          return
        }
        // 用映射表找到父元素
        const parentEl = arr[idMapping[el.pid]]
        if (parentEl) {
          // 把当前元素添加到父元素的`children`数组中
          parentEl.children = [...(parentEl.children || []), el]
        }
      })
      return root
    },
    /**
     * 获取最长的bondTypeName，确定样式宽度
     */
    getMaxBondTypeNameWidth(data) {
      let maxLength = data?.reduce((pre,current)=>{
        if(current.bondTypeName2.length > pre){
          return current.bondTypeName2.length
        }else{
          return pre
        }
      },0) || 0
      return this.px2vw((maxLength + 2) * 14)
    },
    /**
     * 获取不同区域的展示效果
     */
    getCarouselStyle(bond) {
      if(bond.width < 0.24){
        bond.width = 0.24
      }
      return {
        width: this.px2vw(bond.width * 240)
      }
    },
    /**
     * 获取选中的地区数据
     */
    getProviceData(data){
      this.selectParams.provinceCode = data.map(item=>item[0])
      this.selectParams.cityCode = data.map(item=>item[1])
      this.selectParams.districtCode = data.map(item=>item[2])
    },
    /**
     * 路径跳转
     */
    redirectToMenu(){
      this.$store.dispatch('tagsView/updateCurrentTab', {
        path: '/' + this.activeTab.menuId,
        // 跳转需要刷新的话 refresh传true
        meta: {
          refresh: true,
          params: {}
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~assets/styles/portcss.scss';
.strategyCenter {
  width: 100%;
  height: vh(436);
  background: rgba(255,255,255,0.5);
  box-shadow: 0px 4px 20px -2px rgba(0,0,0,0.06);
  border: vh(2) solid transparent;
  border-radius: vh(12);
  background-clip: padding-box, border-box;
  background-origin: padding-box, border-box;
  background-image:linear-gradient(to right, rgba(255,255,255,0.5), rgba(255, 255, 255, 0.5)), radial-gradient(rgba(255, 255, 255, 1), rgba(255, 255, 255, 0.5));
  padding: 0px vw(16);
  display: flex;
  flex-direction: column;
  &-select {
    display: flex;
    gap: vw(8);
    margin-top: vh(15);
  }
  &-cardArea {
    width: 100%;
    height: vh(285);
    margin-top: vh(24);
    &-cardContent {
      width: 100%;
      padding-left: vw(22);
      padding-right: vw(22);
      & > p {
        width: 100%;
        height: vh(40);
        margin-bottom: vh(10);
        display: flex;
        align-items: center;
        justify-content: space-between;
        & > span:nth-of-type(1) {
          height: vh(19);
          font-size: vh(14);
          color: rgba(0,0,0,0.6);
          line-height: vh(19);
          display: flex;
          align-items: center;
        }
        & > span:nth-of-type(2) {
          height: vh(40);
          text-align: center;
          line-height: vh(40);
          font-size: vh(14);
          color: #e6f6ff;
        }
        & > span:nth-of-type(3) {
          height: vh(22);
          font-size: vh(14);
          color: rgba(0,0,0,0.9);
          line-height: vh(22);
        }
      }
    }
  }
  .colorA0 {
    background: linear-gradient( 180deg, #DDC8FF 0%, #BA9AF0 100%);
  }
  .colorA1 {
    background: linear-gradient( 180deg, #FFDC9F 0%, #FFAF69 100%);
  }
  .colorA2 {
    background: linear-gradient( 180deg, #AEC8FF 0%, #76A3FF 100%);
  }
  .colorA3 {
    background: linear-gradient( 180deg, #B3ECAA 0%, #7FDB71 100%);
  }
  .colorA4 {
    background: linear-gradient( 180deg, #FFC3B6 0%, #F2947F 100%);
  }
  .colorB0 {
    background: linear-gradient( 180deg, #EFE6FF 0%, #DDC8FF 100%);
  }
  .colorB1 {
    background: linear-gradient( 180deg, #FFE8D3 0%, #FFC765 100%);
  }
  .colorB2 {
    background: linear-gradient( 180deg, #DAE6FF 0%, #AEC8FF 100%);
  }
  .colorB3 {
    background: linear-gradient( 180deg, #C7FFBF 0%, #8AE17D 100%);
  }
  .colorB4 {
    background: linear-gradient( 180deg, #FFDFD8 0%, #FFC3B6 100%);
  }
}

/*指示器*/
::v-deep .el-carousel__button{
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #CCC;
}
::v-deep .is-active .el-carousel__button{
  background-color: var(--theme--color);
}
::v-deep .el-carousel__arrow{
  top: 35%;
}
</style>
