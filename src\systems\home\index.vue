<template>
  <div class="dashboard">
    <CombinationModule v-if="isGroup" :group="group" :current-active="currentActive" />
    <ActiveModule
      v-else
      is-desktop
      :module-name="group.title"
      :group-id="group.groupid"
      v-bind="getTabParams(group)"
    />
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import ActiveModule from '@jupiterweb/components/active-module'
import CombinationModule from '@jupiterweb/components/combination-module'
import tabParams from '@/mixins/tab-params'
// 我的工作台
export default {
  name: 'Dashboard',
  components: {
    ActiveModule,
    CombinationModule
  },
  mixins: [tabParams],
  data() {
    return {
      currentActive: 'remind',
      group: {}
    }
  },
  computed: {
    ...mapGetters(['menuRoutes'])
  },
  watch: {
    '$store.state.tagsView.currentTab'(v) {
      v === '/tams/index' && this.goTargetTab()
    }
  },
  created() {
    const defaultGroup = {
      moduleid: 'main/init',
      modulename: this.InitialMessage('common.system.dashboard'),
      moduletype: 'combination',
      children: []
    }
    // 投资组合POC工作台，和西安投管POC同库因此写死ID
    this.group = { ...(this.menuRoutes.find(m => m.moduleid === 'TZGZT001' && m.showflag === this.$dict.YN_Y) || defaultGroup) }
    this.isGroup = this.group.moduletype === this.$dict.MODULETYPE_combination
  },
  mounted() {
    this.goTargetTab()
    window.addEventListener('message', (e) => {
      if (e.data.type === 'tabAdd') {
        window.tab.tabAdd(e.data.info)
      }
    })
  },
  methods: {
    goTargetTab() {
      const { visitedViews, currentTab } = this.$store.state.tagsView
      const item = visitedViews.find(item => item.path === currentTab)
      if (item.meta.params && item.meta.params.active) {
        this.currentActive = item.meta.params.active
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.dashboard {
    min-width: 1000px;
    height: 100%;
    margin-left: -4px;
    padding-right: 0 !important;
}
</style>
