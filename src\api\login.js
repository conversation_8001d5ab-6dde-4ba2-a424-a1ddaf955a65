import { UpdateFn, GetInfoFn } from '@jupiterweb/utils/api'

// 生成手机验证码
export const verifyCode = (params, cb) => {
  return UpdateFn('/web/login/verifyCode', params, cb)
}

// 密码重置
export const forgetPwd = (params, cb) => {
  return UpdateFn('/web/login/forgetPwd', params, cb)
}

// 获取组织机构代码
export const findOrgCode = (params) => GetInfoFn('/web/login/registerUser/findOrgCode', params)
// 用户注册
export const registerUser = (params, cb) => {
  return UpdateFn('/web/login/registerUser', params, cb)
}
