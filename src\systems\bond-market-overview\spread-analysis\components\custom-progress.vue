<template>
  <div class="custom-progress">
    <div
      class="custom-progress-right"
      :style="{
        width: progressPercentage + '%'
      }"
      :class="progress > 0 ? 'has-progress' : ''"
    >
      {{ progress || '--' }}
    </div>
  </div>
</template>
<script>
export default {
  name: 'CustomProgress',
  props: {
    progress: {
      type: Number,
      default: 0
    },
    max: {
      type: Number,
      default: 50
    }
  },
  data() {
    return {}
  },
  computed: {
    // 计算进度百分比
    progressPercentage() {
      if (this.max <= 0) return 0
      if (this.progress === 0) return 0
      const percentage = (this.progress / this.max) * 100
      return Math.min(100, Math.max(0, percentage.toFixed(1)))
    },
    // 当前值
    currentValue() {
      return this.progress
    },
    // 最大值
    maxValue() {
      return this.max
    }
  },
  methods: {}
}
</script>

<style lang="scss" scoped>
.custom-progress {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  height: 26px;
  // background-color: #f5f5f5;

  &-right {
    font-size: var(--el-font-size-base);
    line-height: 26px;
    padding-left: 5px;

    font-weight: 500;
  }

  .has-progress {
    background: linear-gradient(to bottom, transparent, #ecc479, transparent);
    box-shadow: 2px 2px 2px rgba(0, 0, 0, 0.3);
  }
}
</style>
