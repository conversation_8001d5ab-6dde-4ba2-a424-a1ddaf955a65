<template>
  <div class="company-announce">
    <cockpit-header :style="{ width: px2vw(362), height: px2vh(40) }" title="公司公告" />
    <div class="company-announce-content">
      <div v-for="(card, index) in cardData" :key="index" class="company-announce-content-single">
        <p>{{ card.nInfoTitle }}</p>
        <span class="company-announce-content-single-date">{{ card.annDt }}</span>
      </div>
    </div>
    <div class="company-announce-more" @click.stop="redirectToMenu">
      <span>更多</span>
      <img src="@/assets/cockpit/more_deep_icon.png" alt="" />
    </div>
  </div>
</template>

<script>
import cockpitHeader from '../../components/cockpit-header.vue'
import { GetListData } from '@jupiterweb/api/common/active-module-svr'
import { px2vw, px2vh } from '../../utils/portcss'
import moment from 'moment'
export default {
  name: 'CompanyAnnounce',
  components: {
    cockpitHeader
  },
  data() {
    return {
      cardData: []
    }
  },
  created() {
    this.getInfomationDataApi()
  },
  methods: {
    px2vw,
    px2vh,
    /**
     * 获取资讯中心数据
     */
    async getInfomationDataApi() {
      const data = await GetListData({
        // CCID是自定义列id ownedModuleid是菜单id
        page: {
          pageNo: 1,
          pageSize: 10
        },
        params: {
          ccid: '48cc1627b224474f8362aedf5a1ca54a',
          ownedModuleid: '1369319537827659776'
        }
      })
      let list = data?.pageInfo?.list || []
      list = list.map((item) => {
        item.annDt = moment(item.annDt).format('YYYY-MM-DD')
        return item
      })
      list.reverse()
      this.cardData = this.deepFreeze(list)
    },
    /**
     * 深度冻结对象
     */
    deepFreeze(obj) {
      Object.keys(obj).forEach((key) => {
        if (typeof obj[key] === 'object' && obj[key] !== null) {
          // 递归
          this.deepFreeze(obj[key])
        }
      })
      return Object.freeze(obj)
    },
    /**
     * 路径跳转
     */
    redirectToMenu(){
      this.$store.dispatch('tagsView/updateCurrentTab', {
        path: '/1315724492663078912',
        // 跳转需要刷新的话 refresh传true
        meta: {
          refresh: true,
          params: {}
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~assets/styles/portcss.scss';
.company-announce {
  width: 100%;
  height: vh(446);
  background-image: url('../../../../assets/cockpit/cockpit_pro_normal_bac.png');
  background-size: 100% 100%;
  padding: vh(8) vw(8);
  position: relative;
  &-content {
    margin-top: vh(16);
    width: 100%;
    height: vh(374);
    overflow-y: scroll;
    overflow-x: hidden;
    padding-left: vw(15);
    padding-right: vw(17);
    &-single {
      width: 100%;
      min-height: vh(86);
      display: flex;
      align-items: flex-end;
      position: relative;
      margin-bottom: vh(9);
      & > p {
        width: 100%;
        min-height: vh(76);
        box-shadow: inset 0px 1px 24px 0px rgba(121, 178, 255, 0.3);
        border-radius: vh(4);
        border: 1px solid #3a80e3;
        margin-bottom: 0;
        padding: vh(23) vw(16) 0;
        color: #ffffff;
        font-weight: 400;
        font-size: vh(14);
        line-height: 20px;
      }
      &-date {
        width: vw(86);
        height: vh(22);
        background: linear-gradient(180deg, #5a95ff 0%, #001327 100%);
        border-radius: vh(2);
        border: 1px solid #3a80e3;
        text-align: center;
        line-height: vh(22);
        font-size: vh(12);
        color: rgba(244, 248, 255, 0.9);
        position: absolute;
        top: 0;
        left: vw(16);
      }
    }
  }
  &-more {
    position: absolute;
    top: vh(18);
    right: vw(16);
    display: flex;
    align-items: center;
    gap: vw(8);
    cursor: pointer;
    & > span {
      height: vh(19);
      font-size: vh(14);
      color: #e6f6ff;
      line-height: vh(19);
    }
    & > img {
      width: vw(7);
      height: vh(11);
    }
  }
}
</style>
