// 风险管理 -> 限额管理 ->组合自动关联指标
import { GetListInfo, UpdateFn, ExportFn } from '@jupiterweb/utils/api'
import request from '@jupiterweb/utils/request'
// 获取规则名称
export const getRuleName = params => GetListInfo('/invest/riskredis/quota/rskportautorelatepoint/RskPortAutoRelatePoint001/getRuleName', params)
// 修改自动关联规则获取可编辑表格数据
export const getEditRuleList = params => GetListInfo('/invest/riskredis/quota/rskportautorelatepoint/RskPortAutoRelatePoint002/findPage', params)
// 修改自动关联规则提交前校验
export const zdglgzCheck = (params, cb) => UpdateFn('/invest/riskredis/quota/rskportautorelatepoint/RskPortAutoRelatePoint002/check', params, cb)
// 规则名称删除
export const delRuleName = (params, cb) => UpdateFn('/invest/riskredis/quota/rskportautorelatepoint/RskPortAutoRelatePoint002/delete', params, cb)
// 规则包成分查看获取列表页
export const getPackRuleList = params => GetListInfo('/invest/riskredis/quota/portfolioindicator/RskSupervisionPortfPoint005/findPage', params)
// 规则包成分查看页面导出
export const PackRuleExport = params => ExportFn('/invest/riskredis/quota/portfolioindicator/RskSupervisionPortfPoint005/export', params)
// 获取字典数据
export const GetDict = params => GetListInfo('/framework/dictionary/select', params)
// 组合自动关联指标新建页面里的表格数据
export const autoLinkUpdateList = params => GetListInfo('/invest/riskredis/quota/rskportautorelatepoint/RskPortAutoRelatePoint003/findPage', params)
// 个性化参数设置页面初始化接口
// 条件设置
export const findPageForCondition = params => GetListInfo('/invest/riskredis/quota/supervisionindividuation/RskSupervisionIndividuation001/findPageForCondition', params)
// 特殊阶段
export const findPageForSpecialStage = params => GetListInfo('/invest/riskredis/quota/supervisionindividuation/RskSupervisionIndividuation001/findPageForSpecial', params)
// 特殊证券阈值
export const findPageForSpecialAsset = params => GetListInfo('/invest/riskredis/quota/supervisionindividuation/RskSupervisionIndividuation001/findPageForSpecialAsset', params)
// 剔除资产
export const findPageForAssetDel = params => GetListInfo('/invest/riskredis/quota/supervisionindividuation/RskSupervisionIndividuation001/findPageForAssetDel', params)
// 剔除资产证券代码下拉框
export const findPageForAssetDelSelect = params => GetListInfo('/invest/riskredis/quota/supervisionindividuation/RskSupervisionIndividuation001/queryFinprodIdList', params)
// 联合设置
export const findPageForUnite = params => GetListInfo('/invest/riskredis/quota/supervisionindividuation/RskSupervisionIndividuation001/findPageForUnite', params)
// 联合设置 -> 指标名称下拉
export const findPageForUniteSelect = params => GetListInfo('/invest/riskredis/quota/supervisionindividuation/RskSupervisionIndividuation001/queryUniteIndIdList', params)
// 自定义条件阈值-> 自定义条件
export const findPageForCustomized = params => GetListInfo('/invest/riskredis/quota/supervisionindividuation/RskSupervisionIndividuation001/findPageForCustomized', params)
// 自定义条件阈值-> 自定义条件保存删除前check
export const handleCustomizedSaveCheck = params => request.post('/invest/riskredis/quota/supervisionindividuation/RskSupervisionIndividuation007/check', params)
// 自定义条件阈值-> 自定义条件保存删除
export const handleCustomizedSave = params => request.post('/invest/riskredis/quota/supervisionindividuation/RskSupervisionIndividuation007/save', params)
// 自定义条件阈值-> 自定义条件阈值  接口404，问下宝峰
export const findPageForCustomizedItem = params => GetListInfo('/invest/riskredis/quota/supervisionindividuation/RskSupervisionIndividuation001/findPageForCustomizedItem', params)
// 自定义条件阈值-> 自定义条件阈值条件项下拉框
export const findConditionList = params => GetListInfo('/invest/riskredis/quota/supervisionindividuation/RskSupervisionIndividuation001/queryConditionList', params)

