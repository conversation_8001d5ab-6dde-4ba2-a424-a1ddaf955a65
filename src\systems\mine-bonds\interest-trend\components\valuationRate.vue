<template>
  <!-- 估值利差 -->
  <div class="valuation-rate">
    <div class="valuation-module">
      <jr-layout-vertical :height="48" disabled>
        <template v-slot:top>
          <div class="module-btn-list">
            <el-button
              v-for="(item, index) in moduleList"
              :key="index"
              :type="activeModule == item.val ? 'primary' : ''"
              @click="changeJudicialModule(item.val)"
            >
              {{ item.name }}
            </el-button>
          </div>
        </template>

        <template v-slot:bottom>
          <div class="valuation-module-chart">
            <Echart ref="charts" :options="options" :styles="{ height: '100%' }" />
            <div class="chart-export">
              <el-dropdown trigger="click" @command="chartCommand">
                <el-button>
                  <jr-svg-icon class="el-icon--right" icon-class="upload" />
                  导出
                  <i class="el-icon-caret-bottom" />
                </el-button>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item command="tp">
                    <jr-svg-icon icon-class="picture" />
                    导出图片
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </div>
          </div>
          <div class="valuation-module-content">
            <div class="valuation-module-content-form">
              <el-form inline label-width="68">
                <jr-form-item v-sysversion="'group'" label="发行人">
                  <jr-combobox
                    v-model="sInfoWindcodelist"
                    style="max-width: 285px"
                    placeholder="请选择"
                    clearable
                    multiple
                    collapse-tags
                    :data="peopleList"
                    option-value="value"
                    option-label="text"
                  />
                </jr-form-item>
                <jr-form-item label="债券简称">
                  <el-input v-model="infoName" clearable style="max-width: 285px" placeholder="请输入债券简称" />
                </jr-form-item>
                <jr-form-item v-if="activeModule == 'SRGZ'">
                  <jr-radio-group v-model="issueStatus" :data="checkboxList" cancelable />
                </jr-form-item>
                <jr-form-item v-if="activeModule == 'XRGZ'" label="估值日期">
                  <el-date-picker
                    v-model="tradeDt"
                    placeholder="请选择日期"
                    value-format="yyyy-MM-dd"
                    class="date-picker"
                    style="max-width: 285px"
                  />
                </jr-form-item>
              </el-form>
              <div class="btn-list">
                <el-button type="primary" @click="submit">查询</el-button>
                <el-button @click="clearSel">清空已选</el-button>
                <div class="export-btn" @click="exportList">
                  <jr-svg-icon icon-class="upload" />
                </div>
              </div>
            </div>
            <div class="valuation-module-content-table">
              <jr-table
                ref="table"
                :height="415"
                :muti-select="true"
                :columns="configTable.columns"
                :data-source="configTable.data"
                :loading="configTable.loading"
                :pagination="configTable.pagination"
                :on-change="
                  (res) => {
                    changeBondList(res)
                  }
                "
                @handleSelectionChange="SelectRow"
              />
            </div>
          </div>
        </template>
      </jr-layout-vertical>
    </div>
  </div>
</template>
<script>
import Echart from '@jupiterweb/components/echarts'
import {
  queryBondRatingAgency,
  queryBondList,
  queryyieldtimage,
  queryBondyieldhist,
  exportBondList,
  queryyieldtradeDt
} from '@/api/bonds/bonds'
export default {
  components: {
    Echart
  },
  props: {},
  data() {
    return {
      activeModule: 'SRGZ',
      moduleList: [
        {
          name: '上市首日估值偏离度',
          val: 'SRGZ'
        },
        {
          name: '信用利差',
          val: 'XRGZ'
        }
      ],
      // 表格数据
      configTable: {
        loading: false,
        columns: [],
        data: [],
        pagination: {
          pageNo: 1,
          pageSize: 10,
          pageSizeOptions: [10, 20, 50, 100],
          total: 0,
          showSizeChanger: true
        }
      },
      columns: [],
      sInfoWindcodelist: [],
      infoName: '',
      issueStatus: '1',
      options: {
        legend: {
          top: 16,
          left: 'center',
          orient: 'horizontal',
          textStyle: {
            color: 'rgba(0, 0, 0, 0.85)'
          },
          type: 'scroll',
          width: '60%'
        },
        grid: {
          top: '72',
          left: '56',
          right: '56',
          bottom: '56'
        },
        toolbox: {
          show: false
        },
        tooltip: {
          trigger: 'axis',
          appendToBody: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          axisLabel: {
            color: 'rgba(0, 0, 0, 0.85)'
          }
        },
        yAxis: {
          name: '单位(BP)',
          type: 'value'
        },
        series: []
      },
      peopleList: [],
      checkboxList: [
        {
          label: '包含已到期',
          value: '1'
        }
      ],
      queryData: {},
      limit: 10,
      tradeDt: '',
      newDate: '',
      tableColumn1: [
        {
          title: '发行人',
          prop: 'binfoIssuer'
        },
        {
          title: '主体性质',
          prop: 'compProperty'
        },
        {
          title: '债券简称',
          prop: 'sinfoName'
        },
        {
          title: '发行截止日',
          prop: 'bissueLastissue'
        },
        {
          title: '中债隐含评级',
          prop: 'cnbdCreditrating'
        },
        {
          title: '剩余期限',
          prop: 'remainTerm'
        },
        {
          title: '票面利率(%)',
          prop: 'latestCouponrate',
          className: 'rate-columns-num'
        },
        {
          title: '首日估值(%)',
          prop: 'firstYieldCnbd',
          className: 'rate-columns-num'
        },
        {
          title: '票面与首次估值利差(BP)',
          prop: 'firstSpread',
          className: 'rate-columns-num'
        }
      ],
      tableColumn2: [
        {
          title: '债券简称',
          prop: 'sinfoName'
        },
        {
          title: '发行截止日',
          prop: 'bissueLastissue'
        },
        {
          title: '中债隐含评级',
          prop: 'cnbdCreditrating'
        },
        {
          title: '剩余期限',
          prop: 'remainTerm'
        },
        {
          title: '票面利率(%)',
          prop: 'latestCouponrate',
          className: 'rate-columns-num'
        },
        {
          title: '首日估值(%)',
          prop: 'firstYieldCnbd',
          className: 'rate-columns-num'
        },
        {
          title: '票面与首次估值利差(BP)',
          prop: 'firstSpread',
          className: 'rate-columns-num'
        }
      ],
      tableColumn3: [
        {
          title: '发行人',
          prop: 'binfoIssuer'
        },
        {
          title: '主体性质',
          prop: 'compProperty'
        },
        {
          title: '债券简称',
          prop: 'sinfoName'
        },
        {
          title: '发行截止日',
          prop: 'bissueLastissue'
        },
        {
          title: '中债隐含评级',
          prop: 'cnbdCreditrating'
        },
        {
          title: '剩余期限',
          prop: 'remainTerm'
        },
        {
          title: '票面利率(%)',
          prop: 'latestCouponrate',
          className: 'rate-columns-num'
        },
        {
          title: '中债估值(%)',
          prop: 'newYieldCnbd',
          className: 'rate-columns-num'
        },
        {
          title: '票面与同期限国开债利差(BP)',
          prop: 'debtSpread',
          className: 'rate-columns-num'
        },
        {
          title: '信用利差(BP)',
          prop: 'newSpread',
          className: 'rate-columns-num'
        }
      ],
      tableColumn4: [
        {
          title: '债券简称',
          prop: 'sinfoName'
        },
        {
          title: '发行截止日',
          prop: 'bissueLastissue'
        },
        {
          title: '中债隐含评级',
          prop: 'cnbdCreditrating'
        },
        {
          title: '剩余期限',
          prop: 'remainTerm'
        },
        {
          title: '票面利率(%)',
          prop: 'latestCouponrate',
          className: 'rate-columns-num'
        },
        {
          title: '中债估值(%)',
          prop: 'newYieldCnbd',
          className: 'rate-columns-num'
        },
        {
          title: '票面与同期限国开债利差(BP)',
          prop: 'debtSpread',
          className: 'rate-columns-num'
        },
        {
          title: '信用利差(BP)',
          prop: 'newSpread',
          className: 'rate-columns-num'
        }
      ],
      pageNum: 1,
      tableSel: [],
      imgList: [],
      newPage: true,
      selectRowTime: null
    }
  },
  watch: {
    activeModule(val) {
      // 重置表单
      if (val === 'SRGZ') {
        this.queryData = {
          issueStatus: '1'
        }
      } else {
        this.queryData = {
          tradeDt: this.newDate
        }
      }
      this.sInfoWindcodelist = []
      this.infoName = ''
      this.issueStatus = '1'
      this.tradeDt = this.newDate
      this.tableSel = []
      this.newPage = true
      this.imgList = []
      this.pageNum = 1
      // 更新列表
      this.getBondList(
        {
          pageNo: 1,
          pageSize: this.limit
        },
        {
          listType: val,
          ...this.queryData
        },
        true
      )
    }
  },
  mounted() {
    const sysVersion = localStorage.getItem('sysVersion')
    this.configTable.columns =
      sysVersion === 'group'
        ? this.tableColumn1
        : this.tableColumn2
    this.getBondIssuerInfo()
    this.getYieldtradeDt()
    this.queryData.issueStatus = this.issueStatus
    this.getBondList(
      {
        pageNo: 1,
        pageSize: this.limit
      },
      {
        listType: this.activeModule,
        ...this.queryData
      },
      true
    )
  },
  methods: {
    changeJudicialModule(val) {
      this.activeModule = val
      // 切换表格列
      const sysVersion = localStorage.getItem('sysVersion')
      if (val === 'SRGZ') {
        this.configTable.columns =
          sysVersion === 'group'
            ? this.tableColumn1
            : this.tableColumn2
      } else {
        this.configTable.columns =
          sysVersion === 'group'
            ? this.tableColumn3
            : this.tableColumn4
      }
    },
    callFn(data) {
      this.columns = data.config.columns
    },
    submit() {
      const form = {}
      const sysVersion = localStorage.getItem('sysVersion')
      if (sysVersion === 'group') {
        form.sInfoWindcodelist = this.sInfoWindcodelist
      }
      if (this.activeModule === 'SRGZ') {
        form.issueStatus = this.issueStatus
      } else {
        form.tradeDt = this.tradeDt
      }
      form.text = this.infoName
      this.queryData = form
      this.getBondList(
        {
          pageNo: 1,
          pageSize: this.limit
        },
        {
          listType: this.activeModule,
          ...this.queryData
        },
        true
      )
    },
    clearSel() {
      this.$refs.table.clearSelection()
      this.options.xAxis.data = []
      this.options.series = []
    },
    exportList() {
      const form = { ... this.queryData }
      form.listType = this.activeModule
      exportBondList(form)
    },
    chartCommand(type) {
      if (type === 'tp') {
        // 下载图片
        const exportOptions = {
          backgroundColor: '#ffffff',
          pixelRatio: 2 // 提高导出清晰度（可选）
        }
        // // 生成图片 URL 并触发下载
        const chart = this.$refs.charts.myChart
        const imgUrl = chart.getDataURL(exportOptions)
        const link = document.createElement('a')
        link.href = imgUrl
        link.download = `利差曲线.png`
        link.click()
      }
    },
    async getBondIssuerInfo() {
      const data = {
        text: ''
      }
      const res = await queryBondRatingAgency(data)
      const list = []
      for (const i of res) {
        if (i) {
          list.push(i)
        }
      }
      this.peopleList = list
    },
    changeBondList(page) {
      let pageNo = page.page
      if (this.configTable.pagination.total < (pageNo - 1) * page.limit) {
        pageNo = 1
      }
      this.limit = page.limit
      this.getBondList(
        {
          pageNo,
          pageSize: page.limit
        },
        {
          listType: this.activeModule,
          ...this.queryData
        }
      )
    },
    getBondList(page, data, status) {
      const that = this
      this.newPage = true
      this.configTable.loading = true
      queryBondList({ page, data }).then((res) => {
        that.configTable.loading = false
        that.configTable.data = res?.list || []
        that.configTable.pagination = {
          ...that.configTable.pagination,
          pageNo: res?.pageNum,
          total: res?.total
        }
        that.pageNum = res?.pageNum
        if (status) {
          that.options.xAxis.data = []
          that.options.series = []
          that.$nextTick(() => {
            that.$refs.table.$children[0]?.toggleAllSelection()
          })
        } else {
          that.$nextTick(() => {
            that.selectByCode()
          })
        }
      })
    },
    SelectRow(data) {
      const that = this
      clearTimeout(that.selectRowTime)
      that.selectRowTime = setTimeout(() => {
        const bondlist = []
        const series = []
        for (const i of data) {
          bondlist.push(i.sinfoWindcode)
        }
        if (bondlist.length === 0 && that.newPage) {
          that.newPage = false
          return
        }
        that.tableSel[that.pageNum] = bondlist
        that.imgList = []
        for (const i of that.tableSel) {
          if (Array.isArray(i)) {
            that.imgList = [...that.imgList, ...i]
          }
        }
        if (that.imgList.length === 0) {
          that.options.xAxis.data = []
          that.options.series = []
          return
        }
        const dateList = []
        if (that.activeModule === 'SRGZ') {
            queryyieldtimage({ imagetype: 'SRGZ', bondlist: that.imgList }).then((res) => {
              const list = res['上市首日估值偏离度'] || []
              const seriesData = {}
              for (const i of list) {
                const date = that.formatDate(i.tradeDt)
                if (seriesData[i.sinfoName]) {
                  seriesData[i.sinfoName].push([date, i.val])
                } else {
                  seriesData[i.sinfoName] = [[date, i.val]]
                }
                dateList.push(date)
              }
              for (const i in seriesData) {
                series.push({
                  name: i,
                  symbolSize: 12,
                  data: seriesData[i],
                  type: 'scatter'
                })
              }
              const deWeightDate = [...new Set(dateList)]
              const sortDate = deWeightDate.sort((a, b) => new Date(a).getTime() - new Date(b).getTime())
              that.options.xAxis.data = sortDate
              that.options.series = series
            })
        } else {
          queryBondyieldhist({ bondlist: that.imgList }).then((res) => {
            for (const i in res) {
              const list = []
              const listData = {}
              for (const j of res[i]) {
                const date = that.formatDate(j.tradeDt)
                if (listData[date]) {
                  listData[date] = listData[date] + (j.val || 0)
                } else {
                  listData[date] = j.val || 0
                }
                dateList.push(date)
              }
              for (const j in listData) {
                const num = listData[j].toFixed(4)
                list.push([j, Number(num)])
              }
              series.push({
                name: i,
                type: 'line', // 设置图表类型为折线图
                data: list
              })
            }
            const deWeightDate = [...new Set(dateList)]
            const sortDate = deWeightDate.sort((a, b) => new Date(a).getTime() - new Date(b).getTime())
            that.options.xAxis.data = sortDate
            that.options.series = series
          })
        }
      })
    },
    getYieldtradeDt() {
      queryyieldtradeDt({}).then((res) => {
        this.newDate = res?.tradeDt || ''
      })
    },
    formatDate(date) {
      if (date && date.length > 0) {
        const list = date.split('-')
        const newDate = list.join('/')
        return newDate
      }
      return ''
    },
    selectByCode() {
      for (const i of this.imgList) {
        const row =  this.configTable.data.find(row => row.sinfoWindcode === i);
        if (row) {
          this.$refs.table.$children[0]?.toggleRowSelection(row, true);
        }
      }
    }
  }
}
</script>
<style lang="scss">
.valuation-rate {
  height: calc(100% - 32px);

  .valuation-module {
    height: 100%;

    .module-btn-list {
      padding: 8px 0 0 16px;
    }

    .vertical-layout {
      background: #fff;
      padding: 0;
      height: 100%;

      &--top {
        &-content {
          padding: 0;
        }
      }

      &--resize {
        height: 1px;
        color: #eae9e9;
      }

      &--bottom {
        margin-top: 8px;
        padding: 0 16px;
        overflow: auto;
      }
    }

    &-chart {
      position: relative;
      height: 415px;

      .chart-export {
        position: absolute;
        top: 0;
        right: 16px;
      }
    }

    &-content {
      .jr-decorated-table--header-left {
        display: none;
      }

      .jr-decorated-table--header-right {
        display: none;
      }

      .jr-decorated-table--body {
        padding: 0;
      }

      &-form {
        position: relative;

        .el-form {
          display: flex;
          align-items: center;
          padding-top: 0;
          width: calc(100% - 312px);

          .el-form-item {
            width: 100%;
            max-width: 353px;

            .el-form-item__label {
              padding: 8px 8px 0 0;
            }

            .el-form-item__content {
              width: calc(100% - 80px);
              height: 32px;
              line-height: 32px;
            }

            &.no-label {
              .el-form-item__content {
                width: 100%;
              }
            }
          }
        }

        .btn-list {
          position: absolute;
          top: 0;
          right: 0;

          .el-button {
            margin-left: 16px;
          }

          .export-btn {
            display: inline-block;
            margin-left: 16px;
            width: 32px;
            height: 32px;
            line-height: 32px;
            text-align: center;
            border: 1px solid var(--el-border-color-button);
            transform: translateY(1px);

            .jr-svg-icon {
              fill: var(--theme--color);
              font-size: var(--el-font-size-medium);
            }

            &:hover {
              border-left-color: var(--theme--color-light-7);
              background: var(--theme--color-light-9);
              cursor: pointer;
            }
          }
        }
      }

      &-table {
        margin-top: 8px;
        height: calc(100% - 40px);

        .el-table {
          border: none;

          &::before {
            display: none;
          }

          &::after {
            display: none;
          }

          table {
            tbody {
              td.el-table__cell {
                border-right: none;

                &.rate-columns-num {
                  text-align: right;
                }
              }
            }
          }
        }
      }
    }
  }
}
</style>
