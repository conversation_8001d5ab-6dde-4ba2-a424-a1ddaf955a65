/*
 * @Author: <PERSON><PERSON>
 * @Date: 2020-8-19 17:02:51
 * @Remark: 下拉列表对应config配置
 *  // 示例DEMO
  counterpartyTable: {
    searchKey: "data",// 查询条件中包裹的参数入口
    columns: [{
      title: "一级分类",
      width: 120, prop:"orgType1Name"
    },......],
    //默认查询条件，用以查询默认值配置。若与searchItems中字段重复，会以searchItems中的覆盖隐藏域的
    defaultParams: {
      cpType: ""
    },
    // 查询条件
    searchItems: [{
      type: "text",
      name: "fullName",
      label: "交易对手方"
    },{
      type: "select",//下拉框选择
      name: "bussinessType",
      label: "业务类型",
      dictKey: "BUSINESSTYPE"//字典KEY，会根据字典key自动查询绑定
    }],
    //接口。 需要以分页方式进行
    api: "/platform/custOrgBsinf/page"
  },
*/
export default {
}
