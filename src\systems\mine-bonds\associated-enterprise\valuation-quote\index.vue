// 估值行情
<template>
  <div class="valuation-quote">
    <div class="valuation-quote-top">
      <div class="valuation-quote-top-title">{{ lodashGet(menuinfo,"meta.params.comp_name","-") }}</div>
      <div class="valuation-quote-top-btn">
        <el-button>
          <jr-svg-icon class="el-icon--left" icon-class="upload" />
          导出
        </el-button>
      </div>
    </div>

    <div class="valuation-quote-content">
      <div class="valuation-quote-content-top">
        <span class="valuation-quote-content-top-title">
          <span class="valuation-quote-content-top-title-tip" />
          {{ `${lodashGet(menuinfo,"meta.params.s_info_name","-")}【${lodashGet(menuinfo,"meta.params.s_info_windcode","-")}】` }}
        </span>
      </div>
      <div class="valuation-quote-content-filter-container">
        <el-form ref="form" label-position="left" :model="searchForm" label-width="140px">
          <jr-form-item label="日期区间" class="valuation-quote-content-filter-container-form-item">
            <el-date-picker
              v-model="searchForm.customDateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="yyyy-MM-dd"
              class="date-picker"
            />
          </jr-form-item>
          <jr-form-item label="行权/到期" class="valuation-quote-content-filter-container-form-item">
            <jr-radio-group v-model="searchForm.b_is_right" cancelable :data="radioList" />
          </jr-form-item>
          <jr-form-item label="估值类型" class="valuation-quote-content-filter-container-form-item">
            <!-- 这里需要拉字典项 -->
            <jr-radio-group
              v-model="searchForm.valtype"
              cancelable
              option-label="cnname"
              :data="radioTypeList"
              class="width-auto"
            />
          </jr-form-item>
        </el-form>
        <!-- 操作按钮区域 -->
        <div class="filter-actions">
          <el-button class="reset-btn" @click="resetSearcRight">重置</el-button>
          <el-button type="primary" class="query-btn" @click="searchRight">查询</el-button>
        </div>
      </div>
      <div class="valuation-quote-content-chart">
        <!-- v-if="searchForm.selectedBondCode" -->
        <template-module
          v-if="rightParams.b_is_right"
          chart-seq="605520115ed14c87afcb66eb3ed8a11e"
          :params="rightParams"
          :height="460"
          :custom-options="customOptions"
        />
      </div>
    </div>
    <div class="valuation-quote-table">
      <div class="valuation-quote-top">
        <span class="valuation-quote-top-title">中债估值列表</span>
        <jr-svg-icon icon-class="exprot_icon" style="width: 28px; height: 28px" @click="exportData" />
      </div>
      <!-- 接自定义列表 tableId换成自己的id -->
      <jr-decorated-table
        stripe
        :params="{
          ...tableParams
        }"
        :custom-render="customRender"
        :custom-id="tableId"
        :menuinfo="menuinfo"
        style="height: 516px;"
        :defaul-page-size="10"
        :initPagination="{
          pageSizeOptions: [10, 15, 20, 50, 100]
        }"
        @refreshed="callFn"
      />
    </div>
  </div>
</template>
<script>
import { exportBondRatingInfo } from '@/api/bonds/bonds'
import { GetComboboxList } from '@/api/home'
import templateModule from '@/components/template-module'
import { get } from 'lodash'
import moment from 'moment'
export default {
  name: 'Config',
  components: { templateModule },
  props: {
    menuinfo: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      // 自定义列id
      tableId: '5bf1e2c225ca42e0b715161aa29c2df1',
      // 菜单id
      ownedModuleid: '1361645930918469632',
      tableParams: {
        ownedModuleid: '1361645930918469632',
        ccid: '5bf1e2c225ca42e0b715161aa29c2df1',
        s_info_windcode: '032380596.IB'
      },
      customRender: {},
      loading: false, // 远程搜索加载状态
      searchForm: {
        // 默认值为当天日期到一年前的当天日期
        customDateRange: [moment().subtract(1, 'years').format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')],
        b_is_right: '', // 行权/到期
        valtype: '' // 估值类型
      },
      radioList: [
        { value: '2', label: '行权' },
        { value: '1', label: '到期' }
      ],
      radioTypeList: [],
      customOptions: {
        yAxis: {
          name: '估价收益率'
        },
        toolbox: {
          show: false
        },
        tooltip: {
          // trigger: 'axis',
          backgroundColor: 'rgba(0,0,0,0.6)',
          borderWidth: '0',
          padding: [12, 16],
          textStyle: {
            color: '#fff',
            fontSize: 14
          },
          formatter: (item) => {
            // ${item.color}
            let content = ''
            content = `${item.name}<br/>
            <span style="margin-right:8px;width:8px;height:8px;border-radius:50%;display:inline-block;background:#E1B01E"></span>
            ${this.selectedBond.s_info_name},${item.value}`
            return content
          }
        },
        legend: {
          show: false
        },
        series: [
          {
            smooth: true,
            lineStyle: {
              color: '#E1B01E',
              type: 'solid',
              width: 2
            },
            itemStyle: {
              color: '#E1B01E'
            }
          }
        ]
      },
      // 图表的入参
      rightParams: {},
      columns: []

    }
  },
  created() {
    console.log(this.menuinfo, 'this.$route.meta.params')

    const params = get(this, 'menuinfo.meta.params', {})
    console.log(params, 'params')
    if (params && params.s_info_windcode) {
      this.searchForm.s_info_windcode = params.s_info_windcode // 直接赋值，无需解构
      this.searchForm.b_is_right = params.b_is_right // 直接赋值，无需解构
      this.searchForm.valtype = params.valtype // 直接赋值，无需解构
    }
    console.log(this.searchForm, 'this.searchForm')

    this.getComboboxList()
  },
  methods: {
    // 查询字典
    async getComboboxList() {
      const data = await GetComboboxList(['VALTYPE'])
      if (data && data.VALTYPE) {
        this.radioTypeList = data.VALTYPE
        // 默认选中第一个
        if (this.radioTypeList.length > 0) {
          this.searchForm.valtype = this.radioTypeList[0].value
        }
        // this.rightParams =
        console.log(this.searchForm, 'this.searchForm')

        this.searchRight() // 调用查询方法
      }
      console.log(this.radioTypeList, 'this.radioTypeList')
    },
    searchRight() {
      const { customDateRange, valtype, b_is_right } = this.searchForm
      const [startDate, endDate] = customDateRange.map((date) => moment(date).format('YYYY-MM-DD'))

      // 更新图表参数
      this.rightParams = {
        ...this.rightParams,
        trade_dtStart: startDate,
        trade_dtEnd: endDate,
        valtype,
        b_is_right
      }

      // 更新表格参数
      this.tableParams = {
        ...this.tableParams,
        trade_dtStart: startDate,
        trade_dtEnd: endDate,
        matu_rnk: b_is_right
      }

      // 更新Y轴名称
      const selectedType = this.radioTypeList.find((item) => item.value === valtype)
      if (selectedType) {
        this.customOptions.yAxis.name = selectedType.cnname
      }
    },
    resetSearcRight() {
      this.searchForm = {
        // 默认值为当天日期到一年前的当天日期
        customDateRange: [moment().subtract(1, 'years').format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')],
        b_is_right: '2', // 行权/到期
        valtype: '' // 估值类型
      }
      this.searchForm.valtype = this.radioTypeList[0].value
      this.searchRight()
    },
    async exportData() {
      const params = {
        sort: null,
        direction: null,
        filename: '中债估值列表',
        title: '中债估值列表',
        params: {
          ...this.tableParams
        },
        page: -1,
        count: 15,
        column: this.columns,
        total: 0
      }
      await exportBondRatingInfo(params)
    },
    callFn(data) {
      // 获取列表的列
      this.columns = data.config.columns
    },
    lodashGet(obj, path, defaultValue) {
      return get(obj, path, defaultValue)
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .jr-radio-group .el-radio{
  min-width: fit-content;
  display: inline-flex;
  align-items: center;
  vertical-align: middle;
}
::v-deep .jr-decorated-table--top-search .jr-decorated-table--header {
  display: none !important;
}
::v-deep .el-form .el-form-item__label{
  font-size: var(--el-font-size-large) !important;
}
::v-deep .el-form .el-radio__label{
  font-size: var(--el-font-size-large) !important;
}
.valuation-quote {
  &-top {
    height: 70px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24px 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: #FFFFFF;
    &-title {
      height: 22px;
      font-family: MicrosoftYaHeiSemibold;
      font-size: var(--el-font-size-extra-large);
      color: rgba(0,0,0,0.85);
      line-height: 22px;
      font-weight: 600;
    }
  }
  &-form {
    display: flex;
    align-items: center;
    padding-top: 16px;
  }
  &-content {
    padding: 0 32px;
    background-color: #FFFFFF;
    &-top {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-top: 21px;
      padding-bottom: 21px;
      &-title {
        height: 22px;
        font-family: MicrosoftYaHeiSemibold;
        font-size: var(--el-font-size-large);
        color: rgba(0,0,0,0.85);
        line-height: 22px;
        gap: 8px;
        font-weight: 600;
        &-tip {
          display: inline-block;
          width: 3px;
          height: 16px;
          background-color: #ff8e2b;
        }
      }
    }

    &-filter-container {
      padding: 24px 32px;
      border-bottom: 1px solid #ebeef5;
      background-color: #f6f8fd;
      min-height: 235px;

      &-form-item {
        .el-form-item__content {
          display: flex;
          align-items: center;
          .date-picker {
            flex: 1;
            max-width: 308px;
          }
        }

        // margin-bottom: 10px;
        .jr-radio-group .el-radio {
          min-width: 30px !important;
          margin-right: 16px;
        }
      }
      .el-form-item {
        padding-bottom: 20px;
      }

      /* 筛选操作按钮样式 */
      .filter-actions {
        display: flex;
        justify-content: flex-end;

        /* 重置按钮样式 */
        .reset-btn {
          // margin-right: 10px;
          border-radius: 2px;
          padding: 10px 20px;
        }

        /* 查询按钮样式 */
        .query-btn {
          background-color: #f18f01; /* 主题色-橙色 */
          border-color: #f18f01;
          border-radius: 2px;
          padding: 10px 20px;

          &:hover {
            background-color: #e08601;
            border-color: #e08601;
          }
        }
      }
    }
  }
  &-table {
    padding: 0px 16px;
    background-color: #FFFFFF;
    &-top{
      height: 70px;
      padding: 24px 0px;
    }
    .jr-decorated-table--header-left {
      display: none;
    }
    .jr-decorated-table--header-right {
      display: none;
    }
    .jr-decorated-table--body {
      padding: 0;
    }
  }
}
</style>
