<template>
  <flat-index :query="handleQuery" :reset="handleReset" :pagination="configTable.pagination">
    <template v-slot:form>
      <el-form ref="elForm" :model="configForm.model">
        <jr-form-item-create :column="0" :data="configForm.data" :model="configForm.model" />
      </el-form>
    </template>

    <template v-slot:right-button>
      <el-button>
        <jr-svg-icon icon-class="export" />
      </el-button>
    </template>

    <template v-slot:table-header-button>
      <el-button type="primary" @click="handleAdd">{{ InitialMessage('common.system.button.add') }}</el-button>
    </template>

    <template v-slot:table-list="{ height }">
      <jr-table
        :height="height"
        :columns="configTable.columns"
        :data-source="configTable.data"
        :loading="configTable.loading"
        :pagination="configTable.pagination"
        :on-change="handleQuery"
        border
      >
        <template v-slot:index>
          <el-table-column
            type="index"
            width="50px"
            align="center"
            :label="InitialMessage('common.columns.index')"
          >
            <template slot-scope="scope">
              <span>{{ (configTable.pagination.pageNo - 1) * configTable.pagination.pageSize + scope.$index +1 }}</span>
            </template>
          </el-table-column>
        </template>
      </jr-table>
    </template>

    <!-- 详情弹框(全局注册的组件)-->
    <modal-non-process
      ref="modal"
      size="full"
      :close-modal="closeModal"
      :save="configModal.save"
      :visible="configModal.visible"
      :modal-type="configModal.modalType"
      :modal-type-name="configModal.modalTypeName"
      :modal-title="configModal.modalTitle"
      :module-id="configModal.moduleId"
      :businessno="configModal.businessno"
      :menuinfo="configModal.menuinfo"
      :item-data="configModal.itemData"
      join-type
    />
  </flat-index>
</template>

<script>
import * as API from '@/api/demo/flat-index'

export default {
  props: {
    // 平台日期
    date: {
      type: [Number, String],
      default: null
    },
    // 权限相关
    permitdetail: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    const { InitialMessage } = this

    return {
      // 查询区域
      configForm: {
        model: {},
        data: [
          {
            title: InitialMessage('demo.flatIndex.query.name'),
            prop: 'field1',
            type: 'text',
            required: true
          },
          {
            title: InitialMessage('demo.flatIndex.query.address'),
            prop: 'field2',
            type: 'select',
            options: [
              { text: '1111', value: '1111' },
              { text: '2222', value: '2222' },
              { text: '3333', value: '3333' }
            ],
            required: true
          }
        ]
      },
      // 表格数据
      configTable: {
        loading: false,
        columns: [
          {
            title: InitialMessage('demo.flatIndex.table.name'),
            prop: 'name'
          },
          {
            title: InitialMessage('demo.flatIndex.table.date'),
            prop: 'date',
            type: 'date'
          },
          {
            title: InitialMessage('demo.flatIndex.table.amount'),
            prop: 'amount',
            type: 'amount'
          },
          {
            title: InitialMessage('demo.flatIndex.table.amount'),
            prop: 'amount2',
            type: 'amount'
          },
          {
            title: InitialMessage('demo.flatIndex.table.tenThousand'),
            prop: 'tenThousand',
            type: 'tenThousand'
          },
          {
            title: InitialMessage('demo.flatIndex.table.rate'),
            prop: 'rate',
            type: 'rate'
          }
        ],
        data: [
          {
            name: '测试名称1',
            date: 1624931532000,
            amount: 100,
            amount2: 200,
            tenThousand: 30000,
            rate: 0.01
          },
          {
            name: '测试名称2',
            date: 1655197702318,
            amount: 100,
            amount2: 200,
            tenThousand: 40000,
            rate: 0.01
          }
        ],
        pagination: {
          pageNo: 1,
          pageSize: window.screen.height > 1000 ? 25 : 15,
          pageSizeOptions: [15, 25, 50, 100],
          total: 0,
          showSizeChanger: true
        }
      },
      // 弹框配置项
      configModal: {
        save: '/invest/basicdata/mstcommissionset/MstCommissionSet002', // 初始化 init 的接口地址，必传(这里随便写的，开发需填写真实接口地址)
        visible: false,
        itemData: {},
        // businessno: DICT.SYSCONSTANT.BUSINSSNO.SYSTEMADJUSTDEAL,
        modalTitle: '演示弹窗',
        modalType: 'new',
        modalTypeName: '新建',
        moduleId: '',
        menuinfo: {
          componenturl: 'demo/update' // 组件地址
        }
      }
    }
  },
  methods: {
    handleAdd() {
      this.configModal.visible = true
    },
    closeModal() {
      this.configModal.visible = false
    },
    // 查询
    handleQuery() {
      const self = this
      const { configForm, configTable } = self

      self.$refs.elForm.validate(async valid => {
        if (valid) {
          configTable.loading = true

          const params = {
            data: { ...configForm },
            page: { ...configTable.pagination }
          }

          const { list = [], total = 0 } = { ...await API.GetListData(params) }

          if (list.length) {
            configTable.loading = false
            configTable.data = list
            configTable.pagination.total = total
          }
        }
      })
    },
    // 重置
    handleReset() {

    }
  }
}
</script>

<style lang="scss">

</style>

