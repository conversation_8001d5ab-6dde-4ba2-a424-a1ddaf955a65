.template-module-chart {
  background-color: #fff;
  min-height: 500px;
  position: relative;
  .custom-actions {
    position: absolute;
    // top: 16px;
    left: 16px;
    z-index: 1;
  }

  .chart-content {
    margin-top: 20px;
    top: 16px;
  }

  .el-dropdown {
    position: absolute;
    right: 0px;
    top: 4px;
    z-index: 1;
    .el-dropdown-link {
      cursor: pointer;
      font-size: var(--el-font-size-base);
      color: #333333;
    }
  }
}

::v-deep .el-radio {
  margin-right: 0 !important;
  min-width: 80px !important;
}

::v-deep .el-form-item__label {
  padding-top: 12px !important;
}

::v-deep .el-form-item__content {
  display: flex;
  align-items: center;
}