<template>
  <div class="explanation-content">
    <p>计算说明：</p>
    <div class="explanation-content-entry">
      <span>1.到期久期 =</span>
      <div class="explanation-content-entry-formula">
        <span>∑( 存续期债券截止到期日的剩余期限 × 债券余额 )</span>
        <span class="explanation-content-entry-formula-divider" />
        <span>∑( 存续期债券余额 )</span>
      </div>
    </div>
    <div class="explanation-content-entry">
      <span>2.行权久期 =</span>
      <div class="explanation-content-entry-formula">
        <span>∑( 存续期债券截止下一行权日或到期日的剩余期限 × 债券余额 )</span>
        <span class="explanation-content-entry-formula-divider" />
        <span>∑( 存续期债券余额 )</span>
      </div>
    </div>
    <div class="explanation-content-entry">
      <span>3.短期/长期 =</span>
      <div class="explanation-content-entry-formula">
        <span>∑( 发行期限小于等于1Y的存续期债券余额 )</span>
        <span class="explanation-content-entry-formula-divider" />
        <span>∑( 发行期限大于1Y的存续期债券余额 )</span>
      </div>
    </div>
    <div class="explanation-content-entry">
      <span>4.综合成本 =</span>
      <div class="explanation-content-entry-formula">
        <span>∑( 存续期债券余额 × 债项成本 )</span>
        <span class="explanation-content-entry-formula-divider" />
        <span>∑( 存续期债券余额 )</span>
      </div>
    </div>
    <p class="explanation-content-tips">注：仅含赎回、回售、延期条款的含权债券采用行权剩余期限计算行权久期。</p>
    <p class="explanation-content-tips" style="width: 540px">
      债项成本默认等于票面利率、承销费率、中介费率和其他服务费率勾选后也将纳入债项成本计算。
    </p>
    <div class="explanation-content-triangular" />
  </div>
</template>

<script>
export default {}
</script>

<style lang="scss" scoped>
.explanation-content {
  background: #000000cc;
  padding: 30px 25px;
  border-radius: 4px;
  color: #ffffff;
  position: relative;
  &-title {
    font-size: var(--el-font-size-base);
  }
  &-entry {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 10px;
    &-formula {
      width: fit-content;
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 4px;
      &-divider {
        width: 100%;
        height: 1px;
        background-color: #ffffff;
      }
    }
  }
  &-tips {
    margin-bottom: 0px;
  }
  &-triangular {
    width: 0;
    height: 0;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-bottom: 10px solid #000000cc;
    position: absolute;
    top: -10px;
    left: 40px;
  }
}
</style>
