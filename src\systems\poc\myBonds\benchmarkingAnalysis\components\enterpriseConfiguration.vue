<!-- 对标企业配置 -->
<template>
  <div class="enterpriseConfiguration">
    <div class="enterpriseConfigurationTitle">对标企业配置</div>
    <div class="searchForm">
      <el-form :model="searchForm" label-width="90px">
        <jr-form-item label="企业名称" class="formItem">
          <el-input
            v-model="searchForm.enterpriseName"
            placeholder="请选择"
          />
        </jr-form-item>
        <el-button type="primary" class="btn" :disabled="!searchForm.enterpriseName" @click="addData">+添加</el-button>
      </el-form>
      <el-tag v-for="tag in enterpriseTags" :key="tag.name" closable @close="handleClose(tag)">
        {{ tag.name }}
      </el-tag>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      searchForm: {
        enterpriseName: ''
      },
      enterpriseTags: [
        { name: '雪佛兰公司' },
        { name: '沙特阿美公司' },
        { name: '道达尔公司' },
        { name: '宁波开发投资集团有限公司' },
        { name: '宁波市奉化区交通投资发展集国有限公司' }
      ]
    }
  },
  methods: {
    addData() {
      this.enterpriseTags.push({ name: this.searchForm.enterpriseName })
      this.$set(this.searchForm, 'enterpriseName', '')
    },
    handleClose(tag) {
      this.enterpriseTags = this.enterpriseTags.filter(item => item.name !== tag.name)
    }
  }
}
</script>
<style lang="scss">
.enterpriseConfiguration {
    margin-top: 10px;
    padding: 6px 10px 6px 10px;
    background: #fff;

    .enterpriseConfigurationTitle {
        font-size: 16px;
        color: #303133;
        font-weight: 700;
        padding-bottom: 6px;
        border-bottom: 1px solid #EBEEF5;
    }
    .formItem {
        display: inline-block;
        width: 300px
    }
    .btn {
        display: inline-block;
        margin-left: 10px;
    }
    .searchForm {
        padding: 10px 0 0 0;
        background: #fff;
        margin-bottom: 0;
        .el-tag {
            margin: 10px;
        }
    }
}
</style>

