<template>
  <div class="bazaar" style="padding: 0 16px !important;">
    <h3 class="card-title">贷款市场报价利率（LPR）</h3>
    <jr-layout-horizontal>
      <template slot="left">
        <card-list ref="cardList" v-bind="{ isType, params, mainTitle, subtitle }" :on-active="onActive" />
      </template>
      <template slot="right">
        <form-list dict-type="LPR_YEAR" :tips-flag="false" v-bind="{ submit, reset, form }">
          <jr-form-item label="发行期限">
            <jr-checkbox-group
              v-model="form.bAnalCurveterm"
              :data="dictList"
              :min="0"
              :max="10"
              option-label="cnname"
              option-value="itemcode"
              @change="checkChange"
            />
          </jr-form-item>
        </form-list>
        <chart
          v-if="chartOptions"
          :options="chartOptions"
          :name="nameFn()"
        />
      </template>
    </jr-layout-horizontal>
  </div>
</template>

<script>
import formList from '../../components/form-list.vue'
import { date } from '@/utils/common'
import chart from '../../components/chart.vue'
import { GetModuleData } from '@jupiterweb/api/common/template-module'
import cardList from '../../components/card-list.vue'
import { GetInfoFn } from '@jupiterweb/utils/api'
export default {
  name: 'NationalDebt',
  provide() {
    return { parant: this }
  },
  components: {
    formList,
    chart,
    cardList
  },
  data() {
    return {
      paramsDatas: {},
      mainTitle: '最新利率(%)',
      subtitle: '涨跌BP',
      list: [],
      dictList: [],
      chartOptions: null,
      isType: '1Y',
      form: {
        bAnalCurveterm: ['LPR1Y.IR'],
        date: [date().subtract(1, 'y'), date().now()],
        radioDate: 12
      },
      chartParams: {
        tradeDtStart: date().subtract(1, 'y').replaceAll('-', ''),
        tradeDtEnd: date().now().replaceAll('-', '')
      },
      params: { ccid: 'c7ef8f306c8645cbb9feaf0c3bf5e91e', ownedModuleid: '708631605142536192', b_anal_curvename: 'LPR' },
      downFileUrl: '/web/Shibor/lprExport'
    }
  },
  created() {
    this.getDictList()
    this.echartsData()
  },
  mounted() {
  },
  methods: {
    async getDictList() {
      const data = await GetInfoFn('DICT', 'LPR_YEAR')
      this.dictList = data || []
    },
    // echarts  接口
    async echartsData() {
      this.paramsDatas = {
        bAnalCurveterm: this.form.bAnalCurveterm.join(),
        tradeDtStart: this.form.date.length > 0 ? this.form.date[0].replaceAll('-', '') : '',
        tradeDtEnd: this.form.date.length > 0 ? this.form.date[1].replaceAll('-', '') : '',
        chartSeq: '14a4c697e01c4dada1848141ebf99057'
      }
      const { data } = await GetModuleData(this.paramsDatas)
      const chartOptions = JSON.parse(JSON.stringify(data))
      chartOptions.toolbox = {
        show: false, // 关闭所有工具栏功能
        feature: {
          saveAsImage: { show: true } // 若仅隐藏导出按钮，设为 false
        }
      }
      const type = this.dictList.length > 0 ? this.dictList.find(k => k.itemcode === this.form.bAnalCurveterm.join()).cnname : '1Y'
      const themeColor = '#729DF7'

      chartOptions.xAxis.name = ''
      chartOptions.yAxis.name = '单位：%'
      chartOptions.series[0].color = '#729DF7'
      chartOptions.series[0].step = 'middle'
      chartOptions.series[0].symbolSize = 0
      chartOptions.series[0].name = type
      chartOptions.legend = {
        x: 'center',
        y: 'bottom',
        padding: [10, 10, 10, 35],
        itemGap: 10,
        show: true,
        itemHeight: 10,
        textStyle: { fontSize: 12, padding: [1, 0, 0, 16], color: '#333' }
      }
      chartOptions.grid = [{ top: '20%', bottom: 80, left: 40, right: 60 }]
      chartOptions.tooltip = {
        trigger: 'axis',
        formatter: (data) => {
          const fmtData = data || []
          let result = `<div style="margin-bottom: 5px;">时间： ${fmtData[0].axisValue}</div>`
          fmtData.forEach((item) => {
            result += `<div syle="margin-bottom: 5px;">
              <span style="display: inline-block;width:12px;height:12px;border-radius:50%;background:${themeColor};"></span>
              期限(${type})：${item.data.value}%
            `
          })
          return result
        }
      }
      console.log(chartOptions)
      this.chartOptions = chartOptions
    },
    // 点击卡片的处理方法
    onActive(item) {
      const itemcode = this.dictList.length > 0 ? this.dictList.find(k => k.cnname === item.bAnalCurveterm).itemcode : ''
      this.form.bAnalCurveterm = itemcode ? [itemcode] : []
      this.echartsData()
    },
    submit() {
      this.echartsData()
    },
    reset() {
      this.echartsData()
      this.$refs.cardList.setCardInd()
    },
    // 发行期限值改变 清除卡片选中效果
    checkChange() {
      this.$nextTick(() => this.$refs.cardList.cardInd = null)
      const len = this.form.bAnalCurveterm.length
      this.form.bAnalCurveterm = len > 0 ? [this.form.bAnalCurveterm[len - 1]] : []
    },
    nameFn() {
      const d = this.form.date.join().replaceAll('-', '').replace(',', '-')
      return `贷款市场报价利率_${d}`
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/assets/styles/market.scss'
</style>
