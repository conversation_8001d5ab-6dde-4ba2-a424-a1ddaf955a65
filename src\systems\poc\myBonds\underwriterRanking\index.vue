<!-- 承销明细 -->
<template>
  <div class="underwriterRanking">
    <div class="searchForm">
      <el-form v-if="isShow" :model="searchForm" label-width="90px" class="searchItem">
        <jr-form-item-create :data="configData" :model="searchForm" />
      </el-form>
      <div class="btnItem">
        <el-button type="primary" class="btn" @click="queryData">查询</el-button>
        <span class="iconBtn" @click="expand">
          <span>{{ isExpand ? '展开' : '收起' }}</span>
          <jr-svg-icon :icon-class="isExpand ? 'batch-unfold' : 'batch-fold'" />
        </span>
      </div>
    </div>
    <ShareRanking />
    <RankingList />
  </div>
</template>

<script>
import ShareRanking from './components/shareRanking.vue'
import RankingList from './components/rankingList.vue'
export default {
  components: { ShareRanking, RankingList },
  data() {
    return {
      isExpand: true,
      isShow: true,
      searchForm: {
        ztkj: '01'
      }
    }
  },
  created() {
    this.init()
  },
  methods: {
    init() {
      this.configData = [
        {
          title: '主体口径',
          prop: 'ztkj',
          type: 'radio',
          optionValue: 'id',
          optionLabel: 'text',
          options: [
            { id: '01', text: '穿透信用主体' },
            { id: '02', text: '名义发生主体' }
          ]
        },
        {
          title: '发行日期',
          prop: 'fxrq',
          type: 'rangeDate'
        },
        {
          title: '机构类型',
          prop: 'jglx',
          type: 'select',
          options: []
        },
        {
          title: '地区',
          prop: 'dq',
          type: 'select',
          options: [],
          expr: {
            show: 'this.isExpand === false'
          }
        },
        {
          title: '债券类型',
          prop: 'zqlx',
          type: 'select',
          options: [],
          expr: {
            show: '!this.isExpand'
          }
        },
        {
          title: '发债期限',
          prop: 'fzqx',
          type: 'select',
          options: [],
          expr: {
            show: '!this.isExpand'
          }
        },
        {
          title: '主体层级',
          prop: 'ztcj',
          type: 'select',
          options: [],
          expr: {
            show: '!this.isExpand'
          }
        },
        {
          title: '主体评级',
          prop: 'ztpj',
          type: 'select',
          options: [],
          expr: {
            show: '!this.isExpand'
          }
        },
        {
          title: '含权债',
          prop: 'ztpj',
          type: 'select',
          options: [],
          expr: {
            show: '!this.isExpand'
          }
        },
        {
          title: '永续债',
          prop: 'ztpj',
          type: 'select',
          options: [],
          expr: {
            show: '!this.isExpand'
          }
        }
      ]
    },
    queryData() {
      console.log('222222')
    },
    expand() {
      this.isExpand = !this.isExpand
      this.isShow = false
      this.$nextTick(() => {
        this.isShow = true
      })
    }
  }
}
</script>
<style lang="scss">
.underwriterRanking {
  height: 100%;
  .el-form .el-form-item__label {
    padding-top: 12px;
  }
  .formItem {
    display: inline-block;
    width: 300px
  }
  .searchForm {
    padding: 8px 0 10px 0;
    background: #fff;
    margin-bottom: 10px;
    display: flex;
    .searchItem {
        flex: 5;
    }
    .btnItem {
        flex: 1;
        .btn {
            display: inline-block;
            margin: 8px 0 10px 10px;
        }
        .iconBtn {
            display: inline-block;
            cursor: pointer;
            margin: 8px 0 10px 10px;
            color: var(--theme--color);
        }
    }
  }
}
</style>
