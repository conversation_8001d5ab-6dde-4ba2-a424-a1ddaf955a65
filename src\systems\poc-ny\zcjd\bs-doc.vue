<template>
  <!-- POC专用 报送材料 -->
  <div class="bs-doc">
    <div class="bs-doc--left">
      <h3>报送材料</h3>
      <ul>
        <li :class="[active === 0 && 'active']" @click="active = 0"><jr-svg-icon icon-class="folder" />公开披露文件</li>
        <li :class="[active === 1 && 'active']" @click="active = 1"><jr-svg-icon icon-class="folder" />反馈意见及回函</li>
      </ul>
    </div>
    <div class="bs-doc--right">
      <template v-if="active === 0">
        <div class="item-list">
          <div class="name">
            Y-2-3中国光大银行关于河北顺德投资集团有限公司注册发行中期票据的推荐函
            <el-link @click="download('Y-2-3中国光大银行关于河北顺德投资集团有限公司注册发行中期票据的推荐函')"><jr-svg-icon icon-class="download" />初稿</el-link>
          </div>
          <div style="color: #ccc;">
            <span style="margin-right: 20px;">更新日期</span> 2024-08-20
          </div>
        </div>
        <div class="item-list">
          <div class="name">
            Y-2-4渤海银行股份有限公司关于河北顺德投资集团有限公司申请注册发行长期含权中期票据的推荐函
            <el-link @click="download('Y-2-4渤海银行股份有限公司关于河北顺德投资集团有限公司申请注册发行长期含权中期票据的推荐函')"><jr-svg-icon icon-class="download" />初稿</el-link>
          </div>
          <div style="color: #ccc;">
            <span style="margin-right: 20px;">更新日期</span> 2024-08-20
          </div>
        </div>
        <div class="item-list">
          <div class="name">
            Y-4-3河北顺德投资集团有限公司2022年度经审计的合并及母公司财务报告
            <el-link @click="download('Y-4-3河北顺德投资集团有限公司2022年度经审计的合并及母公司财务报告')"><jr-svg-icon icon-class="download" />初稿</el-link>
          </div>
          <div style="color: #ccc;">
            <span style="margin-right: 20px;">更新日期</span> 2024-08-20
          </div>
        </div>
        <div class="item-list">
          <div class="name">
            Y-2-2中信银行股份有限公司关于河北顺德投资集团有限公司申请注册发行长期限含权中期票据的推荐函
            <el-link @click="download('Y-2-2中信银行股份有限公司关于河北顺德投资集团有限公司申请注册发行长期限含权中期票据的推荐函')"><jr-svg-icon icon-class="download" />初稿</el-link>
          </div>
          <div style="color: #ccc;">
            <span style="margin-right: 20px;">更新日期</span> 2024-08-20
          </div>
        </div>
        <div class="item-list">
          <div class="name">
            Y-2-1国泰君安证券股份有限公司关于推荐河北顺德投资集团有限公司注册发行中期票据的函
            <el-link @click="download('Y-2-1国泰君安证券股份有限公司关于推荐河北顺德投资集团有限公司注册发行中期票据的函')"><jr-svg-icon icon-class="download" />初稿</el-link>
          </div>
          <div style="color: #ccc;">
            <span style="margin-right: 20px;">更新日期</span> 2024-08-20
          </div>
        </div>
        <div class="item-list">
          <div class="name">
            Y-4-1河北顺德投资集团有限公司2024年一季度合并及母公司财务报表
            <el-link @click="download('Y-4-1河北顺德投资集团有限公司2024年一季度合并及母公司财务报表')"><jr-svg-icon icon-class="download" />初稿</el-link>
          </div>
          <div style="color: #ccc;">
            <span style="margin-right: 20px;">更新日期</span> 2024-08-20
          </div>
        </div>
        <div class="item-list">
          <div class="name">
            Y-3河北顺德投咨集团有限公司2024年度第三期中期票据草集说明书
            <el-link @click="download('Y-3河北顺德投咨集团有限公司2024年度第三期中期票据草集说明书')"><jr-svg-icon icon-class="download" />初稿</el-link>
          </div>
          <div style="color: #ccc;">
            <span style="margin-right: 20px;">更新日期</span> 2024-08-20
          </div>
        </div>
      </template>
      <template v-if="active === 1">
        <div class="item-list">
          <div class="name">
            河北顺德投资集团有限公司2024年度第三期中期票据注册文件延期反馈回复申请
            <el-link @click="download('河北顺德投资集团有限公司2024年度第三期中期票据注册文件延期反馈回复申请')"><jr-svg-icon icon-class="download" />初稿</el-link>
          </div>
          <div style="color: #ccc;">
            <span style="margin-right: 20px;">更新日期</span> 2024-09-13
          </div>
        </div>
        <div class="item-list">
          <div class="name">
            关于河北顺德投资集团有限公司注册文件补充信息的函（2024年08月30日16时55分38秒）
            <el-link @click="download('关于河北顺德投资集团有限公司注册文件补充信息的函（2024年08月30日16时55分38秒）')"><jr-svg-icon icon-class="download" />初稿</el-link>
          </div>
          <div style="color: #ccc;">
            <span style="margin-right: 20px;">更新日期</span> 2024-08-30
          </div>
        </div>
      </template>
    </div>
  </div>
</template>

<script>
import { ExportFn } from '@jupiterweb/utils/api'

export default {
  props: {
    itemData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      active: 0
    }
  },
  created() {
  },
  methods: {
    download(filename) {
      ExportFn('/config/public/download/abc.docx', undefined, undefined, undefined, filename + '.docx')
    }
  }
}
</script>

<style lang="scss" scoped>
.bs-doc {
  display: flex;
  width: 100%;
  height: 100%;
  &--left {
    width: 200px;
    height: 100%;
    background: #f5f5f5;
    h3 {
      font-size: 16px;
      font-weight: 500;
      padding: 10px;
      margin-bottom: 0;
    }
    ul {
      padding: 0;
      margin: 0;
      li {
        list-style: none;
        line-height: 30px;
        padding: 0 10px;
        display: flex;
        align-items: center;
        cursor: pointer;
        .jr-svg-icon {
          margin-right: 10px;
        }
        &.active {
          background-color: #fff;
        }
      }
    }
  }
  &--right {
    width: calc(100% - 200px);
    padding: 20px;
    flex: 1;
    height: 100%;
    border: 1px solid #e8e8e8;
    border-left: none;
    .item-list {
      padding: 10px 0;
      border-bottom: 1px solid #e8e8e8;
    }
    overflow: auto;
  }
  .name {
    display: flex;
    justify-content: space-between;
    font-size: 14px;
  }
}
</style>
