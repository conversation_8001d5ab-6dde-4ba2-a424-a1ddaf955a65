<template>
  <div class="calendarCenter">
    <cockpitHeader title="日历中心" :style="{ width: px2vw(362), height: px2vh(40) }" type="light" />
    <cockpitTabs :tabs="[]" more-title="日历中心" :style="{ marginTop: px2vh(16) }" @handleMore="openCalendar" />
    <div class="calendarCenter-calendar">
      <div class="calendarCenter-calendar-weekday">
        <span v-for="(weekday, index) in weekdays" :key="index">{{ weekday }}</span>
      </div>
      <div class="calendarCenter-calendar-switch">
        <img src="@/assets/cockpit/calendarLeft.png" alt="" @click.stop="handlePrevOrNext('prev')"/>
        <span>{{ computedMonth }}</span>
        <img src="@/assets/cockpit/calendarRight.png" alt="" @click.stop="handlePrevOrNext('next')"/>
      </div>
      <div class="calendarCenter-calendar-dates">
        <div
          v-for="(day, index) in nowWeek"
          :key="index"
          class="calendarCenter-calendar-dates-date"
          :class="activeDay === day.date ? 'calendarCenter-calendar-dates-active' : ''"
          @click.stop="selectDate(day)"
        >
          <p>{{ day.date | dayFormat }}</p>
          <span class="calendarCenter-calendar-dates-date-colors">
            <span v-for="(color, index) in day.colors" :key="index" :style="{ backgroundColor: color }" />
          </span>
          <span v-if="today === day.date" class="calendarCenter-calendar-dates-date-today">今</span>
        </div>
      </div>
    </div>
    <div class="calendarCenter-content">
      <div class="calendarCenter-content-inner" v-for="(data,index) in activeData" :key="index">
        <p>
          <span :style="{backgroundColor: data.color}"/>
          <span :style="{color: data.color}">{{ data.title | titleFormat }}</span>
        </p>
        <p>{{ data.text }}</p>
      </div>
    </div>
    <calendarCenterDate ref="calendarCenterDate" @handleOpen="handleOpen" />
    <calendarCenterDetail ref="calendarCenterDetail"/>
  </div>
</template>

<script>
import cockpitHeader from '../../components/cockpit-header.vue'
import cockpitTabs from '../../components/cockpit-tabs.vue'
import calendarCenterDate from './calendar-center-date.vue'
import calendarCenterDetail from './calendar-center-detail.vue'
import { cockpitGetCalendarListByWeek } from '@/api/cockpit/cockpit.js'
import store from '@jupiterweb/store'
import moment from 'moment'
import { px2vw, px2vh } from '../../utils/portcss'
export default {
  name: 'CalendarCenter',
  components: {
    cockpitHeader,
    cockpitTabs,
    calendarCenterDate,
    calendarCenterDetail
  },
  data() {
    return {
      weekdays: ['一', '二', '三', '四', '五', '六', '日'],
      colors: [
        {
          color: '#5B8FF9',
          text: '债券付息兑付',
          isHidden: value => {
            if (value instanceof Array && value.length > 0) {
              return true
            } else {
              return false
            }
          },
          handleData: arr => {
            return arr.map((item) => {
              const textArr = [];
              ['sInfoName', 'bIssueAmountact', 'couponRate', 'term'].forEach(key=>{
                if(item[key]){
                  textArr.push(item[key])
                }
              })
              return {
                color: '#5B8FF9',
                title: '债券付息兑付',
                text: textArr.join(",")
              }
            })
          }
        },
        {
          color: '#269A99',
          text: '我司新券发行',
          isHidden: (value) => {
            if (value instanceof Array && value.length > 0) {
              return true
            } else {
              return false
            }
          },
          handleData: arr => {
            return arr.map((item) => {
              const textArr = [];
              ['sInfoName', 'bIssueAmountact', 'latestCouponrate', 'term'].forEach(key=>{
                if(item[key]){
                  textArr.push(item[key])
                }
              })
              return {
                color: '#269A99',
                title: '我司新券发行',
                text: textArr.join(",")
              }
            })
          }
        },
        {
          color: '#E1B01E',
          text: '对标企业新券发行',
          isHidden: (value) => {
            if (value instanceof Array && value.length > 0) {
              return true
            } else {
              return false
            }
          },
          handleData: arr => {
            return arr.map((item) => {
              const textArr = [];
              ['sInfoName', 'bIssueAmountact', 'latestCouponrate', 'term'].forEach(key=>{
                if(item[key]){
                  textArr.push(item[key])
                }
              })
              return {
                color: '#E1B01E',
                title: '对标企业新券发行',
                text: textArr.join(",")
              }
            })
          }
        },
        {
          color: '#E8684A',
          text: '行权日',
          isHidden: (value) => {
            return value
          },
          handleData: () => {
            return [
              {
                color: '#E8684A',
                title: '行权日',
                text: ''
              }
            ]
          }
        },
        {
          color: '#9C75DD',
          text: '持有人会议',
          isHidden: (value) => {
            return false
          },
          handleData: arr => {
            return arr.map((item) => {
              return {
                color: '#9C75DD',
                text: '持有人会议',
                text: ''
              }
            })
          }
        }
      ],
      today: '',
      activeDay: '',
      nowWeek: [],
      activeData: []
    }
  },
  computed:{
    computedMonth(){
      return moment(+new Date(this.activeDay)).format("YYYY年MM月")
    }
  },
  created() {
    this.initToday()
  },
  filters: {
    dayFormat(val) {
      return moment(val).format('DD')
    },
    titleFormat(val){
      if(store.getters.sysVersion === 'group' && val === '我司新券发行'){
        return '集团新券发行'
      }else{
        return val
      }
    }
  },
  methods: {
    px2vw,
    px2vh,
    /**
     * 初始化当前日期
     */
    initToday() {
      this.today = moment(+new Date()).format('YYYY-MM-DD')
      this.activeDay = this.today
      this.initNowWeek(this.today)
    },
    /**
     * 生成当前展示周
     */
    initNowWeek(computedDay) {
      this.nowWeek = []
      const day = moment(computedDay).isoWeekday()
      for (let i = 1; i <= 7; i++) {
        this.nowWeek.push({
          date: moment(+new Date(computedDay) - (day - i) * 60 * 60 * 24 * 1000).format('YYYY-MM-DD'),
          value: {}
        })
      }
      this.getCalendarListDataApi(
        moment(this.nowWeek[0].date).format('YYYYMMDD'),
        moment(this.nowWeek[this.nowWeek.length - 1].date).format('YYYYMMDD')
      )
    },
    /**
     * 获取具体日期，或者整个月份的日历数据
     */
    async getCalendarListDataApi(startDate, endDate) {
      const data = await cockpitGetCalendarListByWeek(startDate, endDate)
      this.putDataInNowWeek(data)
    },
    /**
     * 将数据添加进nowWeek中
     */
    putDataInNowWeek(data) {
      this.nowWeek = this.nowWeek.map((item) => {
        const value = data[item.date]
        return {
          date: item.date,
          value: value || {},
          colors: this.colors.reduce((pre, current) => {
            if (current.isHidden(value[current.text])) {
              pre.push(current.color)
            }
            return pre
          }, [])
        }
      })
      this.initActiveData()
    },
    /**
     * 生成下方数据展示
     */
    initActiveData() {
      const value = this.nowWeek.find(item=>item.date === this.activeDay).value
      this.activeData = this.colors.reduce((pre,current)=>{
        if(current.isHidden(value[current.text])){
          pre.push(...current.handleData(value[current.text]))
        }
        return pre
      },[])
    },
    /**
     * 切换上周或下周
     */
    handlePrevOrNext(flag){
      if(flag === 'prev'){
        this.initNowWeek(+new Date(this.nowWeek[0].date) - 60 * 60 * 24 * 1000)
      }else{
        this.initNowWeek(+new Date(this.nowWeek[this.nowWeek.length - 1].date) + 60 * 60 * 24 * 1000)
      }
      this.activeDay = this.nowWeek[0].date
    },
    /**
     * 选中当前日期
     */
    selectDate(day){
      this.activeDay = day.date
      this.initActiveData()
    },
    /**
     * 打开日历中心
     */
    openCalendar() {
      this.$refs.calendarCenterDate.open()
    },
    /**
     * 打开日历详情
     */
    handleOpen(data){
      this.$refs.calendarCenterDetail.open(data)
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~assets/styles/portcss.scss';
.calendarCenter {
  width: 100%;
  height: vh(436);
  background: rgba(255, 255, 255, 0.5);
  box-shadow: 0px 4px 20px -2px rgba(0, 0, 0, 0.06);
  border: vh(2) solid transparent;
  border-radius: vh(12);
  background-clip: padding-box, border-box;
  background-origin: padding-box, border-box;
  background-image: linear-gradient(to right, rgba(255, 255, 255, 1), rgba(255, 255, 255, 0.5)),
    radial-gradient(rgba(255, 255, 255, 1), rgba(255, 255, 255, 0.5));
  padding: 0px vw(16);
  display: flex;
  flex-direction: column;
  div {
    flex-shrink: 0;
  }
  &-calendar {
    width: 100%;
    &-weekday {
      width: 100%;
      display: flex;
      height: vh(22);
      gap: vw(5);
      margin-top: vh(16);
      & > span {
        width: vw(59);
        height: vh(22);
        font-weight: 600;
        font-size: vh(16);
        color: rgba(0,0,0,0.6);
        line-height: vh(22);
        text-align: center;
      }
    }
    &-switch {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: vh(14);
      height: vh(29);
      img {
        width: vw(28);
        height: vh(29);
        cursor: pointer;
      }
      span {
        height: vh(22);
        font-weight: 600;
        font-size: vh(14);
        color: rgba(0,0,0,0.9);
        line-height: vh(22);
      }
    }
    &-dates {
      width: 100%;
      height: vh(60);
      margin-top: vh(9);
      display: flex;
      justify-content: space-between;
      &-date {
        width: vw(59);
        height: 100%;
        background: linear-gradient( 180deg, #FFE8D1 0%, #FAF8F6 100%);
        box-shadow: 0px 4px 20px -2px rgba(0,0,0,0.06);
        border: vh(2) solid transparent;
        border-radius: vh(6);
        background-clip: padding-box, border-box;
        background-origin: padding-box, border-box;
        background-image: linear-gradient(180deg, #ffe8d1 0%, #faf8f6 100%),
          radial-gradient(rgba(255, 255, 255, 1), rgba(255, 255, 255, 0.5));
        padding-top: vh(12);
        padding-left: vw(5);
        padding-right: vw(4);
        cursor: pointer;
        position: relative;
        & > p {
          width: 100%;
          height: vh(24);
          font-weight: 600;
          font-size: vh(16);
          line-height: vh(24);
          color: rgba(0,0,0,0.9);
          text-align: center;
          margin-bottom: 0px;
        }
        &-colors {
          width: 100%;
          margin-top: vh(7);
          display: flex;
          justify-content: space-around;
          span {
            width: 5px;
            height: 5px;
            background: #5b8ff9;
            border-radius: 50%;
          }
        }
        &-today {
          width: vw(18);
          height: vh(18);
          background: linear-gradient(180deg, #ffdba4 0%, #fcb03b 100%);
          border-radius: 0px 5px 0px 5px;
          position: absolute;
          top: 0px;
          right: 0px;
          color: #ffffff;
          font-size: vh(12);
          line-height: vh(18);
          text-align: center;
        }
      }
      &-active {
        background-image: linear-gradient(180deg, #ffe8d1 0%, #faf8f6 100%),
          radial-gradient(#FED3AF, #FED3AF);
      }
    }
  }
  &-content {
    width: 100%;
    height: vh(195);
    margin-top: vh(16);
    overflow: hidden;
    &-inner {
      width: 100%;
      height: vh(63);
      background: linear-gradient( 180deg, #FFE8D1 0%, #FAF8F6 100%);
      box-shadow: 0px 4px 20px -2px rgba(0,0,0,0.06);
      border: vh(2) solid transparent;
      border-radius: vh(12);
      background-clip: padding-box, border-box;
      background-origin: padding-box, border-box;
      background-image: linear-gradient(180deg, #ffe8d1 0%, #faf8f6 100%),
        radial-gradient(rgba(255, 255, 255, 1), rgba(255, 255, 255, 0.5));
      padding-top: vh(12);
      padding-left: vw(17);
      margin-bottom: vh(8);
      p {
        margin-bottom: 0;
      }
      & > p:nth-of-type(1) {
        display: flex;
        align-items: center;
        gap: vw(5);
        margin-bottom: vh(3);
        & > span:nth-of-type(1) {
          width: 5px;
          height: 5px;
          background: #5b8ff9;
          border: 1px solid #ffffff;
          border-radius: 5px;
        }
        & > span:nth-of-type(2) {
          height: vh(20);
          font-weight: 400;
          font-size: vh(14);
          line-height: vh(20);
          color: #5b8ff9;
        }
      }
      & > p:nth-of-type(2) {
        width: 100%;
        height: vh(20);
        line-height: vh(20);
        font-weight: 400;
        font-size: vh(14);
        color: rgba(0,0,0,0.6);
      }
    }
  }
  &-content:hover {
    overflow-y: scroll;
  }
}
</style>
