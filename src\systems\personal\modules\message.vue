<template>
  <div class="message-manage-container">
    <div class="message-manage-left">
      <el-menu
        class="jr-sidebar--menu"
        default-active="message"
        :default-openeds="defaultOpeneds"
        @select="handleSelect"
      >
        <el-menu-item index="message">
          <img src="~@/assets/images/personal/message.png" alt="短信接收设置">
          <span>短信接收设置</span>
        </el-menu-item>
        <el-submenu index="frequency">
          <template slot="title">
            <img src="~@/assets/images/personal/freq.png" alt="信息频率设置">
            <span>信息频率设置</span>
          </template>
          <el-menu-item v-for="item in frequencyConfig" :key="item.id" :index="`${item.id}`">
            {{ item.messageName }}
          </el-menu-item>
        </el-submenu>
      </el-menu>
    </div>
    <div v-show="activeMenu === 'message'" class="message-manage-right">
      <div class="message-manage-right-header">
        <div class="message-manage-right-title">
          <span>短信接收设置</span>
        </div>
        <div class="message-manage-right-header-right">
          短信总开关
          <el-switch
            v-model="messageReceiveConfig.isMsg"
            active-value="Y"
            inactive-value="N"
            @change="handleSwitchChange"
          />
        </div>
      </div>
      <div class="message-manage-right-content">
        <div
          v-for="(item, idx) in messageReceiveConfig.configList"
          :key="item.id"
          class="message-manage-right-content-item"
        >
          <div class="message-manage-right-content-item-label">
            <span>{{ item.messageName }}</span>
          </div>
          <el-switch
            v-model="messageReceiveConfig.configList[idx].isMsg"
            active-value="Y"
            inactive-value="N"
            @change="handleSaveMessageConfig"
          />
        </div>
      </div>
    </div>
    <template v-show="activeMenu === 'frequency'">
      <MessageFrequency
        v-for="item in frequencyConfig"
        v-show="subActiveMenu === item.id"
        :key="item.id"
        :bond-type-option="bondTypeOption"
        :msg-item="item"
      />
    </template>
  </div>
</template>

<script>
import { getMessageReceiveConfig, getBondTypeOption } from '@/api/personal'
import { SaveReceiveConfig } from '@jupiterweb/api/config-platform/message'
import MessageFrequency from './message-frequency.vue'
export default {
  components: {
    MessageFrequency
  },
  data() {
    return {
      activeMenu: 'message',
      subActiveMenu: '',
      defaultOpeneds: ['frequency'],
      messageReceiveConfig: {},
      frequencyConfig: [],
      bondTypeOption: []
    }
  },
  created() {
    this.init()
    this.getBondTypeOption()
  },
  methods: {
    async init() {
      const res = await getMessageReceiveConfig()
      this.messageReceiveConfig = res?.[0] ?? {}
      this.messageReceiveConfig.configList.forEach((item) => {
        this.$set(item, 'isMsg', item.sysSubscribe?.isMsg ?? 'N')
      })
      if (this.messageReceiveConfig.configList.every((item) => item.isMsg === 'Y')) {
        this.messageReceiveConfig.isMsg = 'Y'
      }
      this.frequencyConfig = this.messageReceiveConfig?.configList?.filter?.((item) => item?.isPush === 'Y')
    },
    async getBondTypeOption() {
      const res = await getBondTypeOption()
      this.bondTypeOption = res
    },
    handleSwitchChange(val) {
      this.messageReceiveConfig.configList.forEach((item) => {
        item.isMsg = val
      })
      this.$nextTick(() => {
        this.handleSaveMessageConfig()
      })
    },
    handleSaveMessageConfig() {
      const self = this
      const list = self.messageReceiveConfig.configList
        .filter((item) => item.isMsg === 'Y')
        .map((item) => ({
          ...item,
          configId: item.id
        }))
      SaveReceiveConfig(list, (isSuccess) => {
        if (isSuccess) {
          // self.msgSuccess('保存成功')
          self.init()
        }
      })
    },
    handleSelect(key, keyPath) {
      this.activeMenu = keyPath[0]
      this.subActiveMenu = keyPath[1]
    }
  }
}
</script>

<style lang="scss">
.message-manage-container {
  --jr-sidebar-bg-color: var(--el-fill-color-blank);
  --jr-sidebar-text-color: rgba(0, 0, 0, 0.9);
  width: 100%;
  height: 100%;
  display: flex;
  .el-submenu__icon-arrow.el-icon-arrow-down {
    &::before {
      content: '';
      display: inline-block;
      width: 16px;
      height: 16px;
      background: url('~@/assets/images/personal/arrow-right.png') no-repeat center center;
    }
  }
  .el-submenu.is-opened>.el-submenu__title .el-submenu__icon-arrow {
    transform: rotateZ(-90deg);
  }
  .message-manage-left {
    width: 266px;
    height: 100%;
    padding-left: var(--el-padding-left-right);
    padding-bottom: 28px;
    .jr-sidebar--menu.el-menu > .el-submenu.is-opened .el-menu,
    .jr-sidebar--menu.el-menu:not(.el-menu--horizontal) > .el-submenu.is-opened > .el-submenu__title {
      background: transparent !important;
    }
    & > .el-menu {
      background: #f6f8fd;
      border-radius: 10px;
      border-right: none;
      height: calc(100%);
      & > .el-menu-item.is-active {
        color: var(--theme-mix-color) !important;
      }
      &>.el-menu-item > span,
      .el-submenu__title > span {
        margin-left: 12px;
      }
    }
  }
  .message-manage-right {
    width: calc(100% - 266px);
    height: 100%;
    box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.1);
    .message-manage-right-header {
      display: flex;
      justify-content: space-between;
      padding-left: 32px;
      padding-right: 48px;
      align-items: center;
      height: 60px;
      border-bottom: 1px solid #eae9e9;
      .message-manage-right-title {
        // font-size: var(--el-font-size-extra-large);
        color: #000000;
        font-weight: 600;
      }
      .message-manage-right-header-right {
        display: inline-flex;
        align-items: center;
        // font-size: var(--el-font-size-large);
        .el-switch {
          margin-left: 10px;
        }
      }
    }
    .message-manage-right-content {
      padding-left: 32px;
      padding-right: 48px;
      padding-bottom: 60px;
      height: calc(100% - 60px);
      overflow: auto;
      display: flex;
      flex-wrap: wrap;
      align-content: flex-start;
      & > .message-manage-right-content-item {
        margin-top: 32px;
        display: flex;
        width: 50%;
        align-items: center;
        .message-manage-right-content-item-label {
          // font-size: var(--el-font-size-large);
          width: 249px;
        }
      }
    }
  }
}
</style>
