<template>
  <div class="quota-switching">
    <cockpit-header 
      :style="{ width:px2vw(362),height:px2vh(40) }" 
    >
      <div class="quota-switching-title">
        <span>{{ computedTitle }}</span>
        <el-tooltip effect="dark" placement="bottom-start" :teleported="false" v-if="computedTitle === '额度管理'">
          <div class="toolTip">
            <img src="@/assets/cockpit/info_pro_circle.png" alt="" :style="{width:px2vh(16),height:px2vh(17),marginLeft:px2vw(4)}">
          </div>
          <div slot="content">
            <p>有效注册通知书张数：仅统计余额大于零的未到期注册通知书张数；</p>
            <p>有效剩余额度：统计集团成员有效注册通知书中企业债、协会债、公司债剩余额度情况，TDFI/DFI因无总额不纳入有效剩余额度统计；</p>
          </div>
        </el-tooltip>
        <jr-svg-icon 
          icon-class="swap" 
          :style="{width: px2vw(12),height:px2vh(13),cursor:'pointer',marginLeft:px2vw(8)}"
          @click="changeStatus"
        />
      </div>
    </cockpit-header>
    <div class="quota-switching-component">
      <registrationAmount v-if="computedTitle === '注册额度'" />
      <quotaManage v-if="computedTitle === '额度管理'" />
      <channelProject v-if="computedTitle === '通道中项目'" />
    </div>
  </div>
</template>

<script>
import cockpitHeader from '../../components/cockpit-header.vue';
import registrationAmount from './registration-amount.vue';
import channelProject from './channel-project.vue';
import quotaManage from './quota-manage.vue';
import { px2vw,px2vh } from '../../utils/portcss';
export default {
  name:"QuotaSwitching",
  components:{
    cockpitHeader,
    registrationAmount,
    channelProject,
    quotaManage
  },
  data() {
    return {
      active: false
    }
  },
  computed:{
    isCompany(){
      return this.$store.getters.sysVersion === 'company'
    },
    computedTitle(){
      if(this.active){
        return '通道中项目'
      }else if(this.isCompany){
        return '注册额度'
      }else{
        return '额度管理'
      }
    }
  },
  methods:{
    px2vw,
    px2vh,
    /**
     * 切换为通道中项目
     */
    changeStatus(){
      this.active = !this.active
    }
  },
}
</script>

<style lang="scss" scoped>
@import '~assets/styles/portcss.scss';
.quota-switching{ 
  width: 100%;
  height: vh(446);
  background-image: url("../../../../assets/cockpit/cockpit_pro_normal_bac.png");
  background-size: 100% 100%;
  padding: vh(8) vw(8);
  &-component {
    width: 100%;
    height: vh(396);
  }
  &-title {
    height: vh(19);
    font-size: vh(14);
    color: #e6f6ff;
    line-height: vh(19);
    display: flex;
    align-items: center;
  }
}
</style>