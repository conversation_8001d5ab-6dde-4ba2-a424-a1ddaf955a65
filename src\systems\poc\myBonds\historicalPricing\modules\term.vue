<template>
  <div class="page-market-rate--term">
    <div class="page-market-rate--term-title">
      <span>期限</span>
    </div>
    <div class="page-market-rate--term-content">
      <jr-checkbox-group v-model="form.termSelected" :data="termList" />
      <img class="img1" src="../../../../../assets/1736665391987.png" alt="描述文字">
      <img class="img2" src="../../../../../assets/1736665417603.jpg" alt="描述文字">
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      termList: [{
        text: '3Y,公募,不可续期',
        value: '3Y'
      }, {
        text: '9M,公募,不可续期',
        value: '9M'
      }],
      form: {
        termSelected: ['3Y', '9M']
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.page-market-rate--term {
  width: 500px;
  height: 100%;
  padding: 0 12px;
  background-color: #f5f5f5;
  overflow: auto;
  .page-market-rate--term-title {
    display: flex;
    line-height: 40px;
    justify-content: space-between;
    align-items: center;
    span {
      font-size: 14px;
    }
  }
  .color-warning {
    color: var(--el-theme-color-warning);
  }
  .page-market-rate--term-content {
    height: calc(100% - 50px);
    width: 100%;
    min-width: 268px;
    overflow-x: hidden;
    position: relative;
    ::v-deep.el-checkbox {
      display: inline-flex;
      flex-direction: row-reverse;
      justify-content: space-between;
      background: #fff;
      margin: 0 10px 10px 0px;
      padding: 6px 10px;
      width: 100%;
      align-items: center;
    }
    ::v-deep .jr-checkbox-group > label:nth-child(2n) {
      margin-right: 0;
    }
    ::v-deep .el-checkbox__label {
      margin-left: 20px;
    }
    .img1 {
      position: absolute;
      top: 5px;
      width: 30px;
      height: 20px;
    }
    .img2 {
      position: absolute;
      top: 48px;
      width: 30px;
      height: 20px;
    }
  }
}
</style>
