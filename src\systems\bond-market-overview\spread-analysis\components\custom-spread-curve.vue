<template>
  <div class="template-module-chart">
    <div class="flex template-module-chart-header">
      <div class="flex" style="gap: 8px">
        <jr-svg-icon icon-class="big-title" color="#E6A23C" />
        <span class="template-module-chart-header-text" style="font-weight: 700">{{ chartTitle }}</span>
      </div>

      <el-button style="height: 32px" @click="exportTableToImage">
        <jr-svg-icon icon-class="upload" />
        导出
      </el-button>
    </div>
    <Echart id="spreadCurve" ref="componentsCharts" :options="options" :styles="{ height: height + 'px' }" />
  </div>
</template>
<script>
import Echart from '@jupiterweb/components/echarts'
import * as echarts from 'echarts'
export default {
  components: {
    Echart
  },
  props: {
    height: {
      type: Number,
      default() {
        return 400
      }
    },
    chartdata: {
      type: Object,
      default: () => ({})
    },
    title: {
      type: String,
      default() {
        return ''
      }
    },
    chartTitle: {
      type: String,
      default() {
        return '利差曲线'
      }
    }
  },
  data() {
    return {
      options: {}
    }
  },
  computed: {
    chartOptions() {
      const data = JSON.stringify({ ...this.chartdata })

      return data
    }
  },
  watch: {
    chartOptions(newVal, oldVal) {
      console.log('发生了变化')
      this.init()
    }
  },

  created() {
    this.init()
  },
  methods: {
    exportTableToImage(v) {
      // 下载图片
      const exportOptions = {
        backgroundColor: '#ffffff',
        pixelRatio: 2 // 提高导出清晰度（可选）
      }
      // // 生成图片 URL 并触发下载
      const chart = this.$refs.componentsCharts.myChart

      const imgUrl = chart.getDataURL(exportOptions)
      const link = document.createElement('a')
      link.href = imgUrl
      link.download = `${this.title}.png`
      link.click()
    },
    init() {
      this.$nextTick(() => {
        this.options = this.chartdata
        // this.$refs.componentsCharts.chartResize()
        console.log(this.$refs.componentsCharts)
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.template-module-chart {
  height: 100% !important;
  min-height: 0px !important;
  &-header {
    justify-content: space-between;
    height: 64px;
    padding: 21px 16px;
    display: flex;

    &-text {
      height: 22px;
      font-family: MicrosoftYaHeiSemibold;
      font-size: var(--el-font-size-base);
      color: rgba(0, 0, 0, 0.85);
      line-height: 22px;
      text-align: left;
      font-style: normal;
    }
  }
}
</style>
