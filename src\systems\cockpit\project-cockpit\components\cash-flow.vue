<template>
  <div class="cash-flow">
    <cockpit-header 
      :style="{ width: px2vw(362), height: px2vh(40) }" 
      title="未来偿债现金流" 
    />
    <div class="cash-flow-select">
      <p>
        <span>应付现金流总额(亿元)：</span>
        <span>{{ ( amount || '0' ) | amountFormat}}</span>
      </p>
      <cockpitSelect
        v-model="params.statDimension"
        :style="{ width: px2vw(116), flexShrink: 0 }"
        :options="statDimensionOptions"
        :defaultValue="'1'"
        :type="'deep'"
        @change="selectChange"
      />
    </div>
    <div class="cash-flow-chartArea">
      <echarts :options="options" style="width: 100%; height: 100%" />
    </div>
  </div>
</template>

<script>
import cockpitHeader from '../../components/cockpit-header.vue'
import cockpitSelect from '../../components/cockpit-select.vue'
import echarts from '@jupiterweb/components/echarts'
import { cockpitGetCashFlowAnalysisList,cockpitGetCashFlowPrincipalInterestList } from "@/api/cockpit/cockpit.js"
import * as echartsInstance from 'echarts'
import moment from "moment"
import { px2vw, px2vh } from '../../utils/portcss'
import { ConvertAmount } from '@jupiterweb/utils/common.js'
export default {
  name: 'CashFlow',
  components: {
    cockpitHeader,
    cockpitSelect,
    echarts
  },
  filters: {
    amountFormat(val){
      if(typeof val === 'number'){
        return ConvertAmount('HMU',val * 1,1,4)
      }
      return val
    }
  },
  data() {
    return {
      value: '',
      options: {},
      params: {
        queryFlag: 3,
        statDimension: "1",
        startTime: moment(Date.now()).format('YYYY-MM-DD'),
      },
      amount: '',
      statDimensionOptions: [
        { label: '以行权计', value: '1' },
        { label: '以到期计', value: '2' },
      ]
    }
  },
  created(){
    this.getCashFlowListSumDataApi()
    this.getCashFlowEchartDataApi()
  },
  methods: {
    px2vw,
    px2vh,
    ConvertAmount,
    /**
     * 初始化图表
     */
    initChart(data) {
      const legendData = data.map(item=>item.type)
      const xData = []
      data.forEach(item=>{
        if(!xData.includes(item.payDate)){
          xData.push(item.payDate)
        }
      })
      const principalData = xData.reduce((pre,current)=>{
        const tempArr = data.filter(item=>item.payDate === current && item.type === '本金')
        if(tempArr.length){
          pre.push(tempArr[0].amount)
        }else{
          pre.push(0)
        }
        return pre
      },[])
      const interestData = xData.reduce((pre,current,index)=>{ 
        const tempArr = data.filter(item=>item.payDate === current && item.type === '利息')
        if(tempArr.length){
          pre.push(tempArr[0].amount)
        }else{
          pre.push(0)
        }
        return pre
      },[])
      let option = {
        color: ['#0E94F8', '#5AD8A6'],
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          backgroundColor: '#ffffff',
          borderColor: '#00AEFF',
          borderWidth: 1,
          textStyle: {
            color: '#000000',
            fontSize: 12
          },
          formatter: (params) => {
            let result = `<div style="margin-bottom: 5px; font-weight: bold;">${params[0].axisValue}</div>`
            let total = 0
            
            params.forEach(item => {
              const value = item.value || 0
              total += value
              const formattedValue = this.ConvertAmount('HMU', value, 1, 4)
              result += `<div style="margin: 2px 0;">
                <span style="display: inline-block; width: 8px; height: 8px; background: ${item.color}; border-radius: 50%; margin-right: 6px;"></span>
                ${item.seriesName}: ${formattedValue}亿
              </div>`
            })
            
            const formattedTotal = this.ConvertAmount('HMU', total, 1, 4)
            result += `<div style="margin-top: 8px; padding-top: 5px; border-top: 1px solid #00AEFF; font-weight: bold;">
              合计: ${formattedTotal}亿
            </div>`
            
            return result
          }
        },
        legend: {
          data: legendData,
          itemGap: 15,
          itemWidth: 6,
          itemHeight: 6,
          symbolRotate: 45,
          top: '0',
          right: '0',
          orient: 'horizontal',
          textStyle: {
            color: '#C7D4D9'
          }
        },
        toolbox: {
          show: false
        },
        grid: {
          left: '8%',
          right: '4%',
          bottom: '15%',
          top: '12%',
          containLabel: true
        },
        xAxis: {
          type: "category",
          data: xData,
          axisLine: {
            lineStyle: {
              color: '#00AEFF'
            }
          },
          axisTick: {
              show: false,
          },
          axisLabel: {
            show: true,
            color: '#C7D4D9',
            interval: 0
          },
        },
        yAxis: 
        {
          type: 'value',
          name: '单位：亿',
          axisLabel: {
            color: '#C7D4D9',
            formatter: function (params) {
              return params
            }
          },
          splitLine: {
            show:true,
            lineStyle: {
              type: 'dashed',
              color: 'rgba(125,193,224,0.2)'
            }
          },
          nameTextStyle: {
            color: '#C7D4D9',
            align: 'right',
          },
        },

        series: [
          {
            name: '本金',
            type: 'bar',
            stack: 'a',
            showBackground: false,
            itemStyle: {
                normal: {
                    color: new echartsInstance.graphic.LinearGradient(0, 0, 0, 1, [
                        '#5D90F9', '#92BFFC'
                    ].map((color, offset) => ({
                        color,
                        offset
                    }))), // 渐变
                },
            },
            label: {
                normal: {
                    color: '#000',
                    show: false,
                    position: 'top',
                },
            },
            barWidth: 40,
            data: principalData
          },
          {
            name: '利息',
            type: 'bar',
            stack: 'a',
            showBackground: false,
            itemStyle: {
                normal: {
                    color: new echartsInstance.graphic.LinearGradient(0, 0, 0, 1, [
                        '#1DBAB0', '#7EC0D3'
                    ].map((color, offset) => ({
                        color,
                        offset
                    }))), // 渐变
                },
            },
            label: {
                normal: {
                    color: '#000',
                    show: false,
                    position: 'top',
                },
            },
            barWidth: 40,
            data: interestData
          },
        ]
      }
      this.options = option
    },
    /**
     * 获取总和数据
     */
    async getCashFlowListSumDataApi() {
      const data = await cockpitGetCashFlowAnalysisList({...this.params})
      this.amount = data.amount
    },
    /**
     * 获取图表数据
     */
    async getCashFlowEchartDataApi() {
      const data = await cockpitGetCashFlowPrincipalInterestList({...this.params})
      this.initChart(data)
    },
    /**
     * select切换
     */
    selectChange(){
      this.getCashFlowListSumDataApi()
      this.getCashFlowEchartDataApi()
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~assets/styles/portcss.scss';
.cash-flow {
  width: 100%;
  height: vh(446);
  background-image: url("../../../../assets/cockpit/cockpit_pro_normal_bac.png");
  background-size: 100% 100%;
  padding: vh(8) vw(25) vh(8) vw(23);
  &-select {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: vh(16);
    p {
      margin-bottom: 0px;
      display: flex;
      align-items: center;
      & > span:nth-of-type(1) {
        height: vh(20);
        font-weight: 400;
        font-size: vh(14);
        color: #D5E2E8;
        line-height: vh(20);
      }
      & > span:nth-of-type(2) {
        height: vh(20);
        font-weight: 400;
        font-size: vh(16);
        color: #2CE5F4;
        line-height: vh(20);
      }
    }
  }
  &-chartArea {
    width: 100%;
    height: vh(348);
    margin-top: vh(30);
  }
}
</style>
