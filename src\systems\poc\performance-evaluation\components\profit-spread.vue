<!--
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2024-01-12 15:36:09
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2024-01-13 16:49:36
 * @Description: 历史收益分布
-->
<template>
  <div>
    <h2>历史收益分布</h2>
    <div class="float-form">
      <jr-form-item-create :model="searchForm" :data="searchConfig" :column="1" />
      <el-button type="primary" @click="handleQuery">查询</el-button>
    </div>
    <TemplateModule style="height: 300px;" :detail="{packageId: '20c863e5f55a439ea55e0c1d993e7494'}" :params="{ ...params, ...queryParams }" chart-type="TAB" v-bind="$attrs" />
  </div>
</template>

<script>
import TemplateModule from '@jupiterweb/components/template-module'
export default {
  components: {
    TemplateModule
  },
  props: {
    params: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      queryParams: {
        splitRange: '0.5%'
      },
      searchForm: {
        splitRange: '0.5%'
      },
      searchConfig: [
        {
          prop: 'splitRange',
          title: '切分间隔',
          type: 'select',
          options: ['0.5%', '1%', '2%', '3%', '4%', '5%', '10%'].map(a => ({ text: a, value: a }))
        }
      ]
    }
  },
  methods: {
    handleQuery() {
      Object.assign(this.queryParams, {
        ...this.searchForm
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.float-form {
  display: flex;
  justify-content: space-between;
  position: absolute;
  left: 70px;
  top: 44px;
  width: 200px;
  height: 28px;
  z-index: 999;
  ::v-deep .el-form-item__label {
    white-space: nowrap !important;
  }
  .el-button {
    margin-left: 10px;
  }
}
</style>

