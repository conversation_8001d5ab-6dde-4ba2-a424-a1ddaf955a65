<template>
  <jr-layout-horizontal>
    <template v-slot:left>
      <flat-index :query="handleQuery" :reset="handleReset" :pagination="configTable.pagination">
        <template v-slot:form>
          <el-form ref="elForm" :model="configForm.model">
            <jr-form-item-create :validate-rules="validateRules" :column="0" :data="configForm.data" :model="configForm.model" />
          </el-form>
        </template>

        <template v-slot:right-button>
          <el-button>
            <jr-svg-icon icon-class="export" />
          </el-button>
        </template>

        <template v-slot:table-list="{ height }">
          <jr-table
            :height="height"
            :columns="configTable.columns"
            :data-source="configTable.data"
            :loading="configTable.loading"
            :pagination="configTable.pagination"
            :on-change="handleQuery"
            border
          >
            <template v-slot:index>
              <el-table-column
                type="index"
                width="50px"
                align="center"
                :label="InitialMessage('common.columns.index')"
              >
                <template slot-scope="scope">
                  <span>{{ (configTable.pagination.pageNo - 1) * configTable.pagination.pageSize + scope.$index +1 }}</span>
                </template>
              </el-table-column>
            </template>
          </jr-table>
        </template>
      </flat-index>
    </template>

    <template v-slot:right>
      <div class="flex-row">
        <Chart style="width: calc(100% - 300px);" />
      </div>
    </template>
  </jr-layout-horizontal>
</template>

<script>
import * as API from '@/api/demo/flat-index'
import Chart from './modules/chart.vue'

import { getModalProps } from '@/systems/mixins'
export default {
  components: { Chart },
  mixins: [getModalProps],
  props: {
    // 平台日期
    date: {
      type: [Number, String],
      default: null
    },
    // 权限相关
    permitdetail: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    const { InitialMessage } = this

    return {
      // 查询区域
      configForm: {
        model: {},
        data: [
          {
            title: '发行截止日',
            prop: 'releaseTime',
            type: 'rangeDate'
          },
          {
            title: '我司发行定价曲线',
            prop: 'ricingCurve',
            type: 'select',
            options: [
              { text: '1111', value: '1111' },
              { text: '2222', value: '2222' },
              { text: '3333', value: '3333' }
            ]
          },
          {
            title: '中债国开债到期收益率',
            prop: 'yield1',
            type: 'select',
            options: [
              { text: '1111', value: '1111' },
              { text: '2222', value: '2222' },
              { text: '3333', value: '3333' }
            ]
          },
          {
            title: '中债国债到期收益率',
            prop: 'yield2',
            type: 'select',
            options: [
              { text: '1111', value: '1111' },
              { text: '2222', value: '2222' },
              { text: '3333', value: '3333' }
            ]
          },
          {
            title: '中债中短期票据到期收益率',
            prop: 'yield3',
            type: 'select',
            options: [
              { text: '1111', value: '1111' },
              { text: '2222', value: '2222' },
              { text: '3333', value: '3333' }
            ]
          },
          {
            title: '中债城投债到期收益率',
            prop: 'yield4',
            type: 'select',
            options: [
              { text: '1111', value: '1111' },
              { text: '2222', value: '2222' },
              { text: '3333', value: '3333' }
            ]
          }
        ]
      },
      // 表格数据
      configTable: {
        loading: false,
        columns: [
          {
            title: InitialMessage('demo.flatIndex.table.name'),
            prop: 'name'
          },
          {
            title: InitialMessage('demo.flatIndex.table.date'),
            prop: 'date',
            type: 'date'
          },
          {
            title: InitialMessage('demo.flatIndex.table.amount'),
            prop: 'amount',
            type: 'amount'
          },
          {
            title: InitialMessage('demo.flatIndex.table.amount'),
            prop: 'amount2',
            type: 'amount'
          },
          {
            title: InitialMessage('demo.flatIndex.table.tenThousand'),
            prop: 'tenThousand',
            type: 'tenThousand'
          },
          {
            title: InitialMessage('demo.flatIndex.table.rate'),
            prop: 'rate',
            type: 'rate'
          }
        ],
        data: [
          {
            name: '测试名称1',
            date: 1624931532000,
            amount: 100,
            amount2: 200,
            tenThousand: 30000,
            rate: 0.01
          },
          {
            name: '测试名称2',
            date: 1655197702318,
            amount: 100,
            amount2: 200,
            tenThousand: 40000,
            rate: 0.01
          }
        ],
        pagination: {
          pageNo: 1,
          pageSize: window.screen.height > 1000 ? 25 : 15,
          pageSizeOptions: [15, 25, 50, 100],
          total: 0,
          showSizeChanger: true
        }
      }
    }
  },
  methods: {
    // 查询
    handleQuery() {
      const self = this
      const { configForm, configTable } = self

      self.$refs.elForm.validate(async valid => {
        if (valid) {
          configTable.loading = true

          const params = {
            data: { ...configForm },
            page: { ...configTable.pagination }
          }

          const { list = [], total = 0 } = { ...await API.GetListData(params) }

          if (list.length) {
            configTable.loading = false
            configTable.data = list
            configTable.pagination.total = total
          }
        }
      })
    },
    // 重置
    handleReset() {

    }
  }
}
</script>

<style lang="scss">

</style>

