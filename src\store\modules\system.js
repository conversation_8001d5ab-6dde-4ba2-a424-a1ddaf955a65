const state = {
  sysVersion: localStorage.getItem('sysVersion') || '',
  sysVersionName: '',
  personInfo: {
    companyVer: '',
    companyVerList: []
  }
}

const mutations = {
  CHANGE_SYS_VERSION: (state, sysVersion) => {
    state.sysVersion = sysVersion
    state.sysVersionName = state.personInfo.companyVerList?.find?.(item => item.id === sysVersion)?.text ?? ''
    localStorage.setItem('sysVersion', sysVersion)
  },
  SET_PERSON_INFO: (state, personInfo) => {
    state.personInfo = personInfo
  }
}

const actions = {
  setPersonInfo: ({ commit, state }, personInfo = {}) => {
    commit('SET_PERSON_INFO', personInfo)
    const { companyVerList = [], companyVer = '' } = personInfo
    let sysVersion = state.sysVersion || companyVer
    if (sysVersion && companyVerList.every(item => item.id !== sysVersion)) {
      sysVersion = companyVer?.split(',')?.[0] || ''
    }
    commit('CHANGE_SYS_VERSION', sysVersion)
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
