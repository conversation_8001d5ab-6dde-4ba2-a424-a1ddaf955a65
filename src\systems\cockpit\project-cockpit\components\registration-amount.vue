<template>
  <div class="registration-amount">
    <p class="total-amount">
      <span>剩余总额度(亿元)：</span>
      <span>{{ totalAmount }}</span>
    </p>
    <div class="registration-amount-swiper">
      <el-carousel style="width: 100%;height: 100%;" :autoplay="true" :interval="4000" indicator-position="bottom">
        <el-carousel-item v-for="(item,index) in swiperData" :key="index">
          <wave-chart style="width: 100%; height: 100%" :chartData="item" :totalAmount="totalAmount"/>
        </el-carousel-item>
      </el-carousel>
    </div>
  </div>
</template>

<script>
import { cockpitGetCompanyRegisterQuotaList } from "@/api/cockpit/cockpit"
import { px2vw,px2vh } from '../../utils/portcss';
import moment from "moment"
import WaveChart from './waveChart.vue'
export default {
  name: 'RegistrationAmount',
  components: {
    WaveChart
  },
  data() {
    return {
      totalAmount: '',
      swiperData: []
    }
  },
  created() {
    this.getRegistrationAmountDataApi()
  },
  filters: {
    dateFormat(date) {
      return moment(date).format('YYYYMMDD')
    }
  },
  methods:{
    px2vw,
    px2vh,
    /**
     * 获取注册额度数据
     */
    async getRegistrationAmountDataApi() {
      let data = await cockpitGetCompanyRegisterQuotaList()
      if(Object.keys(data).length === 0){
        data.amountSum = 0
      }
      this.totalAmount = data.amountSum * 1
      const list = data?.info || []
      let tempArr = []
      this.swiperData = list.reduce((pre, current, index) => {
        tempArr.push(current)
        if (tempArr.length >= 3) {
          pre.push(tempArr)
          tempArr = []
        }
        if (index === list.length - 1 && tempArr.length > 0) {
          pre.push(tempArr)
          tempArr = []
        }
        return pre
      }, [])
      console.log(this.swiperData,'swiperData')
    },
  },
}
</script>

<style lang="scss" scoped>
@import '~assets/styles/portcss.scss';
.registration-amount {
  width: 100%;
  height: 100%;
  margin-top: vh(15);
  padding-left: vw(24);
  padding-right: vw(24);
  .total-amount {
    display: flex;
    align-items: center;
    margin-bottom: 0px;
    
    &>span:nth-of-type(1){
      height: vh(20);
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: vh(14);
      color: #D5E2E8;
      line-height: vh(20);
    }
    
    &>span:nth-of-type(2){
      height: vh(20);
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: vh(16);
      color: #2CE5F4;
      line-height: vh(20);
    }
  }
  
  &-swiper {
    width: 100%;
    height: vh(345);
  }
}

/*指示器*/
::v-deep .el-carousel__button{
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #CCC;
}
::v-deep .is-active .el-carousel__button{
  background-color: var(--theme--color);
}
::v-deep .el-carousel__arrow{
  top: 35%;
}
</style>
