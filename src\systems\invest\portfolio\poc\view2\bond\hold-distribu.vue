<!-- 债券持仓分析 -> 持仓分布 -->
<template>
  <div class="home-poc-item hold-distribu-page">
    <div class="home-poc-item--header">持仓分布
      <el-form :model="form" style="flex: 1;padding-left: 20%;padding-right: 29px; margin-top: -1px;">
        <jr-form-item-create :data="cols" :model="form" :column="3" style="line-height: 2;" />
      </el-form>
      <fullscreen @fullscreen="fullscreen" />
    </div>

    <div class="home-poc-item--body">
      <TemplateModule
        ref="TemplateModule"
        chart-seq="39f2e0cd394a4ae3a4619b54ec018952"
        chart-type="PIE"
        style="padding-top: 10px;"
        :params="queryParams"
      />

      <TemplateModule
        ref="TemplateModule2"
        chart-seq="fc6d6870859e403092b43f4521aecba6"
        chart-type="MULTILINE"
        style="padding-top: 10px;"
        :params="queryParams"
      />
    </div>
  </div>
</template>

<script>
const format = 'YYYY-MM-DD'
import dayjs from 'dayjs'
import { getInit } from '@/systems/mixins'
import fullscreen from '../../common/fullscreen'
import TemplateModule from '@jupiterweb/components/template-module'

export default {
  components: {
    fullscreen,
    TemplateModule
  },
  mixins: [getInit('/invest/portfolio/bondposanalysis/BondPosAnalysis001')],
  props: {
    params: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      cols: [],
      form: {
        bondSumDimen: '01',
        timeLength: '01'
      }
    }
  },
  computed: {
    queryParams() {
      const { timeLength } = this.form
      const [vDate, mDate] = this.convertDate(timeLength)
      return { vDate, mDate, ...this.params }
    }
  },
  created() {
    this.init()
  },
  methods: {
    init() {
      const { BOND_SUM_DIMEN, TIME_LENGTH } = this.getInit

      this.cols = [{
        title: '汇总维度',
        type: 'select',
        prop: 'bondSumDimen',
        options: BOND_SUM_DIMEN,
        optionValue: 'id',
        clearable: false
      }, {
        title: '时间框架',
        type: 'select',
        prop: 'timeLength',
        options: TIME_LENGTH,
        optionValue: 'id',
        clearable: false
      }]
    },
    convertDate(timeLength) {
      const sysDate = { ...this.params }.mDate || this.$store.getters.systemTime
      const plateDate = dayjs(sysDate).format(format)

      switch (timeLength) {
        case '04':
          return [dayjs(sysDate).subtract(7, 'day').format(format), plateDate]
        case '03':
          return [dayjs(sysDate).subtract(30, 'day').format(format), plateDate]
        case '02':
          return [dayjs(sysDate).subtract(180, 'day').format(format), plateDate]
        case '01':
          return [dayjs(sysDate).subtract(365, 'day').format(format), plateDate]
      }
    },
    fullscreen(v) {
      this.$emit('fullscreen', v)

      const { chartInstance } = this.$refs.TemplateModule.$children[0]
      chartInstance && this.$nextTick(chartInstance.resize)
    }
  }
}
</script>

<style lang="scss" scoped>
@import "../../common/poc.scss";

.hold-distribu-page {
  .home-poc-item--body {
    display: flex !important;

    .template-show {
      width: 100%;
      overflow: hidden !important;
    }
  }
}
</style>

