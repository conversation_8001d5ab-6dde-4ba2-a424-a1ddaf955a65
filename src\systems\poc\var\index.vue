<template>
  <div class="var-poc has-fullscreen">
    <div class="search-form">
      <el-form class="form-items-container four-columns">
        <!-- 组合名称 -->
        <jr-form-item label="组合名称">
          <jr-combobox
            v-model="searchForm.portfolioId"
            :data="portfolioList"
            option-value="id"
            show-code
            remote
            :remote-method="getPortfolioList"
            @change="handlePortfolioChange"
          />
        </jr-form-item>

        <!-- 维度 -->
        <jr-form-item label="维度">
          <jr-combobox
            v-model="searchForm.dimension"
            :data="dimensionList"
          />
        </jr-form-item>

        <!-- VaR方法 -->
        <jr-form-item label="VaR方法">
          <jr-combobox
            v-model="searchForm.varType"
            :data="varList"
          />
        </jr-form-item>

        <!-- 展望天数 -->
        <jr-form-item label="展望天数">
          <jr-combobox
            v-model="searchForm.days"
            :data="daysList"
            clearable
          />
        </jr-form-item>

        <!-- 置信水平 -->
        <!-- <jr-form-item label="置信水平">
          <jr-combobox
            v-model="searchForm.level"
            :data="levelList"
          />
        </jr-form-item> -->

        <!-- 计算日期 -->
        <jr-form-item
          label="计算日期"
        >
          <el-date-picker v-model="searchForm.cdate" value-format="yyyy-MM-dd" />
        </jr-form-item>

        <!-- 样本天数 -->
        <jr-form-item
          label="样本天数"
        >
          <el-input
            v-model="searchForm.sampleDays"
          />
        </jr-form-item>

        <!-- 样本频率 -->
        <jr-form-item
          label="样本频率"
        >
          <jr-combobox
            v-model="searchForm.frequency"
            :data="frequencyList"
          />
        </jr-form-item>

        <!-- 是否穿透 -->
        <jr-form-item
          label="是否穿透"
        >
          <jr-radio-group
            v-model="searchForm.isPene"
            :data="YNList"
          />
        </jr-form-item>
      </el-form>
      <div class="btn-div">
        <el-button type="primary" @click="handleQuery">查询</el-button>
        <el-button>重置</el-button>
        <!-- <el-link
          type="primary"
          :class="showAllSearch ? 'up' : 'down'"
          :underline="false"
          @click="toggleSearchField"
        >
          {{ showAllSearch ? '收起' : '更多' }}
          <jr-svg-icon :icon-class="showAllSearch ? 'up' : 'down'" />
        </el-link> -->
      </div>
    </div>

    <div class="echarts-box">
      <div class="echarts-item">
        <h4>VaR分析</h4>
        <echarts :options="options1" :styles="{ height: '100%' }" />
      </div>
      <div class="echarts-item">
        <h4>VaR/净资产</h4>
        <echarts :options="options2" :styles="{ height: '100%' }" />
      </div>
      <div class="echarts-item">
        <h4>VaR/市值分析</h4>
        <echarts :options="options3" :styles="{ height: '100%' }" />
      </div>
    </div>
    <div>
      <div class="home-poc-item--header float-left">VaR
      </div>
      <jr-decorated-table
        ref="table"
        custom-id="099a8c13c0a94b3dac098348f0f1d371"
        style="min-height: 300px"
        :params="queryParams"
        v-bind="{ ...$attrs, noPagination: true, defaultExpandAll: true, menuinfo: { pageId: 'Monitor'} }"
        @refreshed="queryEnd"
      />
    </div>
  </div>
</template>

<script>
// import fullscreen from './fullscreen'
import echarts from '@jupiterweb/components/echarts'
import { GetInfoFn } from 'jupiterweb/src/utils/api'
export default {
  components: {
    // fullscreen
    echarts
  },
  data() {
    const defaultValues = {
      portfolioId: '',
      portfolioName: '',
      dimension: 'ZH',
      varType: 'history',
      days: '1',
      level: '95',
      cdate: JSON.parse(sessionStorage.getItem('platDate')),
      sampleDays: '251天',
      frequency: 'D',
      isPene: 'N'
    }
    return {
      searchForm: {
        ...defaultValues
      },
      portfolioList: [],
      dimensionList: [{
        text: '资产一级分类',
        value: 'ZCYJFL'
      },
      {
        text: '组合',
        value: 'ZH'
      }],
      varList: [{
        text: '历史模拟法',
        value: 'history'
      },
      {
        text: '参数法',
        value: 'param'
      },
      {
        text: '蒙特卡罗法',
        value: 'monteC'
      }],
      levelList: [{
        text: '95%',
        value: '95'
      },
      {
        text: '99%',
        value: '99'
      }],
      frequencyList: [{
        text: '日',
        value: 'D'
      },
      {
        text: '周',
        value: 'W'
      }],
      YNList: [{
        text: '是',
        value: 'Y'
      },
      {
        text: '否',
        value: 'N'
      }],
      daysList: [{
        text: '1天',
        value: '1'
      },
      {
        text: '7天',
        value: '7'
      },
      {
        text: '10天',
        value: '10'
      }],
      totalCount: 0,
      queryParams: {
        ...defaultValues
      },
      options1: {},
      options2: {},
      options3: {},
      showAllSearch: false
    }
  },
  created() {
    this.getPortfolioList('')
  },
  methods: {
    handleQuery() {
      Object.assign(this.queryParams, {
        ...this.searchForm
      })
      this.$refs.table.triggerTableRefresh(true, false)
    },
    setOptions(xAxisData, series) {
      const options = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        // dataZoom: [{
        //   show: true,
        //   type: 'slider',
        //   startValue: 0,
        //   height: 8,
        //   // 数据窗口范围的结束数值（一页显示多少条数据）
        //   endValue: 30
        // }],
        legend: {},
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: [{
          type: 'category',
          data: xAxisData,
          axisLine: {
            show: true,
            interval: 0,
            lineStyle: {
              width: 1,
              type: 'solid'
            }
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            show: true
          }
        }],
        yAxis: [{
          type: 'value',
          axisTick: {
            show: false
          },
          axisLine: {
            show: false,
            lineStyle: {
              width: 1,
              type: 'solid'
            }
          },
          splitLine: {
            lineStyle: {
            }
          }
        }],
        // series: series
        series: [{
          name: series[0].name,
          type: 'bar',
          data: series[0].data
        }, {
          name: series[1].name,
          type: 'bar',
          data: series[1].data
        }, {
          name: series[2].name,
          type: 'bar',
          data: series[2].data
        }]
      }
      return options
    },
    filterSeries(data, flag) {
      const arr = []
      if (flag === 1) {
        let xdata = []
        const nameList = [{
          text: 'var 95%',
          value: 'var95'
        }, {
          text: 'var 97.5%',
          value: 'var975'
        }, {
          text: 'var 99%',
          value: 'var99'
        }]
        nameList.map((item) => {
          xdata = (data.map(v => { return v[item.value] }))
          arr.push({ name: item.text, data: xdata })
        })
      } else if (flag === 2) {
        let xdata = []
        const nameList = [{
          text: 'var 95%/净资产',
          value: 'var95NetAssetValue'
        }, {
          text: 'var 97.5%/净资产',
          value: 'var975NetAssetValue'
        }, {
          text: 'var 99%/净资产',
          value: 'var99NetAssetValue'
        }]
        nameList.map((item) => {
          xdata = (data.map(v => { return v[item.value] }))
          arr.push({ name: item.text, data: xdata })
        })
      } else if (flag === 3) {
        let xdata = []
        const nameList = [{
          text: 'var 95%/市值',
          value: 'var95MarketValue'
        }, {
          text: 'var 97.5%/市值',
          value: 'var975MarketValue'
        }, {
          text: 'var 99%/市值',
          value: 'var99MarketValue'
        }]
        nameList.map((item) => {
          xdata = (data.map(v => { return v[item.value] }))
          arr.push({ name: item.text, data: xdata })
        })
      }
      console.log(arr)
      return arr
    },
    filterXaxis(data) {
      const list = []
      data.map(item => list.push(item.finprodName))
      return list
    },
    queryEnd(ins) {
      const tableData = ins.listData.map(l => (l.children || []).map(t => t.children || [])).flat().flat().slice(0, 10)
      this.totalCount = ins.pagination.total
      this.options1 = this.setOptions(this.filterXaxis(tableData), this.filterSeries(tableData, 1))
      this.options2 = this.setOptions(this.filterXaxis(tableData), this.filterSeries(tableData, 2))
      this.options3 = this.setOptions(this.filterXaxis(tableData), this.filterSeries(tableData, 3))
    },
    toggleSearchField() {
      this.showAllSearch = !this.showAllSearch
    },
    handlePortfolioChange(v, row) {
      this.queryParams.portfolioName = row.text
      this.searchForm.portfolioName = row.text
    },
    getPortfolioList(search) {
      const params = {
        search,
        page: 1,
        length: 100
      }
      GetInfoFn('/invest/portfolio/ptloverview/PtlOverview001/getPortfolioId', params).then(res => {
        this.portfolioList = res.root
        if (res.root?.length > 0 && !this.queryParams.portfolioId) {
          const { id, text } = res.root[0]
          this.queryParams.portfolioId = id
          this.searchForm.portfolioId = id
          this.queryParams.portfolioName = text
          this.searchForm.portfolioName = text
          this.handleQuery()
        }
      })
    }
  }
}
</script>

<style lang="scss">
.var-poc{
  .form-items-container .el-form-item__content .el-radio-group {
    vertical-align: bottom;
  }
}
</style>

<style lang="scss" scoped>
.var-poc{
  .search-form{
    display: flex;
    background: #fff;
    margin-bottom: 10px;
    padding: 8px 10px;
    .el-form{
      flex: 1;
      padding-right: 20px;
    }
    .btn-div{
      width: 122px;
      padding-top: 3px;
      position: relative;
      .el-link{
        position: absolute;
        bottom: 0;
        right: 0;
      }
    }
  }
  .echarts-box{
    background: #fff;
    height: 400px;
    display: flex;
    justify-content: space-between;
    padding: 10px;
    & > .echarts-item{
      width: 32.5%;
      border: 1px solid RGB(241,241,241);
      h4{
        margin-bottom: 0;
        padding: 0 10px;
        line-height: 36px;
        height: 36px;
        font-size: 14px;
        background: RGB(250,250,250);
        border-bottom: 1px solid RGB(241,241,241);
      }
      & > div{
        padding: 10px 10px 30px 10px;
      }
    }
  }
  ::v-deep .jr-radio-group .el-radio{
    min-width: auto !important;
  }
}
// @import "./poc.scss";

</style>
