<template>
  <!-- 立项变更 -->
  <div class="judicial-project">
    <jr-layout-vertical :height="48" disabled>
      <template v-slot:top>
        <div class="judicial-project-form">
          <el-form inline :model="form" label-width="68">
            <jr-form-item label="关键字">
              <el-input
                v-model="form.keyWord"
                clearable
                placeholder="请输入"
                style="max-width: 285px"
              />
            </jr-form-item>
            <jr-form-item label="案件身份">
              <jr-combobox
                v-model="form.identity"
                placeholder="请选择"
                clearable
                filterable
                :data="identityList"
                style="max-width: 285px"
              />
            </jr-form-item>
            <jr-form-item label="变更日期">
              <el-date-picker
                v-model="form.dateRange"
                type="daterange"
                range-separator="~"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                format="yyyy-MM-dd"
                class="date-picker"
                style="max-width: 285px"
              />
            </jr-form-item>
          </el-form>
          <div class="btn-list">
            <el-button type="primary" @click="submit">查询</el-button>
            <el-button @click="reset">重置</el-button>
          </div>
        </div>
      </template>

      <template v-slot:bottom>
        <div class="judicial-project-content">
          <jr-decorated-table
            ref="table"
            stripe
            :menuinfo="{ moduleid: '1352320216532336640' }"
            :params="tableParams"
            custom-id="a57acc4187e4464daf83c00d4bdafec3"
            @refreshed="callFn"
          />
        </div>
      </template>
    </jr-layout-vertical>
    <project ref="dialog" />
  </div>
</template>
<script>
import project from '../dialog/project.vue'
export default {
  components: {
    project
  },
  props: {
    permitdetail: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      tableParams: {
        ccid: 'a57acc4187e4464daf83c00d4bdafec3',
        ownedModuleid: '1352320216532336640'
      },
      columns: [],
      identityList: [],
      form: {
        keyWord: '',
        identity: '',
        dateRange: []
      },
      customRender: {
        detail: (h, { rowIndex, row }) => {
          return (
            <jr-svg-icon
              icon-class="file-search"
              onClick={() => {
                this.getDetailDialog(rowIndex, row)
              }}
            >
            </jr-svg-icon>
          )
        }
      }
    }
  },
  methods: {
    callFn(data) {
      this.columns = data.config.columns
    },
    handleClick() {
      //
    },
    submit() {
      this.tableParams = { ...this.tableParams, ...this.form }
    },
    reset() {
      this.form = {
        keyWord: '',
        identity: '',
        dateRange: []
      }
    },
    getDetailDialog(rowIndex, row) {
      this.$refs.dialog.open('')
    }
  }
}
</script>
<style lang="scss">
.judicial-project {
  height: calc(100% - 56px);

  .vertical-layout {
    background: #fff;
    padding: 0;
    height: 100%;

    &--top-content {
      padding: 0 16px;
    }

    &--resize {
      height: 1px;
      color: #EAE9E9;
    }

    &--bottom {
      margin-top: 16px;
      padding: 0 16px;
    }
  }

  &-form {
    position: relative;

    .el-form {
      display: flex;
      align-items: center;
      padding-top: 16px;
      width: calc(100% - 144px);

      .el-form-item {
        width: 100%;
        max-width: 353px;

        .el-form-item__label {
          padding: 11px 8px 0 0;
        }

        .el-form-item__content {
          width: calc(100% - 68px);
        }

        &.no-label {
          .el-form-item__content {
            width: 100%;
          }
        }
      }
    }

    .btn-list {
      position: absolute;
      top: 8px;
      right: 0;

      .el-button {
        margin-left: 16px;
      }
    }
  }

  &-content {
    height: 100%;

    .jr-decorated-table--header-left {
      display: none;
    }

    .jr-decorated-table--header-right {
      display: none;
    }

    .jr-decorated-table--body {
      padding: 0;
      
      .jr-svg-icon {
        fill: var(--theme--color);
        cursor: pointer;
      }
    }
  }
}
</style>
