<template>
  <!-- 舆情监测 -->
  <div class="warn-trial">
    <el-tabs v-model="tabActiveName" class="warn-trial-tabs" @tab-click="handleClick">
      <el-tab-pane label="工商变更" name="industrial" />
      <el-tab-pane label="对外投资详情" name="invest" />
      <el-tab-pane label="司法诉讼" name="judicial" />
      <el-tab-pane label="其他舆情" name="other" />
    </el-tabs>
    <component :is="tabActiveName" />
  </div>
</template>
<script>
import industrial from './components/industrial.vue'
import invest from './components/invest.vue'
import judicial from './components/judicial.vue'
import other from './components/other.vue'
export default {
  components: {
    industrial,
    invest,
    judicial,
    other
  },
  props: {
    permitdetail: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      tabActiveName: 'industrial'
    }
  },
  methods: {
    handleClick() {
      //
    }
  }
}
</script>
<style lang="scss">
.warn-trial {
  height: 100%;

  &-tabs {
    .el-tabs__header {
      background: #fff;
      margin: 0;

      .el-tabs__nav-wrap::after {
        height: 1px;
        background: #f4f4f4;
      }

      .el-tabs__nav-scroll {
        margin-left: 16px;
      }

      .el-tabs__item {
        height: 56px;
        line-height: 56px;
        font-family: MicrosoftYaHeiSemibold;
        font-size: var(--el-font-size-extra-large);
        color: rgba(0, 0, 0, 0.6);
        text-align: left;
        font-style: normal;
      }

      .el-tabs__item.is-active {
        color: var(--theme--color) !important;
      }
    }
  }
}
</style>
