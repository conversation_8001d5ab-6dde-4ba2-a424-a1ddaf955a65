<template>
  <div class="stock-option-table">
    <div class="table-header">
      <div class="table-cell" colspan="2">外币</div>
      <div class="table-cell" colspan="2">折算人名币</div>
    </div>
    <div class="table-row header">
      <div class="table-cell header1">规模（亿）</div>
      <div class="table-cell header2">规模（亿）</div>
      <div class="table-cell header3">只数</div>
    </div>
    <div v-for="(row, index) in dataRows" :key="index" class="table-row">
      <div class="table-cell date">{{ row.date }}</div>
      <div class="table-cell become">{{ row.dueAmount.toFixed(2) }}</div>
      <div class="table-cell become">{{ row.dueCountPercentage }}</div>
      <div class="table-cell exercisable">{{ row.exerciseAmount.toFixed(2) }}</div>
      <div class="table-cell date">{{ row.num }}</div>
    </div>

  </div>
</template>

<script>
export default {
  data() {
    return {
      dataRows: [
        { date: '一年以内', dueAmount: 200.87, dueCountPercentage: '17%', exerciseAmount: 283.71, exerciseCountPercentage: '26%', num: 1 },
        { date: '1-3年', dueAmount: 168.84, dueCountPercentage: '11', exerciseAmount: 176.00, exerciseCountPercentage: '16%', num: 3 },
        { date: '3-5年', dueAmount: 93.00, dueCountPercentage: '9%', exerciseAmount: 3.00, exerciseCountPercentage: '1%', num: 0 },
        { date: '5年以上', dueAmount: 93.00, dueCountPercentage: '9%', exerciseAmount: 3.00, exerciseCountPercentage: '1%', num: 0 },
        { date: '合计', dueAmount: 93.00, dueCountPercentage: '9%', exerciseAmount: 3.00, exerciseCountPercentage: '1%', num: 4 }
      ],
      totalDueAmount: 0,
      totalExerciseAmount: 0
    }
  },
  created() {
    this.totalDueAmount = this.dataRows.reduce((sum, row) => sum + row.dueAmount, 0)
    this.totalExerciseAmount = this.dataRows.reduce((sum, row) => sum + row.exerciseAmount, 0)
  }
}
</script>

<style lang="scss" scoped>
.stock-option-table {
  width: 100%;
  .table-header {
    width: 66.66%;
    margin-left: 16%;
    border-bottom: 1px solid #ccc;
    font-size: 14px;
}
.table-header, .table-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.table-cell {
  flex: 1;
  padding: 8px;
  box-sizing: border-box;
  text-align: center;
  position: relative; /* 为了使用伪元素或绝对定位的子元素 */
}
.table-row {
   .become {
      background: #ffce85;
   }
   .exercisable {
      background: #87c2ff;
      flex: 2 !important;
   }
}
.date {
    color: #909399;
}
.table-cell[colspan="2"] {
  flex: 2;
}
.header {
  position: relative;
  height: 35px;
 .table-cell {
    color: #909399;
 }
 .header1 {
    position: absolute;
    left: 32%;
 }
 .header2{
    position: absolute;
    left: 64%;
 }
 .header3{
    position: absolute;
    left: 88%;
 }
}
.table-row.header .table-cell, .table-row.total .table-cell {
  font-weight: bold;
}
}
</style>
