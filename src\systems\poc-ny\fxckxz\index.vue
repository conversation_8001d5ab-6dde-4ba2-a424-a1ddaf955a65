<template>
  <div class="page-fxckxz">
    <div class="item-title float">近期到期债券</div>
    <jr-decorated-table
      custom-id="a5862a0e47dd40d9997ac881a7e5f61b"
      style="height: 250px"
      v-bind="{ ...$attrs, ...$props }"
    />
    <div class="item-title">发行窗口计算</div>
    <div class="search-box">
      <div>
        <label>起息日期</label>
        <el-date-picker v-model="form.qxrq" type="date" value-format="yyyy-MM-dd" />
      </div>
      <div>
        <label>发行期限</label>
        <div style="display: inline-flex">
          <jr-number-input v-model="form.fxqx" />
          <jr-combobox
            v-model="form.fxqxType"
            style="width: 80px"
            :data="[
              { text: 'Y', value: '1,years' },
              { text: 'Q', value: '3,months' },
              { text: 'M', value: '1,months' },
              { text: 'D', value: '1,days' }
            ]"
          />
        </div>
      </div>
      <div>
        <el-button type="primary" @click="submit">计算</el-button>
        <el-button type="text" icon="el-icon-refresh-right">重置</el-button>
      </div>
    </div>
    <div class="data-content">
      <div class="left">
        <div class="title">
          <span class="title-text">
            <jr-svg-icon icon-class="calendar" />
            起息日期
            {{ formatDate(form.qxrq) }}
          </span>
        </div>
        <div style="display: flex; justify-content: space-between; line-height: 40px">
          <label>是否月末/季末/年末</label>
          <span>否</span>
        </div>
        <div style="display: flex; justify-content: space-between">
          <label>是否税期</label>
          <span style="color: red">是</span>
        </div>
        <el-divider>提示</el-divider>
        <ul>
          <li>月末、季末、年末收银行MPA考核影响，资金面紧张</li>
          <li style="color: red">1月、4月、7月、10月（重点）受交税期影响，资金面紧张</li>
        </ul>
      </div>
      <div class="right">
        <div class="title">
          <span class="title-text">
            <jr-svg-icon icon-class="calendar" />
            到期日期
            {{ formatDate(form.qxrq, true) }}
          </span>
          <span>
            相似到期债券
            <span style="color: var(--theme--color); font-weight: bold">3</span>
            只
            <el-popover placement="bottom" trigger="click" width="70%">
              <jr-decorated-table
                key="a5862a0e47dd40d9997ac881a7e5f61b222"
                custom-id="a5862a0e47dd40d9997ac881a7e5f61b"
                style="height: 200px"
                class="popover-decorated-table"
                v-bind="{ ...$attrs, ...$props, noPagination: true, params: {showPopover} }"
              />
              <span slot="reference" style="cursor: pointer;" @click="showPopover = true">
                <jr-svg-icon icon-class="search" />
              </span>
            </el-popover>
          </span>
        </div>
        <div style="display: flex; justify-content: space-between; line-height: 40px">
          <label>是否周末/节假日</label>
          <span>否</span>
        </div>
        <div style="display: flex; justify-content: space-between">
          <label>是否季末/年末</label>
          <span>否</span>
        </div>
        <el-divider>提示</el-divider>
        <ul>
          <li>应尽量避免节假日、周末到期，节假日信息仅供参考，具体以国家发布为准</li>
          <li>月末、季末、年末收银行MPA考核影响，资金面紧张</li>
        </ul>
      </div>
    </div>
  </div>
</template>
<script>
import moment from 'moment'
export default {
  data() {
    return {
      showPopover: false,
      weekday: ['日', '一', '二', '三', '四', '五', '六'],
      form: {
        qxrq: '2025-01-15',
        fxqx: '1',
        fxqxType: '1,years'
      }
    }
  },
  methods: {
    formatDate(date, isEnd = false) {
      if (!date) return '--'
      date = new Date(date)
      if (isEnd) {
        date = moment(date)
          .add(...this.form.fxqxType.split(','))
          .toDate()
      }
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      return `${year}年 ${month}月 ${day}日 周${this.weekday[date.getDay()]}`
    },
    submit() {
      this.$emit('submit', this.form)
    }
  }
}
</script>
<style lang="scss">
.page-fxckxz {
  padding: 12px;
  & > div {
    background: #fff;
  }
  .item-title {
    margin: 0 0 0 12px !important;
    &.float {
      margin-bottom: -30px !important;
      margin-top: 10px !important;
      margin-left: 12px !important;
      float: left;
    }
  }
  .search-box {
    display: flex;
    padding: 12px;
    & > div {
      display: inline-flex;
      align-items: center;
      margin-right: 10px;
      label {
        margin-right: 10px;
        white-space: nowrap;
      }
      .el-button {
        margin-left: 10px;
      }
    }
  }
  .data-content {
    display: flex;
    .title {
      display: flex;
      font-size: 16px;
      font-weight: bold;
      justify-content: space-between;
      margin-bottom: 20px;
      .jr-svg-icon {
        color: var(--theme--color);
      }
    }
    & > div {
      width: 50%;
      flex: 1;
      box-shadow: -4px 4px 10px 3px #eee;
      padding: 16px 30px;
    }
    &.right {
      margin-left: 20px;
    }
    li {
      list-style: disc;
    }
  }
}
.popover-decorated-table {
  .jr-decorated-table--header {
    display: none;
  }
}
</style>
