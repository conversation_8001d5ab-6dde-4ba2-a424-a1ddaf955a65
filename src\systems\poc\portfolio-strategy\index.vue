<!--
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023-10-24 17:11:49
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2023-11-07 18:20:35
 * @Description: 策略管理
-->
<template>
  <div class="poc-portfolio-strategy">
    <div class="top">
      <PocView />
      <PocRatio />
    </div>
    <div class="bottom">
      <PocMine />
      <PocTrend />
    </div>
  </div>
</template>

<script>
import PocView from './modules/view'
import PocMine from './modules/mine'
import PocTrend from './modules/trend'
import PocRatio from './modules/ratio'
export default {
  components: {
    PocView,
    PocRatio,
    PocMine,
    PocTrend
  }
}
</script>

<style lang="scss">
.poc-portfolio-strategy {
  height: 100%;
  overflow: auto;
  display: flex;
  width: 100%;
  flex-direction: column;
  article {
    padding: 0 16px;
  }

  .linear-cell {
    .cell::before {
      content: ' ';
      height: 14px;
      margin-top: 3px;
      width: var(--wdh);
      background: var(--bg);
      position: absolute;
      left: var(--left);
      right: var(--right);
    }
  }
  .header {
    border-bottom: 1px solid #E6E6E6;
    height: 30px;
    display: flex;
    justify-content: space-between;
    .el-radio-group {
      margin-top: 3px;
      margin-left: 6px;
      .el-radio-button--mini {
        .el-radio-button__inner {
          padding: 4px 10px;
        }
      }
    }
    .el-date-editor {
      width: 200px !important;
    }
    .el-button,
    .el-range-editor--medium.el-input__inner {
      height: 22px !important;
      line-height: 20px !important;
    }
    &> .el-button {
      margin-top: 3px;
    }
    .el-range-editor--medium .el-range__icon {
      line-height: 16px !important;
    }
    .search-list {
      padding-top: 3px;
      .jr-combobox {
        width: 90px;
        .el-input__inner {
          height: 22px!important;
        }
      }
      &>label {
        margin-left: 6px;
      }
      .jr-svg-icon {
        margin-left: 6px;
        cursor: pointer;
      }

      .el-radio-group {
        margin-top: 0;
        vertical-align: super;
      }
    }
    .title {
      line-height: 29px;
      display: inline-block;
      font-size: 14px;
      color: #333;
      border-top: 1px solid var(--theme--color);
    }
  }
  .body {
    height: calc(100% - 30px);
  }
  .top {
    height: 360px;
    display: flex;
    & > article {
      width: calc(50% - 4px);
      background: #fff;
      margin-right: 8px;
      &:last-child {
        margin-right: 0;
      }
    }
  }
  // .middle {
  //   height: 180px;
  //   min-height: 180px;
  //   display: flex;
  //   margin-top: 8px;
  //   & > article {
  //     width: 100%;
  //     background: #fff;
  //   }
  // }
  .bottom {
    height: calc(100% - 360px);
    min-height: 380px;
    margin-top: 8px;
    display: flex;
    & > article {
      width: calc(50% - 4px);
      background: #fff;
      margin-right: 8px;
      &:last-child {
        margin-right: 0;
      }
    }
  }
}
</style>
