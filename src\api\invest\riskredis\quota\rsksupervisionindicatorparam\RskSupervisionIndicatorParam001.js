import { GetInfoFn, UpdateFn } from '@jupiterweb/utils/api'

// 自定义参数
// 获取资产树
export const getAssetTreeNode = (params) => GetInfoFn(`/invest/riskredis/quota/rsksupervisionindicatorparam/RskSupervisionIndicatorParam005/getAssetTreeNode`, params, 'post')
// 获取工业树
export const getIndustryTreeNode = (params) => GetInfoFn(`/invest/riskredis/quota/rsksupervisionindicatorparam/RskSupervisionIndicatorParam005/getIndustryTreeNode`, params, 'post')
// 获取模型值
export const getModelValue = (params) => GetInfoFn(`/invest/riskredis/quota/rsksupervisionindicatorparam/RskSupervisionIndicatorParam005/getModelValue`, params, 'post')
// 保存check
export const zdyCheck = (params, cb) => UpdateFn(`/invest/riskredis/quota/rsksupervisionindicatorparam/RskSupervisionIndicatorParam005/check`, params, cb)

// 语言参数
// 详情信息列表
export const getDetailList = (params) => GetInfoFn(`/invest/riskredis/quota/rsksupervisionindicatorparam/RskSupervisionIndicatorParam004/findPage`, params, 'post')
// 保存check
export const yycsCheck = (params, cb) => UpdateFn(`/invest/riskredis/quota/rsksupervisionindicatorparam/RskSupervisionIndicatorParam002/check`, params, cb)

// 固定值参数
// 保存check
export const zdzCheck = (params, cb) => UpdateFn(`/invest/riskredis/quota/rsksupervisionindicatorparam/RskSupervisionIndicatorParam006/check`, params, cb)
export const fixCheck = (params, cb) => UpdateFn(`/invest/riskredis/quota/rsksupervisionindicatorparam/RskSupervisionIndicatorParam003/check`, params, cb)
// 获取列表数据
export const findPage = (params) => GetInfoFn(`/invest/riskredis/quota/rsksupervisionindicatorparam/RskSupervisionIndicatorParam006/findPage`, params)
