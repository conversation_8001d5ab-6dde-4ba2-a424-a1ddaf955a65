import { createSSRApp } from 'vue'

import App from './App.vue'
// import vant from 'vant';
import install from './install/index'
import store from './stores/index'
import './common/assets.js'
import AppTabBar from '@/components/AppTabBar/index.vue'; // 导入自定义tabBar组件


export function createApp() {
    const app = createSSRApp(App)
    app.use(store)
    app.use(install)
    app.component('AppTabBar', AppTabBar) // 全局注册自定义TabBar组件
    // app.use(vant)
    return {
        app
    }
}
