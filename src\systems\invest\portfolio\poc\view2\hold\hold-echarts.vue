<!-- 持仓分析概览 -> 持仓分布图 -->
<template>
  <div class="home-poc-item hold-echarts-page">
    <div class="home-poc-item--header">持仓分布图
      <el-form :model="form" style="flex: 1;padding-left: 20%;padding-right: 29px;">
        <jr-form-item-create :data="cols" :model="form" :column="3" style="line-height: 2;" />
      </el-form>
      <fullscreen @fullscreen="fullscreen" />
    </div>

    <TemplateModule
      ref="TemplateModule"
      class="home-poc-item--body"
      chart-seq="a680ff709e344259b576f8efad2860d6"
      chart-type="MULTILINE"
      style="padding-top: 10px;"
      :params="queryParams"
    />
  </div>
</template>

<script>
import dayjs from 'dayjs'
import fullscreen from '../../common/fullscreen'
import TemplateModule from '@jupiterweb/components/template-module'
import { getInit } from '@/systems/mixins'
const format = 'YYYY-MM-DD'

export default {
  components: {
    fullscreen,
    TemplateModule
  },
  mixins: [getInit('/invest/portfolio/ptlthematic/PtlThematicAnalysis001')],
  props: {
    params: {
      type: Object,
      default: null
    }
  },
  data() {
    const sysDate = this.$store.getters.systemTime
    const plateDate = dayjs(sysDate).format(format)

    return {
      sysDate,
      plateDate,
      cols: [],
      form: {
        sumDimension: '01',
        rank: 3,
        range: [plateDate, plateDate]
      }
    }
  },
  computed: {
    queryParams() {
      const { range = [], rank, sumDimension } = this.form
      const [vDate, mDate] = range

      return { mDate, vDate, rank, sumDimension, ...this.params }
    }
  },
  created() {
    this.getQueryColumns()
    console.log('--form--：', this.form)
  },
  methods: {
    getQueryColumns() {
      const { PTL_SUM_DIMEN } = this.getInit

      this.cols = [
        {
          type: 'select',
          prop: 'sumDimension',
          optionValue: 'id',
          options: PTL_SUM_DIMEN,
          clearable: false
        },
        {
          type: 'select',
          prop: 'rank',
          showCode: false,
          clearable: false,
          options: [
            { value: 1, text: '1' },
            { value: 2, text: '2' },
            { value: 3, text: '3' },
            { value: 4, text: '4' },
            { value: 5, text: '5' },
            { value: 6, text: '6' },
            { value: 7, text: '7' },
            { value: 8, text: '8' },
            { value: 9, text: '9' },
            { value: 10, text: '10' }
          ]
        },
        {
          type: 'rangeDate',
          prop: 'range',
          class: 'range-panel',
          uiProps: { clearable: false }
        }
      ]
    },
    fullscreen(v) {
      this.$emit('fullscreen', v)

      const { chartInstance } = this.$refs.TemplateModule.$children[0]
      chartInstance && this.$nextTick(chartInstance.resize)
    }
  }
}
</script>

<style lang="scss" scoped>
@import "../../common/poc.scss";
</style>

<style lang="scss">
.hold-echarts-page {
  .el-form-item {
    width: 26% !important;

    &.range-panel {
      width: 48% !important;
    }
  }
}
</style>
