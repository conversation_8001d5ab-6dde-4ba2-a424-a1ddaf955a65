<template>
  <div class="rating-page">
    <div class="chart-section">
      <h2>信用评级时间线</h2>
      <chart-component :options="ratingTimelineOptions" height="300px" />
    </div>

    <div class="chart-section">
      <h2>评级变化趋势</h2>
      <chart-component :options="ratingChangeOptions" height="300px" />
    </div>

    <div class="chart-section">
      <h2>债券收益率趋势</h2>
      <chart-component :options="yieldTrendOptions" height="300px" />
    </div>
  </div>
</template>

<script>
import ChartComponent from './components/chart.vue'

export default {
  name: 'RatingPage',
  components: {
    ChartComponent
  },
  data() {
    return {
      // 信用评级时间线图配置
      ratingTimelineOptions: {
        tooltip: {
          trigger: 'item',
          formatter: function(params) {
            return '<div style="font-weight:bold">' + params.value[0] + '</div>' +
							'<div style="display:flex;align-items:center;margin-top:8px">' +
							'<span style="display:inline-block;width:8px;height:8px;border-radius:50%;background-color:#41B883;margin-right:6px"></span>' +
							'成都城市资产运营集团有限公司：' + params.seriesName +
							'</div>'
          },
          backgroundColor: 'rgba(50,50,50,0.9)',
          borderColor: 'rgba(50,50,50,0.9)',
          borderWidth: 0,
          padding: [10, 15],
          textStyle: {
            color: '#fff'
          }
        },
        legend: {
          data: ['AAA', 'AA+'],
          right: 10,
          top: 10
        },
        grid: {
          top: 50,
          bottom: 50,
          left: 100,
          right: 50
        },
        xAxis: {
          type: 'time',
          show: true,
          splitLine: {
            show: false
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: '#ccc',
              type: 'dashed'
            }
          },
          axisTick: {
            show: true,
            alignWithLabel: true
          },
          axisLabel: {
            show: true,
            formatter: '{yyyy}-{MM}-{dd}',
            color: '#999',
            interval: 0,
            align: 'center',
            margin: 15
          },
          minInterval: 3600 * 24 * 1000
        },
        yAxis: [
          {
            type: 'category',
            data: ['联合资信', '大公国际', '上海新世纪'],
            axisLine: {
              lineStyle: {
                color: '#ccc',
                type: 'dashed'
              }
            }
          }
        ],
        series: [
          {
            name: 'AAA',
            type: 'line',
            symbol: 'circle',
            symbolSize: 10,
            yAxisIndex: 0,
            itemStyle: {
              color: '#41B883'
            },
            emphasis: {
              itemStyle: {
                borderColor: '#fff',
                borderWidth: 2,
                shadowColor: 'rgba(0, 0, 0, 0.5)',
                shadowBlur: 10
              }
            },
            data: [
              { name: 'AAA, 联合资信评估有限公司', value: ['2022-03-31', '联合资信'] },
              { name: 'AAA, 联合资信评估有限公司', value: ['2022-11-14', '联合资信'] },
              { name: 'AAA, 联合资信评估有限公司', value: ['2023-05-31', '联合资信'] },
              { name: 'AAA, 联合资信评估有限公司', value: ['2023-09-01', '联合资信'] },
              { name: 'AAA, 联合资信评估有限公司', value: ['2024-04-15', '联合资信'] },
              { name: 'AAA, 联合资信评估有限公司', value: ['2024-07-18', '联合资信'] },
              { name: 'AAA, 联合资信评估有限公司', value: ['2024-11-27', '联合资信'] }
            ]
          },
          {
            name: 'AA+',
            type: 'line',
            symbol: 'circle',
            symbolSize: 10,
            yAxisIndex: 0,
            itemStyle: {
              color: '#3D7EE8'
            },
            emphasis: {
              itemStyle: {
                borderColor: '#fff',
                borderWidth: 2,
                shadowColor: 'rgba(0, 0, 0, 0.5)',
                shadowBlur: 10
              }
            },
            data: [
              { name: 'AA+', value: ['2022-03-31', '大公国际'] },
              { name: 'AA+', value: ['2022-11-14', '大公国际'] },
              { name: 'AA+', value: ['2023-05-31', '大公国际'] },
              { name: 'AA+', value: ['2023-09-01', '大公国际'] },
              { name: 'AA+', value: ['2024-04-15', '大公国际'] },
              { name: 'AA+', value: ['2024-07-18', '大公国际'] },
              { name: 'AA+', value: ['2024-11-27', '大公国际'] }
            ]
          },
          {
            name: 'AAA',
            type: 'line',
            symbol: 'circle',
            symbolSize: 10,
            yAxisIndex: 0,
            itemStyle: {
              color: '#41B883'
            },
            emphasis: {
              itemStyle: {
                borderColor: '#fff',
                borderWidth: 2,
                shadowColor: 'rgba(0, 0, 0, 0.5)',
                shadowBlur: 10
              }
            },
            data: [
              { name: 'AAA', value: ['2022-03-31', '上海新世纪'] },
              { name: 'AAA', value: ['2022-11-14', '上海新世纪'] },
              { name: 'AAA', value: ['2023-05-31', '上海新世纪'] },
              { name: 'AAA', value: ['2023-09-01', '上海新世纪'] },
              { name: 'AAA', value: ['2024-04-15', '上海新世纪'] },
              { name: 'AAA', value: ['2024-07-18', '上海新世纪'] },
              { name: 'AAA', value: ['2024-11-27', '上海新世纪'] }
            ]
          }
        ],
        visualMap: {
          show: false,
          pieces: [
            {
              gt: 0,
              lte: 1,
              color: '#41B883'
            },
            {
              gt: 1,
              lte: 2,
              color: '#3D7EE8'
            }
          ]
        }
      },

      // 评级变化趋势图配置
      ratingChangeOptions: {
        tooltip: {
          trigger: 'axis',
          formatter: function(params) {
            return '<div style="font-weight:bold">' + params[0].axisValue + '</div>' +
							'<div style="display:flex;align-items:center;margin-top:8px">' +
							'<span style="display:inline-block;width:8px;height:8px;border-radius:50%;background-color:#41B883;margin-right:6px"></span>' +
							'成都城市资产运营集团有限公司：' + params[0].value[1] +
							'</div>'
          },
          backgroundColor: 'rgba(50,50,50,0.9)',
          borderColor: 'rgba(50,50,50,0.9)',
          borderWidth: 0,
          padding: [10, 15],
          textStyle: {
            color: '#fff'
          },
          axisPointer: {
            type: 'line',
            lineStyle: {
              color: 'rgba(0,0,0,0.2)',
              width: 1
            }
          }
        },
        legend: {
          data: ['评级'],
          right: 10,
          top: 10
        },
        grid: {
          top: 50,
          bottom: 50,
          left: 80,
          right: 50
        },
        xAxis: {
          type: 'time',
          show: true,
          splitLine: {
            show: false
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: '#ccc'
            }
          },
          axisTick: {
            show: true,
            alignWithLabel: true
          },
          axisLabel: {
            show: true,
            formatter: '{yyyy}-{MM}-{dd}',
            color: '#999',
            interval: 0,
            align: 'center',
            margin: 15
          },
          minInterval: 3600 * 24 * 1000
        },
        yAxis: {
          type: 'category',
          data: ['AA', 'AA(2)'],
          axisLine: {
            lineStyle: {
              color: '#ccc'
            }
          }
        },
        series: [
          {
            name: '评级',
            type: 'line',
            showSymbol: false,
            smoothMonotone: 'x',
            lineStyle: {
              width: 2,
              color: '#41B883'
            },
            data: [
              ['2022-09-19', 'AA'],
              ['2023-05-06', 'AA'],
              ['2023-05-07', 'AA(2)'],
              ['2025-03-25', 'AA(2)']
            ],
            markArea: {
              silent: true,
              itemStyle: {
                opacity: 0.1
              },
              data: [
                [
                  {
                    xAxis: '2023-05-06'
                  },
                  {
                    xAxis: '2023-05-07'
                  }
                ]
              ]
            }
          }
        ]
      },

      // 债券收益率趋势图配置
      yieldTrendOptions: {
        tooltip: {
          trigger: 'axis',
          formatter: function(params) {
            if (params && params.length > 0) {
              return '<div style="font-weight:bold">' + params[0].axisValue + '</div>' +
								'<div style="display:flex;align-items:center;margin-top:8px">' +
								'<span style="display:inline-block;width:8px;height:8px;border-radius:50%;background-color:#41B883;margin-right:6px"></span>' +
								'成都城市资产运营集团有限公司：' + params[0].value[1].toFixed(2) + '%' +
								'</div>'
            }
            return ''
          },
          axisPointer: {
            type: 'line',
            lineStyle: {
              color: 'rgba(0,0,0,0.2)',
              width: 1
            }
          },
          backgroundColor: 'rgba(50,50,50,0.9)',
          borderColor: 'rgba(50,50,50,0.9)',
          borderWidth: 0,
          padding: [10, 15],
          textStyle: {
            color: '#fff'
          },
          extraCssText: 'z-index: 100'
        },
        legend: {
          data: ['收益率'],
          right: 10,
          top: 10
        },
        grid: {
          top: 50,
          bottom: 50,
          left: 80,
          right: 50
        },
        xAxis: {
          type: 'time',
          show: true,
          splitLine: {
            show: false
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: '#ccc'
            }
          },
          axisTick: {
            show: true,
            alignWithLabel: true
          },
          axisLabel: {
            show: true,
            formatter: '{yyyy}-{MM}-{dd}',
            color: '#999',
            interval: 0,
            align: 'center',
            margin: 15,
            rotate: 15
          },
          minInterval: 3600 * 24 * 1000
        },
        yAxis: {
          type: 'value',
          name: '单位: %',
          nameLocation: 'end',
          min: 5.8,
          max: 6.8,
          interval: 0.2,
          axisLabel: {
            formatter: '{value}'
          },
          axisLine: {
            lineStyle: {
              color: '#ccc'
            }
          }
        },
        series: [
          {
            name: '收益率',
            type: 'line',
            smooth: true,
            symbolSize: 5,
            lineStyle: {
              width: 2,
              color: '#E6A23C'
            },
            itemStyle: {
              color: '#E6A23C'
            },
            data: [
              ['2025-03-07', 6.7],
              ['2025-03-10', 6.5],
              ['2025-03-12', 6.3],
              ['2025-03-13', 6.15],
              ['2025-03-17', 6.47],
              ['2025-03-19', 6.2],
              ['2025-03-21', 6.55],
              ['2025-03-25', 6.1],
              ['2025-03-27', 5.9]
            ],
            markPoint: {
              data: [
                {
                  name: '标记点',
                  coord: ['2025-03-17', 6.47],
                  value: '6.47%',
                  symbolSize: 8,
                  itemStyle: {
                    color: '#E6A23C'
                  }
                }
              ]
            }
          }
        ]
      }
    }
  },
  mounted() {
    // 初始化逻辑
  },
  beforeDestroy() {
    // 清理逻辑
  },
  methods: {}
}
</script>

<style lang="scss" scoped>
.rating-page {
	padding: 20px;
	background-color: #f5f7fa; // 页面背景色，接近图片
	font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif; // 常用字体栈
}

.chart-section {
	margin-bottom: 30px;
	padding: 20px;
	background-color: #fff;
	border-radius: 4px;
	box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);

	h2 {
		margin-top: 0;
		margin-bottom: 20px;
		font-size: 18px;
		font-weight: 600;
		color: #333;
	}
}
</style>
