<template>
  <jr-form-item :label="label">
    <component
      :is="currentComponent"
      v-model="innerValue"
      v-bind="$attrs"
      @change="handleChange"
    />
  </jr-form-item>
</template>

<script>
export default {
  name: 'WithFormInput',
  props: {
    // eslint-disable-next-line vue/require-default-prop
    value: {
      type: [String, Number, Array]
    },
    label: {
      type: String,
      default: ''
    },
    mode: {
      type: String,
      default: 'radio',
      validator: v => ['radio', 'checkbox'].includes(v)
    }
  },
  computed: {
    currentComponent() {
      return this.mode === 'radio'
        ? 'jr-radio-group'
        : 'jr-checkbox-group'
    },
    innerValue: {
      get() {
        // 类型转换逻辑
        if (this.mode === 'radio') {
          return Array.isArray(this.value)
            ? this.value || ''
            : this.value
        }
        return typeof this.value === 'string'
          ? [this.value]
          : this.value || []
      },
      set(val) {
        this.$emit('input', val)
      }
    }
  },
  methods: {
    handleChange(val) {
      // 自动转换输出类型
      const output = this.mode === 'radio'
        ? (Array.isArray(val) ? val : val)
        : (typeof val === 'string' ? [val] : val)

      this.$emit('change', output)
    }
  }
}
</script>
