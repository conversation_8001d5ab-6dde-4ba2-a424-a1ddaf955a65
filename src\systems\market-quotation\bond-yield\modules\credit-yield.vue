<template>
  <div class="bazaar national-rate" style="padding: 0 16px !important;">
    <h3 class="flex" style="justify-content: space-between;">
      <span title=""><jr-svg-icon icon-class="exclamation-circle" />涨跌BP=(实时-前一日日终)</span>
      <span class="flex"><span class="classify">分类</span> <jr-combobox v-model="checked" placeholder="请选择内容" style="width: 200px;" :data="selectData" :popper-append-to-body="false" /></span>
    </h3>
    <jr-table :data-source="tableData" :stripe="false" :height="400">
      <el-table-column
        v-for="item in columnData"
        :key="item.prop"
        :prop="item.prop"
        :label="item.fieldName"
        width="132"
      >
        <template slot-scope="scope">
          <div v-if="item.prop === 'curveName'" class="curveName size-color">{{ scope.row.curveName }}</div>
          <div v-else :class="{'props-col': true, 'no': bAnalYieldBp(scope.row, item) == 0, 'hot': bAnalYieldBp(scope.row, item) > 0, 'fall': bAnalYieldBp(scope.row, item) < 0}">
            <p class="size-color">{{ bAnalYield(scope.row, item) || '-' }}</p>
            <span>{{ bAnalYieldBp(scope.row, item) ? bAnalYieldBp(scope.row, item) + 'BP' : '-' }} <i v-if="bAnalYieldBp(scope.row, item)" :class="{'el-icon-caret-bottom': bAnalYieldBp(scope.row, item) < 0, 'el-icon-caret-top': bAnalYieldBp(scope.row, item) > 0}" /></span>
          </div>
        </template>
      </el-table-column>
    </jr-table>
  </div>
</template>

<script>
import { panel } from '@/api/poc/performance-evaluation'
export default {
  data() {
    return {
      tableData: [],
      columnData: [],
      selectData: [
        { text: '默认', value: '1' },
        { text: '中短期票据', value: '2' },
        { text: '企业债', value: '3' },
        { text: '城投债', value: '4' }
      ],
      checked: '1'
    }
  },
  watch: {
    checked(v) {
      this.marketRatePanel(v)
    }
  },
  created() {
    this.marketRatePanel(this.checked)
  },
  methods: {
    bAnalYield(row, item) {
      return row[`bAnalYield-${item.prop}`]
    },
    bAnalYieldBp(row, item) {
      return row[`bAnalYieldBP-${item.prop}`]
    },
    async marketRatePanel(v) {
      const data = await panel({
        panelType: 'bond',
        type: v
      }, '/marketdata/market/marketRatePanel')
      if (data) {
        this.columnData = data.titleList
        this.tableData = data.dataList
      }
    }
  }
}
</script>

<style lang="scss">
@import '@/assets/styles/national-rate.scss'
</style>
