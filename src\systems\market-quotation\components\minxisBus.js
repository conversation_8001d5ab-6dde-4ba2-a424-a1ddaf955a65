import { GetInfoFn } from '@jupiterweb/utils/api'
export default {
  data() {
    return {}
  },
  methods: {
    // 点击统计维度
    dimensionChange() {
      if (this.form.dimension === '2') {
        this.form.mainRating = []
        this.form.bAnalCurveterm = ''
      } else {
        this.form.mainRating = ''
        this.form.bAnalCurveterm = []
      }
    },
    // 点击卡片上面的评级
    async ratingFn(item, mainRating) {
      // 为了判断统计维度 是同比还是同期限
      const itemcode = this.termList.length > 0 ? this.termList.find(k => k.cnname === mainRating).itemcode : ''
      if (this.form.dimension === '1') {
        this.form.bAnalCurveterm = itemcode ? [itemcode] : []
        this.form.mainRating = item.itemcode
      } else {
        this.form.bAnalCurveterm = itemcode
        this.form.mainRating = item.itemcode ? [item.itemcode] : []
      }
    },
    // 查询债项评级 // 发行期限
    async dict() {
      const data = await GetInfoFn('DICT', [this.ratingDictType, this.termDictType])
      this.ratingList = data[this.ratingDictType] || []
      this.termList = data[this.termDictType] || []
    },
    // 点击卡片的处理方法
    onActive(item) {
      const itemcode = this.termList.length > 0 ? this.termList.find(k => k.cnname === item.bAnalCurveterm).itemcode : ''
      if (this.form.dimension === '1') {
        this.form.bAnalCurveterm = itemcode ? [itemcode] : []
      } else {
        this.form.bAnalCurveterm = itemcode
      }
      this.echartsData()
    },
    submit() {
      this.echartsData()
    },
    reset() {
      this.echartsData()
      this.$refs.cardList.setCardInd()
    },
    // 发行期限值改变 清除卡片选中效果
    checkChange() {
      this.$nextTick(() => this.$refs.cardList.cardInd = null)
    }
  }
}
