<template>
  <div class="stock-option-table">
    <div class="table-header">
      <div class="table-cell" colspan="2">以到期计</div>
      <div class="table-cell" colspan="2">以行权计</div>
    </div>
    <div class="table-row header">
      <div class="table-cell" />
      <div class="table-cell">余额（亿）</div>
      <div class="table-cell">只数（%）</div>
      <div class="table-cell">余额（亿）</div>
      <div class="table-cell">只数（%）</div>
    </div>
    <div v-for="(row, index) in dataRows" :key="index" class="table-row">
      <div class="table-cell date">{{ row.date }}</div>
      <div class="table-cell become">{{ row.dueAmount.toFixed(2) }}</div>
      <div class="table-cell become">{{ row.dueCountPercentage }}</div>
      <div class="table-cell exercisable">{{ row.exerciseAmount.toFixed(2) }}</div>
      <div class="table-cell exercisable">{{ row.exerciseCountPercentage }}</div>
    </div>

  </div>
</template>

<script>
export default {
  data() {
    return {
      dataRows: [
        { date: '一年以内', dueAmount: 200.87, dueCountPercentage: '17%', exerciseAmount: 283.71, exerciseCountPercentage: '26%' },
        { date: '1-3年', dueAmount: 168.84, dueCountPercentage: '11', exerciseAmount: 176.00, exerciseCountPercentage: '16%' },
        { date: '3-5年', dueAmount: 93.00, dueCountPercentage: '9%', exerciseAmount: 3.00, exerciseCountPercentage: '1%' },
        { date: '合计', dueAmount: 93.00, dueCountPercentage: '9%', exerciseAmount: 3.00, exerciseCountPercentage: '1%' }
      ],
      totalDueAmount: 0,
      totalExerciseAmount: 0
    }
  },
  created() {
    this.totalDueAmount = this.dataRows.reduce((sum, row) => sum + row.dueAmount, 0)
    this.totalExerciseAmount = this.dataRows.reduce((sum, row) => sum + row.exerciseAmount, 0)
  }
}
</script>

<style lang="scss" scoped>
.stock-option-table {
  width: 100%;
  .table-header {
    width: 80%;
    margin-left: 20%;
    border-bottom: 1px solid #ccc;
    font-size: 14px;
}
.table-header, .table-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.table-cell {
  flex: 1;
  padding: 8px;
  box-sizing: border-box;
  text-align: center;
  position: relative; /* 为了使用伪元素或绝对定位的子元素 */
}
.table-row {
   .become {
      background: #ffce85;
   }
   .exercisable {
      background: #87c2ff;
   }
}
.date {
    color: #909399;
}
.table-cell[colspan="2"] {
  flex: 2;
}
.header {
 .table-cell {
    color: #909399;
 }
}
.table-row.header .table-cell, .table-row.total .table-cell {
  font-weight: bold;
}
}
</style>
