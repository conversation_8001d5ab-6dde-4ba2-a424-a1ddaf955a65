<template>
  <div class="custom-actions">
    <div class="custom-actions-left">
      <el-button v-if="showBtn('新增')" type="primary" style="height: 32px" @click="add">
        <jr-svg-icon class="el-icon--left" icon-class="plus" />
        新增
      </el-button>
      <el-button v-if="showBtn('承销费计算')" style="height: 32px" @click="calculate">
        <jr-svg-icon class="el-icon--left" icon-class="calculator" />
        承销费计算
      </el-button>
    </div>
    <div v-if="showBtn('导出')" class="custom-actions-right">
      <el-button style="height: 32px" @click="exportData">
        <jr-svg-icon class="el-icon--left" icon-class="upload" />
        导出
      </el-button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CustomTableActions',
  props: {
    menuinfo: {
      type: Object,
      default: () => {}
    },
    permitdetail: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {}
  },
  methods: {
    exportData() {
      this.$emit('exportData')
      // this.downloadExcel()
    },
    add() {
      this.$emit('add')
    },
    calculate() {
      this.$emit('calculate')
    },
    showBtn(str) {
      if (this.menuinfo && Array.isArray(this.menuinfo.btnList) && this.menuinfo.btnList.length > 0) {
        const arr = this.menuinfo.btnList.filter((item) => {
          return item.btnnm === str && Object.prototype.hasOwnProperty.call(this.permitdetail, item.btnkey)
        })
        return Array.isArray(arr) && arr.length > 0
      } else {
        return false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.custom-actions {
  display: flex;
  justify-content: space-between;
  height: 48px;
  width: 100%;
  padding: 8px 10px;

  &-left {
    flex: 1;
    display: flex;
    justify-content: flex-start;
    gap: 8px;
  }

  &-right {
    flex: 1;
    display: flex;
    justify-content: flex-end;
    gap: 16px;
  }
}
</style>
