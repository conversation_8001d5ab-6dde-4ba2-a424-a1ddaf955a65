<template>
  <div class="issuance-popover">
    <el-popover placement="bottom-start" trigger="click" :popper-class="propClass">
      <template #reference>
        <el-button type="text" class="popover-trigger">
          <jr-svg-icon icon-class="info-circle" />
          <span>{{ text }}</span>
        </el-button>
      </template>
      <slot />
    </el-popover>
  </div>
</template>

<script>
export default {
  name: 'IssuancePopover',
  props: {
    text: {
      type: String,
      default: ''
    },
    propClass: {
      type: String,
      default: ''
    }
  },
  data() {
    return {}
  }
}
</script>

<style lang="scss" scoped>
.issuance-popover {
  display: inline-block;

  .popover-trigger {
    display: flex;
    align-items: center;
    gap: 2px;
    border: none;
    color: rgba(0, 0, 0, 0.6);
    font-weight: 400;
    i {
      font-size: 16px;
      margin-right: 4px;
    }
  }
}
</style>
<style lang="scss">
.issuance-explanation-popover {
  padding: 0px !important;
  background: rgba(255, 255, 255, 0.2) !important;
}
</style>
