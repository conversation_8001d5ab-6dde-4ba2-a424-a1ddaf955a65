<template>
  <div class="custom-actions">
    <div class="custom-actions-left">
      <el-button type="primary" style="height: 32px" @click="calculate">
        <jr-svg-icon class="el-icon--left" icon-class="plus" />
        获取缴费通知单
      </el-button>
    </div>
    <div class="custom-actions-right">
      <el-button style="height: 32px" @click="exportData">
        <jr-svg-icon class="el-icon--left" icon-class="upload" />
        导出
      </el-button>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    menuinfo: {
      type: Object,
      default: () => {}
    },
    permitdetail: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {}
  },
  methods: {
    exportData() {
      this.$emit('exportData')
    },
    edit() {
      this.$emit('edit')
    },
    calculate() {
      this.$emit('calculate')
    }
  }
}
</script>

<style lang="scss" scoped>
.custom-actions {
  display: flex;
  justify-content: space-between;
  height: 48px;
  width: 100%;
  padding: 8px 10px;

  &-left {
    flex: 1;
    display: flex;
    justify-content: flex-start;
    gap: 16px;
  }

  &-right {
    flex: 1;
    display: flex;
    justify-content: flex-end;
    gap: 16px;
  }
}
</style>
