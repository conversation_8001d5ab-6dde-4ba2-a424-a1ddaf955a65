import Vue from 'vue'
import jupiterUserStore from '@jupiterweb/store/modules/user'
import { getUserVersionPermission } from '@/api/home'
import { GetInfoFn } from '@jupiterweb/utils/api'

const user = {
  ...jupiterUserStore,

  actions: {
    ...jupiterUserStore.actions,
    // 获取菜单权限
    GetMenuAuth({ commit, state, rootState }) {
      // 获取用户信息
      if (!state.userInfo || !state.userInfo.userid) {
        GetInfoFn('LOGIN_USER')
          .then(info => {
            commit('SET_USER_INFO', info || {})
          })
          .catch(error => console.log(error))
      }
      return new Promise((resolve, reject) => {
        getUserVersionPermission({ companyVer: rootState.system.sysVersion })
          .then(menus => {
            if (!menus || !menus.length) {
              Vue.prototype.msgError('当前用户未授权，请联系我行人员！')
              window.sessionStorage.clear()
              return reject(new Error('权限缺失！'))
            }
            commit('SET_ALL_MENUS', menus)
            const permissionsValue = {}
            if (menus && menus.length) {
              menus.map(r => {
                if (permissionsValue[r.sysid]) {
                  permissionsValue[r.sysid].push(r)
                } else {
                  permissionsValue[r.sysid] = [r]
                }
              })
              commit('SET_PERMISSIONS', permissionsValue)
            } else {
              commit('SET_PERMISSIONS', {})
            }
            resolve(permissionsValue)
          })
          .catch(error => {
            reject(error)
          })
      })
    }
  }
}

export default user
