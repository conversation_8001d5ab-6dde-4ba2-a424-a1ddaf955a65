<template>
  <div class="lczs-fxlc">
    <el-form class="form-items-container" :model="form">
      <jr-form-item-create :data="fieldList" :model="form" :column="2" />
      <el-button type="primary" @click="submit">查询</el-button>
    </el-form>
    <b-template-module chart-seq="8190748b04c94f1abd063d70169d733f" :custom-options="customOptions" />
    <el-button class="float-right" type="text">
      <jr-svg-icon icon-class="export" />
      导出
    </el-button>
    <div class="item-title">利差明细列表</div>
    <template-module style="height: 200px" chart-seq="c7770d6be0b9477f8c1f932fd91b3b1a" chart-type="TABLE" />
  </div>
</template>
<script>
export default {
  data() {
    return {
      form: {
        qymc: 'hbsd',
        fxjzr: ['2023-10-13', '2024-10-13']
      },
      customOptions: {
        legend: {
          left: '20',
          orient: 'horizontal',
          textStyle: {
            color: '#333'
          },
          show: true
        },
        series: [
          {},
          {},
          {},
          {
            type: 'scatter'
          }
        ]
      },
      fieldList: [
        {
          title: '企业名称',
          prop: 'qymc',
          type: 'select',
          options: [
            {
              text: '河北顺德投资集团有限公司',
              value: 'hbsd'
            }
          ]
        },
        {
          title: '发行截止日',
          type: 'date',
          prop: 'fxjzr',
          uiProps: {
            valueFormat: 'yyyy-MM-dd',
            type: 'daterange'
          }
        }
      ]
    }
  },
  methods: {
    submit() {
      console.log(this.form)
    }
  }
}
</script>
<style lang="scss" scoped>
.lczs-fxlc {
  background: #fff;
  width: 100%;
  height: 100%;
  padding: 12px;
  .float-right {
    margin-top: 14px;
  }
  .el-form.form-items-container {
    display: flex;
    align-items: center;
    padding-right: 50%;
    .el-button {
      margin-left: 20px;
    }
  }
}
</style>
