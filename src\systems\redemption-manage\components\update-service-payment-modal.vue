<template>
  <jr-modal
    modal-class="update-modal"
    :loading="confirmLoading"
    :handle-cancel="handleCancel"
    :handle-ok="handleOk"
    :visible="visible"
    size="small"
  >
    <slot name="title">{{ customTitle }}</slot>
    <template v-slot:body>
      <el-form ref="updateForm" :model="form" label-width="120px">
        <jr-form-item-create
          :prop-path="''"
          :validate-rules="validateRules"
          :data="infoFormFields"
          :model="form"
          :column="1"
          :disabled="false"
        />
      </el-form>
      <div class="footerBac"></div>
    </template>
  </jr-modal>
</template>
<script>
import { GetListData } from '@jupiterweb/api/common/active-module-svr'
import { GetComboboxList, queryLeadUnderwriter } from '@/api/home'
import { feeUnderwritingInfoSaveOrModify, feeAgencyInfoSaveOrModify } from '@/api/redemption-manage/redemption-manage'
const PAYTYPE = 'payType' // 支付状态字典项
const FEETYPE = 'feeType' // 费用类型
const AGENCYFEETYPE = 'agencyFeeType' // 中介费费用类型
const OTFEETYPE = 'OtFeeType' // 其他服务费费用类型
import { EventBus } from '../event-bus'
import { queryBondShortName } from '@/api/bonds/bonds'

export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    closeModal: {
      type: Function,
      default: () => {}
    },
    permitdetail: {
      type: Object,
      default() {
        return {}
      }
    },
    row: {
      type: Object,
      default() {
        return {}
      }
    },
    openType: {
      type: String,
      default() {
        return 'add'
      }
    },
    orglist: {
      type: Array,
      default() {
        return []
      }
    }
  },
  data() {
    return {
      confirmLoading: false,
      tmflagDict: [],
      ynDict: [],
      form: {
        bondShortName: ''
      },
      isAdd: true,
      defaultForm: {
        authenticationflag: this.$dict.YN_N,
        tmflag: this.$dict.TMFLAG_E
      },
      stateList: [],
      customTitle: '',

      infoFormFields: [],
      payTypeList: [],
      validateRules: {},
      feeTypeList: [],
      agencyFeeList: [],
      otFeeList: [],
      collectionList: [],
      bondShortNameList: [],
      bondShortCode: ''
    }
  },

  async created() {
    console.log(this.row)
    if (Array.isArray(this.orglist) && this.orglist.length > 0) {
      this.collectionList = this.orglist
    }
    this.form = {
      id: this.row.id || '',
      bondShortName: this.row.bondShortName || '',
      bondCode: this.row.bondCode || '',
      payState: this.row.payState || '',
      feeType: this.row.feeType || '',
      payType: this.row.payType || '',
      payDate: this.row.payDate || '',
      payAmount: this.row.payAmount || '',
      payeeOrg: this.row.payeeOrg || '',
      feeRate: this.row.feeRate || '',
      feeClassify: ''
    }

    await this.getDictOptionsApi()
    if ('add' in this.permitdetail) {
      const obj = this.permitdetail['add']
      if (obj.customType === '中介费') {
        this.customTitle = this.openType === 'edit' ? '修改中介费' : '新增中介费'
        this.form.feeClassify = 'agen'
        this.infoFormFields = [
          {
            title: '债券简称',
            required: true,
            prop: 'bondShortName',
            type: 'remoteInputSelect',
            popperClass: '',
            disabled: this.openType === 'edit',
            showCode: false,
            placeholder: '请输入',
            optionValue: 'text',
            remoteMethod: async (queryString) => {
              if (queryString) {
                const data = await queryBondShortName({
                  text: queryString
                })

                if (data && Object.keys(data).length) {
                  this.bondShortNameList = data
                  return data
                } else {
                  return []
                }
              } else {
                return []
              }
            },
            change: (e) => {
              console.log(e)
              if (e) {
                this.form.bondShortName = e
                this.bondShortCode = this.getCode(this.bondShortNameList, e, 'value', 'text')
                this.getOrgList(this.bondShortCode)
                console.log(this.bondShortCode, 'bondShortCode')
              } else {
                this.bondShortCode = ''
              }
            }
          },
          {
            title: '费用类型',
            required: true,
            prop: 'feeType',
            type: 'select',
            disabled: this.openType === 'edit',
            options: this.agencyFeeList,
            optionValue: 'itemcode',
            optionLabel: 'cnname',
            showCode: false,
            placeholder: '请选择'
          },
          {
            title: '收款机构',
            required: true,
            prop: 'payeeOrg',
            type: 'select',
            disabled: false,
            options: this.collectionList,
            optionValue: 'code',
            optionLabel: 'name',
            showCode: false,
            placeholder: '请选择'
          },
          {
            title: '支付金额',
            prop: 'payAmount',
            required: true,
            type: 'amount',
            precision: 2,
            disabled: false,
            placeholder: '请输入'
          },
          {
            title: '支付日期',
            prop: 'payDate',
            required: true,
            type: 'date',
            disabled: false
          }
        ]
      } else if (obj.customType === '其他费用') {
        this.form.feeClassify = 'oth'
        this.customTitle = this.openType === 'edit' ? '修改其他服务费用' : '新增其他服务费用'
        this.infoFormFields = [
          {
            title: '债券简称',
            required: true,
            prop: 'bondShortName',
            type: 'remoteInputSelect',
            popperClass: '',
            disabled: this.openType === 'edit',
            showCode: false,
            placeholder: '请输入',
            optionValue: 'text',
            remoteMethod: async (queryString) => {
              if (queryString) {
                const data = await queryBondShortName({
                  text: queryString
                })

                if (data && Object.keys(data).length) {
                  this.bondShortNameList = data
                  return data
                } else {
                  return []
                }
              } else {
                return []
              }
            },
            change: (e) => {
              console.log(e)
              if (e) {
                this.form.bondShortName = e
                this.bondShortCode = this.getCode(this.bondShortNameList, e, 'value', 'text')
                this.getOrgList(this.bondShortCode)
                console.log(this.bondShortCode, 'bondShortCode')
              } else {
                this.bondShortCode = ''
              }
            }
          },
          {
            title: '费用类型',
            required: true,
            prop: 'feeType',
            type: 'select',
            disabled: this.openType === 'edit',
            options: this.otFeeList,
            optionValue: 'itemcode',
            optionLabel: 'cnname',
            showCode: false,
            placeholder: '请选择'
          },
          {
            title: '收款机构',
            required: true,
            prop: 'payeeOrg',
            type: 'select',
            disabled: false,
            options: this.collectionList,
            optionValue: 'code',
            optionLabel: 'name',
            showCode: false,
            placeholder: '请选择'
          },
          {
            title: '支付金额',
            prop: 'payAmount',
            required: true,
            type: 'amount',
            precision: 2,
            disabled: false,
            placeholder: '请输入'
          },
          {
            title: '支付日期',
            prop: 'payDate',
            required: true,
            type: 'date',
            disabled: false
          }
        ]
      } else {
        this.customTitle = this.openType === 'edit' ? '修改承销费' : '新增承销费'

        this.infoFormFields = [
          {
            title: '债券简称',
            prop: 'bondShortName',
            placeholder: '请输入',
            type: 'remoteInputSelect',
            popperClass: 'jr-autocomplete-suggestion',
            disabled: this.openType === 'edit',
            showCode: false,
            required: this.openType === 'add',
            optionValue: 'text',
            remoteMethod: async (queryString) => {
              if (queryString) {
                const data = await queryBondShortName({
                  text: queryString
                })

                if (data && Object.keys(data).length) {
                  this.bondShortNameList = data
                  return data
                } else {
                  return []
                }
              } else {
                return []
              }
            },
            change: (e) => {
              console.log(e)
              if (e) {
                this.form.bondShortName = e
                this.bondShortCode = this.getCode(this.bondShortNameList, e, 'value', 'text')
                this.getOrgList(this.bondShortCode)
                console.log(this.bondShortCode, 'bondShortCode')
              } else {
                this.bondShortCode = ''
              }
            }
          },
          {
            title: '承销费率',
            prop: 'feeRate',
            type: 'rate',
            precision: 4,
            disabled: false,
            required: true,
            placeholder: '请输入'
          },
          {
            title: '收款机构',
            required: true,
            prop: 'payeeOrg',
            type: 'select',
            disabled: false,
            options: this.collectionList,
            optionValue: 'code',
            optionLabel: 'name',
            showCode: false,
            placeholder: '请选择'
          },
          {
            title: '支付金额',
            prop: 'payAmount',
            type: 'amount',
            precision: 2,
            required: true,
            disabled: false,
            placeholder: '请输入'
          },
          {
            title: '支付日期',
            prop: 'payDate',
            type: 'date',
            required: true,
            disabled: false
          },
          {
            title: '支付类型',
            prop: 'payType',
            // type: 'radiogroupButton',
            type: 'select',
            disabled: false,
            options: this.payTypeList,
            optionValue: 'itemcode',
            optionLabel: 'cnname',
            required: true,
            showCode: false,
            placeholder: '请选择'
          }
        ]
      }
    }
  },
  methods: {
    getCode(list, select, valueStr = 'itemcode', labelStr = 'cnname') {
      let code = ''
      for (let index = 0; index < list.length; index++) {
        const element = list[index]
        if (select === element[labelStr]) {
          code = element[valueStr]
        }
      }

      return code
    },
    // 查询债券机构
    getOrgList(code) {
      queryLeadUnderwriter({
        bondCode: code
      }).then((data) => {
        this.collectionList = data
        this.infoFormFields.find((item) => item.prop === 'payeeOrg').options = data
      })
    },
    refresh() {
      if (this.openType === 'add') {
        EventBus.$emit('refresh-service-payment-list')
        EventBus.$emit('refresh-intermediary-fee-list')
        EventBus.$emit('refresh-other-list')
      } else {
        EventBus.$emit('refresh-pay-plan-list')
      }
    },
    /**
     * 提交
     */
    handleOk() {
      this.$refs['updateForm'].validate((valid) => {
        console.log(valid)

        if (valid) {
          console.log(this.form)
          this.form.payState = '20' // 默认支付状态未支付
          if (this.customTitle === '新增承销费' || this.customTitle === '修改承销费') {
            feeUnderwritingInfoSaveOrModify(this.form).then((res) => {
              this.$message({
                type: 'success',
                message: this.form.id ? '修改成功' : '新增成功'
              })
              this.handleCancel()
              this.refresh()
            })
          } else if (
            this.customTitle === '新增中介费' ||
            this.customTitle === '修改中介费' ||
            this.customTitle === '新增其他服务费用' ||
            this.customTitle === '修改其他服务费用'
          ) {
            feeAgencyInfoSaveOrModify(this.form).then((res) => {
              this.$message({
                type: 'success',
                message: this.form.id ? '修改成功' : '新增成功'
              })
              this.handleCancel()
              this.refresh()
            })
          }
        }
      })
    },
    handleCancel() {
      this.confirmLoading = false
      this.closeModal(false)
      this.form = {}
    },
    /**
     * 获取字典字段
     */
    async getDictOptionsApi() {
      const res = await GetComboboxList([PAYTYPE, FEETYPE, AGENCYFEETYPE, OTFEETYPE])
      this.payTypeList = res[PAYTYPE]
      this.feeTypeList = res[FEETYPE]
      this.agencyFeeList = res[AGENCYFEETYPE]
      this.otFeeList = res[OTFEETYPE]
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-form-item__content {
  margin-left: 0 !important;
}

::v-deep .jr-modal {
  .el-dialog__footer {
    border-top: none !important;
    background: none !important;
  }
}

.footerBac {
  width: 100%;
  height: 207px;
  background-image: url(../../../assets//images/bondEntryBac.png);
  background-size: 100% 100%;
  position: fixed;
  bottom: 0px;
  left: 0px;
  z-index: 0;
}
</style>
