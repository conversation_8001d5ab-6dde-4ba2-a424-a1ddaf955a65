<!--
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023-04-20 19:49:22
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2023-05-12 14:17:01
 * @Description: 收益贡献
-->
<template>
  <div class="home-poc-item poc-portfolio-contribution">
    <div class="home-poc-item--header">
      债券收益贡献
      <el-form :model="form">
        <jr-form-item-create :data="cols" :model="form" :column="3" />
      </el-form>
    </div>
    <!-- 收益贡献 -->
    <jr-decorated-table
      ref="table"
      custom-id="128e2aeb957240beb067ab7793c8ff4f"
      v-bind="{ ...$attrs, params: { ...form, ...seDate }, menuinfo: { pageId: 'Contribution'} }"
    />
  </div>
</template>

<script>
import dayjs from 'dayjs'

import * as API from '@/api/home'
// 近一周、近一月、近三月、近半年、年初至今、近一年
const RANGE_LIST = [
  {
    text: '近一周',
    value: '01'
  },
  {
    text: '近一个月',
    value: '02'
  },
  {
    text: '近三个月',
    value: '03'
  },
  {
    text: '近半年',
    value: '04'
  },
  {
    text: '年初至今',
    value: '05'
  },
  {
    text: '近一年',
    value: '06'
  }
]
export default {
  props: {
    params: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      form: {
        portfolioId: '',
        top: '10',
        range: '05'
      },
      cols: [{
        type: 'radio',
        options: [{
          text: 'Top5',
          value: '5'
        }, {
          text: 'Top10',
          value: '10'
        }],
        prop: 'top'
      }, {
        type: 'remoteSelect',
        api: '/invest/portfolio/ptloverview/PtlOverview001/getPortfolioId',
        prop: 'portfolioId',
        optionValue: 'id'
      }, {
        type: 'select',
        options: RANGE_LIST,
        placeholder: '时间',
        prop: 'range'
      }]
    }
  },
  computed: {
    seDate() {
      const { range } = this.form
      const [vDate, mDate] = this.convertDate(range) || []
      return {
        mDate, vDate
      }
    }
  },
  watch: {
    params: {
      handler(v) {
        if (v && v.portfolioId) {
          this.cols && this.$set(this.cols[1], 'options', [{ id: v.portfolioId, text: v.portfolioName }])
          Object.assign(this.form, v)
        }
      }
    }
  },
  created() {
    this.initPortfolio()
  },
  methods: {
    async initPortfolio() {
      const res = await API.GetPortfolioList({
        length: 1,
        page: 1,
        order: 'PORTFOLIO_ID',
        sort: 'desc',
        search: 'JCPC01'
      })
      if (res && res.length) {
        this.cols && this.$set(this.cols[1], 'options', res)
        Object.assign(this.form, { portfolioId: 'JCPC01' })
      }
    },
    convertDate(range) {
      const format = 'YYYY-MM-DD'
      const sysDate = this.$store.getters.systemTime
      const plateDate = dayjs(sysDate).format(format)
      switch (range) {
        case '01':
          return [dayjs(sysDate).subtract(7, 'day').format(format), plateDate]
        case '02':
          return [dayjs(sysDate).subtract(30, 'day').format(format), plateDate]
        case '03':
          return [dayjs(sysDate).subtract(90, 'day').format(format), plateDate]
        case '04':
          return [dayjs(sysDate).subtract(180, 'day').format(format), plateDate]
        case '05':
          return [dayjs(new Date(new Date(sysDate).getFullYear(), 0, 1)).format(format), plateDate]
        case '06':
          return [dayjs(sysDate).subtract(365, 'day').format(format), plateDate]
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import "./poc.scss";
</style>

<style lang="scss">
.poc-portfolio-contribution {
  .el-form {
    flex: 1;
    .jr-form-item {
      padding-left: 10px;
      padding-top: 3px;
      .el-radio {
        margin-right: 10px;
      }
    }
  }
  .form-item-create-component {
    line-height: 0;
  }
  .jr-decorated-table {
    padding-top: 8px;
    height: calc(100% - 40px);
    width: 100%;
    .jr-pagination {
      display: none;
    }
  }
  .jr-decorated-table--header {
    display: none;
  }
}
</style>
