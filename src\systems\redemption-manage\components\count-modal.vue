<template>
  <jr-modal
    modal-class="update-modal"
    :loading="confirmLoading"
    :handle-cancel="() => handleCancel(false)"
    :handle-ok="handleOk"
    :visible="visible"
    size="middle"
  >
    <slot name="title">承销费计算</slot>
    <template v-slot:body>
      <div style="width: 100%">
        <collapse-panel title="基本信息">
          <el-form ref="countForm" :model="form" label-width="120px">
            <jr-form-item-create
              :prop-path="''"
              :validate-rules="{}"
              :data="infoFormFields"
              :model="form"
              :column="2"
              :disabled="false"
            />
          </el-form>
        </collapse-panel>

        <collapse-panel :title="getSummaryText()">
          <el-form>
            <jr-table-editor v-model="tableList" :columns="elementColumns" :actionWidth="80" />
          </el-form>
        </collapse-panel>
      </div>

      <!-- <el-form ref="countForm" :model="form" label-width="120px"> -->
      <!-- <div class="top-title">
          <span class="top-title-tip" />
          基本信息
        </div> -->

      <!-- <p class="top-title">
          <span class="top-title-tip" />
          支付计划
          <span class="total-money">合计(元)： {{ totalMoney }}</span>
        </p> -->
      <!-- </el-form> -->
      <div class="footerBac"></div>
    </template>
  </jr-modal>
</template>
<script>
import { GetComboboxList } from '@/api/home'
const PAYSTATE = 'payState' // 支付状态字典项
const CD0117 = 'basis' // 计息基准字典项
const PAYTYPE = 'payType' // 支付类型
const PAYMETHOD = 'payMethod' // 支付方式
import { GetListData } from '@jupiterweb/api/common/active-module-svr'
import { queryBondInfo, saveBath, calPaymentAmt } from '@/api/redemption-manage/redemption-manage'
import { EventBus } from '../event-bus'
import moment from 'moment'
import { queryLeadUnderwriter } from '@/api/home'
import { queryBondShortName } from '@/api/bonds/bonds'
import { ConvertAmount } from '@jupiterweb/utils/common.js'

export default {
  props: {
    itemData: {
      type: Object,
      default: null
    },
    visible: {
      type: Boolean,
      default: false
    },
    closeModal: {
      type: Function,
      default: () => {}
    }
  },
  data() {
    return {
      confirmLoading: false,
      tmflagDict: [],
      ynDict: [],
      tableList: [],
      form: {
        term: '',
        amountact: '',
        bondShortName: ''
      },
      isAdd: true,
      defaultForm: {
        authenticationflag: this.$dict.YN_N,
        tmflag: this.$dict.TMFLAG_E
      },
      payStateList: [],
      otherList: [],
      payTypeList: [],
      infoFormFields: [],

      elementColumns: [],
      payMethodList: [],
      orgList: [],
      bondShortNameList: [],
      bondShortCode: ''
    }
  },
  computed: {
    totalMoney() {
      if (this.tableList.length > 0) {
        let sum = 0
        this.tableList.map((v) => {
          sum = sum + (v.payAmount ? v.payAmount : 0)
        })
        return sum
      } else {
        return 0
      }
    }
  },

  created() {
    this.getDictOptionsApi()
  },

  methods: {
    getSummaryText() {
      return `支付计划  合计(元)： ${ConvertAmount('yuan', this.totalMoney, 1, 2)}`
    },
    // 查询债券机构
    getOrgList(code) {
      queryLeadUnderwriter({
        bondCode: code
      }).then((data) => {
        this.orgList = data

        this.elementColumns[0].options = this.orgList
      })
    },
    async computedTableData() {
      const arr = []
      if (this.form.feeRate >= 0 && this.form.paymentMethod && this.form.term) {
        if (this.form.paymentMethod === 'O' && this.form.basis) {
          for (let index = 0; index < this.tableList.length; index++) {
            let item = this.tableList[index]
            console.log(item)

            if (item.share > 0) {
              const money = await this.calPaymentAmtCount({
                share: String(item.share),
                feeRate: String(this.form.feeRate),
                basis: this.form.basis,
                paymentMethod: this.form.paymentMethod,
                term: this.form.term
              })

              item = {
                ...item,
                payAmount: money
              }
              arr[index] = item
            } else {
              item = {
                ...item,
                payAmount: 0
              }
              arr[index] = item
            }
          }
        } else if (this.form.paymentMethod === 'Y') {
          for (let index = 0; index < this.tableList.length; index++) {
            let item = this.tableList[index]
            if (item.share > 0) {
              const money = await this.calPaymentAmtCount({
                share: String(item.share),
                feeRate: String(this.form.feeRate),
                basis: this.form.basis,
                paymentMethod: this.form.paymentMethod,
                term: this.form.term
              })
              item = {
                ...item,
                payAmount: money
              }
              arr[index] = item
            } else {
              item = {
                ...item,
                payAmount: 0
              }
              arr[index] = item
            }
          }
        }
      } else {
        for (let index = 0; index < this.tableList.length; index++) {
          let item = this.tableList[index]
          item = {
            ...item,
            payAmount: 0
          }
          arr[index] = item
        }
      }

      if (arr.length > 0) {
        this.tableList = arr
      }
    },
    // 计算支付费用
    async calPaymentAmtCount(params) {
      const res = await calPaymentAmt(params)

      console.log(res)
      return res
    },
    // 刷新列表
    refresh() {
      EventBus.$emit('refresh-service-payment-list')
    },
    // 提交
    handleOk() {
      console.log(this.tableList)

      this.$refs['countForm'].validate((valid) => {
        console.log(valid)

        if (valid) {
          const arr = []
          this.tableList.map((v, i) => {
            v.payState = '20'
            arr[i] = { ...v, ...this.form }
            if ('payDate' in arr[i] && arr[i].payDate) {
              arr[i].payDate = moment(arr[i].payDate).format('YYYYMMDD')
            }
          })

          console.log(arr)

          saveBath(arr).then((res) => {
            console.log(res, '新增成功')
            this.$message({
              type: 'success',
              message: '新增成功'
            })
            this.handleCancel(false)
            this.refresh()
          })
        }
      })
    },
    /**
     * 获取字典字段
     */
    async getDictOptionsApi() {
      const res = await GetComboboxList([PAYSTATE, CD0117, PAYTYPE, PAYMETHOD])
      this.payStateList = res[PAYSTATE]
      this.otherList = res[CD0117]
      this.payTypeList = res[PAYTYPE]
      this.payMethodList = res[PAYMETHOD]
      this.infoFormFields = [
        {
          title: '债券简称',
          prop: 'bondShortName',
          type: 'remoteInputSelect',
          popperClass: '',
          disabled: false,
          showCode: false,
          required: true,
          optionValue: 'text',
          placeholder: '请输入',
          remoteMethod: async (queryString) => {
            if (queryString) {
              const data = await queryBondShortName({
                text: queryString
              })

              if (data && Object.keys(data).length) {
                this.bondShortNameList = data
                return data
              } else {
                return []
              }
            } else {
              return []
            }
          },
          change: (e) => {
            console.log(e)
            if (e) {
              this.form.bondShortName = e
              this.bondShortCode = this.getCode(this.bondShortNameList, e, 'value', 'text')
              this.getOrgList(this.bondShortCode)

              queryBondInfo({ bondCode: this.bondShortCode }).then((res) => {
                this.form.term = res.term // 期限
                this.form.amountact = res.amountact // 规模
              })

              console.log(this.bondShortCode, 'bondShortCode')
            } else {
              this.bondShortCode = ''
            }
          }
        },
        {
          title: '规模',
          prop: 'amountact',
          type: 'number',
          disabled: true,
          placeholder: '暂无规模'
        },
        {
          title: '发行期限',
          prop: 'term',
          type: 'text',
          disabled: true,
          placeholder: '暂无发行期限'
        },
        {
          title: '年承销费率(%)',
          prop: 'feeRate',
          type: 'rate',
          required: true,
          precision: 4,
          disabled: false,
          change: (e) => {
            this.computedTableData()
          }
        },
        {
          title: '支付方式',
          prop: 'paymentMethod',
          type: 'select',
          disabled: false,
          required: true,
          options: this.payMethodList,
          optionLabel: 'cnname',
          optionValue: 'itemcode',
          showCode: false,
          change: (e) => {
            // 支付方式为一次性支付显示计息基准
            if (this.payMethodList.length > 0) {
              this.computedTableData()

              const arr = this.payMethodList.filter((v) => {
                return v.itemcode === e
              })

              if (arr.length > 0 && arr[0].cnname === '一次性支付') {
                this.infoFormFields.push({
                  title: '计息基准',
                  prop: 'basis',
                  type: 'select',
                  disabled: false,
                  options: this.otherList,
                  optionLabel: 'cnname',
                  optionValue: 'itemcode',
                  required: true,
                  showCode: false,
                  change: (e) => {
                    this.computedTableData()
                  }
                })
              } else {
                this.form['basis'] = ''
                this.infoFormFields = this.infoFormFields.filter((v) => {
                  return v.title !== '计息基准'
                })
              }
            }
          }
        }
      ]

      this.elementColumns = [
        {
          prop: 'payeeOrg',
          title: '收款机构',
          type: 'select',
          options: this.orgList,
          optionLabel: 'name',
          optionValue: 'code',
          showCode: false,
          required: true
        },
        {
          prop: 'share',
          title: '份额(亿)',
          type: 'hundredMillion',
          precision: 4,
          required: true,
          change: (e) => {
            this.computedTableData()
          }
        },
        {
          prop: 'payAmount',
          title: '支付金额(元)',
          type: 'number',
          precision: 2,

          required: true,
          disabled: true
        },
        {
          prop: 'payDate',
          title: '支付日期',
          type: 'date',
          required: true
        },
        {
          prop: 'payType',
          title: '支付类型',
          type: 'select',
          options: this.payTypeList,
          description: '设置为自动扣付时，系统将不发送短信提醒',
          optionLabel: 'cnname',
          optionValue: 'itemcode',
          showCode: false,
          required: true
        }
      ]
    },
    handleCancel(isSuccess) {
      this.confirmLoading = false
      this.closeModal(isSuccess)
      this.form = {
        voList: []
      }
    },
    getCode(list, select, valueStr = 'itemcode', labelStr = 'cnname') {
      let code = ''
      for (let index = 0; index < list.length; index++) {
        const element = list[index]
        if (select === element[labelStr]) {
          code = element[valueStr]
        }
      }

      return code
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-form-item__content {
  margin-left: 0 !important;
}

::v-deep .platform-modal-content {
  padding: 0 20px 0 20px !important;
}

::v-deep .jr-modal {
  .el-dialog__footer {
    border-top: none !important;
    background: none !important;
  }
}

.footerBac {
  width: 100%;
  height: 207px;
  background-image: url(../../../assets//images/bondEntryBac.png);
  background-size: 100% 100%;
  position: fixed;
  bottom: 0px;
  left: 0px;
  z-index: 0;
}

.top-title {
  font-family: MicrosoftYaHeiSemibold;
  font-size: var(--el-font-size-base);
  color: rgba(0, 0, 0, 0.85);
  line-height: 22px;
  text-align: left;
  font-style: normal;
  height: 56px;
  padding: 17px 0;
  &-tip {
    display: inline-block;
    width: 3px;
    height: 16px;
    background-color: #ff8e2b;
    margin-right: 8px;
  }

  .total-money {
    margin-left: 16px;
    font-family: MicrosoftYaHei;
    font-size: var(--el-font-size-base);
    color: rgba(0, 0, 0, 0.9);
    line-height: 22px;
    text-align: left;
    font-style: normal;
  }
}
</style>
