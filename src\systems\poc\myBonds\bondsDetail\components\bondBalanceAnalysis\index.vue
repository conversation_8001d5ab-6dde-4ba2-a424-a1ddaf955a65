<!-- 债券余额分析 -->
<template>
  <div class="bondBalanceAnalysis">
    <div class="bondBalanceAnalysisTitle">债券余额分析</div>
    <div class="content">
      <div class="leftContent">
        <div class="total">
          存续期人民币债券
          <span class="num"> 43 </span>只，规模<span class="num"> 74.0000 </span>亿元，余额<span class="num"> 462.7100 </span>
          亿元。其中永续债占总余额<span class="num"> 34.36 </span>{{ '%(' }}<span class="num"> 159.0000 </span>{{ '亿元)' }}
        </div>
        <Table />
      </div>
      <div class="rightContent">
        <TemplateModule chart-type="PIE" chart-seq="769bf2c585734327b33bf0d8eb55f012" />
      </div>
    </div>
  </div>
</template>

<script>
import TemplateModule from '@jupiterweb/components/template-module'
import Table from './table.vue'
export default {
  components: { Table, TemplateModule },
  data() {
    return {}
  },
  methods: {}
}
</script>
<style lang="scss">
.bondBalanceAnalysis {
    padding: 6px 10px 6px 10px;
    background: #fff;
    .bondBalanceAnalysisTitle {
        font-size: 16px;
        color: #303133;
        font-weight: 700;
        padding-bottom: 6px;
        border-bottom: 1px solid #EBEEF5;
    }
    .total {
        text-align: center;
        padding: 10px;
        color: #606266;
        .num {
            color: #E6A23C;
        }
    }
    .content {
        display: flex;
        .leftContent {
            flex: 1;
        }
        .rightContent {
            flex: 1;
        }
    }
}
</style>
