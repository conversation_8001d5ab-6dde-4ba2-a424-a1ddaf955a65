// 二级成交
<template>
  <div class="secondary-transaction">
    <jr-layout-vertical :height="48" disabled>
      <template v-slot:top>
        <el-form inline :model="form" class="secondary-transaction-form" label-width="68">
          <!-- 仅集团版展示；展示本企业及集团成员 -->
          <jr-form-item v-sysversion="'group'" label="发行人">
            <jr-combobox
              v-model="form.s_info_name"
              style="max-width: 285px"
              placeholder="请选择"
              clearable
              multiple
              collapse-tags
              :data="peopleList"
              option-value="value"
              option-label="text"
            />
          </jr-form-item>
          <jr-form-item label="债券简称">
            <el-input
              v-model="form.b_info_issuercode"
              clearable
              style="max-width: 285px"
              placeholder="请输入债券简称"
            />
          </jr-form-item>
          <jr-form-item label="交易日期">
            <el-date-picker
              v-model="form.customDateRange"
              style="max-width: 285px"
              type="daterange"
              range-separator="至"
              unlink-panels
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="yyyy-MM-dd"
              class="date-picker"
            />
          </jr-form-item>
          <jr-form-item>
            <jr-radio-group v-model="form.include_expired" :data="checkboxList" cancelable />
            <el-button type="primary" @click="submit">查询</el-button>
          </jr-form-item>
        </el-form>
      </template>

      <template v-slot:bottom>
        <div class="secondary-transaction-content">
          <div class="secondary-transaction-content-top">
            <span class="secondary-transaction-content-top-title">
              详情信息
            </span>
          </div>
          <div class="secondary-transaction-content-table">
            <!-- 接自定义列表 tableId换成自己的id -->
            <jr-decorated-table
              stripe
              :params="tableParams"
              :custom-id="tableId"
              :menuinfo="{ moduleid: ownedModuleid }"
              :permitdetail="{...permitdetail, export: { icon: 'upload', name: '导出', componenturl: 'export', btnkey: 'export' }}"
              :custom-render-header="customRenderHeader"
              :handleexport="exportData"
              @refreshed="callFn"
            />
            <span class="secondary-transaction-tips">
              <jr-svg-icon icon-class="info-circle" />
              集合票据，集合企业债不纳入统计范围。
            </span>
          </div>
        </div>
      </template>
    </jr-layout-vertical>
  </div>
</template>
<script>
import { exportExcelByCustomColumn } from '@/api/public/public'
import { queryBondRatingAgency } from '@/api/bonds/bonds'
import moment from 'moment'
export default {
  name: 'AssociatedEnterprise',
  props: {
    permitdetail: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      allBond: false,
      columns: [],
      // 自定义列id
      tableId: '800d2545336a4dc390a008b94cf1f032',
      // 菜单id
      ownedModuleid: '1352320137612312576',
      tableParams: {
        ownedModuleid: '1352320137612312576',
        include_expired: 0
      },
      form: {
        s_info_name: '',
        b_info_issuercode: '',
        customDateRange: [],
        include_expired: ''
      },
      peopleList: [],
      nameList: [],
      checkboxList: [
        {
          label: '包含已到期',
          value: '1'
        }
      ],
      customRenderHeader: {
        lastValuation: () => {
          return (
            <span>
              <el-tooltip effect="dark" content="非含权债展示到期估值，含权债展示行权估值" placement="top">
                <jr-svg-icon style="margin-right: 4px;" icon-class="info-circle" />
              </el-tooltip>
              前一日中债估值(%)
            </span>
          )
        }
      }
    }
  },
  mounted() {
    const day = new Date()
    const threeMonthsAgo = new Date()
    threeMonthsAgo.setMonth(day.getMonth() - 3)
    if (day.getDate() !== threeMonthsAgo.getDate()) {
      threeMonthsAgo.setDate(0) // 自动调整到上个月最后一天
    }
    const start = threeMonthsAgo.toISOString().split('T')[0]
    const end = day.toISOString().split('T')[0]
    this.form.customDateRange = [start, end]
    this.tableParams = {
        ...this.tableParams,
        startDate: start,
        endDate: end
      }
    this.getBondIssuerInfo()
  },
  methods: {
    submit() {
      const obj = {
        b_info_issuername: this.form.s_info_name,
        s_info_name: this.form.b_info_issuercode,
        include_expired: this.form.include_expired ? '1' : '0'
      }
      if (Array.isArray(this.form.customDateRange) && this.form.customDateRange.length > 0) {
        obj.startDate = moment(this.form.customDateRange[0]).format('YYYY-MM-DD')
        obj.endDate = moment(this.form.customDateRange[1]).format('YYYY-MM-DD')
      } else {
        obj.startDate = ''
        obj.endDate = ''
      }
      this.tableParams = {
        ...this.tableParams,
        ...obj
      }
    },
    async exportData() {
      const exportColumns = []
      for (const i of this.columns) {
        if (!(i.hidden || !i.defaultShow)) {
          exportColumns.push(i)
        }
      }
      console.log(exportColumns)
      const params = {
        params: {
          pageInfo: {},
          filename: '二级成交明细',
          column: exportColumns,
          ownedModuleid: this.ownedModuleid,
          ccid: this.tableId,
          ...this.tableParams
        },
        page: {
          sort: null,
          direction: null
        }
      }
      await exportExcelByCustomColumn(params)
    },
    callFn(data) {
      // 获取列表的列
      this.columns = data.config.columns
    },
    async getBondIssuerInfo() {
      const data = {
        text: ''
      }
      const res = await queryBondRatingAgency(data)
      const list = []
      for (const i of res) {
        if (i) {
          list.push(i)
        }
      }
      this.peopleList = list
    }
  }
}
</script>
<style lang="scss">
.secondary-transaction {
  height: 100%;

  .vertical-layout {
    background: #fff;
    padding: 0;

    &--top-content {
      padding: 0 16px;
    }

    &--resize {
      height: 1px;
      color: #EAE9E9;
    }

    &--bottom {
      margin-top: 16px;
      padding: 0 16px;
    }
  }

  &-form {
    display: flex;
    align-items: center;
    padding-top: 16px;

    .el-form-item {
      width: 100%;
      max-width: 352px;

      .el-form-item__label {
        padding: 11px 8px 0 0;
      }

      .el-form-item__content {
        width: calc(100% - 68px);

        .el-button {
          margin-left: 20px;
        }
      }

      &.no-label {
        .el-form-item__content {
          width: 100%;
        }
      }
    }
  }

  &-content {
    height: 100%;

    &-top {
      display: flex;
      justify-content: space-between;
      align-items: center;

      &-title {
        font-size: var(--el-font-size-extra-large);
        color: rgba(0, 0, 0, 0.85);
        font-weight: 600;

        &-tip {
          display: inline-block;
          width: 3px;
          height: 16px;
          background-color: #ff8e2b;
        }
      }
    }

    &-table {
      position: relative;
      padding-top: 20px;
      height: calc(100% - 46px);

      .jr-decorated-table--header-left {
        display: none;
      }

      .jr-decorated-table--header-right {
        position: absolute;
        top: -48px;
        right: -10px;
        width: auto;
      }

      .jr-decorated-table--body {
        padding: 0;
      }

      .jr-pagination {
        .el-pagination {
          transform: translateY(14px);
        }
      }
    }
  }

  &-tips {
    position: absolute;
    left: 0px;
    bottom: 6px;
    font-size: var(--el-font-size-base);
    color: rgba(0, 0, 0, 0.6);
  }
}
</style>
