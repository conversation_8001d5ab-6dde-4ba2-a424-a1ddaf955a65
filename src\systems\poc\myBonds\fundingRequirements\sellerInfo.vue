<template>
  <div class="sellerInfo">
    <collapse-panel title="卖方信息">
      <div class="collapseBody">
        <jr-form-item-create :validate-rules="validateRules" :data="sellInfo" :model="data" />
      </div>
    </collapse-panel>
    <collapse-panel title="标的要求">
      <div class="collapseBody">
        <jr-form-item-create :validate-rules="validateRules" :data="objectInfo" :model="data" />
      </div>
    </collapse-panel>
  </div>
</template>

<script>
import { getModalProps, getModalComputed } from '@/systems/mixins'
export default {
  mixins: [getModalProps, getModalComputed],
  props: {
    itemData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      baseFileds: []
    }
  },
  created() {
    this.itemData && this.$emit('setFieldsValue', {
      ...this.itemData
    })

    this.initData()
  },
  methods: {

    initData() {
      this.sellInfo = [
        {
          title: '卖家名称',
          prop: 'mjmc',
          required: true
        }
      ]
      this.objectInfo = [
        {
          title: '名称',
          prop: 'mc',
          required: true
        },
        {
          title: '是否上市公司',
          prop: 'sfssgs',
          type: 'radio',
          required: true,
          options: [
            { text: '是', value: '1' },
            { text: '否', value: '2' }
          ]
        },
        {
          title: '区域',
          prop: 'qy',
          type: 'select',
          required: true,
          options: [
            { text: '合肥市', value: '1' }
          ]
        },
        {
          title: '标的所属机构',
          prop: 'bdssjg',
          type: 'select',
          required: true,
          options: [
            { text: 'xx银行', value: '1' }
          ]
        },
        {
          title: '行业',
          prop: 'hy',
          type: 'select',
          required: true,
          options: [
            { text: '金融', value: '1' }
          ]
        },
        {
          title: '资产规模',
          prop: 'bgys',
          required: true,
          type: 'hundredMillion'
        },
        {
          title: '所有者权益',
          prop: 'syzqy',
          required: true,
          type: 'rate'
        },
        {
          title: '出售股权比例',
          prop: 'csgqbl',
          type: 'rate'
        },
        {
          title: '是否出让控制权',
          prop: 'sfcrkzq',
          type: 'radio',
          options: [
            { text: '是', value: '1' },
            { text: '否', value: '2' }
          ]
        },
        {
          title: '预期交易对价',
          prop: 'yqjydj',
          type: 'hundredMillion'
        },
        {
          title: '标的主营业务及产品',
          prop: 'bdzyywjcp',
          required: true,
          class: 'textarea',
          type: 'textarea'
        },
        {
          title: '标的简介',
          prop: 'bdjj',
          required: true,
          class: 'textarea',
          type: 'textarea'
        },
        {
          title: '对买方要求',
          prop: 'dmfyq',
          class: 'textarea',
          type: 'textarea'
        },
        {
          title: '其他信息',
          prop: 'qtxx',
          class: 'textarea',
          type: 'textarea'
        }
      ]
    },
    async preSaveHandel() {
      return true
    }

  }
}
</script>

<style lang="scss">
.sellerInfo {
    .textarea {
        width: 100% !important;
        .el-form-item__label {
            width: 9% !important;
        }
        .el-form-item__content {
            width: 100% !important;
        }
    }
}
</style>
