.home-poc-item {
  height: 100%;
  width: 100%;
  background: #fff;
  overflow: hidden;
  &.has-fullscreen {
    ::v-deep .jr-decorated-table--header {
      padding-right: 40px !important;
    }
    .home-poc-item--fullscreen {
      border-left: none;
    }
  }
  
  .home-poc-item--fullscreen {
    position: absolute;
    right: 12px;
    top: 8px;
    z-index: 99;
    width: 28px;
    height: 28px;
    color: var(--theme--color) !important;
    padding-left: 0 !important;
    padding-right: 0 !important;
    text-align: center;
  }
  .home-layout--subtitle {
    font-size: 13px;
    padding-left: 12px;
    font-weight: bold;
    line-height: 28px;
    float: left;
  }
  &--header {
    height: 40px;
    line-height: 40px;
    padding-left: 16px;
    font-size: 13px;
    font-weight: bold;
    background: #fff;
    white-space: nowrap;
    &::before {
      content: '';
      display: inline-block;
      width: 3px;
      height: 16px;
      background: var(--theme--color, #409eff);
      vertical-align: -3px;
      margin-right: 8px;
      margin-top: 12px;
    }
    .el-radio__label {
      vertical-align: middle;
    }
    &.float-left {
      float: left;
      height: 45px;
      line-height: 45px;
    }
    &:not(.float-left) {
      display: flex;
      border-bottom: 1px solid #e8e8e8;
      padding-right: 10px;
      padding-left: 10px;
      z-index: 1;
      position: relative;
    }
    .el-form {
      text-align: right;
      margin-left: 10px;
      .el-form-item__content {
        width: 100% !important;
      }
    }
  }
  &--body {
    height: calc(100% - 40px) !important;
    overflow: auto;
    .productName {
      white-space: nowrap;
    }
  }
}
