<!-- 发行人弹框 -->
<template>
  <div class="detailModal">
    <jr-modal
      :modal-class="'detailModal-modal'"
      :visible="visible"
      size="large"
      :has-footer="false"
      :handle-cancel="closeModal"
    >
      <slot
        name="title"
      >{{ itemData.fxr }}</slot>
      <template v-slot:body>
        <div class="content">
          <el-menu
            default-active="1-1"
            :default-openeds="['1']"
            class="contentLeft"
          >
            <el-submenu index="1">
              <template slot="title">
                <i class="el-icon-location" />
                <span>基本信息</span>
              </template>
              <el-menu-item-group>
                <el-menu-item index="1-1" @click="menuClick('1')">工商登记</el-menu-item>
                <el-menu-item index="1-2" @click="menuClick('2')">工商变更</el-menu-item>
              </el-menu-item-group>
            </el-submenu>
          </el-menu>
          <div class="contentRight">
            <Registration v-if="menuId === '1'" />
            <BusinessChange v-else />
          </div>
        </div>
      </template>
    </jr-modal>
  </div>
</template>

<script>
import Registration from './registration.vue'
import BusinessChange from './businessChange.vue'
export default {
  components: { Registration, BusinessChange },
  props: {
    isVisible: {
      type: Boolean,
      default: false
    },
    itemData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      menuId: '1',
      visible: false
    }
  },
  watch: {
    isVisible(val) {
      this.visible = val
    }
  },
  methods: {
    closeModal() {
      this.$emit('setVisible', false)
    },
    menuClick(v) {
      this.menuId = v
    }
  }
}
</script>
<style lang="scss">
.detailModal-modal {
    .content {
        display: flex;
        height: 100%;
        .contentLeft {
            width: 15%;
            border: none;
        }
        .contentRight {

            width: 85%;
        }
    }
}
</style>

