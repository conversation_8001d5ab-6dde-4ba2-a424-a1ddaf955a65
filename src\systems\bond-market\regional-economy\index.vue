// 区域经济
<template>
  <div class="regional-economy">
    <jr-layout-vertical :height="48" disabled>
      <template v-slot:top>
        <el-form inline :model="form" class="regional-economy-form" label-width="68">
          <jr-form-item label="区域名称">
            <SelectAutoSetCollapseTages
              ref="addressName"
              style="max-width: 285px"
              :options="addressOpt"
              mode="one"
              placeholder="请选择"
              @emitConfirmData="setAddressName"
            />
          </jr-form-item>
          <jr-form-item label="数据年份">
            <jr-combobox
              v-model="yearSel"
              placeholder="请选择"
              clearable
              filterable
              :data="yearList"
              style="max-width: 280px"
            />
          </jr-form-item>

          <el-button type="primary" @click="submit">查询</el-button>
        </el-form>
      </template>

      <template v-slot:bottom>
        <div class="regional-economy-content">
          <div class="regional-economy-content-table">
            <!-- 接自定义列表 tableId换成自己的id -->
            <jr-decorated-table
              stripe
              :params="tableParams"
              :custom-id="tableId"
              :menuinfo="{ moduleid: ownedModuleid }"
              :permitdetail="{...permitdetail, export: { icon: 'upload', name: '导出', componenturl: 'export', btnkey: 'export' }}"
              :handleexport="exportData"
              @refreshed="callFn"
            />
            <span class="table-tip">
              <jr-svg-icon icon-class="info-circle" />
              暂不包括开发区经济数据
            </span>
          </div>
        </div>
      </template>
    </jr-layout-vertical>
  </div>
</template>
<script>
import { exportExcelByCustomColumn } from '@/api/public/public'
import { queryRegionEeconomicYear } from '@/api/bonds/bonds'
import { regionDict } from '@/api/home'
import SelectAutoSetCollapseTages from '@/components/selectAutoSetCollapseTages'
export default {
  name: 'AssociatedEnterprise',
  components: {
    SelectAutoSetCollapseTages
  },
  props: {
    permitdetail: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  data() {
    return {
      allBond: false,
      sort: null,
      direction: null,
      columns: [],
      // 自定义列id
      tableId: 'bbd053854dbf4713b359192010b0c760',
      // 菜单id
      ownedModuleid: '1352315177277407232',
      tableParams: {},
      form: {
        s_info_name: '',
        b_info_issuercode: '',
        customDateRange: [],
        include_expired: ''
      },
      peopleList: [],
      nameList: [],
      checkboxList: [
        {
          label: '包含已到期',
          value: '1'
        }
      ],
      // 区域名称
      addressName: [],
      // 区域列表
      addressOpt: [
      ],
      yearList: [],
      // 年份选择
      yearSel: null
    }
  },
  mounted() {
    regionDict().then((res) => {
      const list = []
      if (res?.length > 0) {
        for (const i of res) {
          const province = list.find((item) => item.value === i.provinceCode)
          if (!province) {
            if (i.cityCode !== '') {
              list.push({
                label: i.provinceName,
                value: i.provinceCode,
                children: [
                  {
                    label: i.cityName,
                    value: i.cityCode,
                    children: [
                      {
                        label: i.regionName,
                        value: i.regionCode
                      }
                    ]
                  }
                ]
              })
            } else {
              list.push({
                label: i.provinceName,
                value: i.provinceCode,
                children: [
                  {
                    label: i.regionName,
                    value: i.regionCode
                  }
                ]
              })
            }
          } else {
            const cityList = province.children
            const city = cityList.find((item) => item.value === i.cityCode)
            if (!city) {
              if (i.cityCode !== '') {
                cityList.push({
                  label: i.cityName,
                  value: i.cityCode,
                  children: [
                    {
                      label: i.regionName,
                      value: i.regionCode
                    }
                  ]
                })
              } else {
                cityList.push({
                  label: i.regionName,
                  value: i.regionCode
                })
              }
            } else {
              const regionList = city.children
              regionList.push({
                label: i.regionName,
                value: i.regionCode
              })
            }
          }
        }
      }
      this.addressOpt = list
      this.$nextTick(() => {
        this.$refs.addressName.selectAll()
      })
    })
    const list = []
    for (let i = 0; i < 10; i++) {
      list.push({
        text: '',
        value: new Date().getFullYear() - i
      })
    }
    this.yearList = list
    // 加载最新数据年份
    queryRegionEeconomicYear().then((res) => {
      const year = res.year || null
      this.yearSel = year
      if (year) {
        this.tableParams = {
          ownedModuleid: '1352315177277407232',
          include_expired: 0
        }
      } else {
        this.tableParams = {
          ownedModuleid: '1352315177277407232',
          include_expired: 0,
          data_date: year + '1231'
        }
      }
    })
  },
  methods: {
    submit() {
      const obj = {
        s_info_name: this.form.s_info_name,
        b_info_issuercode: this.form.b_info_issuercode,
        include_expired: this.form.include_expired ? '1' : '0'
      }
      if (this.yearSel) {
        obj.data_date = this.yearSel + '1231'
      } else {
        obj.data_date = ''
      }
      obj.region_code = this.addressName
      this.tableParams = {
        ...this.tableParams,
        ...obj
      }
    },
    async exportData() {
      const params = {
        params: {
          pageInfo: {},
          filename: '区域经济',
          column: this.columns,
          ownedModuleid: this.ownedModuleid,
          ccid: this.tableId,
          ...this.tableParams,
          mergeColumns: [0]
        },
        page: {
          sort: this.sort,
          direction: this.direction
        }
      }
      await exportExcelByCustomColumn(params)
    },
    callFn(data) {
      // 获取列表的列
      this.columns = data.config.columns
      this.sort = data.sort
      this.direction = data.direction
    },
    setAddressName(val) {
      this.addressName = val
    }
  }
}
</script>
<style lang="scss">
.regional-economy {
  height: 100%;

  .vertical-layout {
    background: #fff;
    padding: 0;

    &--top-content {
      padding: 0 16px;
    }

    &--resize {
      height: 1px;
      color: #EAE9E9;
    }

    &--bottom {
      padding: 0 16px;
    }
  }

  &-form {
    display: flex;
    align-items: center;
    padding-top: 16px;

    .el-form-item {
      width: 100%;
      max-width: 353px;

      .el-form-item__label {
        padding: 11px 8px 0 0;
      }

      .el-form-item__content {
        width: calc(100% - 68px);
      }
    }

    .el-button {
      transform: translateY(-4px);
    }
  }

  &-content {
    height: 100%;

    &-table {
      position: relative;
      height: 100%;

      .jr-decorated-table--header-right {
        transform: translateX(10px);
      }

      .jr-decorated-table--body {
        padding: 0;
      }

      .table-tip {
        position: absolute;
        left: 0;
        bottom: 20px;
        font-size: var(--el-font-size-base);
        color: rgba(0, 0, 0, 0.6);
      }
    }
  }
}
</style>
