import { GetListInfo, UpdateFn } from '@jupiterweb/utils/api'
// 驾驶舱-利率驾驶舱-资讯中心
export const cockpitGetInfoList = params => GetListInfo('/cockpit/interestratecockpit/information', params)
// 驾驶舱-利率驾驶舱-策略中心
export const cockpitGetStrategyList = (type,params) => GetListInfo(`/cockpit/interestratecockpit/strategy?type=${type}`, params)
// 驾驶舱-利率驾驶舱-日历中心
export const cockpitGetCalendarList = (date,yearMonth) => GetListInfo(`/cockpit/interestratecockpit/calendar?date=${date}&yearMonth=${yearMonth}`)
// 驾驶舱-利率驾驶舱-日历中心-周展示
export const cockpitGetCalendarListByWeek = (startDate,endDate) => GetListInfo(`/cockpit/interestratecockpit/calendarWeek?startDate=${startDate}&endDate=${endDate}`)
// 驾驶舱-利率驾驶舱-发行利差
export const cockpitGetIssueDifferenceList = params => GetListInfo('/cockpit/interestratecockpit/queryIssueSpread', params)
// 驾驶舱-项目驾驶舱-企业注册额度查询
export const cockpitGetCompanyRegisterQuotaList = params => GetListInfo('/register/progress/queryCompanyRegistrationInfo', params)
// 驾驶舱-项目驾驶舱-通道中项目查询
export const cockpitGetChannelProjectList = params => GetListInfo('/register/progress/queryRegistrationChannelInfo', params)
// 驾驶舱-项目驾驶舱-集团额度管理查询
export const cockpitGetGroupRegisterQuotaList = params => GetListInfo('/register/progress/queryGroupAnalyseInfo', params)
// 驾驶舱-项目驾驶舱-主体评级最新信息查询
export const cockpitGetSubjectRatingList = params => GetListInfo('/myBonds/ratingInfo/queryCompanyLatestRating', params)
// 驾驶舱-项目驾驶舱-中债隐含评级最新信息查询
export const cockpitGetZbRatingList = params => GetListInfo('/myBonds/ratingInfo/queryCompanyLatestImpliedRating', params)
// 驾驶舱-项目驾驶舱-债券概览-卡片区
export const cockpitGetBondOverview = params => GetListInfo('/cockpit/projectcockpit/bondsOverviewCard', params)
// 驾驶舱-项目驾驶舱-债券概览-债券类型分析
export const cockpitGetBondTypeList = params => GetListInfo(`/cockpit/projectcockpit/bondsOverviewAnalysis?type=${params}`, {})
// 驾驶舱-项目驾驶舱-债券概览-债券分析按债券类型
export const cockpitGetBondTypeAnalysisList = params => GetListInfo(`/cockpit/projectcockpit/bondsOverviewAnalysisByBondType?type=${params}`, {})
// 驾驶舱-项目驾驶舱-债券概览-债券分析按发行方式
export const cockpitGetIssueTypeAnalysisList = params => GetListInfo(`/cockpit/projectcockpit/bondsOverviewAnalysisByIssueType?type=${params}`, {})
// 兑付管理-未来偿付现金流-分析汇总信息
export const cockpitGetCashFlowAnalysisList = params => GetListInfo('/paymentManagerment/futureCashFlow/queryAnalyseInfo', params)
// 兑付管理-未来偿付现金流-本金利息图
export const cockpitGetCashFlowPrincipalInterestList = params => GetListInfo('/paymentManagerment/futureCashFlow/queryPrincipalAndInterestInfo', params)
// 驾驶舱-利率驾驶舱-行情中心LPR曲线接口
export const cockpitGetLprCurveList = params => GetListInfo('/web/Shibor/policy', params)
