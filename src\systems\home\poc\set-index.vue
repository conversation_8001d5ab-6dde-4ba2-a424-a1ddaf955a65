<!--
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023-04-24 15:27:29
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2023-04-26 21:06:38
 * @Description: 描述
-->
<template>
  <el-popover v-model="customColumnPopShow" :manual="true" placement="bottom-end" popper-class="poc-custom-table-column">
    <el-button v-if="!admin" slot="reference" title="列配置" @click="init"><jr-svg-icon icon-class="system-config" /></el-button>
    <div v-if="admin" slot="reference">列配置</div>

    <div ref="columnSort" v-clickoutside.native="handleCancel" class="poc-custom-table-column-body">
      <el-input v-model="query" class="query-panel" placeholder="请输入字段名称" @keyup.native.enter="handleQuery">
        <jr-svg-icon slot="suffix" icon-class="search" @click="handleQ<PERSON>y" />
      </el-input>
      <div class="title">{{ parentName }}设置</div>
      <jr-table
        row-key="indexCode"
        :columns="tableConfig.columns"
        stripe
        border
        :height="300"
        :data-source="columns"
      />

      <div class="action-panel" style="text-align: center;">
        <el-button type="primary" @click="handleSave">{{ InitialMessage('common.system.btn.save') }}</el-button>
        <el-button @click="handleReset">{{ InitialMessage('common.btn.empty') }}</el-button>
        <el-button @click="handleCancel">{{ InitialMessage('common.system.btn.cancel') }}</el-button>
      </div>
    </div>
  </el-popover>
</template>
<script>
import Sortable from 'sortablejs'
// import { isNullOrUndefined } from '@jupiterweb/utils/common'
export default {
  name: 'ColumnSort',
  props: {
    admin: { type: Boolean, default: false },
    indexList: {
      type: Array, default: () => []
    }
  },
  data() {
    const self = this

    return {
      parentName: '',
      query: '',
      loading: false,
      columns: [],
      oriColumns: [],
      customColumnPopShow: false,
      isFiltered: false,
      tableConfig: {
        columns: [
          {
            title: '序号',
            width: 45,
            render: (h, { row, column, rowIndex }) => (
              <span>{ `${rowIndex + 1}` }</span>
            )
          },
          {
            title: '字段名称',
            render: (h, { row, column, rowIndex }) => (
              <span>
                { row.indexName}
                {<span class='controls'>
                  {(!self.isFiltered) && <span title='拖拽排序'><jr-svg-icon icon-class='drag' class='drag' /></span>}
                  <span title='置顶'><jr-svg-icon icon-class='vertical-align-top' onClick={ self.handleTopOrBottom.bind(self, rowIndex, 'top') } /></span>
                  <span title='置底'><jr-svg-icon icon-class='vertical-align-bottom' onClick={ self.handleTopOrBottom.bind(self, rowIndex, 'bottom') } /></span>
                </span>}
              </span>
            )
          },
          {
            title: '显示',
            width: 65,
            align: 'center',
            render: (h, { row, column }) => (
              <span>
                {<el-switch
                  v-model={ row.showFlag }
                  active-value={ 'Y' }
                  inactive-value={ 'N' }
                  onChange={self.handleChange.bind(this, row, 'showFlag')}
                />}
              </span>
            )
          }
        ]
      }
    }
  },
  watch: {
    customColumnPopShow(visible) {
      if (visible) {
        this.init()
        this.$nextTick(this.rowDrop)
      }
    }
  },
  methods: {
    // 初始化
    init() {
      const self = this

      const { indexList, indexName } = self.indexList[0] || {}
      self.query = ''
      self.isFiltered = false
      self.parentName = indexName
      const list = (indexList || []).map((v, i) => {
        v.$index = i
        return v
      })
      self.columns = JSON.parse(JSON.stringify(list))
      self.oriColumns = JSON.parse(JSON.stringify(list))
    },
    // 行拖拽
    rowDrop() {
      const self = this
      const el = self.$refs.columnSort.querySelector('.el-table__body tbody')

      if (!el) {
        setTimeout(() => self.rowDrop, 500)
        return
      }

      Sortable.create(el, {
        handle: '.drag',
        onEnd({ newIndex, oldIndex }) {
          const currRow = self.columns.splice(oldIndex, 1)[0]
          self.columns.splice(newIndex, 0, currRow)
          self.oriColumns = JSON.parse(JSON.stringify(self.columns))
          self.rowDrop()
        }
      })
    },
    handleChange(row, key, v) {
      this.oriColumns.find(o => o.indexCode === row.indexCode)[key] = v
    },
    // 置顶/置底
    handleTopOrBottom(index, flag) {
      const self = this
      const newIndex = flag === 'top' ? 0 : self.oriColumns.length - 1
      const currRow = self.columns.splice(index, 1)[0]

      self.columns.splice(newIndex, 0, currRow)
      // 找出在原始数据的位置
      const oldIndex = self.oriColumns.findIndex(item => item.$index === currRow.$index)
      self.oriColumns.splice(oldIndex, 1)
      self.oriColumns.splice(newIndex, 0, currRow)
      self.rowDrop()
    },
    // 查询
    handleQuery() {
      const self = this

      self.columns = self.oriColumns.filter(v => (v.indexName).includes(self.query))
      self.isFiltered = self.columns.length !== self.oriColumns.length //   是不是进行过滤了  进行过滤了  不允许排序
    },
    // 保存
    async handleSave() {
      this.$emit('setSessionColumns', this.oriColumns)

      this.customColumnPopShow = false
    },
    // 重置
    handleReset() {
      const self = this

      self.query = ''
      self.isFiltered = false
      self.columns = JSON.parse(JSON.stringify(self.initData))
      self.oriColumns = JSON.parse(JSON.stringify(self.initData))
    },
    // 取消
    handleCancel() {
      this.customColumnPopShow = false
    }
  }
}
</script>
<style lang="scss">
@import '~@jupiterweb/assets/styles/variables.scss';
.poc-custom-table-column.el-popover {
  padding: 0;
  .tip {
    padding-top: 10px;
    font-size: $font_size_14;
  }
}
.poc-custom-table-column-body {
  width: 560px;
  max-height: calc(100vh - 220px);
  min-height: 10vh;
  overflow: auto;
  padding: 12px;
  .poc-checkbox-group {
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    .controls {
      background-color: #fff;
      position: absolute;
      color: #646363;
      right: 0;
      padding-right: 10px;
      font-size: $font_size_14;
      display: none;
    }
    .el-checkbox {
      padding: 5px 10px;
      border: 1px dashed $--button-border-color;
      width: calc(50% - 8px);
      margin-right: 0;
      margin-top: 10px;
      overflow: hidden;
      &:nth-child(2n) {
        margin-left: 9px;
      }
      .el-checkbox__label {
        width: 100%;
      }
      &:hover .controls {
        display: inline-block;
      }
    }
  }
  .title {
    font-size: $font_size_16;
    font-weight: bold;
    line-height: 24px;
  }

  .query-panel {
    width: 50% !important;
    float: right;
    margin-bottom: 8px;
    .el-input__suffix {
      cursor: pointer;
      display: flex;
      align-items: center;
      margin-right: 2px;

      svg {
        color: #909399;
      }
    }
  }

  .el-table {
    margin: 10px 0 20px 0;

    .controls {
      background-color: #fff;
      position: absolute;
      color: #646363;
      right: 0;
      padding-right: 10px;
      font-size: $font_size_14;
      display: none;

      span {
        cursor: pointer;
        margin-left: 3px;
      }
    }

    .el-table__row {
      height: 30px !important;
      line-height: 30px !important;

      .el-switch__core {
        min-width: 40px !important;
      }

      &:hover .controls {
        display: inline-block;
      }
    }
  }

  .action-panel {
    text-align: center;
  }
}
</style>
