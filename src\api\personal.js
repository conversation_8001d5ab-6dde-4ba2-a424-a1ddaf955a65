import { GetInfoFn, UpdateFn } from '@jupiterweb/utils/api'
// 消息管理查看功能
export const getMessageConfig = () => {
  return GetInfoFn('/sys/messageConfig/ext/viewConfig')
}
// 消息接收设置查看功能
export const getMessageReceiveConfig = () => {
  return GetInfoFn('/sys/messageConfig/ext/view')
}
// 消息频率管理更新功能
export const updateMessageConfig = (data, cb) => {
  return UpdateFn('/sys/messageConfig/ext/save', data, cb)
}
// 债券类型数据
export const getBondTypeOption = () => {
  return GetInfoFn('/common/baseinfo/bondTypeOption')
}
// 消息频率管理查看功能
export const getMessageConfigViewConfig = (data) => {
  return GetInfoFn('/sys/messageConfig/ext/viewConfig', data)
}

// 查询集团成员配置
export const getGroupMemberConfig = (data) => {
  return GetInfoFn('/center/groupcfg/page', data)
}

// 集团成员配置新增更新功能
export const saveGroupMemberConfig = (data, cb) => {
  return UpdateFn('/center/groupcfg/save', data, cb)
}

// 查询持股比例
export const queryShareholding = (data) => {
  return GetInfoFn('/center/groupcfg/queryShareholding', data)
}
