<template>
  <!-- 常用模板 -->
  <div class="commonly-templates">
    <jr-layout-vertical :height="48" disabled>
      <template v-slot:top>
        <div class="commonly-templates-form">
          <el-form inline :model="form" label-width="68">
            <jr-form-item label="标题">
              <el-input
                v-model="form.title"
                clearable
                placeholder="请输入"
                style="max-width: 285px"
              />
            </jr-form-item>
            <jr-form-item label="发布日期">
              <el-date-picker
                v-model="form.publishDate"
                type="daterange"
                range-separator="~"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                format="yyyy-MM-dd"
                class="date-picker"
                style="max-width: 285px"
              />
            </jr-form-item>
          </el-form>
          <div class="btn-list">
            <el-button type="primary" @click="submit">查询</el-button>
            <el-button @click="reset">重置</el-button>
          </div>
        </div>
      </template>

      <template v-slot:bottom>
        <div class="commonly-templates-content">
          <jr-decorated-table
            ref="table"
            stripe
            :menuinfo="{ moduleid: '1315724205558775808' }"
            :params="tableParams"
            :custom-render="customRender"
            custom-id="43a04c9898d34c18801321ad7ed63e60"
            @refreshed="callFn"
          />
        </div>
      </template>
    </jr-layout-vertical>
    <commonly-dialog ref="dialog" />
  </div>
</template>
<script>
import commonlyDialog from './dialog/commonlyDialog.vue'
export default {
  components: {
    commonlyDialog
  },
  props: {
    permitdetail: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      tableParams: {
        ccid: '43a04c9898d34c18801321ad7ed63e60',
        ownedModuleid: '1315724205558775808',
        title: '',
        publishDate: []
      },
      columns: [],
      customRender: {
        title: (h, { rowIndex, row }) => {
          return (
            <div
              class="table-link"
              onClick={() => {
                this.getDetailDialog(rowIndex, row)
              }}
            >
              {row.title}
            </div>
          )
        }
      },
      form: {
        title: '',
        publishDate: []
      }
    }
  },
  methods: {
    callFn(data) {
      this.columns = data.config.columns
    },
    handleClick() {
      //
    },
    submit() {
      this.tableParams = { ...this.tableParams, ...this.form }
    },
    reset() {
      this.form = {
        title: '',
        publishDate: []
      }
    },
    getDetailDialog(rowIndex, row) {
      console.log(rowIndex, row)
      this.$refs.dialog.open('')
    }
  }
}
</script>
<style lang="scss">
.commonly-templates {
  height: 100%;

  .vertical-layout {
    background: #fff;
    padding: 0;
    height: 100%;

    &--top-content {
      padding: 0 16px;
    }

    &--resize {
      height: 1px;
      color: #EAE9E9;
    }

    &--bottom {
      margin-top: 16px;
      padding: 0 16px;
    }
  }

  &-form {
    position: relative;

    .el-form {
      display: flex;
      align-items: center;
      padding-top: 16px;
      width: calc(100% - 144px);

      .el-form-item {
        width: 100%;
        max-width: 353px;

        .el-form-item__label {
          padding: 11px 8px 0 0;
        }

        .el-form-item__content {
          width: calc(100% - 68px);
        }

        &.no-label {
          .el-form-item__content {
            width: 100%;
          }
        }
      }
    }

    .btn-list {
      position: absolute;
      top: 8px;
      right: 0;

      .el-button {
        margin-left: 16px;
      }
    }
  }

  &-content {
    height: 100%;

    .jr-decorated-table--header-top {
      display: none;
    }

    .jr-decorated-table--header-left {
      display: none;
    }

    .jr-decorated-table--header-right {
      display: none;
    }

    .jr-decorated-table--body {
      padding: 0;
    }

    .table-link {
      width: 100%;
      color: var(--theme--color);
      overflow: hidden;
      text-overflow: ellipsis;
      cursor: pointer;
    }
  }
}
</style>
