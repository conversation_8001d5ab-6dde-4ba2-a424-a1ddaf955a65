<template>
  <div class="serviceFeePayment">
    <div class="public-tabs-container">
      <el-tabs v-model="tabActiveName" @tab-click="changeTabs">
        <el-tab-pane label="承销费" name="ServicePayment" />
        <el-tab-pane label="中介费" name="IntermediaryFee" />
        <el-tab-pane label="其他服务费用" name="OtherServicesPayment" />
      </el-tabs>
    </div>
    <component
      :is="tabActiveName"
      :current-tab="currentTab"
      v-bind="{ ...$attrs, ...$props, params: { ...tableParams } }"
    />
  </div>
</template>

<script>
import ServicePayment from '../components/service-payment'
import IntermediaryFee from '../components/intermediary-fee'
import OtherServicesPayment from '../components/other-services-payment'
import { get } from 'lodash'
export default {
  name: 'ServiceFeePayment',
  components: {
    ServicePayment,
    IntermediaryFee,
    OtherServicesPayment
  },
  props: {
    permitdetail: {
      type: Object,
      default: () => ({})
    },
    menuinfo: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      tabActiveName: 'ServicePayment',
      tableParams: {
        ccid: '92fd33cdbb364789967a93eb5b649e4d'
      },
      btnArr: {}
    }
  },
  computed: {
    currentTab() {
      return this.$store.getters.currentTab
    }
  },
  created() {
    this.btnArr = JSON.stringify(this.menuinfo.btnList)
    // 获取路由跳转参数
    const params = get(this, 'menuinfo.meta.params', {})
    if (params && params.defaultTabActiveName) {
      this.tabActiveName = params.defaultTabActiveName
      this.changeTabs({ name: this.tabActiveName })
    }
  },
  methods: {
    /**
     * 更换tab页
     */
    changeTabs(e) {
      switch (e.name) {
        case 'ServicePayment':
          this.tableParams.ccid = '92fd33cdbb364789967a93eb5b649e4d'
          this.menuinfo.btnList = JSON.parse(this.btnArr)
          break
        case 'IntermediaryFee':
          this.tableParams.ccid = 'da1e9e0a61e7491ca3657e39366f0db5'

          this.menuinfo.btnList = JSON.parse(this.btnArr).filter((v) => {
            if (v.btnnm === '新增') {
              v.customType = '中介费'
            }
            return v.btnnm !== '承销费计算'
          })
          break
        case 'OtherServicesPayment':
          this.tableParams.ccid = '350ca64244a74c4d902c8a0e78a28e91'
          this.menuinfo.btnList = JSON.parse(this.btnArr).filter((v) => {
            if (v.btnnm === '新增') {
              v.customType = '其他费用'
            }
            return v.btnnm !== '承销费计算'
          })
          break
        default:
          this.tableParams.ccid = ''
          break
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~@jupiterweb/assets/styles/variables.scss';
::v-deep .el-tabs__nav-wrap::after {
  display: none;
}

::v-deep .el-range-separator {
  line-height: 30px !important;
}
.serviceFeePayment {
  height: 100%;
  &-tabs {
    width: 100%;
    height: 56px;
    padding: 17px 0px 0px 16px;
    box-sizing: border-box;
    background-color: #ffffff;
  }
}
</style>
