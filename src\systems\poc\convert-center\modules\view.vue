<!--
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023-10-25 10:54:01
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2023-11-02 17:07:31
 * @Description: 概览
-->
<template>
  <article>
    <div class="header">
      <span class="title">概览</span>
      <el-radio-group v-model="combo" size="mini">
        <el-radio-button v-for="com in comboxList" :key="com" :label="com" />
      </el-radio-group>
    </div>
    <div class="body">
      <section v-for="data in dataList" :key="data.text">
        <span>{{ data.value }}</span>
        <p>{{ data.text }}</p>
      </section>
    </div>
  </article>
</template>

<script>
export default {
  data() {
    return {
      combo: '全部',
      comboxList: ['全部', '固收', '权益', '混合'],
      dataList: [
        {
          value: 53,
          text: '存续产品(个)'
        },
        {
          value: 8,
          text: '已配置产品(个)'
        },
        {
          value: 1,
          text: '正在配置产品(个)'
        },
        {
          value: 4,
          text: '需要优化的组合(个)'
        },
        {
          value: 45,
          text: '待转配置产品(个)'
        },
        {
          value: 1,
          text: '已转配置方案(个)'
        },
        {
          value: 7,
          text: '待转配置方案(个)'
        },
        {
          value: 1,
          text: '配置方案转化中(个)'
        },
        {
          value: '12.5%',
          text: '配置方案转化率'
        },
        {
          value: '6.31%',
          text: '已配方案累计收益率'
        }
      ]
    }
  }
}
</script>

<style lang="scss" scoped>
// .header {}
.body {
  display: grid;
  grid-template-columns: repeat(5, 20%);
  section {
    text-align: center;
    padding-top: 18px;
    span {
      font-size: 30px;
      line-height: 60px;
      color: var(--theme--color);
    }
    p {
      font-weight: bold;
    }
  }
}
</style>
