<!-- 财务对标 -->
<template>
  <div class="benchmark-analysis financial-benchmark">
    <div style="display: flex; padding: 8px 0;align-items: center;">
      <span class="subtitle-label">财务对标</span>
      <el-form :model="searchForm" inline class="search-form">
        <jr-form-item>
          <el-radio-group v-model="searchForm.benchmarkType" @change="handleBenchmarkTypeChange">
            <el-radio :label="0">指标对比</el-radio>
            <el-radio :label="1">趋势分析</el-radio>
          </el-radio-group>
        </jr-form-item>
      </el-form>
    </div>
    <div class="search-form search-form--flex">
      <el-form :model="searchForm" inline>
        <jr-form-item v-if="searchForm.benchmarkType==0" label="报告期">
          <el-cascader
            v-model="reportPeriod"
            style="width: 100%"
            placeholder="请选择内容"
            :options="reportPeriodOptions"
            :props="{ value:'reportCode', label: 'reportName', children: 'list' }"
          />
        </jr-form-item>
        <jr-form-item v-if="searchForm.benchmarkType==0" label="对标企业">
          <jr-combobox
            v-model="benchmarkCompList"
            style="width:240px;"
            multiple
            collapse-tags
            placeholder="请选择内容"
            :data="benchmarkCompOptions"
            option-value="bmEntId"
            option-label="bmEntName"
          />
        </jr-form-item>
        <jr-form-item v-if="searchForm.benchmarkType==1" label="对标企业">
          <jr-combobox
            v-model="benchmarkComp"
            style="width:240px;"
            placeholder="请选择内容"
            :data="benchmarkCompOptions"
            option-value="bmEntId"
            option-label="bmEntName"
          />
        </jr-form-item>
        <jr-form-item v-if="searchForm.benchmarkType==0" label="指标自定义">
          <split-select
            v-model="indicators"
            style="width:240px;"
            value-key="fieldCode"
            label-key="fieldName"
            :clearable="false"
            :options="indicatorsOptions"
            @emitOptsData="handleIndicatorsOptsData"
            @addItem="() => saveIndicatorsInfo()"
            @removeItem="() => saveIndicatorsInfo()"
          />
        </jr-form-item>
        <jr-form-item v-if="searchForm.benchmarkType==1" label="指标选择">
          <jr-combobox
            v-model="indicatorsChoose"
            style="width:240px;"
            :clearable="false"
            collapse-tags
            multiple
            :multiple-limit="3"
            placeholder="请选择内容"
            :data="indicatorsOptions"
            option-value="fieldCode"
            option-label="fieldName"
          />
        </jr-form-item>
        <jr-form-item v-if="searchForm.benchmarkType==1" label="报告时间">
          <custom-date-picker
            v-model="reportYearRange"
            type="yearrange"
            style="width:260px !important;"
            :clearable="false"
            range-separator="至"
            start-placeholder="开始年份"
            end-placeholder="结束年份"
            value-format="yyyy"
          />
        </jr-form-item>
        <jr-form-item v-if="searchForm.benchmarkType==1" label="报告类型">
          <jr-combobox
            v-model="searchForm.reportType"
            style="width:180px;"
            placeholder="请选择内容"
            :data="params.reportTypeOptions"
            option-value="itemcode"
            option-label="cnname"
          />
        </jr-form-item>
        <jr-form-item v-if="searchForm.benchmarkType==0">
          <el-checkbox v-model="searchForm.isDataEnt" true-label="1" false-label="0">过滤无数据企业</el-checkbox>
        </jr-form-item>
        <el-button type="primary" @click="queryData">查询</el-button>
        <span class="align-right">
          <span v-if="searchForm.benchmarkType==0">
            <el-radio-group v-model="showComponent" class="radio-group">
              <el-radio-button label="chart"><i class="el-icon-s-data" /></el-radio-button>
              <el-radio-button label="table"><jr-svg-icon icon-class="ben-table" /></el-radio-button>
            </el-radio-group>
            <el-button v-if="showComponent=='table'" class="export-btn" @click="exportDataToExcel">
              <jr-svg-icon icon-class="upload" />
              <span class="export-btn-label">导出</span>
            </el-button>
          </span>
          <span v-if="(searchForm.benchmarkType==0 && showComponent=='chart') || searchForm.benchmarkType==1">
            <el-button class="export-btn" @click="exportChartToImage">
              <jr-svg-icon icon-class="upload" />
              <span class="export-btn-label">导出</span>
            </el-button>
          </span>
        </span>
      </el-form>
      <div v-if="searchForm.benchmarkType==0" class="indacitor-tabs">
        <el-tabs v-model="selectedIndicator" type="card">
          <el-tab-pane
            v-for="item in ylist"
            :key="item.fieldCode"
            :label="item.fieldName"
            :name="item.fieldCode"
          />
        </el-tabs>
      </div>
    </div>
    <div class="content">
      <div class="content-area">
        <Echart
          v-if="searchForm.benchmarkType==0 && showComponent=='chart'"
          ref="indicatorsEchart"
          :options="indicatorsChartOptions"
          :styles="{ height: '400px' }"
        />
        <jr-table
          v-if="searchForm.benchmarkType==0 && showComponent=='table'"
          class="content-table"
          :height="tableConfig.pagination.total > 0 ? 330 : 370"
          :border="false"
          :columns="tableConfig.columns"
          :data-source="tableConfig.data"
          :loading="tableConfig.loading"
          :pagination="tableConfig.pagination"
          @sort-change="handleTableSortChange"
        />
        <Echart
          v-if="searchForm.benchmarkType==1 "
          ref="trendEchart"
          :options="trendChartOptions"
          :styles="{ height: '400px' }"
        />
      </div>
    </div>
  </div>
</template>

<script>
import * as API from '@/api/benchmark-analysis/benchmark-analysis.js'
import * as ChartCf from './chart-config.js'
import * as TableCf from './table-config.js'
import SplitSelect from '@/components/split-select'
import CustomDatePicker from '@/components/custom-date-picker/index.js'
import Echart from '@jupiterweb/components/echarts'
import moment from 'moment'

export default {
  components: {
    SplitSelect, CustomDatePicker, Echart
  },
  props: {
    params: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      entOrgCode: this.params.entOrgCode,
      moduleType: 'FINANCIAL_BENCHMARKING',
      showComponent: 'chart',
      searchForm: {
        benchmarkType: 0,
        reportYear: null,
        reportCode: null,
        issuerCodeList: [],
        isDataEnt: '0',
        reportType: '04',
        reportYearStart: '',
        reportYearEnd: ''
      },
      ylist: [],
      nlist: [],
      reportPeriod: [],
      benchmarkCompList: [],
      benchmarkComp: this.params.entId,
      selectedIndicator: 'totassets', // 默认选中“总资产”
      indicators: [],
      indicatorsChoose: ['totassets'], // 默认选中“总资产”
      reportYearRange: [],
      benchmarkCompOptions: [],
      reportPeriodOptions: [],
      indicatorsOptions: [],
      indicatorsChartOptions: {},
      trendChartOptions: {},
      tableConfig: {},
      loading: false
    }
  },
  watch: {
    'benchmarkCompList'(val) {
      this.searchForm.issuerCodeList = val
    },
    'benchmarkComp'(val) {
      this.searchForm.issuerCodeList = [val]
    },
    'reportPeriod'(val) {
      if (Array.isArray(val)) {
        this.searchForm.reportYear = val[0] || ''
        this.searchForm.reportCode = val[1] || ''
      }
    },
    'ylist': {
      handler(val) {
        this.handleDynamicColumns(val)
      },
      deep: true
    },
    'selectedIndicator'(val) {
      this.handleIndicatorsChartData(val)
    },
    'indicatorsChoose': {
      handler(val) {
        // 初始化页面默认值
        this.initPageDefaultValue()
        this.handleTrendChartData(val)
      },
      immediate: true
    },
    'reportYearRange'(val) {
      if (Array.isArray(val)) {
        this.searchForm.reportYearStart = val[0] || ''
        this.searchForm.reportYearEnd = val[1] || ''
      }
    }
  },
  async created() {
    // 初始化页面默认值
    // this.initPageDefaultValue()
    // 查询获取债券类型下拉框信息
    await this.queryReportPeriodSelectList()
    // 查询获取已配置的对标企业下拉选择信息
    this.queryBenchmarkCompanySelectList()
    //
    this.queryIndicatorsSelectList()
  },
  methods: {
    /**
     * 初始化页面默认值
     */
    initPageDefaultValue() {
      this.$set(this, 'indicatorsChartOptions', { ...ChartCf.financialIndicatorsChartOptions })
      this.$set(this, 'trendChartOptions', { ...ChartCf.financialTrendChartOptions })
      this.$set(this, 'tableConfig', { ...TableCf.tableConfig })
    },
    /**
     * 查询获取报告期下拉框信息
     */
    async queryReportPeriodSelectList() {
      const data = await API.getFinancialBenchmarkReportPeriod({})
      this.reportPeriodOptions = Array.isArray(data) ? data : []
      // 设置报告期默认值为最新一期
      this.handleSelectedLatestPeriod()
    },
    /**
     * 查询获取已配置的对标企业下拉框信息
     */
    async queryBenchmarkCompanySelectList() {
      const { entId, entOrgCode, groupId } = this.params
      const data = await API.getBenchmarkCompanySelectList({ entId, entOrgCode, groupId })
      this.benchmarkCompOptions = Array.isArray(data) ? data : []
      // 设置对标企业全部选中
      this.setBenchmarkCompanyAllSelected()
    },
    /**
     * 设置对标企业全部选中
     */
    setBenchmarkCompanyAllSelected() {
      let allSelectedList = []
      const { benchmarkCompOptions } = this
      if (Array.isArray(benchmarkCompOptions) && benchmarkCompOptions.length > 0) {
        allSelectedList = benchmarkCompOptions.map(item => item.bmEntId)
      }
      this.benchmarkCompList = allSelectedList
      // 设置全部选中后，重新加载chart、table数据
      this.$nextTick(() => {
        this.queryData()
      })
    },
    /**
     * 处理默认选中最新一期
     */
    handleSelectedLatestPeriod() {
      const opts = this.reportPeriodOptions
      const valArr = []
      if (Array.isArray(opts) && opts.length > 0) {
        const yearObj = opts[opts.length - 1]
        valArr.push(yearObj.reportCode)
        if (Array.isArray(yearObj.list) && yearObj.list.length > 0) {
          const quarterObj = yearObj.list[yearObj.list.length - 1]
          valArr.push(quarterObj.reportCode)
        }
      }
      this.reportPeriod = valArr
    },
    /**
     * 查询指标下拉选择框信息
     */
    async queryIndicatorsSelectList() {
      const { entOrgCode, moduleType } = this
      const data = await API.getFinancialIndicatorsList({ entOrgCode, moduleType })
      const [indicatorsYArr, indicatorsNArr] = [[...data?.Y || []], [...data?.N || []]]
      this.indicators = indicatorsYArr.map(item => item.fieldCode)
      this.indicatorsOptions = [...indicatorsYArr, ...indicatorsNArr]
    },
    /**
     * 处理指标待选择、已选择数据
     * @param params 待选、已选数据源
     */
    handleIndicatorsOptsData({ selectedOpts, unselectedOpts }) {
      this.ylist = selectedOpts
      this.nlist = unselectedOpts
    },
    /**
     * 保存指标信息
     */
    saveIndicatorsInfo() {
      this.loading = true
      const { entOrgCode, moduleType, ylist, nlist } = this
      API.saveFinancialIndicators({
        entOrgCode, moduleType, ylist, nlist
      }, () => {
        this.loading = false
      })
    },
    /**
     * 财务对标类型改变处理函数
     */
    handleBenchmarkTypeChange() {
      this.queryData()
    },
    /**
     * 获取查询参数
     */
    getSearchParams() {
      const { entOrgCode, moduleType } = this
      const { entId, groupId } = this.params
      const { sortColumn, sortOrder } = this.tableConfig
      return {
        ...this.searchForm, entId, entOrgCode, groupId, moduleType, sortColumn, sortOrder
      }
    },
    /**
     * 查询财务对标数据
     */
    queryData() {
      this.tableConfig.loading = true
      API.getFinancialBenchmarkList({
        ...this.getSearchParams()
      }).then(data => {
        this.tableConfig.loading = false
        // table表格数据
        this.tableConfig.data = data || []
        // 处理指标对比chart数据
        if (this.searchForm.benchmarkType === 0 && this.showComponent === 'chart') {
          this.handleIndicatorsChartData(this.selectedIndicator)
        }
        // 处理趋势分析chart数据
        if (this.searchForm.benchmarkType === 1) {
          this.handleTrendChartData(this.indicatorsChoose)
        }
      }).finally(() => {
        this.tableConfig.loading = false
      })
    },
    /**
     * 处理财务指标对比图表数据
     * @param {String} field 需在图表中展示的字段
     */
    handleIndicatorsChartData(field) {
      // 图表数据
      const chartData = this.tableConfig.data || []
      let [xAxisData, seriesData] = [[], []]
      if (Array.isArray(chartData) && chartData.length > 0) {
        xAxisData = chartData.map(item => item.issuerName)
        seriesData = chartData.map(item => item[field])
      }
      this.indicatorsChartOptions.xAxis[0].data = xAxisData
      this.indicatorsChartOptions.series[0].data = seriesData
    },
    /**
     * 处理财务趋势分析图表数据
     * @param {Array} fieldArr 需在图表中展示的字段数组
     */
    handleTrendChartData(fieldArr) {
      // 目前仅支持处理3个指标，超过提示
      if (Array.isArray(fieldArr) && fieldArr.length > 3) {
        this.$message.warning('趋势分析图表当前仅支持显示3个指标')
        return
      }
      // 图表数据
      const chartData = this.tableConfig.data || []
      let xAxisData = []
      const seriesData = []
      if (Array.isArray(chartData) && chartData.length > 0) {
        xAxisData = chartData.map(item => item.reportYear)
        fieldArr.forEach(field => {
          seriesData.push(chartData.map(item => item[field]))
        })
      }
      this.trendChartOptions.xAxis[0].data = xAxisData
      for (const i of this.trendChartOptions.yAxis) {
        i.splitNumber = 4
      }
      this.trendChartOptions.series[0].data = seriesData[0] || []
      this.trendChartOptions.series[1].data = seriesData[1] || []
      this.trendChartOptions.series[2].data = seriesData[2] || []
    },
    /**
     * 处理列表动态列
     * @param {Array} columnsList 列参数
     */
    handleDynamicColumns(columnsList) {
      const columns = [{
        prop: 'issuerName',
        title: '发行人',
        fixed: true,
        width: '275'
      }]
      if (Array.isArray(columnsList) && columnsList.length > 0) {
        columnsList.forEach(column => {
          const { fieldCode, fieldName } = column
          const width = fieldName.length * 14 + 40
          const columnProps = {
            prop: fieldCode,
            title: fieldName,
            fit: true,
            align: ['issuerName'].includes(fieldCode) ? 'left' : 'right',
            sorter: ['issuerName'].includes(fieldCode) ? false : 'custom',
            width
          }
          columns.push(columnProps)
        })
      }
      this.tableConfig.columns = columns
    },
    /**
     * 列表字段排序处理函数
     * @param {Object} params 列配置信息
     */
    handleTableSortChange({ prop, order }) {
      this.tableConfig.sortColumn = prop
      this.tableConfig.sortOrder = order === 'descending' ? 'desc' : (order === 'ascending' ? 'asc' : null)
      this.queryData()
    },
    /**
     * 获取导出文件名称
     */
    getExportFileName() {
      const { groupName } = this.params
      const benchmarkTypeName = this.searchForm.benchmarkType === 0 ? '指标对比' : '趋势分析'
      return `${groupName}_${benchmarkTypeName}`
    },
    /**
     * 导出chart图表数据到image图片
     */
    exportChartToImage() {
      const fileName = `${this.getExportFileName()}_${moment().format('YYYYMMDDHHmmssSSS')}`
      const chart = this.searchForm.benchmarkType === 0 ? this.$refs.indicatorsEchart.myChart : this.$refs.trendEchart.myChart
      const imgUrl = chart.getDataURL({
        backgroundColor: '#ffffff',
        pixelRatio: 2 // 提高导出清晰度（可选）
      })
      const link = document.createElement('a')
      link.href = imgUrl
      link.download = `${fileName}.png`
      link.click()
    },
    /**
     * 导出债券对标数据到Excel
     */
    exportDataToExcel() {
      const fileName = `${this.getExportFileName()}`
      const params = {
        ...this.getSearchParams(),
        fileName
      }
      API.exportFinancialBenchmarkToExcel(params, this.searchForm.benchmarkType)
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .indacitor-tabs {
  .el-tabs--card>.el-tabs__header {
    border: 0;
    border-bottom: 0;
    .el-tabs__nav {
      border: 0;
      border-bottom: 0;
    }
    .el-tabs__nav-wrap.is-scrollable {
      padding: 0 40px;
    }
    .el-tabs__item {
      background: #F4F4F4;
      margin-right: 8px;
      border: 1px solid rgba(255,255,255,0);
      border-bottom: 1px solid rgba(255,255,255,0);
      border-left: 1px solid rgba(255,255,255,0);
      border-radius: 2px;
      padding: 0 16px;
      color: rgba(0,0,0,0.9);
    }
    .el-tabs__item.is-active {
      background: rgba(255,142,43,0.1);
      color: var(--theme--color);
    }
    .el-tabs__nav-next, .el-tabs__nav-prev {
      width: 32px;
      height: 32px;
      line-height: 30px;
      box-shadow: inset 1px 0px 0px 0px #CCCCCC, inset -1px 0px 0px 0px #CCCCCC, inset 0px 1px 0px 0px #CCCCCC, inset 0px -1px 0px 0px #CCCCCC;
      border-radius: 2px;
      color: #000000;
      text-align: center;
    }
  }
  .el-tabs__header {
    margin: 16px 0 0;
  }
}

.financial-benchmark {
    background: #fff;
    padding: 0 16px 16px 16px;

    .content {
        display: flex;
        overflow: hidden;
        margin-top: 16px;

        .content-area {
          flex: 1;
          min-width: 0;
          border: 1px solid #EAE9E9;
          border-radius: 4px;
        }

        .content-table {
          width: 100%;
          padding: 16px;
          .jr-pagination .el-pagination {
            padding: 10px 0 0;
          }
        }
    }
}
</style>

