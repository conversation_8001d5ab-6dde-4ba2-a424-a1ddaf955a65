<template>
  <div class="payment-reminder">
    <cockpit-header 
      :style="{ width:px2vw(362),height:px2vh(40) }" 
      title="付息兑付提醒"
    />
    <div class="payment-reminder-content">
      <div class="payment-reminder-content-item" v-for="i in 3" :key="i">
        <p>23成都建工MTN001有一笔付息兑付款828,840,000.00元待支付(支付金额以收款机构提供的支付信息为准)，请在2025年05月25日前结清，并尽快发布付息兑付公告</p>
        <p class="payment-reminder-content-item-date">2025-05-06</p>
      </div>
    </div>
  </div>
</template>

<script>
import cockpitHeader from '../../components/cockpit-header.vue';
import { px2vw,px2vh } from '../../utils/portcss';
export default {
  name: 'PaymentReminder',
  components:{
    cockpitHeader
  },
  methods:{
    px2vw,
    px2vh
  },
}
</script>

<style lang="scss" scoped>
@import '~assets/styles/portcss.scss';
.payment-reminder {
  width: 100%;
  height: vh(446);
  background-image: url("../../../../assets/cockpit/cockpit_pro_normal_bac.png");
  background-size: 100% 100%;
  padding: vh(8) vw(8);
  &-content {
    width: 100%;
    height: vh(390);
    padding-left: vw(31);
    padding-right: vw(32);
    overflow-y: scroll;
    &-item {
      width: 100%;
      padding-bottom: vh(17);
      border-bottom: 1px solid rgba(255,255,255,0.15);
      margin-top: vh(16);
      & > p {
        margin-bottom: 0;
      }
      & > p:nth-of-type(1) {
        width: 100%;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: vh(14);
        color: rgba(255,255,255,0.9);
        line-height: vh(20);
      }
      & > p:nth-of-type(2) {
        height: vh(20);
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: vh(14);
        color: rgba(255,255,255,0.55);
        line-height: vh(20);
        margin-top: vh(2);
        text-align: right;
      }
    }
  }
}
</style>
