<!--
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2024-01-15 20:43:28
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2024-01-16 10:25:59
 * @Description: 评价记录
-->
<template>
  <div class="evaluate-log">
    <h2>评价记录</h2>
    <el-collapse v-if="evaluateLog.length" v-model="activeName" accordion>
      <el-collapse-item v-for="(item,index) in evaluateLog" :key="`${index + 1}`" :name="`${index + 1}`">
        <template #title>
          <span>评价时间：{{ item.createTime | formatDate }}</span>
          <span>评价人员：{{ item.createUser }}</span>
          <span>总分：{{ item.rating }}</span>
          <span>星级：<el-rate :value="item.ratingLevel" disabled /></span>
          <!-- <span>时间范围：{{ item.mdate }} - {{ item.vdate }}</span> -->
        </template>
        <jr-table :columns="columns" :data-source="item.detail" />
      </el-collapse-item>
    </el-collapse>
    <jr-empty v-else text="暂无评价数据" />
  </div>
</template>

<script>
import * as API from '@/api/poc/performance-evaluation'
import { FormatDate } from 'jupiterweb/src/utils/common'
export default {
  filters: {
    formatDate(v) {
      return FormatDate(v, 'yyyy-MM-dd') || '--'
    }
  },
  props: {
    data: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      activeName: '1',
      evaluateLog: [],
      columns: [{
        prop: 'indexName',
        title: '评价指标'
      }, {
        prop: 'weight',
        title: '指标权重(%)',
        align: 'right',
        type: 'rate'
      }, {
        prop: 'indexValue',
        title: '指标得分',
        align: 'right',
        type: 'amount'
      }]
    }
  },
  created() {
    this.init()
  },
  methods: {
    init() {
      this.queryEvaluateLog()
    },
    queryEvaluateLog() {
      API.QueryEvaluateLog(this.data).then(res => {
        this.evaluateLog = res
      })
    }
  }
}
</script>

<style lang="scss">
.evaluate-log {
  .el-collapse-item__header {
    display: flex;
    justify-content: space-between;
    line-height: 28px;
    width: 100%;
    height: 40px;
    padding-left: 16px;
    &>span {
      display: inline-flex;
      line-height: 18px;
      flex: 1;
    }
  }
  .el-collapse-item__content {
    padding: 16px;
  }
}
</style>
