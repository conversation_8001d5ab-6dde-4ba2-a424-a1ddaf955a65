::v-deep .jr-decorated-table {
  position: relative;

  .jr-decorated-table--header {
    position: absolute;
    top: -48px;
    right: 8px;

    .jr-decorated-table--header-right {
      width: auto;
    }
  }
}

.issue-spread {
  background: #fff;
  padding: 0 16px;
  min-height: 100%;

  &-line {
    position: relative;
    width: 100%;
    height: 1px;

    &-inner {
      width: 100%;
      background-color: #eae9e9;
      position: absolute;
      top: -5px;
      height: 1px;
    }
  }

  &-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 16px;
    border: 1px solid #eae9e9;
    border-radius: 4px;
    height: 464px;
    padding: 16px;

    &-left {
      height: 100%;
      padding: 0 !important;
      flex: 0 0 580px !important;
      width: 580px !important;

      ::v-deep .el-form-item__label {
        padding-top: 12px !important;
      }
      &-header {
        display: flex;
        width: 100%;
        align-items: center;
        height: 34px;
      }

      &-search {
        margin-top: 8px;
        height: calc(100% - 80px) !important;
        overflow: scroll;
        width: 100% !important;

        ::v-deep .el-input__suffix-inner {
          height: 100% !important;
          display: flex;
          align-items: center;
        }

        display: flex;
        flex-direction: column;
        gap: 8px;
      }
    }

    &-right {
      width: calc(100% - 580px);
      margin-right: 16px;
      flex: 1;
      height: 100% !important;
      &-header {
        justify-content: space-between;
        height: 64px;
        padding: 21px;
        display: flex;
      }
    }
  }

  &-table {
    padding: 16px 0;
    background-color: #fff;

    ::v-deep .jr-decorated-table--header-left {
      display: none !important;
    }

    ::v-deep .el-table {
      th,
      tr,
      td {
        height: 36px !important;
      }

      .cell {
        height: 36px !important;
        line-height: 17px !important;
      }
    }

    &-header {
      &-btns {
        // padding-top: 8px;
        padding-left: 16px;
        padding-right: 16px;
        display: flex;
        // align-items: center;
        flex-wrap: wrap;
        gap: 8px;
        width: 100%;
        .left-area-single {
          flex: 1;
          display: flex;
          align-items: center;
          flex-wrap: wrap;
          overflow: hidden;
          gap: 8px;
          height: 32px !important;
        }

        .left-area {
          flex: 1;
          display: flex;
          align-items: center;
          flex-wrap: wrap;
          gap: 8px;
        }

        .right-ctrl {
          flex: 0 0 80px;
        }

        ::v-deep .el-button {
          margin-left: 0 !important;
        }
      }

      &-form {
        height: 48px !important;
        padding: 8px 8px;
        display: flex;
        align-items: center;
        gap: 16px;
        width: calc(100% - 48px);

        ::v-deep .el-form-item__label {
          padding-top: 10px !important;
          width: 88px;
        }

        ::v-deep .el-form-item__content {
          width: calc(100% - 88px);
        }

        &-left {
          flex: 1;
          display: flex;
          align-items: center;
          justify-content: flex-end;
          gap: 6px;

          ::v-deep .el-button--text {
            font-size: var(--el-font-size-base) !important;
            color: rgba(0, 0, 0, 0.9) !important;
          }
        }
      }
    }
  }
}
