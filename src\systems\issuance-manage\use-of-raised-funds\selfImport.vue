<template>
  <jr-modal
    :visible="visible"
    :handle-cancel="handleCancel"
    :height="277"
    :handle-ok="handleOk"
    :width="'33.54%'"
    :has-footer="false"
  >
    导入
    <template v-slot:body>
      <div class="area">
        <span>上传文件</span>
        <div class="area-middleArea">
          <div class="area-middleArea-input">
            <span>{{ fileName }}</span>
            <span class="area-middleArea-input-icon">
              <jr-svg-icon icon-class="excel" @click.stop="exportData" />
            </span>
          </div>
          <p>1.不支持导入募集资金用途的"使用明细"数据。</p>
          <p>2.导入数据更新方式为增量更新</p>
        </div>
        <span style="color: var(--theme--color); cursor: pointer" @click="downloadFile">下载模板</span>
        <span
          v-if="showErrorBtn"
          style="color: var(--theme--color); cursor: pointer; margin-left: 8px"
          @click="downloadErrorFile"
        >
          下载失败数据
        </span>
        <input
          ref="uploadInput"
          class="file"
          type="file"
          accept=".xls,.xlsx"
          style="display: none"
          enctype="multipart/form-data"
          @change="readExcel($event)"
        />
      </div>
      <div class="footer">
        <el-button @click.stop="handleCancel">取消</el-button>
        <el-button type="primary" @click.stop="handleOk">确认</el-button>
      </div>
    </template>
  </jr-modal>
</template>

<script>
import { issuancePurposeImport, rasiedFundsExport } from '@/api/issuance/issuance'
import { downloadTemplate } from '@/api/public/public'
import { EventBus } from '../../redemption-manage/event-bus'
export default {
  props: {
    tabActiveName: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      visible: true,
      fileName: '',
      formData: null,
      errorList: [],
      showErrorBtn: false
    }
  },
  methods: {
    async downloadFile() {
      await downloadTemplate({
        name: this.tabActiveName === '1' ? '债券类募集资金用途导入模板' : '非债券类募集资金用途导入模板',
        type: 'excel'
      })
    },
    async downloadErrorFile() {
      await rasiedFundsExport({
        moduleType: this.tabActiveName,
        errorList: this.errorList
      })
    },
    /**
     * 弹框关闭
     */
    handleCancel() {
      this.visible = false
    },
    // 导入
    exportData() {
      this.$refs.uploadInput.click()
    },
    // 导入信息
    readExcel(e) {
      if (e.target && !e.target.files[0]) return false
      const formData = new FormData()
      // 通过files就可以拿到所有上传的文件，如果是多个文件循环即可
      formData.append('file', e.target.files[0])
      formData.append('moduleType', this.tabActiveName)
      this.fileName = e.target.files[0].name
      this.formData = formData
    },
    /**
     * 提交按钮回调
     */
    async handleOk() {
      if (!this.formData) {
        this.$message.warning('请选择文件')
        return
      }

      await issuancePurposeImport(this.formData, (...args) => this.getResponese(...args))
    },
    getResponese(...args) {
      const res = args[1]
      console.log(res, 'res')

      if (Array.isArray(res)) {
        this.errorList = res
        this.showErrorBtn = true
        this.$message.warning('导入失败，请下载失败数据')
      } else {
        if (!res) {
          this.showErrorBtn = false
          EventBus.$emit('refresh-use-of-raised-funds-list')
          this.handleCancel()
        }
        // this.$message.success('导入成功')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.area {
  display: flex;
  align-items: start;
  justify-content: center;
  span {
    height: 28px;
    font-family: MicrosoftYaHei;
    font-size: var(--el-font-size-base);
    color: rgba(0, 0, 0, 0.85);
    line-height: 28px;
    text-align: center;
    font-style: normal;
  }
  p {
    height: 24px;
    font-size: var(--el-font-size-base);
    color: rgba(0, 0, 0, 0.6);
    margin-bottom: 0px;
  }
  &-middleArea {
    margin-left: 8px;
    &-input {
      width: 282px;
      height: 32px;
      background: #ffffff;
      border-radius: 2px;
      border: 1px solid #cccccc;
      margin-bottom: 8px;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      &-icon {
        width: 30px;
        height: 100%;
        background: #f4f4f4;
        border-radius: 0px 2px 2px 0px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
      }
    }
  }
}
.footer {
  width: 100%;
  height: 119px;
  display: flex;
  align-items: flex-end;
  justify-content: center;
  padding-bottom: 12px;
  button {
    width: 88px;
  }
  background-image: url('../../../assets/images/bondEntryBac.png');
  background-size: 100% 100%;
  position: absolute;
  bottom: 0px;
  left: 0px;
}
</style>
