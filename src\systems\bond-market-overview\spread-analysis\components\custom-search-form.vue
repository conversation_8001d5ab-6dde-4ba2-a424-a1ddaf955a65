<template>
  <div class="list-item">
    <el-checkbox
      v-model="localChecked"
      @change="emitCheckStatus"
      :disabled="!localValue || (Array.isArray(localValue) && localValue.length === 0)"
    >
      <div class="list-item-label">
        <div class="list-item-label-icon">
          <div class="list-item-label-icon-left" :style="{ background: iconcolor }" />
          <div class="list-item-label-icon-center" :style="{ borderColor: iconcolor }" />
          <div class="list-item-label-icon-right" :style="{ background: iconcolor }" />
        </div>
        <span class="list-item-label-text">
          {{ label }}{{ subtext }}
          <el-tooltip v-if="showtooltip" placement="right" effect="dark">
            <jr-svg-icon icon-class="info-circle" />
            <div slot="content" v-html="tiptext" style="max-width: 300px" />
          </el-tooltip>
        </span>
      </div>
    </el-checkbox>

    <jr-combobox
      v-model="localValue"
      class="list-item-right"
      style="width: 172px; height: 32px"
      :data="selectlist"
      :auto-convert="true"
      :multiple="true"
      clearable
      collapse-tags
      option-value="itemcode"
      option-label="cnname"
      placeholder="请选择内容"
      @change="emitCheckStatus"
    />
  </div>
</template>

<script>
export default {
  props: {
    selectlist: {
      type: Array,
      default() {
        return []
      }
    },
    checked: {
      type: Boolean,
      default() {
        return false
      }
    },
    iconcolor: {
      type: String,
      default() {
        return '#409EFF'
      }
    },
    label: {
      type: String,
      default() {
        return ''
      }
    },
    subtext: {
      type: String,
      default() {
        return ''
      }
    },
    showtooltip: {
      type: Boolean,
      default() {
        return false
      }
    },
    tiptext: {
      type: String,
      default() {
        return ''
      }
    },
    value: {
      type: Array,
      default() {
        return []
      }
    },
    index: {
      type: Number,
      default() {
        return 0
      }
    },
    liststr: {
      type: String,
      default() {
        return ''
      }
    }
  },
  data() {
    return {}
  },
  methods: {
    emitCheckStatus() {
      this.$emit('change')
    }
  },
  computed: {
    localValue: {
      get() {
        return this.value
      },

      set(value) {
        this.$emit('update:value', value)
      }
    },
    localChecked: {
      get() {
        return this.checked
      },

      set(value) {
        this.$emit('update:checked', value)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import '../css/custom-search-form-temporary.scss';
</style>
