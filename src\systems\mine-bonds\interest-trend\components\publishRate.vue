<template>
  <!-- 发行利差 -->
  <div class="publish-rate">
    <jr-layout-vertical :height="48" disabled>
      <template v-slot:top>
        <div class="publish-rate-form">
          <el-form inline label-width="80">
            <jr-form-item v-sysversion="'group'" label="发行人">
              <jr-combobox
                v-model="sInfoWindcodelist"
                style="max-width: 285px"
                placeholder="请选择"
                clearable
                multiple
                collapse-tags
                :data="peopleList"
                option-value="value"
                option-label="text"
              />
            </jr-form-item>
            <jr-form-item label="发行截止日">
              <el-date-picker
                v-model="dateRange"
                type="daterange"
                range-separator="至"
                unlink-panels
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="yyyy-MM-dd"
                format="yyyy-MM-dd"
                class="date-picker"
                style="max-width: 285px"
                :picker-options="pickerOptions"
              />
            </jr-form-item>
          </el-form>
          <div class="btn-list">
            <el-button type="primary" @click="submit">查询</el-button>
          </div>
        </div>
      </template>

      <template v-slot:bottom>
        <div class="publish-rate-content">
          <div class="publish-rate-content-chart">
            <Echart ref="charts" :options="options" :styles="{ height: '100%' }" />
            <div class="chart-export">
              <el-dropdown trigger="click" @command="chartCommand">
                <el-button>
                  <jr-svg-icon class="el-icon--right" icon-class="upload" />
                  导出
                  <i class="el-icon-caret-bottom" />
                </el-button>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item command="tp">
                    <jr-svg-icon icon-class="picture" />
                    导出图片
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </div>
          </div>
          <div class="publish-rate-content-table">
            <div class="btn-list">
              <div class="export-btn" @click="exportList">
                <jr-svg-icon icon-class="upload" />
              </div>
            </div>
            <jr-table
              :height="415"
              :columns="configTable.columns"
              :data-source="configTable.data"
              :loading="configTable.loading"
              :pagination="configTable.pagination"
              :on-change="
                (res) => {
                  changeBondList(res)
                }
              "
            />
          </div>
        </div>
      </template>
    </jr-layout-vertical>
  </div>
</template>
<script>
import Echart from '@jupiterweb/components/echarts'
import { queryBondRatingAgency, queryBondList, queryyieldtimage, exportBondList } from '@/api/bonds/bonds'
export default {
  components: {
    Echart
  },
  props: {
    permitdetail: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      // 表格数据
      configTable: {
        loading: false,
        columns: [],
        data: [],
        pagination: {
          pageNo: 1,
          pageSize: 10,
          pageSizeOptions: [10, 20, 50, 100],
          total: 0,
          showSizeChanger: true
        }
      },
      columns: [],
      queryData: {
        bIssueLastissueBegin: null,
        bIssueLastissueEnd: null
      },
      peopleList: [],
      sInfoWindcodelist: [],
      dateRange: [],
      options: {
        legend: {
          top: 16,
          left: 'center',
          orient: 'horizontal',
          textStyle: {
            color: 'rgba(0, 0, 0, 0.85)'
          }
        },
        grid: {
          top: '72',
          left: '56',
          right: '56',
          bottom: '56'
        },
        toolbox: {
          show: false
        },
        tooltip: {
          trigger: 'axis',
          appendToBody: true,
          formatter: function(params) {
            let dom = '<div>'
            for (const i of params) {
              let val = i.value[1]
              if (i.seriesName === '集团债券票面利率' || i.seriesName === '我司债券票面利率') {
                val = val.toFixed(4);
                while (val.split('.')[1].length < 4) {
                  val += '0';
                }
              } else {
                val = val.toFixed(2);
                while (val.split('.')[1].length < 2) {
                  val += '0';
                }
              }
              dom += `<div>${i.marker}<span style="display: inline-block; width: 200px;">${i.seriesName}</span><span>${val}</span></div>`
            }
            return dom + '</div>'
          }
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          axisLabel: {
            color: 'rgba(0, 0, 0, 0.85)'
          }
        },
        yAxis: [
          {
            name: '单位(BP)',
            type: 'value',
            splitNumber: 4
          },
          {
            name: '单位(%)',
            type: 'value',
            splitNumber: 4
          }
        ],
        series: []
      },
      limit: 10,
      defaultDate: [],
      pickerOptions: {
        disabledDate: (time) => {
          if (!this.limitDate) {
            return false
          }

          const start = new Date(this.limitDate).setFullYear(new Date(this.limitDate).getFullYear() - 3)
          const end = new Date(this.limitDate).setFullYear(new Date(this.limitDate).getFullYear() + 3)
          const current = new Date(time)

          // 不在范围内则禁用
          return current < start || current > end
        },
        onPick: (obj) => {
          if (!obj.maxDate) {
            this.limitDate = obj.minDate
          } else {
            this.limitDate = null
          }
        }
      },
      limitDate: null
    }
  },
  mounted() {
    const sysVersion = localStorage.getItem('sysVersion')
    this.configTable.columns =
      sysVersion === 'group'
        ? [
          {
            title: '发行人',
            prop: 'binfoIssuer'
          },
          {
            title: '主体性质',
            prop: 'compProperty'
          },
          {
            title: '债券简称',
            prop: 'sinfoName'
          },
          {
            title: '发行截止日',
            prop: 'bissueLastissue'
          },
          {
            title: '发行规模(亿元)',
            prop: 'bissueAmountact',
            className: 'rate-columns-num'
          },
          {
            title: '发行期限',
            prop: 'term'
          },
          {
            title: '票面利率(%)',
            prop: 'latestCouponrate',
            className: 'rate-columns-num'
          },
          {
            title: '同期限国开债利差(BP)',
            prop: 'bankdebtSpread',
            className: 'rate-columns-num'
          },
          {
            title: '同期限国债利差(BP)',
            prop: 'debtSpread',
            className: 'rate-columns-num'
          },
          {
            title: '同期限同评级基准利差(BP)',
            prop: 'referenceSpread',
            className: 'rate-columns-num'
          },
          {
            title: '状态',
            prop: 'issueStatus'
          }
        ]
        : [
          {
            title: '债券简称',
            prop: 'sinfoName'
          },
          {
            title: '发行截止日',
            prop: 'bissueLastissue'
          },
          {
            title: '发行规模(亿元)',
            prop: 'bissueAmountact',
            className: 'rate-columns-num'
          },
          {
            title: '发行期限',
            prop: 'term'
          },
          {
            title: '票面利率(%)',
            prop: 'latestCouponrate',
            className: 'rate-columns-num'
          },
          {
            title: '同期限国开债利差(BP)',
            prop: 'bankdebtSpread',
            className: 'rate-columns-num'
          },
          {
            title: '同期限国债利差(BP)',
            prop: 'debtSpread',
            className: 'rate-columns-num'
          },
          {
            title: '同期限同评级基准利差(BP)',
            prop: 'referenceSpread',
            className: 'rate-columns-num'
          },
          {
            title: '状态',
            prop: 'issueStatus'
          }
        ]
    this.getBondIssuerInfo()
    this.dateRange = [new Date().getFullYear() + '-01-01', new Date().getFullYear() + '-12-31']
    this.queryData = {
      bIssueLastissueBegin: this.dateRange[0],
      bIssueLastissueEnd: this.dateRange[1]
    }
    this.getBondList(
      {
        pageNo: 1,
        pageSize: this.limit
      },
      {
        listType: 'FXLC',
        ...this.queryData
      }
    )
    this.drawChart()
  },
  methods: {
    callFn(data) {
      this.columns = data.config.columns
    },
    submit() {
      this.dateRange
      ? this.queryData = {
          bIssueLastissueBegin: this.dateRange[0],
          bIssueLastissueEnd: this.dateRange[1]
        }
      : this.queryData = {}
      const sysVersion = localStorage.getItem('sysVersion')
      if (sysVersion === 'group') {
        this.queryData.sInfoWindcodelist = this.sInfoWindcodelist || []
        this.getBondList(
          {
            pageNo: 1,
            pageSize: this.limit
          },
          {
            listType: 'FXLC',
            ...this.queryData
          }
        )
      } else {
        this.getBondList(
          {
            pageNo: 1,
            pageSize: this.limit
          },
          {
            listType: 'FXLC',
            ...this.queryData
          }
        )
      }
      this.drawChart()
    },
    chartCommand(type) {
      if (type === 'tp') {
        // 下载图片
        const exportOptions = {
          backgroundColor: '#ffffff',
          pixelRatio: 2 // 提高导出清晰度（可选）
        }
        // // 生成图片 URL 并触发下载
        const chart = this.$refs.charts.myChart
        const imgUrl = chart.getDataURL(exportOptions)
        const link = document.createElement('a')
        link.href = imgUrl
        link.download = `利差曲线.png`
        link.click()
      }
    },
    async getBondIssuerInfo() {
      const data = {
        text: ''
      }
      const res = await queryBondRatingAgency(data)
      const list = []
      for (const i of res) {
        if (i) {
          list.push(i)
        }
      }
      this.peopleList = list
    },
    getBondList(page, data) {
      this.configTable.loading = true
      queryBondList({ page, data }).then((res) => {
        this.configTable.data = res?.list || []
        this.configTable.pagination = {
          ...this.configTable.pagination,
          pageNo: res?.pageNum,
          total: res?.total
        }
        this.configTable.loading = false
      })
    },
    changeBondList(page) {
      let pageNo = page.page
      if (this.configTable.pagination.total < (pageNo - 1) * page.limit) {
        pageNo = 1
      }
      this.limit = page.limit
      this.getBondList(
        {
          pageNo,
          pageSize: page.limit
        },
        {
          listType: 'FXLC',
          ...this.queryData
        }
      )
    },
    drawChart() {
      const sysVersion = localStorage.getItem('sysVersion')      
      const series = []
      const form = { imagetype: 'FXLC', ...this.queryData }
      if (sysVersion === 'group') {
        form.sInfoWindcodelist = this.sInfoWindcodelist
      }
      const dateList = []
      queryyieldtimage(form).then((res) => {
        for (const i in res) {
          const list = []
          const listData = {}
          for (const j of res[i]) {
            const date = this.formatDate(j.tradeDt)
            if (listData[date]) {
              listData[date] = listData[date] + (j.val || 0)
            } else {
              listData[date] = j.val || 0
            }
            dateList.push(date)
          }
          for (const j in listData) {
            const num = listData[j].toFixed(4)
            list.push([j, Number(num)])
          }
          if (i !== '债券票面利率') {
            series.push({
              name: i,
              type: 'line', // 设置图表类型为折线图
              data: list
            })
          } else {
            const name = sysVersion === 'group' ? '集团' : '我司'
            series.push({
              name: name + i,
              symbolSize: 12,
              data: list,
              type: 'scatter',
              yAxisIndex: 1
            })
          }
        }
        const deWeightDate = [...new Set(dateList)];
        const sortDate = deWeightDate.sort((a, b) => new Date(a).getTime() - new Date(b).getTime());
        this.options.xAxis.data = sortDate
        this.options.series = series
      })
    },
    exportList() {
      const form = { ...this.queryData }
      form.listType = 'FXLC'
      exportBondList(form)
    },
    formatDate(date) {
      if (date && date.length > 0) {
        const list = date.split('-');
        const newDate = list.join('/')
        return newDate
      }
      return ''
    }
  }
}
</script>
<style lang="scss">
.publish-rate {
  height: calc(100% - 32px);

  .vertical-layout {
    background: #fff;
    padding: 0;
    height: 100%;

    &--top-content {
      padding: 0 16px;
    }

    &--resize {
      height: 1px;
      color: #eae9e9;
    }

    &--bottom {
      margin-top: 16px;
      padding: 0 16px;
    }
  }

  &-form {
    position: relative;

    .el-form {
      display: flex;
      align-items: center;
      padding-top: 16px;
      width: calc(100% - 80px);

      .el-form-item {
        width: 100%;
        max-width: 353px;

        .el-form-item__label {
          padding: 8px 8px 0 0;
        }

        .el-form-item__content {
          width: calc(100% - 80px);
        }

        &.no-label {
          .el-form-item__content {
            width: 100%;
          }
        }
      }
    }

    .btn-list {
      position: absolute;
      top: 8px;
      right: 0;

      .el-button {
        margin-left: 16px;
      }
    }
  }

  &-content {
    height: 100%;

    .jr-decorated-table--header-left {
      display: none;
    }

    .jr-decorated-table--header-right {
      display: none;
    }

    .jr-decorated-table--body {
      padding: 0;
    }

    &-chart {
      position: relative;
      height: 415px;

      .chart-export {
        position: absolute;
        top: 0;
        right: 0;
      }
    }

    &-table {
      .btn-list {
        text-align: right;

        .export-btn {
          display: inline-block;
          margin: 8px 0;
          width: 32px;
          height: 32px;
          line-height: 32px;
          text-align: center;
          border: 1px solid var(--el-border-color-button);
          transform: translateY(1px);

          .jr-svg-icon {
            fill: var(--theme--color);
            font-size: var(--el-font-size-medium);
          }

          &:hover {
            border-left-color: var(--theme--color-light-7);
            background: var(--theme--color-light-9);
            cursor: pointer;
          }
        }
      }

      .el-table {
        border: none;

        &::before {
          display: none;
        }

        &::after {
          display: none;
        }

        table {
          tbody {
            td.el-table__cell {
              border-right: none;

              &.rate-columns-num {
                text-align: right;
              }
            }
          }
        }
      }
    }
  }
}
</style>
