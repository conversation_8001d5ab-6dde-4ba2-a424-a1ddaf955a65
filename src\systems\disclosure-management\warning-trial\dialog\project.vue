<template>
  <div>
    <el-dialog title="立案信息详情" :visible.sync="visible" width="1120px" :before-close="handleCancel">
      <div class="dialog-content">
        <el-descriptions :column="3" :size="size" border>
          <el-descriptions-item>
            <template slot="label">案号</template>
            2025沪公刑诉字第056号
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">法院</template>
            成都市中级人民法院
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">承办法官</template>
            罗翔
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">法官助理</template>
            张伟
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">立案时间</template>
            2025-01-05
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">开庭时间</template>
            2025-02-05
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">结束时间</template>
            2025-03-05
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">案件状态</template>
            --
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">当事人</template>
            张三
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  data() {
    return {
      visible: false,
      title: '',
      data: {},
      size: ''
    }
  },
  computed: {},
  methods: {
    /**
     * 弹框打开
     * @param {Object} row 行内数据
     * @param {String} title 展示表格名称
     */
    open(data) {
      this.visible = true
      this.data = data
    },
    /**
     * 弹框关闭
     */
    handleCancel() {
      this.visible = false
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-dialog__title {
  font-size: var(--el-font-size-extra-large);
  font-family: MicrosoftYaHeiSemibold;
  color: rgba($color: #000000, $alpha: 0.85);
  font-weight: bold;
}
::v-deep .el-dialog__body {
  padding: 0;

  .dialog-content {
    padding: 16px;
    background: #F0F2F5;

    .el-descriptions {
      padding: 16px;
      height: 552px;
      background: url(../../../../assets/images/dialog-bg.png) no-repeat left bottom;
      background-size: 100% auto;
      background-color: #ffffff;

      th.el-descriptions-item__cell.el-descriptions-item__label.is-bordered-label {
        background: #F7F8FA;
      }

      td.el-descriptions-item__cell.el-descriptions-item__content {
        width: 192px;
      }
    }
  }
}
</style>
