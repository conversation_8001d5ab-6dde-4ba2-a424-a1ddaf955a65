// 关联企业配置
<template>
  <div class="associated-config">
    <div class="public-table-search-container associated-config-select">
      <label>企业名称</label>
      <el-autocomplete
        v-model="form.relatedCompName"
        style="width: 246px"
        placeholder="请输入企业全称"
        clearable
        remote
        :fetch-suggestions="remoteSearch"
        :loading="loading"
        @clear="handleClear"
        @select="handleSelect"
      />
      <el-button v-if="getAuth('添加')" type="primary" :disabled="isAddDisabled" @click="openModal">添加</el-button>
    </div>
    <div class="associated-config-content">
      <div class="associated-config-content-table">
        <!-- 接自定义列表 tableId换成自己的id -->
        <jr-decorated-table
          ref="table"
          stripe
          :params="tableParams"
          :custom-id="tableId"
          :menuinfo="menuinfo"
          v-bind="{ ...$props }"
          :handleedit="handleEdit"
          :handledelete="handleDelete"
          @refreshed="callFn"
        />
      </div>
    </div>
    <!-- 新增修改弹窗 -->
    <jr-modal
      :modal-class="'associated-config-modal'"
      :visible="visible"
      size="small"
      :height="150"
      :handle-cancel="closeModal"
      :handle-ok="handleOk"
      :has-footer="false"
    >
      <span class="associated-config-modal-title">{{ isEdit ? '修改关联关系' : '新增关联关系' }}</span>
      <template v-slot:body>
        <el-form
          ref="modalFormRef"
          :rules="rules"
          :model="modalForm"
          label-width="80px"
          class="associated-config-modal-form"
        >
          <!-- 要展示 form-item 还是展示span展示文字 -->
          <jr-form-item v-if="!isEdit" label="企业名称" style="height: 32px">
            <!-- 里面的输入框依赖外面的值 -->
            <el-input v-model="modalForm.relatedCompName" style="width: 246px" disabled />
          </jr-form-item>
          <span v-else class="associated-config-modal-show">{{ modalForm.relatedCompName }}</span>
          <jr-form-item label="关联关系" prop="checked" style="margin-top: 20px">
            <jr-checkbox-group v-model="modalForm.checked" style="width: 246px" :data="checkboxList" />
          </jr-form-item>
          <div class="associated-config-modal-buttons">
            <el-button @click.stop="closeModal">取消</el-button>
            <el-button type="primary" @click.stop="handleOk">确定</el-button>
          </div>
        </el-form>
      </template>
    </jr-modal>
  </div>
</template>
<script>
import { queryCompName, addBondRelated, updateBondRelated, deleteBondRelated } from '@/api/bonds/bonds'
import { GetComboboxList } from '@/api/home'
import { debounce } from 'lodash'
export default {
  name: 'Config',
  props: {
    permitdetail: {
      type: Object,
      default: () => ({})
    },
    menuinfo: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      columns: [],
      // 自定义列id
      tableId: 'fa7bdf913b8e432db30bcb65efc07fe1',
      // 菜单id
      ownedModuleid: '1361284917287215104',
      tableParams: {
        ownedModuleid: '1361284917287215104'
        // include_expired: 0
      },
      form: {
        relatedCompName: '',
        relatedCompId: ''
      },
      modalForm: {
        relatedCompName: '',
        relatedCompId: '',
        checked: []
      },
      peopleList: [],
      nameList: [],
      visible: false, // 控制模态框的显示
      checkboxList: [],
      isEdit: false, // 新增编辑状态标识
      currentRow: null, // 当前操作的行数据
      loading: false, // 远程搜索加载状态
      rules: {
        checked: [{ type: 'array', required: true, message: '请至少选择一个活动性质', trigger: 'change' }]
      }
    }
  },
  computed: {
    isAddDisabled() {
      return !this.form.relatedCompId // 当企业名称为空时禁用添加按钮
    }
  },
  created() {
    console.log(this.permitdetail, 'permitdetail')
    console.log(this.menuinfo, 'menuinfo')
    // 初始化表格列
    this.getComboboxList()
    this.debouncedRemoteSearch = debounce(this.rawRemoteSearch, 500)
  },
  methods: {
    // 判断是否包含某按钮权限
    getAuth(str) {
      const btnList =
        Object.hasOwnProperty(this.menuinfo, btnList) && Array.isArray(this.menuinfo.btnList)
          ? this.menuinfo.btnList
          : []
      if (btnList.length === 0) return false
      const authArr = btnList.find((item) => item.btnnm === str)
      if (Array.isArray(authArr) && authArr.length > 0) {
        return Object.hasOwnProperty(this.permitdetail, authArr[0].btnkey)
      }
      return false
    },
    callFn(data) {
      console.log(data, 'data')
      data.config.hasAction = this.getAuth('编辑') || this.getAuth('删除')
      data.resize()
    },
    openModal() {
      this.isEdit = false // 重置编辑状态
      this.modalForm = {
        relatedCompName: this.form.relatedCompName,
        relatedCompId: this.form.relatedCompId,
        checked: []
      }
      this.visible = true // 显示模态框
      //   点击查询给tableParams赋值 会触发列表的查询
    },
    handleEdit(...args) {
      const row = args[2]
      this.isEdit = true
      this.currentRow = row
      // 根据当前行数据填充表单
      this.modalForm = {
        objectId: row.objectId,
        compId: row.compId,
        compName: row.compName,
        relatedCompName: row.relatedCompName, // 企业名称
        relatedCompId: row.relatedCompId, // 企业id
        checked: row.relationship.split(',') // 关联关系 字符串转数组
      }
      this.visible = true
    },
    // 删除
    handleDelete(...args) {
      const row = args[2]
      this.$confirm('确定要删除这条记录吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        customClass: 'no-icon-confirm'
      })
        .then(() => {
          // 这里可以触发自定义删除逻辑
          // 参数待确认
          const params = {
            objectId: row.objectId
          }
          deleteBondRelated(params).then((res) => {
            this.$message({
              type: 'success',
              message: '删除成功'
            })
            this.$refs.table.triggerTableRefresh()
          })
          //   this.$emit('delete', row)
        })
        .catch(() => {})
    },
    // 保存
    handleOk() {
      this.$refs.modalFormRef.validate((valid) => {
        if (valid) {
          // 表单校验通过
          console.log('提交数据:', this.modalForm)
          this.visible = false
          console.log(this.checkboxList, 'checkboxList')

          // 通过checked数组 通过checkboxList获取compName值
          const relationshipName = this.modalForm.checked.map((item) => {
            return this.checkboxList.find((checkboxItem) => checkboxItem.value === item).cnname
          })
          console.log(relationshipName, 'relationshipName')

          const params = {
            relatedCompId: this.modalForm.relatedCompId, // 关联企业id 暂时为空
            relatedCompName: this.modalForm.relatedCompName, // 关联企业名称
            relationship: this.modalForm.checked, // 关联关系
            relationshipName: relationshipName
          }
          console.log(params, 'params')

          //
          // return
          if (this.isEdit) {
            params.objectId = this.modalForm.objectId
            params.compId = this.modalForm.compId
            params.compName = this.modalForm.compName
            // 修改
            updateBondRelated(params).then((res) => {
              this.$message({
                type: 'success',
                message: '修改成功'
              })
              this.$refs.table.triggerTableRefresh()
              this.visible = false
            })
          } else {
            // 新增
            addBondRelated(params).then((res) => {
              console.log(res, '新增成功')
              this.$message({
                type: 'success',
                message: '新增成功'
              })
              this.visible = false
              this.modalForm = {
                relatedCompName: '',
                relatedCompId: '',
                checked: []
              }
              this.$refs.table.triggerTableRefresh()
            })
          }

          // 这里添加实际的提交API调用
          // await submitData(this.modalForm)
        }
      })
    },
    closeModal() {
      this.visible = false
    },
    remoteSearch(query, cb) {
      if (!query) return cb([])
      this.loading = true
      this.debouncedRemoteSearch(query, cb)
    },
    rawRemoteSearch(query, cb) {
      queryCompName(query) // 企业id 暂时为空
        .then((res) => {
          // 转换数据格式，确保每个选项有value字段
          const suggestions = res.map((item) => ({
            value: item.compName, // 显示文本
            ...item // 保留其他字段
          }))
          cb(suggestions)
        })
        .finally(() => {
          this.loading = false
        })
    },
    handleSelect(value) {
      if (value) {
        // 保存选中的数据
        this.form.relatedCompId = value.compId
        this.form.relatedCompName = value.compName
      }
    },
    handleClear() {
      // 清空选中的数据
      this.form.relatedCompId = ''
      this.form.relatedCompName = ''
    },
    async getComboboxList() {
      const data = await GetComboboxList(['RELATIONSHIP'])
      if (data && data.RELATIONSHIP) {
        this.checkboxList = data.RELATIONSHIP
      }
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-button.is-disabled {
  background: #e2e2e2;
  color: #ffffff !important;
}
::v-deep .jr-decorated-table--top-search .jr-decorated-table--header-left {
  display: none !important;
}
::v-deep .jr-checkbox-group .el-checkbox {
  height: 22px;
  line-height: 22px;
  vertical-align: middle;
  display: inline-flex;
  vertical-align: middle;
  align-items: center;
}
::v-deep .platform-modal-content {
  height: 215px !important;
}
.associated-config {
  width: 100%;
  height: 100%;
  &-select {
    width: 100%;
    background-color: #ffffff;
    display: flex;
    align-items: center;
    label {
      height: 22px;
      font-family: MicrosoftYaHei;
      font-size: var(--el-font-size-base);
      color: rgba(0, 0, 0, 0.85);
      line-height: 22px;
      margin-right: 8px;
    }
    button {
      width: 88px;
      margin-left: 13px;
      margin-right: 22px;
    }
  }
  &-content {
    margin-top: 1px;
    width: 100%;
    height: calc(100% - 65px);
    background-color: #ffffff;
    &-table {
      width: 100%;
      height: 100%;
      padding-top: 8px;
      padding-bottom: 16px;

      ::v-deep .el-table {
        .el-table__body {
          width: 100% !important;
        }

        .el-table__header {
          width: 100% !important;
        }
      }
    }
  }
  &-modal {
    &-title {
      height: 24px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: var(--el-font-size-medium);
      color: rgba(0, 0, 0, 0.85);
      line-height: 24px;
      font-weight: 600;
    }
    &-form {
      display: flex;
      flex-direction: column;
      align-items: center;
    }
    &-show {
      width: 326px;
      max-width: 326px;
      height: 22px;
      font-family: MicrosoftYaHeiSemibold;
      font-size: var(--el-font-size-medium);
      color: rgba(0, 0, 0, 0.85);
      line-height: 22px;
      font-weight: 600;
    }
    &-buttons {
      display: flex;
      align-items: center;
      margin-top: 30px;
      gap: 12px;
      button {
        width: 72px;
      }
    }
  }
}
// .associated-config-modal {
//   width: 480px !important;
//   .el-dialog__footer {
//     background: none !important;
//     border-top: none !important;
//   }
// }
// .no-icon-confirm {
//   .el-message-box__status {
//     display: none !important;
//   }
//   .el-message-box__message {
//     padding-left: 0 !important;
//   }
// }
</style>
