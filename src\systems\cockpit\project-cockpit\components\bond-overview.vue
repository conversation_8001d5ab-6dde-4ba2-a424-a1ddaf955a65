<template>
  <div class="bond-overview">
    <cockpit-header :style="{ width: px2vw(362), height: px2vh(40) }">
      <div class="bond-overview-title">
        <span>债券概览</span>
        <el-tooltip effect="dark" placement="bottom-start" :teleported="false">
          <div class="toolTip">
            <img src="@/assets/cockpit/info_pro_circle.png" alt="" :style="{width:px2vh(13),height:px2vh(13),marginLeft:px2vw(4)}">
          </div>
          <div slot="content" v-if="$store.getters.sysVersion === 'company'">
            <p>存续期债券：统计企业存续期债券数量与余额。</p>
            <p>近期付息兑付：统计企业存续期债券未来三月的付息兑付现金流。</p>
            <p>近期新发债券：统计企业近三月发行且已确定票面利率的债券。</p>
            <p>综合成本：统计企业存续期债券的加权票面信息，无余额和无票面利率信息的债券不纳入统计。</p>
          </div>
          <div slot="content" v-if="$store.getters.sysVersion === 'group'">
            <p>存续期债券：统计集团本级及一二级发债子公司存续期债券数量与余额。</p>
            <p>近期付息兑付：统计集团本级及一二级发债子公司存续期债券未来三月的付息兑付现金流。</p>
            <p>近期新发：统计集团本级及一二级发债子公司近三月新发行的债券。</p>
            <p>综合成本：统计集团本级及一二级发债子公司存续期债券的加权票面信息，无余额和无票面利率信息的债券不纳入统计。</p>
          </div>
        </el-tooltip>
      </div>
    </cockpit-header>
    <div class="bond-overview-cardArea">
      <div
        class="bond-overview-cardArea-card"
        v-for="(card, index) in cardData"
        :key="index"
        :class="cardActive === '0' + (index + 1) ? 'bond-overview-cardArea-active' : ''"
        @click.stop="cardClick(index)"
      >
        <p>{{ card.title }}</p>
        <img :src="card.imgSrc" alt="" />
        <div class="bond-overview-cardArea-card-info">
          <p>
            <span>{{ card.numb }}</span>
            <span>{{ card.numLabel }}</span>
          </p>
          <p>
            <span style="text-align: right;">{{ ConvertAmount('HMU', card.amtsum * 1, 1, 2) }}</span>
            <span>{{ card.amountLabel }}</span>
          </p>
        </div>
      </div>
    </div>
    <div class="bond-overview-chartArea">
      <div class="bond-overview-chartArea-chart">
        <echarts :options="options" style="width: 100%; height: 100%" />
      </div>
      <div class="bond-overview-chartArea-table">
        <div class="bond-overview-chartArea-table-tabs" v-if="$store.getters.sysVersion === 'group'">
          <div
            class="bond-overview-chartArea-table-tabs-chartType"
            :class="chartType === 'bar' ? 'bond-overview-chartArea-table-tabs-activeType' : ''"
            :style="{ marginLeft: px2vw(21) }"
            @click.stop="chartTypeChange('bar')"
          >
            <img src="@/assets/cockpit/cockpit_bar.png" alt="" />
          </div>
          <div
            v-if="tab === '0'"
            class="bond-overview-chartArea-table-tabs-chartType"
            :class="chartType === 'pie' ? 'bond-overview-chartArea-table-tabs-activeType' : ''"
            @click.stop="chartTypeChange('pie')"
          >
            <img src="@/assets/cockpit/cockpit_pie.png" alt="" />
          </div>
          <div
            class="bond-overview-chartArea-table-tabs-tab"
            :style="{ marginLeft: px2vw(40) }"
            :class="tab === '0' ? 'bond-overview-chartArea-table-tabs-tabActive' : ''"
            @click.stop="tabChange('0')"
          >
            <span>按债券类型</span>
          </div>
          <div
            v-if="chartType === 'bar'"
            class="bond-overview-chartArea-table-tabs-tab"
            :style="{ marginLeft: px2vw(16) }"
            :class="tab === '1' ? 'bond-overview-chartArea-table-tabs-tabActive' : ''"
            @click.stop="tabChange('1')"
          >
            <span>按发行方式</span>
          </div>
        </div>
        <div class="bond-overview-chartArea-table-tableInner">
          <template v-if="chartType === 'bar'">
            <div class="bond-overview-chartArea-table-tableInner-tr" v-for="(data, index) in tableData" :key="index">
              <div class="bond-overview-chartArea-table-tableInner-tr-head">
                <img :src="data.imgSrc" alt="" />
                <span 
                  :title="data.title"
                  class="company-name"
                >{{ truncateCompanyName(data.title) }}</span>
                <span>{{ ConvertAmount('HMU', data.sum, 1, 4) }}</span>
              </div>
              <div class="bond-overview-chartArea-table-tableInner-tr-content">
                <div
                  class="bond-overview-chartArea-table-tableInner-tr-content-item"
                  v-for="(item, iIndex) in data.list"
                  :key="iIndex"
                >
                  <span :style="{ background: getItemColor(item, iIndex) }"></span>
                  <span 
                    :title="item[tab === '0' ? 'bondTypeName2' : 'issueType'] || ''"
                    class="bond-type-name"
                  >{{ truncateBondTypeName(item[tab === '0' ? 'bondTypeName2' : 'issueType'] || '', ConvertAmount('HMU', item.amtsum || 0, 1, 4)) }}：</span>
                  <span class="bond-amount">{{ ConvertAmount('HMU', item.amtsum || 0, 1, 4) }}</span>
                </div>
              </div>
            </div>
          </template>
          <template v-else>
            <div class="bond-overview-chartArea-table-pieTable">
              <div class="bond-overview-chartArea-table-pieTable-header">
                <span></span>
                <span>债券数</span>
                <span>合计</span>
              </div>
              <div
                class="bond-overview-chartArea-table-pieTable-row"
                v-for="(item, index) in pieTableData"
                :key="index"
              >
                <div class="bond-overview-chartArea-table-pieTable-row-type">
                  <span :style="{ backgroundColor: item.color }"></span>
                  <span>{{ item.bondTypeName2 }}</span>
                </div>
                <span class="bond-overview-chartArea-table-pieTable-row-numb">{{ item.numb }}</span>
                <span class="bond-overview-chartArea-table-pieTable-row-amount">
                  {{ ConvertAmount('HMU', item.amtsum * 1, 1, 4) }}
                </span>
              </div>
            </div>
          </template>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import cockpitHeader from '../../components/cockpit-header.vue'
import {
  cockpitGetBondOverview,
  cockpitGetBondTypeAnalysisList,
  cockpitGetIssueTypeAnalysisList,
  cockpitGetBondTypeList
} from '@/api/cockpit/cockpit'
import cockpit_bond_card01 from '@/assets/cockpit/cockpit_bond_card01.png'
import cockpit_bond_card02 from '@/assets/cockpit/cockpit_bond_card02.png'
import cockpit_bond_card03 from '@/assets/cockpit/cockpit_bond_card03.png'
import cockpit_bond_card04 from '@/assets/cockpit/cockpit_bond_card04.png'
import * as echartsInstance from 'echarts'
import echarts from '@jupiterweb/components/echarts'
import { ConvertAmount } from '@jupiterweb/utils/common.js'
import { px2vw, px2vh } from '../../utils/portcss'
export default {
  name: 'BondOverview',
  components: {
    cockpitHeader,
    echarts
  },
  data() {
    return {
      cardData: [
        {
          title: '存续期债券',
          imgSrc: cockpit_bond_card01,
          numLabel: '债券数',
          amountLabel: '余额(亿)'
        },
        {
          title: '近期付息兑付',
          imgSrc: cockpit_bond_card02,
          numLabel: '债券数',
          amountLabel: '现金流(亿)'
        },
        {
          title: '近期新发债券',
          imgSrc: cockpit_bond_card03,
          numLabel: '债券数',
          amountLabel: '规模(亿)'
        },
        {
          title: '综合成本',
          imgSrc: cockpit_bond_card04,
          numLabel: '债券数',
          amountLabel: '票面加权(%)'
        }
      ],
      cardActive: '01',
      chartType: 'bar',
      tab: '0',
      tableData: [],
      pieTableData: [],
      options: {},
      // chartColors: [
      //   '#5B8FF9', // 蓝色系主色
      //   '#73A0FB', // 蓝色系浅色
      //   '#91B5FC', // 蓝色系更浅
      //   '#5AD8A6', // 青绿色
      //   '#6DC8EC', // 天蓝色
      //   '#73D6A0', // 青绿浅色
      //   '#83E2B5', // 青绿更浅
      //   '#95EECA' // 青绿最浅
      // ]
      chartColors: [
        '#678EF2',
        '#4E9898',
        '#7FD5A9',
        '#D9B243',
        '#D76F53',
        '#7CBBF9',
        '#9677D7',
        '#E9378C',
        '#D98A66',
        '#7DE183'
      ]
    }
  },
  created() {
    this.getBondAnalysisCardDataApi()
    if(this.$store.getters.sysVersion === 'company'){
      this.chartType = 'pie'
      this.getPieDataApi()
    }else{
      this.chartType = 'bar'
      this.getTableChartDataApi()
    }
  },
  computed: {
    computedInterface() {
      return this.tab === '0' ? cockpitGetBondTypeAnalysisList : cockpitGetIssueTypeAnalysisList
    }
  },
  methods: {
    px2vw,
    px2vh,
    ConvertAmount,
    /**
     * 获取颜色的深色版本
     */
    getDarkerColor(color) {
      // 统一的深色处理，创建协调的渐变效果
      const colorMap = {
        '#5B8FF9': '#4A7AE8', // 蓝色系主色深版
        '#73A0FB': '#5B8FF9', // 蓝色系浅色深版
        '#91B5FC': '#73A0FB', // 蓝色系更浅深版
        '#5AD8A6': '#42C48A', // 青绿色深版
        '#6DC8EC': '#4EADD6', // 天蓝色深版
        '#73D6A0': '#5AD8A6', // 青绿浅色深版
        '#83E2B5': '#6BD099', // 青绿更浅深版
        '#95EECA': '#7DE4B8' // 青绿最浅深版
      }
      return colorMap[color] || color
    },
    /**
     * 获取项目对应的颜色
     */
    getItemColor(item, itemIndex) {
      // 获取当前项目在图例中的索引
      const legendData = []
      this.tableData.forEach((tableItem) => {
        tableItem.list.forEach((subItem) => {
          const typeName = this.tab === '0' ? subItem.bondTypeName2 : subItem.issueType
          if (typeName && !legendData.includes(typeName)) {
            legendData.push(typeName)
          }
        })
      })

      const currentTypeName = this.tab === '0' ? item.bondTypeName2 : item.issueType
      const colorIndex = legendData.indexOf(currentTypeName)
      return this.chartColors[colorIndex % this.chartColors.length] || '#5B8FF9'
    },
    /**
     * 获取债券概览分析数据
     */
    async getBondAnalysisCardDataApi() {
      const data = await cockpitGetBondOverview()
      this.cardData = this.cardData.map((item) => {
        const obj = data.find((val) => val.title === item.title)
        this.$set(item, 'numb', obj.numb)
        this.$set(item, 'amtsum', obj.amtsum)
        return item
      })
    },
    /**
     * 获取图表以及表格数据
     */
    async getTableChartDataApi() {
      const data = await this.computedInterface(this.cardActive)
      this.tableData = []
      for (let key in data) {
        this.tableData.push({
          title: key,
          sum: data[key][0]?.issueamtsum || 0,
          list: data[key]
        })
      }
      this.tableData = this.tableData.map((item, index) => {
        item.imgSrc = require(`@/assets/cockpit/cockpit_rank0${index > 2 ? 4 : index + 1}.png`)
        return item
      })
      this.initChart()
    },
    /**
     * 获取饼图数据
     */
    async getPieDataApi() {
      const data = await cockpitGetBondTypeList(this.cardActive)

      // 过滤有效数据
      const validData = data.filter((item) => item.amtsum > 0 && item.bondTypeName2)

      // 生成饼图数据
      const pieData = validData.map((item, index) => ({
        name: item.bondTypeName2,
        value: item.amtsum,
        itemStyle: {
          color: this.chartColors[index % this.chartColors.length]
        }
      }))

      // 计算总数
      const total = validData.reduce((sum, item) => sum + item.amtsum, 0)

      // 按金额从大到小排序并生成表格数据
      const sortedData = validData.sort((a, b) => b.amtsum - a.amtsum)
      this.pieTableData = sortedData.map((item, index) => ({
        bondTypeName2: item.bondTypeName2,
        numb: item.numb,
        amtsum: item.amtsum,
        color: this.chartColors[index % this.chartColors.length]
      }))

      this.initPieChart(pieData, total)
    },
    /**
     * 初始化饼图
     */
    initPieChart(pieData, total) {
      const option = {
        backgroundColor: 'transparent',
        tooltip: {
          trigger: 'item',
          formatter: (params) => {
            const formattedValue = this.ConvertAmount('HMU', params.value, 1, 4)
            return `${params.marker}${params.name}: ${formattedValue}亿 (${params.percent}%)`
          }
        },
        legend: {
          show: false
        },
        toolbox: {
          show: false
        },
        graphic: [
          {
            type: 'text',
            left: 'center',
            top: '50%',
            style: {
              text: '总计',
              fontSize: 14,
              fill: '#C7D4D9',
              y: -15
            }
          },
          {
            type: 'text',
            left: 'center',
            top: '45%',
            style: {
              text: this.ConvertAmount('HMU', total, 1, 4),
              fontSize: 20,
              fontWeight: 'bold',
              fill: '#FFFFFF',
              y: 5
            }
          }
        ],
        series: [
          {
            name: '债券类型',
            type: 'pie',
            radius: ['40%', '70%'],
            center: ['50%', '50%'],
            data: pieData,
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            },
            itemStyle: {
              borderWidth: 2,
              borderColor: 'rgba(255, 255, 255, 0.1)'
            },
            label: {
              show: false
            },
            labelLine: {
              show: false
            }
          }
        ]
      }

      this.options = option
    },
    /**
     * 初始化图表
     */
    initChart() {
      const height = this.$el.querySelector('.bond-overview-chartArea-chart').offsetHeight

      // 从 tableData 提取数据
      const legendData = []
      const xData = []
      const seriesData = []

      // 获取所有公司名称作为 x 轴数据
      this.tableData.forEach((item) => {
        xData.push(item.title)
      })

      // 获取所有债券类型作为图例数据
      const allTypes = new Set()
      this.tableData.forEach((item) => {
        item.list.forEach((subItem) => {
          allTypes.add((this.tab === '0' ? subItem.bondTypeName2 : subItem.issueType) || 'temp')
        })
      })
      legendData.push(...Array.from(allTypes))

      // 创建底部装饰系列（钻石形状）
      const baseData = xData.map(() => 1)

      // 为每个债券类型创建一个系列
      legendData.forEach((type, index) => {
        const data = xData.map((company) => {
          const companyData = this.tableData.find((item) => item.title === company)
          const typeData = companyData.list.find(
            (item) => (this.tab === '0' ? item.bondTypeName2 : item.issueType) === type
          )
          return typeData ? Number(typeData.amtsum) : 0
        })

        // 主要柱形图（渐变效果）
        seriesData.push({
          name: type || 'temp',
          type: 'bar',
          stack: 'total',
          barWidth: 27,
          data: data,
          itemStyle: {
            normal: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1, // 从顶部到底部的垂直渐变
                colorStops: [
                  {
                    offset: 0,
                    color: this.chartColors[index % this.chartColors.length] + 'FF' // 顶部完全不透明
                  },
                  {
                    offset: 0.2,
                    color: this.chartColors[index % this.chartColors.length] + 'E6' // 20%处开始半透明
                  },
                  {
                    offset: 0.6,
                    color: this.chartColors[index % this.chartColors.length] + '80' // 60%处更透明
                  },
                  {
                    offset: 0.9,
                    color: this.chartColors[index % this.chartColors.length] + '20' // 底部几乎完全透明
                  },
                  {
                    offset: 1,
                    color: this.chartColors[index % this.chartColors.length] + '00' // 底部几乎完全透明
                  }
                ]
              },
              borderWidth: 0
            }
          },
          label: {
            normal: {
              show: false,
              position: 'top',
              color: '#fff'
            }
          }
        })
      })

      // 为每个堆叠段添加顶部钻石装饰
      legendData.forEach((type, index) => {
        const data = xData.map((company) => {
          const companyData = this.tableData.find((item) => item.title === company)
          let stackValue = 0
          // 计算到当前系列的堆叠值
          for (let i = 0; i <= index; i++) {
            const currentType = legendData[i]
            const typeData = companyData.list.find(
              (item) => (this.tab === '0' ? item.bondTypeName2 : item.issueType) === currentType
            )
            if (typeData) {
              stackValue += Number(typeData.amtsum)
            }
          }

          // 检查当前系列是否有数据
          const currentTypeData = companyData.list.find(
            (item) => (this.tab === '0' ? item.bondTypeName2 : item.issueType) === type
          )

          return currentTypeData && Number(currentTypeData.amtsum) > 0 ? stackValue : null
        })

        seriesData.push({
          data: data,
          type: 'pictorialBar',
          barMaxWidth: '20',
          symbolPosition: 'end',
          symbol: 'diamond',
          symbolOffset: [0, '-50%'],
          symbolSize: [27, 10],
          zlevel: 2,
          tooltip: {
            show: false // 隐藏装饰系列的tooltip
          },
          itemStyle: {
            normal: {
              color: this.chartColors[index % this.chartColors.length] + 'FF', // 顶部装饰使用完全不透明
              borderColor: '#FFFFFF', // 纯白色边框
              borderWidth: 3
            }
          }
        })
      })

      let option = {
        backgroundColor: 'transparent',
        color: this.chartColors,
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          formatter: (params) => {
            let result = params[0].name + '<br/>'
            // 过滤掉值为0或null的数据，只显示主柱形图数据
            const validParams = params.filter((param) => {
              return param.seriesType === 'bar' && param.value > 0
            })

            validParams.forEach((param) => {
              result += param.marker + ' ' + param.seriesName + ': ' + this.ConvertAmount('HMU', param.value, 1, 4) + '亿<br/>'
            })

            return result
          }
        },
        dataZoom: [
          {
            type: 'inside',
            xAxisIndex: 0,
            start: 0,
            end: 60,
            zoomOnMouseWheel: false,
            moveOnMouseMove: true,
            moveOnMouseWheel: true,
            zoomLock: true
          }
        ],
        grid: {
          top: 40,
          left: 30,
          right: 10,
          bottom: 50,
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: xData,
          axisLine: {
            lineStyle: {
              color: '#00AEFF'
            }
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            show: true,
            color: '#C7D4D9',
            interval: 0,
            rotate: 0,
            margin: 12,
            fontSize: 12,
            formatter: function (value) {
              if (value.length > 6) {
                return value.substring(0, 6) + '...'
              }
              return value
            }
          }
        },
        yAxis: {
          type: 'value',
          name: '单位：亿',
          axisLabel: {
            color: '#C7D4D9',
            fontSize: 12
          },
          splitLine: {
            show: true,
            lineStyle: {
              type: 'dashed',
              color: 'rgba(125,193,224,0.2)'
            }
          },
          nameTextStyle: {
            color: '#C7D4D9',
            align: 'right',
            fontSize: 12,
            padding: [0, 0, 0, -10]
          }
        },
        toolbox: {
          show: false
        },
        legend: {
          show: true,
          type: 'scroll',
          data: legendData,
          itemGap: 20,
          itemWidth: 14,
          itemHeight: 14,
          top: 'auto',
          bottom: 15,
          left: 'center',
          orient: 'horizontal',
          textStyle: {
            color: '#C7D4D9',
            fontSize: 12,
            padding: [0, 6]
          },
          pageIconColor: '#C7D4D9',
          pageIconInactiveColor: 'rgba(199,212,217,0.3)',
          pageIconSize: 12,
          pageTextStyle: {
            color: '#C7D4D9',
            fontSize: 11
          }
        },
        series: seriesData
      }
      this.options = option
    },
    /**
     * tab切换
     */
    tabChange(tab) {
      this.tab = tab
      if (this.chartType === 'pie') {
        this.getPieDataApi()
      } else {
        this.getTableChartDataApi()
      }
    },
    /**
     * 卡片点击
     */
    cardClick(index) {
      this.cardActive = '0' + (index + 1)
      if (this.chartType === 'pie') {
        this.getPieDataApi()
      } else {
        this.getTableChartDataApi()
      }
    },
    /**
     * 图表类型切换
     */
    chartTypeChange(type) {
      this.chartType = type
      console.log(this.chartType)
      if (this.chartType === 'pie') {
        this.getPieDataApi()
      } else {
        this.getTableChartDataApi()
      }
    },
    truncateCompanyName(name) {
      if (!name) return ''
      // 公司名称超过8个字符时截断
      if (name.length > 12) {
        return name.substring(0, 12) + '...'
      }
      return name
    },
    truncateBondTypeName(typeName, amount) {
      if (!typeName) return ''
      
      // 估算文字宽度，中文字符约14px，数字和英文约8px
      const estimateWidth = (text) => {
        const chineseChars = text.match(/[\u4e00-\u9fa5]/g) || []
        const otherChars = text.replace(/[\u4e00-\u9fa5]/g, '')
        return chineseChars.length * 14 + otherChars.length * 8
      }
      
      const typeNameWidth = estimateWidth(typeName + '：')
      const amountWidth = estimateWidth(amount.toString())
      const totalWidth = typeNameWidth + amountWidth
      
      // 如果总宽度超过容器的50%（约80px），则截断类型名称
      const maxContainerWidth = 160 // 50%容器宽度的估算值
      if (totalWidth > maxContainerWidth) {
        // 计算可用于类型名称的宽度
        const availableWidth = maxContainerWidth - amountWidth - 14 // 14px是冒号的宽度
        const maxChars = Math.floor(availableWidth / 14) // 按中文字符计算
        
        if (maxChars < 2) {
          return typeName.substring(0, 2) + '...'
        } else if (typeName.length > maxChars) {
          return typeName.substring(0, maxChars) + '...'
        }
      }
      
      return typeName
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~assets/styles/portcss.scss';
.bond-overview {
  width: 100%;
  height: vh(908);
  background-image: url('../../../../assets/cockpit/cockpit_pro_highter_bac.png');
  background-size: 100% 100%;
  padding: vh(8) vw(24) vh(8) vw(32);
  &-title {
    display: flex;
    align-items: center;
    gap: vw(5);
    cursor: pointer;
    & > span {
      height: vh(21);
      font-size: vh(16);
      color: #ffffff;
      line-height: vh(21);
      text-shadow: 0px 2px 4px rgba(61, 29, 0, 0.2);
      font-weight: 600;
    }
  }
  &-cardArea {
    width: 100%;
    height: vh(218);
    gap: vw(16);
    display: flex;
    margin-top: vh(19);
    &-card {
      width: vw(191);
      height: vh(218);
      background-image: url('../../../../assets/cockpit/cockpit_bond_overview.png');
      background-size: 100% 100%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      cursor: pointer;
      & p {
        margin-bottom: 0;
      }
      & > p {
        height: vh(24);
        font-family: MicrosoftYaHeiSemibold;
        font-size: vh(18);
        color: #e6f6ff;
        line-height: vh(24);
        text-shadow: 0px 2px 4px rgba(0, 0, 0, 0.5);
        margin-top: vh(24);
        text-align: center;
      }
      & > img {
        width: vw(57);
        height: vh(93);
        margin: vh(11) auto vh(0);
      }
      &-info {
        width: 100%;
        padding: 0px vw(16);
        display: flex;
        justify-content: space-between;
        & > p {
          display: flex;
          flex-direction: column;
          & > span:nth-of-type(1) {
            height: vh(22);
            font-family: JinShanYunJiShuTi;
            font-size: vh(18);
            line-height: vh(26);
            background: linear-gradient(90deg, #efc409 0%, #fff6a0 100%);
            background-clip: text;
            -webkit-background-clip: text;
            color: transparent;
          }
          & > span:nth-of-type(2) {
            height: vh(19);
            font-family: MicrosoftYaHei;
            font-size: vh(14);
            color: rgba(230, 246, 255, 0.6);
            line-height: vh(19);
            margin-top: vh(4);
            text-align: right;
          }
        }
      }
    }
    &-active {
      background-image: url('../../../../assets/cockpit/cockpit_bond_overview_active.png');
    }
  }
  &-chartArea {
    width: 100%;
    display: flex;
    gap: vw(44);
    margin-top: vh(31);
    &-chart {
      width: vw(432);
      height: vh(568);
    }
    &-table {
      width: vw(344);
      &-tabs {
        width: 100%;
        height: vh(32);
        display: flex;
        align-items: center;
        &-chartType {
          width: vw(32);
          height: vh(32);
          background: rgba(30, 53, 85, 0.4);
          border-radius: 0px 2px 2px 0px;
          border: 1px solid #315280;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          img {
            width: vw(16);
            height: vh(17);
          }
        }
        &-activeType {
          background: rgba(10, 79, 185, 0.58);
          border-radius: 2px 0px 0px 2px;
          border: 1px solid;
          border-image: linear-gradient(90deg, rgba(9, 131, 239, 1), rgba(160, 216, 255, 1)) 1 1;
        }
        &-tab {
          width: vw(102);
          height: vh(33);
          background-image: url('../../../../assets/cockpit/cockpit_tab.png');
          background-repeat: no-repeat;
          background-size: 100% 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          span {
            height: vh(19);
            font-family: MicrosoftYaHei;
            font-size: vh(14);
            color: rgba(255, 255, 255, 0.85);
            line-height: vh(19);
          }
        }
        &-tabActive {
          background-image: url('../../../../assets/cockpit/cockpit_tab_active.png');
        }
      }
      &-tableInner {
        width: 100%;
        height: vh(504);
        margin-top: vh(32);
        overflow-y: scroll;
        &-tr {
          width: 100%;
          background: linear-gradient(to top, rgba(88, 120, 255, 0) 0%, rgba(44, 111, 255, 0.2) 100%);
          border-radius: 4px;
          margin-bottom: vh(8);
          padding-top: vh(9);
          padding-left: vw(14);
          padding-bottom: vh(9);
          &-head {
            width: 100%;
            height: vh(27);
            display: flex;
            align-items: flex-end;
            gap: vw(8);
            img {
              width: vw(31);
              height: vh(27);
            }
            & > span:nth-of-type(1) {
              height: vh(22);
              font-family: MicrosoftYaHeiSemibold;
              font-size: vh(14);
              color: #ffffff;
              line-height: vh(22);
              font-weight: 600;
            }
            & > span:nth-of-type(2) {
              height: vh(22);
              font-size: vh(14);
              color: #1ce2cc;
              line-height: vh(22);
              font-weight: 600;
            }
            
            .company-name {
              max-width: vw(120);
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              cursor: help;
            }
          }
          &-content {
            margin-top: vh(8);
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            align-items: center;
            &-item {
              width: 48%;
              flex-shrink: 0;
              display: flex;
              align-items: center;
              margin-bottom: vh(11);
              & > span:nth-of-type(1) {
                width: 6px;
                height: 6px;
                margin-right: vw(7);
                transform: rotate(45deg);
              }
              & > span:nth-of-type(2) {
                height: vh(19);
                font-family: MicrosoftYaHei;
                font-size: vh(14);
                color: rgba(230, 246, 255, 0.6);
                line-height: vh(19);
              }
              & > span:nth-of-type(3) {
                height: vh(19);
                font-family: MicrosoftYaHei;
                font-size: vh(14);
                color: #ffffff;
                line-height: vh(19);
              }
              
              .bond-type-name {
                max-width: 60%;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                cursor: help;
              }
              
              .bond-amount {
                margin-left: 0;
              }
            }
          }
        }
      }
      &-pieTable {
        width: 100%;
        height: vh(504);
        margin-top: vh(32);
        overflow-y: scroll;
        &-header {
          display: flex;
          align-items: center;
          height: vh(32);
          padding: 0 vw(14);
          border-radius: 4px;
          margin-bottom: vh(8);
          & > span:nth-of-type(1) {
            flex: 1;
            font-size: vh(14);
            color: #ffffff;
            font-weight: 600;
          }
          & > span:nth-of-type(2) {
            width: vw(60);
            text-align: center;
            font-size: vh(14);
            color: #ffffff;
            font-weight: 600;
          }
          & > span:nth-of-type(3) {
            width: vw(80);
            text-align: right;
            font-size: vh(14);
            color: #ffffff;
            font-weight: 600;
          }
        }
        &-row {
          display: flex;
          align-items: center;
          height: vh(40);
          padding: 0 vw(14);
          background: linear-gradient(to top, rgba(88, 120, 255, 0) 0%, rgba(44, 111, 255, 0.1) 100%);
          border-radius: 4px;
          margin-bottom: vh(8);
          &-type {
            flex: 1;
            display: flex;
            align-items: center;
            gap: vw(8);
            & > span:nth-of-type(1) {
              width: 8px;
              height: 8px;
              border-radius: 2px;
            }
            & > span:nth-of-type(2) {
              font-size: vh(14);
              color: #ffffff;
            }
          }
          &-numb {
            width: vw(60);
            text-align: center;
            font-size: vh(14);
            color: rgba(255,255,255,0.9);
          }
          &-amount {
            width: vw(80);
            text-align: right;
            font-size: vh(14);
            color: rgba(255,255,255,0.9);
          }
        }
      }
    }
  }
}
</style>
