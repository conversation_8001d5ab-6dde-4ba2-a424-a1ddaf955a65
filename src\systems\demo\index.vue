<template>
  <el-tabs v-model="active" class="demo-page" @tab-click="handleClick">
    <el-tab-pane label="手写一览列表(包含调用 init )" name="first">
      <flat-index-demo />
    </el-tab-pane>

    <el-tab-pane label="手写一览列表(左右布局)" name="second" lazy>
      <flat-index-horizontal-demo />
    </el-tab-pane>

    <el-tab-pane label="手写一览列表(上下布局)" name="three" lazy>
      <flat-index-vertical-demo />
    </el-tab-pane>

    <el-tab-pane label="调用自定义列组件" name="four" lazy>
      <decorated-table-demo />
    </el-tab-pane>

    <el-tab-pane label="配置表单项" name="five" lazy>
      <el-form>
        <form-item-create-demo />
      </el-form>
    </el-tab-pane>
  </el-tabs>
</template>

<script>
import FlatIndexDemo from './flat-index'
import FlatIndexHorizontalDemo from './flat-index-horizontal'
import FlatIndexVerticalDemo from './flat-index-vertical'
import DecoratedTableDemo from './decorated-table'
import FormItemCreateDemo from './form-item-create'

export default {
  components: { FlatIndexDemo, FlatIndexHorizontalDemo, FlatIndexVerticalDemo, DecoratedTableDemo, FormItemCreateDemo },
  data() {
    return {
      active: 'first'
    }
  },
  methods: {
    handleClick(tab, event) {
      // console.log(tab, event)
    }
  }
}
</script>

<style lang="scss">
  .demo-page {
    height: 100%;
    margin: 10px;

    .el-tabs__content {
      height: 100%;

      .el-tab-pane {
        height: 100%;
      }
    }
  }
</style>
