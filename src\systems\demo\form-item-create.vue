<template>
  <jr-form-item-create :data="configForm.data" :model="configForm.model" />
</template>

<script>
export default {
  data() {
    return {
      configForm: {
        model: {},
        data: [
          {
            title: '资产类型',
            prop: 'finprodType2',
            type: 'select',
            uiProps: {
              multiple: true
            }
          }, {
            title: '资产代码',
            prop: 'finprodMarketId',
            type: 'text'
          }, {
            title: '交易场所',
            prop: 'tradeMarket',
            type: 'select',
            options: [
              { text: '1111', value: '1111' },
              { text: '2222', value: '2222' },
              { text: '3333', value: '3333' }
            ]
          }, {
            title: '交易方向',
            prop: 'ps',
            type: 'select',
            options: [
              { text: '1111', value: '1111' },
              { text: '2222', value: '2222' },
              { text: '3333', value: '3333' }
            ]
          }, {
            title: '指令下达日期',
            prop: 'createTime',
            type: 'rangeDate'
          }, {
            title: '完成状态',
            prop: 'entrustStatus',
            type: 'select',
            options: [
              { text: '已完成', value: '04' },
              { text: '已关闭', value: '06' },
              { text: '未完成', value: '99' }
            ]
          }, {
            title: '币种',
            prop: 'ccy',
            type: 'select',
            options: [
              { text: '已完成', value: '04' },
              { text: '已关闭', value: '06' },
              { text: '未完成', value: '99' }
            ]
          }, {
            title: '是否显示二级指令',
            prop: 'secondaryMarketTrade',
            type: 'select',
            options: [
              { text: '否', value: 'N' },
              { text: '是', value: 'Y' }
            ]
          }
        ]
      }
    }
  }
}
</script>

<style>

</style>
