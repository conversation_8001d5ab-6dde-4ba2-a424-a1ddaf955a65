<template>
  <div class="releaseWindowSelect">
    <div class="releaseWindowSelect-topSelect public-table-search-container">
      <label style="width: 110px">
        行权/到期日期
        <el-tooltip placement="right" effect="dark">
          <jr-svg-icon icon-class="info-circle" />
          <div slot="content" style="max-width: 300px">含权债用兑付日期，非含权用到期日</div>
        </el-tooltip>
      </label>
      <div class="releaseWindowSelect-topSelect-datePicker">
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          unlink-panels
          start-placeholder="含权债用兑付日期"
          end-placeholder="非含权用到期日"
          value-format="yyyy-MM-dd"
        />
      </div>
      <el-button type="primary" @click.stop="confirmSearch">查询</el-button>
    </div>
    <div style="background-color: #ffffff; height: 516px; padding-top: 8px">
      <jr-decorated-table
        :params="{
          ...confirmParams
        }"
        style="height: 474px"
        custom-id="d2af062213ec4ee79d1e73ec78bb614c"
        :default-page-size="10"
        :initPagination="{
          pageSizeOptions: [10, 15, 20, 50, 100]
        }"
        v-bind="{
          ...$props
        }"
        :menuinfo="menuinfo"
      />
    </div>
    <div class="releaseWindowSelect-middleSelect public-table-search-container">
      <label>起息日期</label>
      <div style="width: 220px; margin-left: 8px">
        <el-date-picker
          v-model="cardAreaParams.startTime"
          type="date"
          placeholder="选择日期"
          value-format="yyyy-MM-dd"
        />
      </div>
      <label style="margin-left: 24px">
        发行期限
        <el-tooltip placement="right" effect="dark">
          <jr-svg-icon icon-class="info-circle" />
          <div slot="content" style="max-width: 300px">不包含含权期限</div>
        </el-tooltip>
      </label>
      <div style="width: 285px; margin-left: 8px; margin-right: 14px">
        <inputSelectUnit ref="inputSelectUnit" v-model="cardAreaParams.date" placeholder="不包含含权期限" />
      </div>
      <el-button type="primary" @click.stop="getCardAreaDataApi">计算</el-button>
      <el-button @click.stop="resetCardData">重置</el-button>
    </div>
    <div class="releaseWindowSelect-cardArea">
      <div class="releaseWindowSelect-cardArea-card">
        <div class="releaseWindowSelect-cardArea-card-date">
          <span v-if="cardStartShow">
            起息日期 {{ formatDateStringCN(cardAreaData.startDate) }}
            <span>{{ getWeek(cardAreaData.startDate) }}</span>
          </span>
          <span v-else>起息日期 --年 --月 --日</span>
        </div>
        <div class="releaseWindowSelect-cardArea-card-status">
          <p>
            <span v-if="cardStartShow">是否月末/季末/年末</span>
            <span v-else>--</span>
            <span v-if="cardStartShow" :style="isMonthReasonYearEndStyle">
              {{ isMonthReasonYearEnd ? '是' : '否' }}
            </span>
          </p>
          <p>
            <span v-if="cardStartShow">是否税期</span>
            <span v-else>--</span>
            <span v-if="cardStartShow" :style="isTaxRentStyle">{{ isTaxRent ? '是' : '否' }}</span>
          </p>
        </div>
        <div class="releaseWindowSelect-cardArea-card-tips">
          <p>
            <span>提示</span>
          </p>
          <p :style="isMonthReasonYearEndStyle">· 月末、季末、年末受银行MPA考核影响，资金面紧张</p>
          <p :style="isTaxRentStyle">· 1月、4月、7月、10月(重点)受交税期影响，资金面紧张</p>
        </div>
      </div>
      <div class="releaseWindowSelect-cardArea-card">
        <div class="releaseWindowSelect-cardArea-card-date">
          <span v-if="cardEndShow">
            到期日期 {{ formatDateStringCN(cardAreaData.endDate) }}
            <span :style="cardEndDateStatus">{{ getWeek(cardAreaData.endDate) }}</span>
          </span>
          <span v-else>到期日期 --年 --月 --日</span>
          <span v-if="cardEndShow">
            相近到期债券
            <span style="color: #d80000">{{ cardAreaData.num }}</span>
            只
            <el-popover placement="bottom-end" width="1200" trigger="click">
              <jr-table :data-source="cardAreaData.tableData">
                <el-table-column prop="sInfoWindcode" label="债券代码" />
                <el-table-column prop="sInfoName" label="债券简称" />
                <el-table-column prop="bondTypeName2" label="债券类型" />
                <el-table-column prop="bIssueLastissue" label="发行日期" />
                <el-table-column prop="term" label="发行期限" />
                <el-table-column prop="remainTerm" label="剩余期限" />
                <el-table-column prop="bIssueAmountact" label="规模(亿元)" />
                <el-table-column prop="bInfo0utstandingbalance" label="余额(亿元)" />
                <el-table-column prop="latestCouponrate" label="票面利率(%)" />
                <el-table-column prop="time" label="行权/到期日" />
              </jr-table>
              <p class="down-circle-tips">
                <jr-svg-icon icon-class="info-circle" />
                <span>
                  展示到期日期与拟发行债券到期日期相距一个月的存续期债券信息，对于赎回、回售、延期这3类含权债券，采用行权日进行比较
                </span>
              </p>
              <jr-svg-icon slot="reference" icon-class="down-circle" />
            </el-popover>
          </span>
        </div>
        <div class="releaseWindowSelect-cardArea-card-status">
          <p>
            <span v-if="cardEndShow">是否周末/节假日</span>
            <span v-else>--</span>
            <span v-if="cardEndShow" :style="cardEndDateStatus">{{ cardAreaData.endDateStatus ? '是' : '否' }}</span>
          </p>
          <p>
            <span v-if="cardEndShow">是否季末/年末</span>
            <span v-else>--</span>
            <span v-if="cardEndShow" :style="cardEndDateIsReasonYearEndStyle">
              {{ cardEndDateIsReasonYearEnd ? '是' : '否' }}
            </span>
          </p>
        </div>
        <div class="releaseWindowSelect-cardArea-card-tips">
          <p>
            <span>提示</span>
          </p>
          <p :style="cardEndDateStatus">· 应尽量避免节假日、周末到期，节假日信息仅供参考，具体以国家发布为准</p>
          <p :style="cardEndDateIsReasonYearEndStyle">· 季末、年末受银行MPA考核影响，资金面紧张</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import moment from 'moment'
import inputSelectUnit from '@/components/input-select-unit'
import { issuanceGetWindowCardData } from '@/api/issuance/issuance'
export default {
  name: 'ReleaseWindowSelect',
  components: {
    inputSelectUnit
  },
  filters: {
    dateWeekend(value, getWeek) {
      return value + ' ' + getWeek(value)
    }
  },
  props: {
    menuinfo: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      value1: '',
      dateRange: [],
      confirmParams: {
        startTime: '',
        endTime: '',
        ownedModuleid: '708631605142536192'
      },
      cardAreaParams: {
        startTime: '',
        date: '',
        binfoIssuercode: '502036608'
      },
      cardAreaData: {
        startDate: '',
        endDate: '',
        endDateStatus: false,
        num: 0,
        tableData: []
      }
    }
  },
  computed: {
    // 计算是否展示起息日期部分卡片
    cardStartShow() {
      return !!this.cardAreaData.startDate
    },
    // 计算是否展示到期日期部分卡片
    cardEndShow() {
      return !!this.cardAreaData.endDate
    },
    // 计算是否月末/季末/年末
    isMonthReasonYearEnd() {
      return (
        this.isMonthEnd(this.cardAreaData.startDate) ||
        this.isQuarterEnd(this.cardAreaData.startDate) ||
        this.isYearEnd(this.cardAreaData.startDate)
      )
    },
    // 计算是否月末/季末/年末处理样式
    isMonthReasonYearEndStyle() {
      return this.isMonthReasonYearEnd ? { color: '#D80000' } : {}
    },
    // 是否税期
    isTaxRent() {
      if (this.cardAreaData.startDate) {
        const rightMonth = ['01', '04', '07', '10']
        return rightMonth.includes(moment(this.cardAreaData.startDate).format('MM'))
      } else {
        return false
      }
    },
    // 是否税期样式
    isTaxRentStyle() {
      return this.isTaxRent ? { color: '#D80000' } : {}
    },
    // 到期日期是否周末/节假日样式
    cardEndDateStatus() {
      return this.cardAreaData.endDateStatus ? { color: '#D80000' } : {}
    },
    // 到期日期计算是否季末/年末
    cardEndDateIsReasonYearEnd() {
      return this.isQuarterEnd(this.cardAreaData.endDate) || this.isYearEnd(this.cardAreaData.endDate)
    },
    // 到期日期计算是否季末/年末样式
    cardEndDateIsReasonYearEndStyle() {
      return this.cardEndDateIsReasonYearEnd ? { color: '#D80000' } : {}
    }
  },
  created() {
    this.initDateRange()
    this.confirmSearch()
    this.initStartTime()
  },
  methods: {
    /**
     * 初始化dateRange
     */
    initDateRange() {
      // 获取当前系统时间
      const currentDate = moment(+new Date())
      this.dateRange.push(moment(currentDate).format('YYYY-MM-DD'))
      // 往后推三个月
      const futureDate = currentDate.add(3, 'months')
      // 格式化输出
      const formattedDate = futureDate.format('YYYY-MM-DD')
      this.dateRange.push(formattedDate)
    },
    /**
     * 确认查询
     */
    confirmSearch() {
      this.confirmParams.startTime = this.dateRange[0]
      this.confirmParams.endTime = this.dateRange[1]
    },
    /**
     * 初始化卡片区域起息日期
     */
    initStartTime() {
      this.cardAreaParams.startTime = moment(+new Date()).format('YYYY-MM-DD')
    },
    /**
     * 获取卡片区域数据
     */
    formatterDateString(str) {
      if (!str) return ''
      return str.replace(/^(\d{4})(\d{2})(\d{2})$/, '$1-$2-$3')
    },
    formatDateStringCN(dateStr) {
      if (!dateStr) return ''
      return dateStr.replace(/^(\d{4})-(\d{2})-(\d{2})$/, '$1年$2月$3日')
    },
    async getCardAreaDataApi() {
      if (!this.cardAreaParams.date) {
        this.$message.warning('请输入发行期限')
        return
      }
      if (!this.cardAreaParams.startTime) {
        this.$message.warning('请选择起息日期')
        return
      }
      const data = await issuanceGetWindowCardData(this.cardAreaParams)
      this.cardAreaData.startDate = moment(data.startDate).format('YYYY-MM-DD')
      this.cardAreaData.endDate = moment(data.endDate).format('YYYY-MM-DD')
      this.cardAreaData.num = data.num
      this.cardAreaData.endDateStatus = data.endDateStatus
      const tableData = []
      for (let i = 0; i < data.dataList.length; i++) {
        const item = data.dataList[i]

        tableData.push({
          ...item,
          bIssueLastissue: this.formatterDateString(item.bIssueLastissue),
          time: this.formatterDateString(item.time)
        })
      }

      this.cardAreaData.tableData = tableData
    },
    /**
     * 重置卡片区域数据
     */
    resetCardData() {
      this.$refs.inputSelectUnit.reset()
      this.cardAreaParams.date = ''
      this.initStartTime()
      Object.assign(this._data.cardAreaData, this.$options.data().cardAreaData)
    },
    /**
     * 辅助函数，获取周几
     */
    getWeek(date) {
      // 时间戳
      const week = moment(date).day()
      switch (week) {
        case 1:
          return '周一'
        case 2:
          return '周二'
        case 3:
          return '周三'
        case 4:
          return '周四'
        case 5:
          return '周五'
        case 6:
          return '周六'
        case 0:
          return '周日'
      }
    },
    /**
     * 辅助函数，判断当前日期是否为月末
     */
    isMonthEnd(date) {
      if (!date) {
        return false
      }
      const monthEnd = moment(date).endOf('month').format('YYYY-MM-DD')
      return moment(date).format('YYYY-MM-DD') === monthEnd
    },
    /**
     * 辅助函数，判断当前日期是否为季末
     */
    isQuarterEnd(date) {
      if (!date) {
        return false
      }
      const quarterEnd = moment(date).endOf('quarter').format('YYYY-MM-DD')
      return moment(date).format('YYYY-MM-DD') === quarterEnd
    },
    /**
     * 辅助函数，判断当前日期是否为年末
     */
    isYearEnd(date) {
      if (!date) {
        return false
      }
      const yearEnd = moment(date).endOf('year').format('YYYY-MM-DD')
      return moment(date).format('YYYY-MM-DD') === yearEnd
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~@jupiterweb/assets/styles/variables.scss';
::v-deep .jr-decorated-table--top-search .jr-decorated-table--header {
  height: 0px;
}
::v-deep .jr-decorated-table > div {
  height: 100%;
  padding-left: 16px;
  padding-right: 16px;
}
.releaseWindowSelect {
  &-topSelect {
    width: 100%;
    padding: 16px 0px 16px 16px;
    background-color: #ffffff;
    display: flex;
    align-items: center;
    & > label {
      width: 90px;
      height: 22px;
      font-size: var(--el-font-size-base);
      color: rgba(0, 0, 0, 0.85);
      line-height: 22px;
    }
    & > button {
      // width: 88px;
      border-radius: 2px;
      margin-left: 24px;
    }
    &-datePicker {
      width: 296px;
      margin-left: 8px;
    }
  }
  &-middleSelect {
    width: 100%;
    background-color: #ffffff;
    padding: 21px 0px 16px 16px;
    display: flex;
    align-items: center;
    & > button {
      // width: 88px;
      border-radius: 2px;
      margin-left: 10px;
    }
  }
  &-cardArea {
    width: 100%;
    height: 456px;
    padding: 8px 17px 93px;
    display: flex;
    gap: 30px;
    background-color: #ffffff;
    &-card {
      width: 48.25%;
      height: 363px;
      background: linear-gradient(180deg, #fff6ee 0%, #ffffff 23%, #ffffff 100%);
      box-shadow: 2px 2px 8px 0px rgba(55, 90, 170, 0.04), -2px -2px 8px 0px rgba(170, 145, 55, 0.1);
      border-radius: 4px;
      border: 2px solid #ffffff;
      padding: 29px 25px 0px 17px;
      &-date {
        display: flex;
        padding: 0px 0px 0px 20px;
        justify-content: space-between;
      }
      &-status {
        margin-top: 62px;
        & > p {
          width: 100%;
          height: 40px;
          display: flex;
          justify-content: space-between;
          margin-bottom: 17px;
          padding: 11px 18px 10px 20px;
          background: rgba(255, 142, 43, 0.03);
          & > span {
            height: 19px;
            font-size: var(--el-font-size-base);
            color: #666666;
            line-height: 19px;
          }
        }
      }
      &-tips {
        & > p {
          color: #999999;
        }
        & > p:nth-of-type(1) {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 100%;
          height: 30px;
          margin-bottom: 15px;
          color: #666666;
          & > span {
            width: 97px;
            height: 30px;
            background: #ffffff;
            border-radius: 15px;
            border: 1px solid #e9e9e9;
            text-align: center;
            line-height: 30px;
          }
        }
        & > p:nth-of-type(1):after,
        & > p:nth-of-type(1)::before {
          content: '';
          width: calc((100% - 97px) / 2);
          height: 1px;
          border: 1px solid #e9e9e9;
        }
      }
    }
  }
}
.down-circle-tips {
  margin-bottom: 0px;
  margin-top: 15px;
  span {
    height: 19px;
    font-family: MicrosoftYaHei;
    font-size: var(--el-font-size-base);
    color: rgba(0, 0, 0, 0.6);
    line-height: 19px;
  }
}
</style>
