<!-- 债券持仓分析 -> 期末前10大集中度 -->
<template>
  <div class="home-poc-item hold-distribu-page">
    <div class="home-poc-item--header">期末前10大集中度
      <fullscreen @fullscreen="fullscreen" />
    </div>

    <div class="home-poc-item--body">
      <TemplateModule
        ref="TemplateModule"
        chart-seq="72cb522d085e48e18f3d6e43c5d0505b"
        chart-type="PIE"
        style="padding-top: 10px;"
        :params="queryParams"
      />

      <TemplateModule
        ref="TemplateModule2"
        chart-seq="6489eccc754f48f39e80acd0d665375d"
        chart-type="MULTILINE"
        style="padding-top: 10px;"
        :params="queryParams"
      />
    </div>
  </div>
</template>

<script>
import { getInit } from '@/systems/mixins'
import fullscreen from '../../common/fullscreen'
import TemplateModule from '@jupiterweb/components/template-module'

export default {
  components: {
    fullscreen,
    TemplateModule
  },
  mixins: [getInit('/invest/portfolio/bondposanalysis/BondPosAnalysis001')],
  props: {
    params: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      form: {}
    }
  },
  computed: {
    queryParams() {
      return { ...this.params }
    }
  },
  methods: {
    fullscreen(v) {
      this.$emit('fullscreen', v)

      const { chartInstance } = this.$refs.TemplateModule.$children[0]
      chartInstance && this.$nextTick(chartInstance.resize)
    }
  }
}
</script>

<style lang="scss" scoped>
@import "../../common/poc.scss";

.hold-distribu-page {
  .home-poc-item--body {
    display: flex !important;

    .template-show {
      width: 100%;
      overflow: hidden !important;
    }
  }
}
</style>

