<!-- 募集资金用途 -->
<template>
  <div class="useProceed">
    <jr-decorated-table
      style="height: 100%"
      custom-id="56db29a80bed4906b5114d6f9ebc2e8c"
      :custom-render="customRender"
      v-bind="{...$attrs, ...$props}"
    />
    <modal-non-process :close-modal="() => (modalConfig.visible = false)" v-bind="{ ...modalConfig }" />
  </div>
</template>

<script>
import { ConvertAmount } from '@jupiterweb/utils/common'
export default {
  data() {
    return {
      customRender: {
        lx: (h, { row }) => {
          return <span> { ConvertAmount('1', row.lx, 1, 2)} <jr-svg-icon icon-class='caret-right' onClick={this.onRowlClick.bind(this, row)}/></span>
        }
      },
      modalConfig: {
        visible: false,
        modalType: 'add',
        modalTypeName: '付息计划维护',
        itemData: {},
        size: 'large',
        save: '',
        joinType: true,
        menuinfo: {
          componenturl: 'poc/myBonds/useProceed/payPlan.vue'
        }
      }
    }
  },
  methods: {
    onRowlClick(row) {
      console.log(row, 'mmmmnnnnn')
      Object.assign(this.modalConfig, {
        visible: true,
        itemData: row
      })
    }
  }
}
</script>
<style lang="scss">
.useProceed{
    height: 100%;
    .jr-svg-icon--caret-right {
        display: inline-block;
        cursor: pointer;
    }
}
</style>
