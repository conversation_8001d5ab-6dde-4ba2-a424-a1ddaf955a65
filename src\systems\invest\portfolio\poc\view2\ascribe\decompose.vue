<!-- 组合归因分析 -> 组合收益分解 -->
<template>
  <div class="home-poc-item has-fullscreen decompose-page">
    <div class="home-poc-item--header float-left">组合收益分解 <fullscreen v-on="{ ...$listeners }" /></div>

    <div class="content-panel">
      <jr-decorated-table
        custom-id="918e30e0d87c412796d05f64c3d8a379"
        v-bind="{
          ...$attrs,
          params,
          noPagination: true,
          menuinfo: {
            pageId: 'PtlAttributeAnalysis001',
            btnList: [{
              btnPosition: 'HEAD',
              btnkey: 'export',
              btnnm: '导出',
              componenturl: 'export',
              effectflag: 'E',
              moduleid: 'PtlAttributeAnalysis001_002_001',
              moduletype: 'btn',
              orde: 4,
              parameter: JSON.stringify({ noSelect: true }),
              permitflag: 'C',
              permittag: '02',
              showflag: 'Y',
              tmFlag: 'E'
            }]
          }
        }"
        @refreshed="queryListEnd"
      />

      <div>
        <echarts :options="options" :styles="{ height: '100%', marginBottom: '20px' }" />
      </div>
    </div>
  </div>
</template>

<script>
import echarts from '@jupiterweb/components/echarts'
import fullscreen from '../../common/fullscreen'

export default {
  components: {
    echarts,
    fullscreen
  },
  props: {
    params: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      cols: [{
        title: '起始日期',
        prop: 'startDate',
        type: 'date'
      }, {
        title: '截止日期',
        prop: 'endDate',
        type: 'date'
      }],
      options: {}
    }
  },
  methods: {
    getChartOptions(data) {
      // console.log('--x--：', x)
      // console.log('--series--：', series)

      return {
        toolbox: {
          right: '12px',
          feature: {
            saveAsImage: { show: true }
          }
        },
        title: {
          left: 'center',
          text: '收益贡献',
          textStyle: {
            fontSize: 13
          }
        },
        series: [
          {
            type: 'treemap',
            roam: false,
            nodeClick: false,
            width: '90%',
            top: '28px',
            bottom: '60px',
            breadcrumb: {
              show: false
            },
            label: {
              show: true,
              formatter: '{b}\n（{c}%）',
              backgroundColor: '#409eff'
            },
            data: data.filter(v => v.assetTypeName !== '合计').map(v => ({
              name: v.assetTypeName,
              value: (v.profitContribute * 100)
            }))
          }
        ]
        // series
      }
    },
    queryListEnd(ins) {
      const data = ins.listData.length ? ins.listData : [{ assetTypeName: '', profitContribute: 1 }]
      this.options = this.getChartOptions(data)
    }
  }
}
</script>

<style lang="scss" scoped>
@import "../../common/poc.scss";
</style>

<style lang="scss">
.decompose-page {
  height: 100%;

  .home-poc-item--header {
    .home-poc-item--fullscreen {
      border-left: 1px solid #DCDFE6 !important;
    }
  }

  &.home-poc-item.has-fullscreen {
    .jr-decorated-table--header {
      padding-right: 10px !important;
    }
  }

  .content-panel {
    height: 100%;
    display: flex;
    clear: both;

    & > div {
      width: 100%;
      height: 100%;
    }
  }
}
</style>
