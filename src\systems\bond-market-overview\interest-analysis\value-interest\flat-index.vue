<template>
  <jr-layout-horizontal>
    <template v-slot:left>
      <flat-index :query="handleQuery" :reset="handleReset" :pagination="configTable.pagination">
        <template v-slot:form>
          <el-form ref="elForm" :model="configForm.model">
            <jr-form-item-create :validate-rules="validateRules" :column="0" :data="configForm.data" :model="configForm.model" />
          </el-form>
        </template>

        <template v-slot:right-button>
          <el-button>
            <jr-svg-icon icon-class="export" />
          </el-button>
        </template>

        <template v-slot:table-list="{ height }">
          <jr-table
            :height="height"
            :columns="configTable.columns"
            :data-source="configTable.data"
            :loading="configTable.loading"
            :pagination="configTable.pagination"
            :on-change="handleQuery"
            border
          >
            <template v-slot:index>
              <el-table-column
                type="index"
                width="50px"
                align="center"
                :label="InitialMessage('common.columns.index')"
              >
                <template slot-scope="scope">
                  <span>{{ (configTable.pagination.pageNo - 1) * configTable.pagination.pageSize + scope.$index +1 }}</span>
                </template>
              </el-table-column>
            </template>
          </jr-table>
        </template>
      </flat-index>
    </template>

    <template v-slot:right>
      <div class="flex-row">
        <Chart style="width: calc(100% - 300px);" />
      </div>
    </template>
  </jr-layout-horizontal>
</template>

<script>
import * as API from '@/api/demo/flat-index'
import Chart from './modules/chart.vue'

import { getModalProps } from '@/systems/mixins'
export default {
  components: { Chart },
  mixins: [getModalProps],
  props: {
    // 平台日期
    date: {
      type: [Number, String],
      default: null
    },
    // 权限相关
    permitdetail: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      // 查询区域
      configForm: {
        model: {},
        data: [
          {
            title: '发行截止日',
            prop: 'releaseTime',
            type: 'rangeDate'
          },
          {
            title: '债券类型',
            prop: 'bondType',
            type: 'select',
            options: [
              { text: '1111', value: '1111' },
              { text: '2222', value: '2222' },
              { text: '3333', value: '3333' }
            ]
          },
          {
            title: '债项评级',
            prop: 'debtRating',
            type: 'select',
            options: [
              { text: '1111', value: '1111' },
              { text: '2222', value: '2222' },
              { text: '3333', value: '3333' }
            ]
          },
          {
            title: '隐含评级',
            prop: 'implicitRating',
            type: 'select',
            options: [
              { text: '1111', value: '1111' },
              { text: '2222', value: '2222' },
              { text: '3333', value: '3333' }
            ]
          },
          {
            title: '发行期限',
            prop: 'issuePeriod',
            type: 'select',
            options: [
              { text: '1111', value: '1111' },
              { text: '2222', value: '2222' },
              { text: '3333', value: '3333' }
            ]
          },
          {
            title: '有无担保',
            prop: 'security',
            type: 'select',
            options: [
              { text: '1111', value: '1111' },
              { text: '2222', value: '2222' },
              { text: '3333', value: '3333' }
            ]
          },
          {
            title: '含权债',
            prop: 'obligation',
            type: 'select',
            options: [
              { text: '1111', value: '1111' },
              { text: '2222', value: '2222' },
              { text: '3333', value: '3333' }
            ]
          },
          {
            title: '主体层级',
            prop: 'subjectLevel',
            type: 'select',
            options: [
              { text: '1111', value: '1111' },
              { text: '2222', value: '2222' },
              { text: '3333', value: '3333' }
            ]
          },
          {
            title: '地区',
            prop: 'district',
            type: 'select',
            options: [
              { text: '1111', value: '1111' },
              { text: '2222', value: '2222' },
              { text: '3333', value: '3333' }
            ]
          }
        ]
      },
      // 表格数据
      configTable: {
        loading: false,
        columns: [
          {
            title: '债券简称',
            prop: 'name'
          },
          {
            title: '发行截止日',
            prop: 'issueDeadline',
            type: 'date'
          },
          {
            title: '中债隐含评级',
            prop: 'implicitRating'
          },
          {
            title: '发行期限',
            prop: 'releaseTime'
          },
          {
            title: '票面利率(%)',
            prop: 'rate1',
            type: 'rate'
          },
          {
            title: '首次估值(%)',
            prop: 'rate2',
            type: 'rate'
          },
          {
            title: '票面与首次估值利差(BP)',
            prop: 'amount',
            type: 'amount'
          },
          {
            title: '发行人',
            prop: 'user'
          },
          {
            title: '是否城投',
            prop: 'investment'
          }
        ],
        data: [
          {
            name: '24淄博城运MTN002A',
            issueDeadline: 1624931532000,
            implicitRating: 'AA(2)',
            releaseTime: '3Y',
            rate1: 2.5900,
            rate2: 2.6065,
            amount: -1.65,
            user: '淄博市城市资产运营集团有限公司',
            investment: '否'
          },
          {
            name: '24淄博城运MTN002A',
            issueDeadline: 1624931532000,
            implicitRating: 'AA(2)',
            releaseTime: '3Y',
            rate1: 2.5900,
            rate2: 2.6065,
            amount: -1.65,
            user: '淄博市城市资产运营集团有限公司',
            investment: '否'
          },
          {
            name: '24淄博城运MTN002A',
            issueDeadline: 1624931532000,
            implicitRating: 'AA(2)',
            releaseTime: '3Y',
            rate1: 2.5900,
            rate2: 2.6065,
            amount: -1.65,
            user: '淄博市城市资产运营集团有限公司',
            investment: '否'
          },
          {
            name: '24淄博城运MTN002A',
            issueDeadline: 1624931532000,
            implicitRating: 'AA(2)',
            releaseTime: '3Y',
            rate1: 2.5900,
            rate2: 2.6065,
            amount: -1.65,
            user: '淄博市城市资产运营集团有限公司',
            investment: '否'
          },
          {
            name: '24淄博城运MTN002A',
            issueDeadline: 1624931532000,
            implicitRating: 'AA(2)',
            releaseTime: '3Y',
            rate1: 2.5900,
            rate2: 2.6065,
            amount: -1.65,
            user: '淄博市城市资产运营集团有限公司',
            investment: '否'
          }
        ],
        pagination: {
          pageNo: 1,
          pageSize: window.screen.height > 1000 ? 25 : 15,
          pageSizeOptions: [15, 25, 50, 100],
          total: 0,
          showSizeChanger: true
        }
      }
    }
  },
  methods: {
    // 查询
    handleQuery() {
      const self = this
      const { configForm, configTable } = self

      self.$refs.elForm.validate(async valid => {
        if (valid) {
          configTable.loading = true

          const params = {
            data: { ...configForm },
            page: { ...configTable.pagination }
          }

          const { list = [], total = 0 } = { ...await API.GetListData(params) }

          if (list.length) {
            configTable.loading = false
            configTable.data = list
            configTable.pagination.total = total
          }
        }
      })
    },
    // 重置
    handleReset() {

    }
  }
}
</script>

<style lang="scss">

</style>

