/**
 * 获取多选导出参数
 * @param {Array} rows 单分页已选行
 * @param {Array} listData 单分页表格数据
 * @param {Array} multipleSelectRows 批量导出多选存储数组
 * @param {Array} multipleSelectIds 批量导出多选id集合
 */
function getMultipleRowsAndIds(rows, listData, multipleSelectRows, multipleSelectIds, that, maxLength = 50) {
  if (multipleSelectIds.length <= maxLength) {
    const currentSelectedIds = rows.map((item) => item.uuid) // 单分页已选行id集合
    const currentUnselectedIds = [] // 单分页未选行id集合

    for (let index = 0; index < listData.length; index++) {
      const item = listData[index]
      if (!currentSelectedIds.includes(item.uuid)) {
        currentUnselectedIds.push(item.uuid)
      }
    }

    const selectRows = [...multipleSelectRows, ...rows]
      .filter((item) => {
        return !currentUnselectedIds.includes(item.uuid)
      })
      .filter((item, index, self) => index === self.findIndex((t) => JSON.stringify(t) === JSON.stringify(item)))

    const ids = Array.from(new Set([...multipleSelectIds, ...currentSelectedIds])).filter((item) => {
      return !currentUnselectedIds.includes(item)
    })

    if (ids.length <= maxLength) {
      return {
        rows: selectRows,
        ids: ids
      }
    } else {
      that.$message.warning(`最多只能导出${maxLength}条数据`)
      const currentIds = ids.splice(0, maxLength)
      setDefaultSelected(currentIds, listData, that)
      return {
        rows: selectRows.splice(0, maxLength),
        ids: currentIds
      }
    }
  } else {
    that.$message.warning(`最多只能导出${maxLength}条数据`)
    return {
      rows: multipleSelectRows,
      ids: multipleSelectIds
    }
  }
}
/**
 * 设置默认选中
 * @param {Array} selectRowIds 选中行id集合
 * @param {Array} tableData 表格数据
 * @param {Object} that 当前组件实例
 */
function setDefaultSelected(selectRowIds, tableData, that) {
  const elTableRef = getElTableFromJrDecoratedTable(that)
  console.log(elTableRef, 'elTableRef')

  that.$nextTick(() => {
    tableData.forEach((row) => {
      if (selectRowIds.includes(row.uuid)) {
        elTableRef?.toggleRowSelection(row, true)
      } else {
        elTableRef?.toggleRowSelection(row, false)
      }
    })
  })
}
/**
 * 获取jrDecoratedTable的elTableRef
 * @param {Object} that 当前组件实例
 * @returns {Object} elTableRef
 */
function getElTableFromJrDecoratedTable(that) {
  if (
    that.$refs.jrTable &&
    that.$refs.jrTable.$children &&
    Array.isArray(that.$refs.jrTable.$children) &&
    that.$refs.jrTable.$children.length > 0 &&
    that.$refs.jrTable.$children[0] &&
    that.$refs.jrTable.$children[0].$el &&
    that.$refs.jrTable.$children[0].$el.__vue__ &&
    that.$refs.jrTable.$children[0].$el.__vue__.$children &&
    Array.isArray(that.$refs.jrTable.$children[0].$el.__vue__.$children) &&
    that.$refs.jrTable.$children[0].$el.__vue__.$children.length > 0 &&
    that.$refs.jrTable.$children[0].$el.__vue__.$children[0] &&
    that.$refs.jrTable.$children[0].$el.__vue__.$children[0].$el &&
    that.$refs.jrTable.$children[0].$el.__vue__.$children[0].$el.__vue__ &&
    that.$refs.jrTable.$children[0].$el.__vue__.$children[0].$el.__vue__.$children
  ) {
    return that.$refs.jrTable.$children[0].$el.__vue__.$children[0].$el.__vue__.$el.__vue__.$children[0]
  } else {
    return null
  }
}

export { getMultipleRowsAndIds, setDefaultSelected }
