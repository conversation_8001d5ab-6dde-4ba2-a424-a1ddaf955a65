<!-- 债券余额分析 -->
<template>
  <div class="bond-balance-analysis">
    <jr-decorated-table
      stripe
      :date="1662022042"
      :params="params"
      custom-id="9784744570374c0abd53ea0e6e28d968"
      :row-click="clickFunc"
      :row-dbl-click="dblClickFunc"
      :menuinfo="{ moduleid: 'xxxx' }"
      @refreshed="callFn"
    />
  </div>
</template>

<script>
export default {
  props: {
    params: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {}
  },
  methods: {
    callFn(data) {
      const columns = data.config.columns
      console.log('columns===>', columns)
    },
    handleQuery() {
      this.params.ceshi = this.configQuery.ceshi
    },
    clickFunc(row) {
      console.log(row, 'clickFunc')
    },
    dblClickFunc(row) {
      console.log(row, 'dblClickFunc')
    }
  }
}
</script>
<style lang="scss">
.bond-balance-analysis {
	padding: 0;
	background: #fff;
	height: 400px;
	.bondBalanceAnalysisTitle {
    font-size: var(--el-font-size-base) !important;
		color: #303133;
		font-weight: 700;
		padding-bottom: 6px;
		border-bottom: 1px solid #EBEEF5;
	}

}
</style>
