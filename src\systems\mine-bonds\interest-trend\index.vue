<template>
  <!-- 利差走势 -->
  <div class="interest-trend">
    <el-tabs v-model="tabActiveName" class="interest-trend-tabs" @tab-click="handleClick">
      <el-tab-pane label="发行利差" name="publishRate" />
      <el-tab-pane label="估值利差" name="valuationRate" />
    </el-tabs>
    <component :is="tabActiveName" />
  </div>
</template>
<script>
import publishRate from './components/publishRate.vue'
import valuationRate from './components/valuationRate.vue'
export default {
  components: {
    publishRate,
    valuationRate
  },
  props: {
    permitdetail: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      tabActiveName: 'publishRate'
    }
  },
  methods: {
    handleClick() {
      //
    }
  }
}
</script>
<style lang="scss">
.interest-trend {
  height: 100%;

  &-tabs {
    .el-tabs__header {
      background: #fff;
      margin-bottom: 0;

      .el-tabs__nav-wrap::after {
        height: 1px;
        background: #f4f4f4;
      }

      .el-tabs__nav-scroll {
        margin-left: 16px;
      }

      .el-tabs__item.is-active {
        color: var(--theme--color) !important;
      }
    }
  }
}
</style>
