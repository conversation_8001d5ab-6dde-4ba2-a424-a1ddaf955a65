<template>
  <div class="public-chart">
    <Echart id="spreadCurve" ref="componentsCharts" :options="options" :styles="{ height: height + 'px' }" />
  </div>
</template>
<script>
import Echart from '@jupiterweb/components/echarts'
import * as echarts from 'echarts'
export default {
  components: {
    Echart
  },
  props: {
    height: {
      type: Number,
      default() {
        return 400
      }
    },
    chartdata: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      options: {}
    }
  },
  computed: {
    chartOptions() {
      return this.chartdata
    }
  },
  watch: {
    chartOptions: {
      handler(newVal, oldVal) {
        this.init()
      },
      deep: true
    }
  },

  created() {
    this.init()
  },
  methods: {
    getMyChart() {
      return this.$refs.componentsCharts.myChart
    },
    init() {
      this.$nextTick(() => {
        this.options = this.chartdata
        this.$refs.componentsCharts?.chartResize()
      })
    }
  }
}
</script>
