<!-- 债券对标 -->
<template>
  <div class="benchmark-analysis bond-benchmark">
    <div style="display: flex; padding: 8px 16px;align-items: center;">
      <span class="subtitle-label">债券对标</span>
      <el-form :model="searchForm" inline class="search-form">
        <jr-form-item>
          <el-radio-group v-model="searchForm.benchmarkType" @change="handleBenchmarkTypeChange">
            <el-radio label="Exist">存续对标</el-radio>
            <el-radio label="Issue">发行对标</el-radio>
          </el-radio-group>
        </jr-form-item>
        <jr-form-item v-if="searchForm.benchmarkType == 'Issue'" style="margin-left: 16px" label="发行起始日期">
          <el-date-picker
            v-model="issueDate"
            type="daterange"
            style="width: 260px !important"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          />
        </jr-form-item>
      </el-form>
    </div>
    <div class="search-form search-form--flex container-padding">
      <el-form :model="searchForm" inline>
        <jr-form-item label="对标企业">
          <jr-combobox
            v-model="searchForm.issuerCodeList"
            style="width: 260px"
            clearable
            multiple
            collapse-tags
            placeholder="请选择内容"
            :data="benchmarkCompOptions"
            option-value="bmEntId"
            option-label="bmEntName"
          />
        </jr-form-item>
        <jr-form-item label="债券类型">
          <select-auto-set
            style="width: 100%"
            :options="bondTypeOptions"
            value-key="bondTypeCode"
            label-key="bondTypeName"
            children="list"
            @emitConfirmData="(data) => (searchForm.bondTypeList = data)"
          />
        </jr-form-item>
        <jr-form-item v-if="searchForm.benchmarkType == 'Issue'" label="含权债">
          <jr-combobox
            v-model="searchForm.isRight"
            style="width: 135px"
            clearable
            placeholder="请选择内容"
            :data="containRightOptions"
            option-value="itemcode"
            option-label="cnname"
          />
        </jr-form-item>
        <jr-form-item v-if="searchForm.benchmarkType == 'Issue'" label="期限">
          <jr-combobox
            v-model="searchForm.wrpcTermList"
            style="width: 180px"
            clearable
            multiple
            collapse-tags
            placeholder="请选择内容"
            :data="params.wprcTermOptions"
            option-value="itemcode"
            option-label="cnname"
          />
        </jr-form-item>
        <el-button type="primary" @click="queryData">查询</el-button>
        <el-dropdown class="align-right export-dropdown-btn" @command="handleExport">
          <el-button class="export-btn">
            <jr-svg-icon icon-class="upload" />
            <span class="export-btn-label">导出</span>
            <i class="el-icon-arrow-down el-icon-caret-bottom" />
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command="image">
              <jr-svg-icon icon-class="picture" />
              导出图片
            </el-dropdown-item>
            <el-dropdown-item command="excel">
              <jr-svg-icon icon-class="table" />
              导出表格
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </el-form>
    </div>
    <div class="content container-padding" :style="{ paddingRight: showRightContent ? '16px' : '0px' }">
      <div class="left-content">
        <Echart ref="bondBenchmarkEchart" :options="echartOptions" :styles="{ height: '400px' }" />
      </div>
      <div class="middle-content">
        <div
          class="shrink-expand-btn"
          :class="showRightContent ? 'shrink-expand-btn--shrink' : 'shrink-expand-btn--expand'"
          @click.stop="handleShrinkExpand"
        >
          <div class="triangle" />
        </div>
      </div>
      <div
        class="right-content"
        :class="showRightContent ? 'right-content--hasborder' : 'right-content--noborder'"
        :style="{ flex: showRightContent ? '1' : '0' }"
      >
        <jr-table
          v-show="showRightContent"
          class="right-table"
          :height="tableConfig.pagination.total > 0 ? 330 : 370"
          :border="false"
          :columns="tableConfig.columns"
          :data-source="tableConfig.data"
          :loading="tableConfig.loading"
          :pagination="tableConfig.pagination"
          @sort-change="handleTableSortChange"
        />
      </div>
    </div>
  </div>
</template>

<script>
import * as API from '@/api/benchmark-analysis/benchmark-analysis.js'
import * as ChartCf from './chart-config.js'
import * as TableCf from './table-config.js'
import SelectAutoSet from '@/components/selectAutoSet'
import Echart from '@jupiterweb/components/echarts'
import moment from 'moment'

export default {
  components: {
    SelectAutoSet,
    Echart
  },
  props: {
    params: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      searchForm: {
        issuerCodeList: [],
        bondTypeList: [],
        benchmarkType: 'Exist',
        isRight: null,
        wrpcTermList: []
      },
      benchmarkCompOptions: [],
      bondTypeOptions: [],
      containRightOptions: [
        { itemcode: 1, cnname: '包含' },
        { itemcode: 0, cnname: '不包含' }
      ],
      showRightContent: true,
      loading: false,
      echartOptions: {},
      tableConfig: {},
      bondTableColumns: [],
      issueDate: [new Date().getFullYear() + '-01-01', new Date().getFullYear() + '-12-31']
    }
  },
  created() {
    // 初始化页面默认值
    this.initPageDefaultValue()
    // 查询获取已配置的对标企业下拉选择信息
    this.queryBenchmarkCompanySelectList()
    // 查询获取债券类型下拉框信息
    this.queryBenchmarkBondTypeSelectList()
  },
  methods: {
    /**
     * 初始化页面默认值
     */
    initPageDefaultValue() {
      const chartOpt = ChartCf.getBondChartOptions(this.searchForm.benchmarkType)
      this.$set(this, 'echartOptions', { ...chartOpt })
      this.$set(this, 'tableConfig', { ...TableCf.tableConfig })
      this.$set(this, 'bondTableColumns', [...TableCf.bondTableColumns])
    },
    /**
     * 查询获取已配置的对标企业下拉框信息
     */
    queryBenchmarkCompanySelectList() {
      const { entId, entOrgCode, groupId } = this.params
      this.loading = true
      API.getBenchmarkCompanySelectList({
        entId,
        entOrgCode,
        groupId
      })
        .then((data) => {
          this.loading = false
          this.benchmarkCompOptions = Array.isArray(data) ? data : []
          // 设置对标企业全部选中
          this.setBenchmarkCompanyAllSelected()
        })
        .catch(() => {
          this.loading = false
          this.$message.warning('查询获取债券对标企业下拉数据失败!')
          this.benchmarkCompOptions = []
          // 设置对标企业全部选中
          this.setBenchmarkCompanyAllSelected()
        })
    },
    /**
     * 设置对标企业全部选中
     */
    setBenchmarkCompanyAllSelected() {
      let allSelectedList = []
      const { benchmarkCompOptions } = this
      if (Array.isArray(benchmarkCompOptions) && benchmarkCompOptions.length > 0) {
        allSelectedList = benchmarkCompOptions.map((item) => item.bmEntId)
      }
      this.searchForm.issuerCodeList = allSelectedList
      // 设置全部选中后，重新加载chart、table数据
      this.queryData()
    },
    /**
     * 查询获取债券类型下拉框信息
     */
    queryBenchmarkBondTypeSelectList() {
      this.loading = true
      API.getBenchmarkBondTypeSelectList({})
        .then((data) => {
          this.loading = false
          const treeData = Array.isArray(data) ? data : []
          this.bondTypeOptions = this.formatTreeData(treeData, 'list')
        })
        .catch(() => {
          this.loading = false
          this.bondTypeOptions = []
        })
    },
    /**
     * 递归处理树形结构数据
     * @param {Array} data 树形数据
     * @param {String} name 子节点字段名
     */
    formatTreeData(data, name) {
      if (Array.isArray(data) && name) {
        return data.map((item) => {
          if (item[name] && !item[name].length) {
            item[name] = undefined
          } else if (item[name]) {
            this.formatTreeData(item[name], name)
          }
          return item
        })
      }
    },
    /**
     * 债券对标类型改变处理函数
     */
    handleBenchmarkTypeChange() {
      const chartOpt = ChartCf.getBondChartOptions(this.searchForm.benchmarkType)
      this.$set(this, 'echartOptions', { ...chartOpt })
      this.queryData()
    },
    /**
     * 获取查询参数
     */
    getSearchParams() {
      const { entId, groupId } = this.params
      const { sortColumn, sortOrder, pagination } = this.tableConfig
      const { benchmarkType } = this.searchForm
      let firstDtStart, firstDtEnd
      if (Array.isArray(this.issueDate)) {
        firstDtStart = this.issueDate[0] || ''
        firstDtEnd = this.issueDate[1] || ''
      }
      const data = { ...this.searchForm, firstDtStart, firstDtEnd, entId, groupId, sortColumn, sortOrder }
      const page = { ...pagination }
      return benchmarkType === 'Issue' ? { data, page, benchmarkType } : { ...data }
    },
    /**
     * 查询债券对标数据
     */
    queryData() {
      // 查询图表数据
      this.queryChartData()
      // 初始化列表配置信息
      this.initTableConfig()
      // 查询列表数据
      this.queryTableData()
    },
    /**
     * 查询债券对标echart图表数据
     */
    queryChartData() {
      const params = { ...this.getSearchParams() }
      const { benchmarkType } = this.searchForm
      this.loading = true
      API.getBondBenchmarkChartData(benchmarkType === 'Issue' ? params.data : params)
        .then((data) => {
          this.loading = false
          const chartData = Array.isArray(data) ? data : []
          this.handleChartData(chartData)
        })
        .catch(() => {
          this.loading = false
        })
    },
    /**
     * 处理债券对标图表数据
     * @param {Array} chartData 图表数据
     */
    handleChartData(chartData) {
      let [xAxisData, seriesXhzData, seriesGszData, seriesQyzData, seriesPmjqData] = [[], [], [], [], []]
      if (Array.isArray(chartData) && chartData.length > 0) {
        xAxisData = chartData.map((item) => item.issuerName)
        seriesXhzData = chartData.map((item) => item.debtSum)
        seriesGszData = chartData.map((item) => item.corporateSum)
        seriesQyzData = chartData.map((item) => item.enterpriseSum)
        seriesPmjqData = chartData.map((item) => item.sumBalanceRate)
      }
      this.echartOptions.xAxis[0].data = xAxisData
      for (const i of this.echartOptions.yAxis) {
        i.splitNumber = 4
      }
      this.echartOptions.series[0].data = seriesXhzData
      this.echartOptions.series[1].data = seriesGszData
      this.echartOptions.series[2].data = seriesQyzData
      this.echartOptions.series[3].data = seriesPmjqData
    },
    /**
     * 初始化表单数据
     */
    initTableConfig() {
      // 根据债券对标类型，动态显示查询列表的列
      const { benchmarkType } = this.searchForm
      this.tableConfig.sortColumn = null
      this.tableConfig.sortOrder = null
      this.tableConfig.pagination.pageNo = 1
      this.tableConfig.pagination.total = 0
      if (benchmarkType === 'Issue') {
        this.tableConfig.columns = this.bondTableColumns
      } else {
        this.tableConfig.columns = []
      }
    },
    /**
     * 查询获取债券对标列表数据
     */
    queryTableData() {
      // 调用接口获取数据
      this.tableConfig.loading = true
      API.getBondBenchmarkList({
        ...this.getSearchParams()
      })
        .then((data) => {
          this.tableConfig.loading = false

          // 若为发行对标，直接使用后台返回的数据
          if (this.searchForm.benchmarkType === 'Issue') {
            this.tableConfig.data = data.list || []
            this.tableConfig.pagination.total = data.total
          } else {
            // 若为存续对标，需先动态处理列
            this.handleDynamicColumns(data || {})
          }
        })
        .catch(() => {
          this.tableConfig.loading = false
        })
    },
    /**
     * 处理列表动态列
     * @param {Object} params 列参数
     */
    handleDynamicColumns({ columnsList, dataList }) {
      const columns = []
      if (Array.isArray(columnsList) && columnsList.length > 0) {
        columnsList.forEach((column) => {
          const { columnCode, columnTitle } = column
          const columnProps = {
            prop: columnCode,
            title: columnTitle,
            width: '165',
            align: ['issuerName', 'isWindInvest'].includes(columnCode) ? 'left' : 'right',
            sorter: ['issuerName', 'isWindInvest'].includes(columnCode) ? false : 'custom'
          }
          // 发行人
          if (columnCode === 'issuerName') {
            columnProps.fixed = true
            columnProps.width = '275'
          }
          // 债券只数
          if (columnCode === 'bondCount') {
            columnProps.width = '120'
          }
          columns.push(columnProps)
        })
      }
      this.tableConfig.columns = columns
      this.tableConfig.data = dataList || []
      // this.tableConfig.pagination.total = dataList.length
    },
    /**
     * 列表字段排序处理函数
     * @param {Object} params 列配置信息
     */
    handleTableSortChange({ prop, order }) {
      this.tableConfig.sortColumn = prop
      this.tableConfig.sortOrder = order === 'descending' ? 'desc' : order === 'ascending' ? 'asc' : null
      this.queryTableData()
    },
    /**
     * 收缩展开处理函数
     */
    handleShrinkExpand() {
      this.showRightContent = !this.showRightContent
    },
    /**
     * 获取导出文件名称
     */
    getExportFileName() {
      const { groupName } = this.params
      const benchmarkTypeName = this.searchForm.benchmarkType === 'Issue' ? '发行对标' : '存续对标'
      return `${groupName}_${benchmarkTypeName}`
    },
    /**
     * 导出处理函数
     * @param {String} command 导出类型：image-导出图片，excel-导出excel
     */
    handleExport(command) {
      const exportFileName = this.getExportFileName()
      // 导出图片，文件名添加时间戳
      if (command === 'image') {
        this.exportChartToImage(`${exportFileName}_${moment().format('YYYYMMDDHHmmssSSS')}`)
      }
      // 导出excel，后台会自动添加时间戳
      if (command === 'excel') {
        this.exportDataToExcel(exportFileName)
      }
    },
    /**
     * 导出chart图表数据到image图片
     * @param {String} fileName 导出文件名
     */
    exportChartToImage(fileName) {
      const chart = this.$refs.bondBenchmarkEchart.myChart
      const imgUrl = chart.getDataURL({
        backgroundColor: '#ffffff',
        pixelRatio: 2 // 提高导出清晰度（可选）
      })
      const link = document.createElement('a')
      link.href = imgUrl
      link.download = `${fileName}.png`
      link.click()
    },
    /**
     * 导出债券对标数据到Excel
     * @param {String} fileName 导出文件名
     */
    exportDataToExcel(fileName) {
      const searchParams = { ...this.getSearchParams() }
      const { benchmarkType } = this.searchForm
      const params = {
        ...(benchmarkType === 'Issue' ? searchParams.data : searchParams),
        fileName
      }
      API.exportBondBenchmarkToExcel(params, this.searchForm.benchmarkType)
    }
  }
}
</script>
<style lang="scss" scoped>
.bond-benchmark {
  background: #fff;
  .container-padding {
    padding: 0px 16px;
  }

  .content {
    display: flex;
    overflow: hidden;
    margin-top: 16px;
    height: 400px;

    .left-content,
    .right-content--hasborder {
      min-width: 0;
      border: 1px solid #eae9e9;
      border-radius: 4px;
    }

    .left-content {
      flex: 1;
    }

    .right-content {
      overflow: hidden;
      width: 100%;
      transition: flex 0.3s;
      &--noborder {
        border: 0;
      }
    }

    .middle-content {
      width: 16px;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      user-select: none;
      z-index: 1;

      .shrink-expand-btn {
        width: 12px;
        height: 80px;
        background: #f0f0f0;
        position: relative;
        display: flex;
        align-items: center;
        border-radius: 100px 0px 0px 100px;
        cursor: pointer;

        .triangle {
          width: 0;
          height: 0;
          border-top: 5px solid transparent;
          border-bottom: 5px solid transparent;
        }

        &--shrink {
          justify-content: flex-end;
          padding-right: 2px;
          .triangle {
            border-left: 5px solid rgba(0, 0, 0, 0.6);
          }
        }

        &--expand {
          justify-content: center;
          .triangle {
            border-right: 5px solid rgba(0, 0, 0, 0.6);
          }
        }
      }
    }

    .right-table {
      width: 100%;
      padding: 16px;
    }
  }
}
</style>
