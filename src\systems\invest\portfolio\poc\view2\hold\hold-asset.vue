<!-- 持仓分析概览 -> 持仓资产分布 -->
<template>
  <div class="home-poc-item has-fullscreen plt-base-info">
    <div class="home-poc-item--header float-left">持仓资产 <fullscreen v-on="{ ...$listeners }" /></div>
    <jr-decorated-table
      custom-id="79c128aa3cb74efabdd4efa3a54029c8"
      v-bind="{
        ...$attrs,
        params,
        noPagination: true,
        menuinfo: {
          pageId: 'PtlPositionAnalysis001',
          btnList: [{
            btnPosition: 'HEAD',
            btnkey: 'export',
            btnnm: '导出',
            componenturl: 'export',
            effectflag: 'E',
            moduleid: 'PtlPositionAnalysis001_002_001',
            moduletype: 'btn',
            orde: 4,
            parameter: JSON.stringify({ noSelect: true }),
            permitflag: 'C',
            permittag: '02',
            showflag: 'Y',
            tmFlag: 'E'
          }]
        },
      }"
    />
  </div>
</template>

<script>
import fullscreen from '../../common/fullscreen'

export default {
  components: {
    fullscreen
  },
  props: {
    params: {
      type: Object,
      default: null
    }
  }
}
</script>

<style lang="scss" scoped>
@import "../../common/poc.scss";
</style>
