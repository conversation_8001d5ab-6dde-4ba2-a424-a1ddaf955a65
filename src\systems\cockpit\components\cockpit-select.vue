<template>
  <el-select 
    v-model="selected" 
    placeholder="全部" 
    @change="change"
    :multiple="multiple"
    collapse-tags
    :class="type === 'deep' ? 'cockpit_deep' : ''"
  >
    <el-option
      v-for="(item,index) in options"
      :key="index"
      :label="item[labelKey]"
      :value="item[valueKey]"
    />
  </el-select>
</template>

<script>
export default {
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    options: {
      type: Array,
      default: () => []
    },
    labelKey: {
      type: String,
      default: 'label'
    },
    valueKey: {
      type: String,
      default: 'value'
    },
    defaultValue: {
      type: [String,Array],
      default: ''
    },
    multiple: {
      type: Boolean,
      default: false
    },
    type: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      selected: ''
    }
  },
  watch: {
    defaultValue:{
      deep:true,
      immediate:true,
      handler(){
        if (this.defaultValue || this.defaultValue.length) {
          this.selected = this.defaultValue
        }
      }
    },
  },
  methods: {
    /**
     * 选择change事件
     */
    change() {
      this.$emit('change', this.selected)
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~assets/styles/portcss.scss';
::v-deep .el-input--medium .el-input__inner::placeholder {
  height: 22px;
  font-size: 14px;
  color: rgba(0,0,0,0.6);
  line-height: 22px;
}
::v-deep .el-select__tags{
  flex-wrap: nowrap;
  max-width: 50% !important;
}
::v-deep .el-input--medium .el-input__inner{
  height: vh(32);
  line-height: vh(32);
}
</style>
<style lang="scss">
.cockpit_deep .el-input--medium .el-input__inner{
  background: rgba(30,53,85,0.4);
  border: 1px solid #315280;
  color: #FFFFFF !important;
}
</style>
