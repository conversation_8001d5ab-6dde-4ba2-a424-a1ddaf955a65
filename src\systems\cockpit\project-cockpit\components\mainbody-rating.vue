<template>
  <div class="mainbody-rating">
    <cockpit-header 
      :style="{ width: px2vw(362), height: px2vh(40) }" 
    >
      <div class="mainbody-rating-title" @click.stop="changeActiveSwap">
        <span>{{ activeSwap ? '中债隐含评级' : '主体评级' }}</span>
        <img src="@/assets/cockpit/cockpit_head_swap.png" alt="">
      </div>
    </cockpit-header>
    <div class="mainbody-rating-table">
      <div class="mainbody-rating-table-th">
        <span>公司名称</span>
        <span>最新评级</span>
        <span>评级日期</span>
      </div>
      <div class="mainbody-rating-table-tr" v-for="(data,index) in listData" :key="index">
        <span>{{ data.companyname }}</span>
        <span>{{ data.rating }}</span>
        <span>{{ data.ratingdate }}</span>
      </div>
    </div>
    <div class="mainbody-rating-more" @click.stop="redirectToMenu">
      <span>更多</span>
      <img src="@/assets/cockpit/more_deep_icon.png" alt="" />
    </div>
  </div>
</template>

<script>
import cockpitHeader from '../../components/cockpit-header.vue'
import { cockpitGetSubjectRatingList,cockpitGetZbRatingList } from "@/api/cockpit/cockpit"
import { px2vw,px2vh } from '../../utils/portcss';
export default {
  name:'MainbodyRating',
  components: {
    cockpitHeader
  },
  data() {
    return {
      activeSwap: false,
      listData: []
    }
  },
  created() {
    this.getListDataApi()
  },
  methods:{
    px2vw,
    px2vh,
    /**
     * 获取主体评级/中债隐含评级数据展示
     */
    async getListDataApi() {
      const data = await (!this.activeSwap ? cockpitGetSubjectRatingList() : cockpitGetZbRatingList())
      this.listData = data
    },
    /**
     * 切换主体评级/中债隐含评级
     */
    changeActiveSwap(){
      this.activeSwap = !this.activeSwap
      this.getListDataApi()
    },
    /**
     * 路径跳转
     */
    redirectToMenu(){
      this.$store.dispatch('tagsView/updateCurrentTab', {
        path: '/1352319892086145024',
        // 跳转需要刷新的话 refresh传true
        meta: {
          refresh: true,
          params: {}
        }
      })
    }
  },
}
</script>

<style lang="scss" scoped>
@import '~assets/styles/portcss.scss';
.mainbody-rating {
  width: 100%;
  height: vh(446);
  background-image: url('../../../../assets/cockpit/cockpit_pro_normal_bac.png');
  background-size: 100% 100%;
  padding: vh(8) vw(8);
  position: relative;
  &-title {
    display: flex;
    align-items: center;
    gap: vw(10);
    cursor: pointer;
    &>span {
      height: vh(21);
      font-size: vh(16);
      color: #FFFFFF;
      line-height: vh(21);
      text-shadow: 0px 2px 4px rgba(61,29,0,0.2);
      font-weight: 600;
    }
    &>img {
      width: vw(12);
      height: vh(13);
    }
  }
  &-table {
    width: 100%;
    height: vh(360);
    padding-left: vw(15);
    padding-right: vw(17);
    margin-top: vh(16);
    overflow-y: scroll;
    overflow-x: hidden;
    &-th {
      width: 100%;
      height: vh(32);
      display: flex;
      box-shadow: inset 0px -1px 0px 0px rgba(213,226,232,0.15);
      &>span {
        padding-left: vw(16);
        line-height: vh(32);
        font-size: vh(14);
        color: #E6F6FF;
        flex-shrink: 0;
        flex-grow: 0;
        font-weight: 600;
      }
    }
    &-tr {
      width: 100%;
      height: vh(36);
      display: flex;
      box-shadow: inset 0px -1px 0px 0px rgba(213,226,232,0.15);
      &>span {
        padding-left: vw(16);
        line-height: vh(36);
        font-size: vh(14);
        color: rgba(255,255,255,0.9);
        flex-shrink: 0;
        flex-grow: 0;
      }
    }
    & span:nth-of-type(1){
      width: vw(222);
    }
    & span:nth-of-type(2){
      width: vw(80);
    }
    & span:nth-of-type(3){
      width: vw(140);
    }
  }
  &-more {
    position: absolute;
    top: vh(18);
    right: vw(16);
    display: flex;
    align-items: center;
    gap: vw(8);
    cursor: pointer;
    & > span {
      height: vh(19);
      font-size: vh(14);
      color: #e6f6ff;
      line-height: vh(19);
    }
    & > img {
      width: vw(7);
      height: vh(11);
    }
  }
}
</style>