<template>
  <div class="inputSelectUnit">
    <jr-number-input v-model="issuePeriod" :placeholder="placeholder" />
    <el-select v-model="unit" placeholder="选择单位" style="width: 62px; flex-shrink: 0" class="unit">
      <el-option label="D" value="D" />
      <el-option label="Y" value="Y" />
    </el-select>
  </div>
</template>

<script>
export default {
  name: 'InputSelectUnit',
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    placeholder: {
      type: String,
      default: '请输入内容'
    }
  },
  data() {
    return {
      issuePeriod: '',
      unit: 'D' // 默认单位为天
    }
  },
  watch: {
    issuePeriod() {
      this.issuePeriodInput()
    },
    unit() {
      this.issuePeriodInput()
    }
  },
  methods: {
    issuePeriodInput() {
      if (typeof this.issuePeriod === 'number') {
        this.$emit('change', this.issuePeriod + this.unit)
      }
    },
    reset(){
      this.unit = 'D'
      this.issuePeriod = ''
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .jr-formatted-input.el-input--medium .el-input__inner {
  text-align: left;
  border: none;
}
::v-deep .unit .el-input--medium .el-input__inner {
  border: none;
  background: #f4f4f4;
}
.inputSelectUnit {
  width: 100%;
  display: flex;
  border: 1px solid #ccc;
}
</style>
