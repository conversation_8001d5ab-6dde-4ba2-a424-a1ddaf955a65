<template>
  <div>
    <el-timeline v-if="Array.isArray(datasourse) && datasourse.length > 0">
      <el-timeline-item
        v-for="(item, index) in datasourse"
        :key="index"
        :timestamp="item.date"
        placement="top"
        :color="item.isCurrent ? 'var(--theme--color)' : ''"
      >
        {{ item.statusName }}
      </el-timeline-item>
    </el-timeline>
    <div v-else>暂无数据</div>
  </div>
</template>

<script>
export default {
  props: {
    datasourse: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {}
  },
  created() {},

  methods: {}
}
</script>

<style lang="scss" scoped></style>
