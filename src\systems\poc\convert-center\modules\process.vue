<!--
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023-10-25 10:54:32
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2023-10-26 10:59:30
 * @Description: 转化进程
-->
<template>
  <article>
    <div class="header">
      <span class="title">转化进程</span>
      <div class="search-list">
        <label>日期：</label><el-date-picker v-model="rangeDate" type="daterange" />
        <label>组合：</label><jr-combobox v-model="indexType" :data="indexTypeList" />
        <label>转指令状态：</label><jr-combobox v-model="status" :data="statusList" />
      </div>
    </div>
    <div class="body">
      <jr-table :columns="columns" :data-source="dataList" :height="height" :cell-style="cellStyle" />
    </div>
  </article>
</template>

<script>
export default {
  data() {
    const platDate = JSON.parse(sessionStorage.getItem('platDate'))
    return {
      height: null,
      rangeDate: [platDate.substring(0, 8) + '01', platDate],
      indexType: '全部',
      indexTypeList: ['全部', '其他'].map(t => ({ text: t, value: t })),
      status: '全部',
      statusList: ['全部', '其他'].map(t => ({ text: t, value: t })),
      dataList: [
        { '序号': 1, '日期': '2022/8/16', '组合代码': 'CP001', '组合名称': '组合001', '操作类型': '新建', '转指令状态': '已全部转化', '指令状态': '有效', '操作': '指令明细' },
        { '序号': 2, '日期': '2022/8/16', '组合代码': 'CP002', '组合名称': '组合002', '操作类型': '新建', '转指令状态': '部分转化', '指令状态': '流程中', '操作': '指令明细' }
      ]
    }
  },
  computed: {
    columns() {
      return Object.keys(this.dataList[0]).map(item => ({
        prop: item,
        title: item
      })).concat({
        prop: 'id',
        title: '操作',
        align: 'center',
        width: 60,
        render: () => {
          const btns = [
            <el-tooltip content='指令明细'><jr-svg-icon icon-class='profile' /></el-tooltip>
          ]
          return <span class='table-action-box'>{btns}</span>
        }
      })
    }
  },
  mounted() {
    this.height = this.$el.querySelector('.body').clientHeight - 20
  },
  methods: {
    cellStyle({ column }) {
      if (column.columnKey === '转指令状态') {
        return { color: 'red' }
      }
      return {}
    }
  }
}
</script>

<style lang="scss" scoped>
.body {
  padding: 10px 0;
}
</style>
