<template>
  <div class="realTimePricing">
    <SerchForm />
    <PricingResults />
    <BondList v-bind="{...$attrs, ...$props}" />
  </div>
</template>

<script>
import PricingResults from './components/pricingResults.vue'
import SerchForm from './components/serchForm.vue'
import BondList from './components/bondList.vue'
export default {
  components: {
    PricingResults, SerchForm, BondList
  }
}
</script>

<style lang="scss">
.realTimePricing {
  height: 100%;
  width: 100%;
}
</style>
