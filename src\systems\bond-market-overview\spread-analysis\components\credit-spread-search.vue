<template>
  <div class="save-form">
    <div class="save-form-header">
      <jr-svg-icon icon-class="funds-box-fill" />
      <span class="save-form-header-title">配置{{ curvename }}</span>
      <el-tooltip :content="tiptext" placement="right" effect="dark" style="margin-top: 3px">
        <jr-svg-icon icon-class="info-circle" />
      </el-tooltip>
      <div :class="effective ? 'save-form-header-end' : 'save-form-header-end-inactive'" @click="changeEffective">
        <jr-svg-icon icon-class="check-circle" />
        <span>生效</span>
      </div>
    </div>
    <div class="save-form-contant">
      <el-form ref="updateForm" :model="form" label-width="90px">
        <jr-form-item-create
          :prop-path="''"
          :validate-rules="{}"
          :data="infoFormFields"
          :model="form"
          :column="2"
          :disabled="false"
        />
      </el-form>
      <durationIssuanceRangeTable v-if="false" />
    </div>
  </div>
</template>

<script>
import { getdictionarybystr, getDictOptionsApi } from '../get-dictionary'
import { queryAllBondType, regionLink } from '@/api/public/public'
import durationIssuanceRangeTable from './duration-issuance-range-table.vue'
import { issuanceQueryBondTypeList } from '@/api/issuance/issuance'

const DICTIONARYARRAY = [
  'isright',
  'isguarantor',
  'bondRatingCustomized',
  'implyRatingCustomized',
  'issueTermCustomized',
  'COMPPROPERTY'
]
export default {
  components: { durationIssuanceRangeTable },

  props: {
    tiptext: {
      type: String,
      default() {
        return ''
      }
    },
    curvename: {
      type: String,
      default() {
        return ''
      }
    },
    params: {
      type: Object,
      default() {
        return {}
      }
    },
    tabname: {
      type: String,
      default() {
        return 'CreditSpread'
      }
    },
    outEffective: {
      type: Boolean,
      default() {
        return true
      }
    }
  },
  data() {
    return {
      form: {},
      infoFormFields: [],
      dictionaryObject: {},
      areaList: [],
      bondTypeOptions: [],
      effective: true
    }
  },
  beforeDestroy() {
    this.form = {}
  },
  async created() {
    this.effective = this.outEffective
    this.infoFormFields = [
      {
        title: '债券类型',
        required: false,
        prop: 'bondType',
        type: 'cascader',
        disabled: false,
        trigger: 'change',
        props: {
          label: 'label',
          value: 'value'
        },
        uiProps: {
          showAllLevels: false
        },
        showCode: false,
        multiple: true,
        change: (e) => {
          this.form.bondTypeName = this.geCascadertName(this.bondTypeOptions, e).join(',')
          this.searchParamsChange()
        }
      },
      {
        title: '债项评级',
        required: false,
        prop: 'bondRating',
        type: 'select',
        disabled: false,
        options: [],
        optionValue: 'itemcode',
        optionLabel: 'cnname',
        showCode: false,
        multiple: true,
        change: (e) => {
          this.form.bondRatingName = this.getName(
            getdictionarybystr(this.dictionaryObject, 'bondRatingCustomized'),
            e,
            'itemcode',
            'cnname'
          )
          this.searchParamsChange()
        }
      },
      {
        title: '',
        customLabel: true,
        required: false,
        prop: 'implyRating',
        implyRatingprepend: () => {
          return (
            <div style='display:flex;align-items:center;gap:5px;'>
              <span>隐含评级</span>
              <el-tooltip content='中债隐含评级' placement='right' effect='dark' style='margin-top:2px'>
                <jr-svg-icon iconClass='info-circle' />
              </el-tooltip>
            </div>
          )
        },
        type: 'select',
        disabled: false,
        options: [],
        optionValue: 'itemcode',
        optionLabel: 'cnname',
        showCode: false,
        multiple: true,
        change: (e) => {
          this.form.implyRatingName = this.getName(
            getdictionarybystr(this.dictionaryObject, 'implyRatingCustomized'),
            e,
            'itemcode',
            'cnname'
          )
          this.searchParamsChange()
        }
      },
      {
        required: false,
        prop: 'termStr',
        type: 'select',
        disabled: false,
        options: [],
        optionValue: 'itemcode',
        optionLabel: 'cnname',
        showCode: false,
        multiple: true,
        termStrprepend: () => {
          return (
            <div style='display:flex;align-items:center;gap:5px;'>
              <span>发行期限</span>
              <el-tooltip placement='right' effect='dark' style='margin-top:2px'>
                <jr-svg-icon iconClass='info-circle' />
                <div slot='content'>
                  <durationIssuanceRangeTable />
                </div>
              </el-tooltip>
            </div>
          )
        },
        change: (e) => {
          this.form.termName = this.getName(
            getdictionarybystr(this.dictionaryObject, 'issueTermCustomized'),
            e,
            'itemcode',
            'cnname'
          )
          this.searchParamsChange()
        }
      },
      {
        title: '有无担保',
        required: false,
        prop: 'isguarantee',
        type: 'select',
        disabled: false,
        options: [],
        optionValue: 'itemcode',
        optionLabel: 'cnname',
        showCode: false,
        multiple: true,
        change: (e) => {
          this.form.isguaranteeName = this.getName(
            getdictionarybystr(this.dictionaryObject, 'isguarantor'),
            e,
            'itemcode',
            'cnname'
          )
          this.searchParamsChange()
        }
      },
      {
        title: '',
        customLabel: true,
        required: false,
        prop: 'isright',
        isrightprepend: () => {
          return (
            <div style='display:flex;align-items:center;gap:5px;'>
              <span>含权债</span>
              <el-tooltip
                content='此处含权债为含权条款类型为赎回、回售、延期三种条款的债券'
                placement='right'
                effect='dark'
                style='margin-top:2px'
              >
                <jr-svg-icon iconClass='info-circle' />
              </el-tooltip>
            </div>
          )
        },
        type: 'select',
        disabled: false,
        options: [],
        optionValue: 'itemcode',
        optionLabel: 'cnname',
        showCode: false,
        multiple: false,
        change: (e) => {
          this.form.isrightName = this.getName(
            getdictionarybystr(this.dictionaryObject, 'isright'),
            e,
            'itemcode',
            'cnname'
          )
          this.searchParamsChange()
        }
      },
      {
        title: '地区',
        required: false,
        prop: 'districtsub',
        type: 'cascader',
        disabled: false,
        options: [],
        multiple: true,
        props: {
          label: 'cname'
        },
        change: (e) => {
          this.form.district = this.getDistrict(e)
          this.form.areaName = this.getAreaNames(this.areaList, e).join(',')
          this.searchParamsChange()
        }
      }
    ]
    this.dictionaryObject = await getDictOptionsApi(DICTIONARYARRAY)
    await this.getArea()
    await this.getBondTypeOptionsApi()

    if (this.isObject(this.params)) {
      this.form = this.params
    }
    this.form.bondTypeAll = this.getCascaderAllSelected(this.bondTypeOptions)
    this.form.bondRatingAll = this.getAllSelected(
      getdictionarybystr(this.dictionaryObject, 'bondRatingCustomized'),
      'itemcode'
    )
    this.form.implyRatingAll = this.getAllSelected(
      getdictionarybystr(this.dictionaryObject, 'implyRatingCustomized'),
      'itemcode'
    )
    this.form.termStreAll = this.getAllSelected(
      getdictionarybystr(this.dictionaryObject, 'issueTermCustomized'),
      'itemcode'
    )

    this.infoFormFields = [
      {
        title: '债券类型',
        required: false,
        prop: 'bondType',
        type: 'cascader',
        disabled: false,
        options: this.bondTypeOptions,
        trigger: 'change',
        props: {
          label: 'label',
          value: 'value'
        },
        uiProps: {
          showAllLevels: false
        },
        showCode: false,
        multiple: true,
        change: (e) => {
          this.form.bondTypeName = this.geCascadertName(this.bondTypeOptions, e).join(',')
          this.searchParamsChange()
        }
      },
      {
        title: '债项评级',
        required: false,
        prop: 'bondRating',
        type: 'select',
        disabled: false,
        options: getdictionarybystr(this.dictionaryObject, 'bondRatingCustomized'),
        optionValue: 'itemcode',
        optionLabel: 'cnname',
        showCode: false,
        multiple: true,
        change: (e) => {
          this.form.bondRatingName = this.getName(
            getdictionarybystr(this.dictionaryObject, 'bondRatingCustomized'),
            e,
            'itemcode',
            'cnname'
          )
          this.searchParamsChange()
        }
      },
      {
        title: '',
        customLabel: true,
        required: false,
        prop: 'implyRating',
        implyRatingprepend: () => {
          return (
            <div style='display:flex;align-items:center;gap:5px;'>
              <span>隐含评级</span>
              <el-tooltip content='中债隐含评级' placement='right' effect='dark' style='margin-top:2px'>
                <jr-svg-icon iconClass='info-circle' />
              </el-tooltip>
            </div>
          )
        },
        type: 'select',
        disabled: false,
        options: getdictionarybystr(this.dictionaryObject, 'implyRatingCustomized'),
        optionValue: 'itemcode',
        optionLabel: 'cnname',
        showCode: false,
        multiple: true,
        change: (e) => {
          this.form.implyRatingName = this.getName(
            getdictionarybystr(this.dictionaryObject, 'implyRatingCustomized'),
            e,
            'itemcode',
            'cnname'
          )
          this.searchParamsChange()
        }
      },
      {
        required: false,
        prop: 'termStr',
        type: 'select',
        disabled: false,
        options: getdictionarybystr(this.dictionaryObject, 'issueTermCustomized'),
        optionValue: 'itemcode',
        optionLabel: 'cnname',
        showCode: false,
        multiple: true,
        termStrprepend: () => {
          return (
            <div style='display:flex;align-items:center;gap:5px;'>
              <span>发行期限</span>
              <el-tooltip placement='right' effect='dark' style='margin-top:2px'>
                <jr-svg-icon iconClass='info-circle' />
                <div slot='content'>
                  <durationIssuanceRangeTable />
                </div>
              </el-tooltip>
            </div>
          )
        },
        change: (e) => {
          this.form.termName = this.getName(
            getdictionarybystr(this.dictionaryObject, 'issueTermCustomized'),
            e,
            'itemcode',
            'cnname'
          )
          this.searchParamsChange()
        }
      },
      {
        title: '有无担保',
        required: false,
        prop: 'isguarantee',
        type: 'select',
        disabled: false,
        options: getdictionarybystr(this.dictionaryObject, 'isguarantor'),
        optionValue: 'itemcode',
        optionLabel: 'cnname',
        showCode: false,
        multiple: true,
        change: (e) => {
          this.form.isguaranteeName = this.getName(
            getdictionarybystr(this.dictionaryObject, 'isguarantor'),
            e,
            'itemcode',
            'cnname'
          )
          this.searchParamsChange()
        }
      },
      {
        title: '',
        customLabel: true,
        required: false,
        prop: 'isright',
        isrightprepend: () => {
          return (
            <div style='display:flex;align-items:center;gap:5px;'>
              <span>含权债</span>
              <el-tooltip
                content='此处含权债为含权条款类型为赎回、回售、延期三种条款的债券'
                placement='right'
                effect='dark'
                style='margin-top:2px'
              >
                <jr-svg-icon iconClass='info-circle' />
              </el-tooltip>
            </div>
          )
        },
        type: 'select',
        disabled: false,
        options: getdictionarybystr(this.dictionaryObject, 'isright'),
        optionValue: 'itemcode',
        optionLabel: 'cnname',
        showCode: false,
        multiple: false,
        change: (e) => {
          this.form.isrightName = this.getName(
            getdictionarybystr(this.dictionaryObject, 'isright'),
            e,
            'itemcode',
            'cnname'
          )
          this.searchParamsChange()
        }
      },
      {
        title: '地区',
        required: false,
        prop: 'districtsub',
        type: 'cascader',
        disabled: false,
        options: this.areaList,
        multiple: true,
        props: {
          label: 'cname'
        },
        change: (e) => {
          this.form.district = this.getDistrict(e)
          this.form.areaName = this.getAreaNames(this.areaList, e).join(',')
          this.searchParamsChange()
        }
      }
    ]

    // this.searchParamsChange()
  },
  methods: {
    getCascaderAllSelected(options) {
      if (!Array.isArray(options) || (Array.isArray(options) && options.length === 0)) return ''

      const selectedValues = []

      function pushSelectedValues(options) {
        if (Object.hasOwnProperty.call(options, 'children')) {
          if (Array.isArray(options.children) && options.children.length > 0) {
            for (let index = 0; index < options.children.length; index++) {
              const element = options.children[index]
              pushSelectedValues(element)
            }
          }
        } else {
          selectedValues.push(options.value)
        }
      }

      for (let index = 0; index < options.length; index++) {
        const element = options[index]
        pushSelectedValues(element)
      }

      return selectedValues.join(',')
    },
    async getBondTypeOptionsApi() {
      const data = await issuanceQueryBondTypeList()
      this.bondTypeOptions = data.reduce((pre, current) => {
        if (current) {
          const index = pre.findIndex((item) => item.value === current.bondTypeCode)
          if (index === -1) {
            pre.push({
              label: current.bondTypeName,
              value: current.bondTypeCode,
              children: []
            })
          } else {
            pre[index].children.push({
              label: current.bondTypeName2,
              value: current.bondTypeCode2
            })
          }
        }
        return pre
      }, [])
    },
    isObject(value) {
      return Object.prototype.toString.call(value) === '[object Object]'
    },
    getAllSelected(options, valueStr) {
      if (Array.isArray(options) && options.length === 0) return ''
      return options.map((option) => option[valueStr]).join(',')
    },
    getName(list, select, valueStr = 'itemcode', labelStr = 'cnname') {
      const arr = []
      if (Array.isArray(select)) {
        for (let index = 0; index < list.length; index++) {
          const element = list[index]
          if (select.includes(element[valueStr])) {
            arr.push(element[labelStr])
          }
        }
      } else {
        for (let index = 0; index < list.length; index++) {
          const element = list[index]
          if (select === element[valueStr]) {
            arr.push(element[labelStr])
          }
        }
      }

      return arr.join(',')
    },
    getDistrict(districtsub) {
      const district = []
      if (Array.isArray(districtsub) && districtsub.length > 0) {
        for (let index = 0; index < districtsub.length; index++) {
          const element = districtsub[index]
          if (Array.isArray(element) && element[2]) {
            district.push(element[2])
          }
        }
      }
      return district
    },
    getAreaNames(areaTrees, selectedCodes) {
      if (Array.isArray(selectedCodes) && selectedCodes.length > 0) {
        // 创建省份查找表 {id: province}
        const provinceMap = {}
        areaTrees.forEach((province) => {
          provinceMap[province.id] = province
        })

        return selectedCodes.map((codes) => {
          const [provinceCode, cityCode, districtCode] = codes

          // 查找省份
          const province = provinceMap[provinceCode]
          const provinceName = province?.cname || '未知省份'

          // 查找城市
          let cityName = '未知城市'
          let districtName = '未知区县'

          if (province && province.children) {
            const city = province.children.find((c) => c.id === cityCode)
            if (city) {
              cityName = city.cname || '未知城市'

              // 查找区县
              if (city.children) {
                const district = city.children.find((d) => d.id === districtCode)
                districtName = district?.cname || '未知区县'
              }
            }
          }

          return `${provinceName}/${cityName}/${districtName}`
        })
      } else {
        return []
      }
    },
    changeEffective() {
      this.effective = !this.effective
      this.searchParamsChange()
    },
    searchParamsChange() {
      if (this.tabname === 'IssueSpread') {
        this.$emit('params-change', this.form, this.effective)
      }
    },
    getChildrenForm() {
      return this.form
    },
    async getArea() {
      const res = await regionLink() // 企业id 暂时为空
      this.areaList = this.formatAreaList(res)
    },

    // 格式化省市区列表
    formatAreaList(areaList) {
      let arr = []
      arr = JSON.parse(JSON.stringify(areaList))

      const idMapping = arr.reduce((acc, el, i) => {
        acc[el.id] = i
        return acc
      }, {})

      const root = []
      arr.forEach((el) => {
        // 判断根节点
        if (!el.pid) {
          root.push(el)
          return
        }
        // 用映射表找到父元素
        const parentEl = arr[idMapping[el.pid]]
        if (parentEl) {
          // 把当前元素添加到父元素的`children`数组中
          parentEl.children = [...(parentEl.children || []), el]
        }
      })
      return root
    }
  }
}
</script>

<style lang="scss" scoped>
@import '../css/custom-save-search-form-temporary.scss';
</style>
