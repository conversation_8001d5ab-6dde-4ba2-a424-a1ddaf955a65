<template>
  <div class="quota-manage">
    <div :style="{ width: px2vw(188), height: px2vh(188), margin: '0 auto' }">
      <div id="chart" style="width: 100%; height: 100%;min-width: 130px;min-height: 130px;"></div>
    </div>
    <div class="quota-manage-table">
      <div
        v-for="(data, index) in tableData"
        :key="index"
        class="quota-manage-table-single"
        :class="index > 0 ? 'quota-manage-table-line' : ''"
      >
        <span>
          <span :style="{ backgroundColor: data.color }" />
        </span>
        <span 
          :style="{ color: index > 0 ? 'rgba(230,246,255,0.6)' : '' }"
          :title="data.typename"
        >{{ data.typename }}</span>
        <span :title="data.remainingamount | amountFormat">{{ data.remainingamount | amountFormat }}</span>
        <span :title="data.count">{{ data.count }}</span>
      </div>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import { cockpitGetGroupRegisterQuotaList } from '@/api/cockpit/cockpit'
import { ConvertAmount } from '@jupiterweb/utils/common.js'
import { px2vw, px2vh } from '../../utils/portcss'
export default {
  name: 'QuotaManage',
  data() {
    return {
      options: {},
      originData: [],
      tableData: [],
      angle: 0,
      animationId: null,
      myChart: null
    }
  },
  filters: {
    amountFormat(value){
      if(typeof value === 'number'){
        return ConvertAmount('HMU', value * 1, 1, 4)
      }
      return value
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.myChart = echarts.init(document.querySelector('#chart'))
      this.getQuotaManageDataApi()
    })
  },
  methods: {
    px2vw,
    px2vh,
    /**
     * 获取额度查询数据
     */
    async getQuotaManageDataApi() {
      const data = await cockpitGetGroupRegisterQuotaList()
      if(!Object.keys(data).length){
        data = []
      }
      const colorArr = ['#5B8FF9', '#F2BA15']
      this.tableData = data.map((item, index) => {
        return {
          ...item,
          color: colorArr[index],
          count: item.count + '张'
        }
      })
      this.tableData.unshift({
        color: 'transparent',
        typename: '债券类型',
        remainingamount: '有效剩余额度(亿)',
        count: '有效注册通知书'
      })
      this.originData = data
      this.initChart(data)
    },
    /**
     * 生成图表
     */
    initChart(data) {
      let sum = data.reduce((pre, current) => {
        pre += current.remainingamount * 10000
        return pre
      }, 0)
      sum = sum / 10000
      const firstPercent = (data[0]?.remainingamount || 0) / sum
      const secondPercent = (data[1]?.remainingamount || 0) / sum
      this.options = {
        title: {
          text: '{a|' + ConvertAmount('HMU', sum * 1, 1, 4)  + '}\n{c|' + '总剩余额度(亿)' + '}',
          x: 'center',
          y: 'center',
          textStyle: {
            rich: {
              a: {
                fontSize: window.innerWidth < 1400 ? 14 : 18,
                color: '#fff',
                fontWeight: '600'
              },
              c: {
                fontSize: window.innerWidth < 1400 ? 8 : 12,
                color: '#E6F6FF',
                padding: [4, 0, 0, 0]
              }
            }
          }
        },
        legend: {
          show: false
        },
        toolbox: {
          show: false
        },
        series: [
          ...this.getSeriesData(),
          {
            name: '吃猪肉频率',
            type: 'pie',
            radius: ['80%', '68%'],
            silent: true,
            clockwise: true,
            startAngle: 0,
            z: 0,
            zlevel: 0,
            label: {
              normal: {
                position: 'center'
              }
            },
            data: [
              {
                value: firstPercent,
                name: '',
                itemStyle: {
                  normal: {
                    color: '#5B8FF9'
                  }
                }
              },
              {
                value: secondPercent,
                name: '',
                label: {
                  normal: {
                    show: false
                  }
                },
                itemStyle: {
                  normal: {
                    color: '#F2BA15'
                  }
                }
              }
            ]
          },
        ]
      }
      this.myChart.setOption(this.options)
      setTimeout(() => {
        this.drawEchart()
      }, 1000)
    },
    /**
     * 获取图表series数据
     */
    getSeriesData() {
      const self = this
      return [
        // 紫色
        {
          name: 'ring5',
          type: 'custom',
          coordinateSystem: 'none',
          renderItem: function (params, api) {
            return {
              type: 'arc',
              shape: {
                cx: api.getWidth() / 2,
                cy: api.getHeight() / 2,
                r: (Math.min(api.getWidth(), api.getHeight()) / 2) * 0.9,
                startAngle: ((0 + self.angle) * Math.PI) / 180,
                endAngle: ((90 + self.angle) * Math.PI) / 180
              },
              style: {
                stroke: '#315FA0',
                fill: 'transparent',
                lineWidth: 6
              },
              silent: true
            }
          },
          data: [0]
        },
        {
          name: 'ring5',
          type: 'custom',
          coordinateSystem: 'none',
          renderItem: function (params, api) {
            return {
              type: 'arc',
              shape: {
                cx: api.getWidth() / 2,
                cy: api.getHeight() / 2,
                r: (Math.min(api.getWidth(), api.getHeight()) / 2) * 0.9,
                startAngle: ((90 + self.angle) * Math.PI) / 180,
                endAngle: ((360 + self.angle) * Math.PI) / 180
              },
              style: {
                stroke: '#315FA0',
                fill: 'transparent',
                lineWidth: 2
              },
              silent: true
            }
          },
          data: [0]
        },
        {
          name: 'ring5',
          type: 'custom',
          coordinateSystem: 'none',
          renderItem: function (params, api) {
            return {
              type: 'arc',
              shape: {
                cx: api.getWidth() / 2,
                cy: api.getHeight() / 2,
                r: (Math.min(api.getWidth(), api.getHeight()) / 2) * 0.6,
                startAngle: ((180 + self.angle) * Math.PI) / 180,
                endAngle: ((270 + self.angle) * Math.PI) / 180
              },
              style: {
                stroke: '#315FA0',
                fill: 'transparent',
                lineWidth: 6
              },
              silent: true
            }
          },
          data: [0]
        },
        {
          name: 'ring5',
          type: 'custom',
          coordinateSystem: 'none',
          renderItem: function (params, api) {
            return {
              type: 'arc',
              shape: {
                cx: api.getWidth() / 2,
                cy: api.getHeight() / 2,
                r: (Math.min(api.getWidth(), api.getHeight()) / 2) * 0.6,
                startAngle: ((270 + self.angle) * Math.PI) / 180,
                endAngle: ((180 + self.angle) * Math.PI) / 180
              },
              style: {
                stroke: '#315FA0',
                fill: 'transparent',
                lineWidth: 2
              },
              silent: true
            }
          },
          data: [0]
        }
      ]
    },
    /**
     * 重绘图表数据，做出动态效果
     */
    drawEchart() {
      if (this.animationId) {
        cancelAnimationFrame(this.animationId)
      }
      this.angleAnimation()
    },
    /**
     * 绘制图表的动画
     */
    angleAnimation() {
      if (this.angle >= 360) {
        this.angle = 0
      }
      this.angle = this.angle + 0.5
      this.myChart.setOption(this.options, true)
      this.animationId = requestAnimationFrame(this.angleAnimation)
    }
  },
  beforeDestroy() {
    if (this.animationId) {
      cancelAnimationFrame(this.animationId)
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~assets/styles/portcss.scss';
.quota-manage {
  width: 100%;
  height: 100%;
  margin-top: vh(19);
  padding-left: vw(26);
  padding-right: vw(24);
  &-table {
    width: 100%;
    height: vh(136);
    margin-top: vh(25);
    &-single {
      width: 100%;
      height: vh(40);
      display: flex;
      justify-content: space-between;
      margin-bottom: vh(8);
      color: #ffffff;
      & > span:nth-of-type(1) {
        width: 12%;
        height: vh(40);
        display: flex;
        align-items: center;
        justify-content: center;
        span {
          width: 8px;
          height: 8px;
        }
      }
      & > span:nth-of-type(2) {
        width: 20%;
        height: vh(40);
      }
      & > span:nth-of-type(3) {
        width: 34%;
        height: vh(40);
      }
      & > span:nth-of-type(4) {
        width: 31%;
        height: vh(40);
      }
      & > span {
        text-align: center;
        line-height: vh(40);
        flex-shrink: 0;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
    &-line {
      background: rgba(52, 138, 255, 0.2);
      border-radius: 4px;
    }
  }
}
</style>
