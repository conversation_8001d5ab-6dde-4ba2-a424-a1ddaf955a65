<!--
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023-10-25 10:54:32
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2023-10-25 17:35:29
 * @Description: 资产配置规模变化趋势
-->
<template>
  <article>
    <div class="header">
      <span class="title">资产配置规模变化趋势</span>
      <el-radio-group v-model="combo" size="mini">
        <el-radio-button v-for="com in comboxList" :key="com" :label="com" />
      </el-radio-group>
    </div>
    <section class="body">
      <echarts :options="chartOptions" />
    </section>
  </article>
</template>

<script>
import echarts from '@jupiterweb/components/echarts'
export default {
  components: { echarts },
  data() {
    return {
      combo: '图',
      comboxList: ['图', '表'],
      chartOptions: {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            lineStyle: {
              color: '#57617B'
            }
          }
        },
        legend: {
          icon: 'rect',
          itemWidth: 14,
          itemHeight: 5,
          itemGap: 13,
          data: ['移动'],
          right: '4%',
          textStyle: {
            fontSize: 12,
            color: '#F1F1F3'
          }
        },
        grid: {
          top: 30,
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: [{
          type: 'category',
          axisTick: {
            show: false
          },
          data: []
        }],
        yAxis: [{
          type: 'value'
        }],
        series: [{
          name: '资产配置规模变化趋势',
          type: 'line',
          smooth: true,
          symbol: 'circle',
          symbolSize: 5,
          showSymbol: true,
          lineStyle: {
            normal: {
              width: 1
            }
          },
          areaStyle: {
            normal: {
              color: new echarts.echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                offset: 0,
                color: 'rgba(237, 189, 27, 0.5)'
              }, {
                offset: 1,
                color: 'rgba(237, 189, 27, 0)'
              }], false),
              shadowColor: 'rgba(0, 0, 0, 0.1)',
              shadowBlur: 20
            }
          },
          itemStyle: {
            normal: {
              color: 'rgb(237,189,27)',
              borderColor: 'rgba(237,189,2,0.27)',
              borderWidth: 12

            }
          },
          data: [900, 1802, 891, 1034, 950, 1020, 1110, 1125, 1415, 1212, 165, 522]
        }]
      }
    }
  }
}
</script>

<style lang="scss" scoped></style>
