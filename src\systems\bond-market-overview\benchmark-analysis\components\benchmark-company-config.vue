<!-- 对标企业配置 -->
<template>
  <div class="benchmark-analysis benchmark-company-config">
    <div class="public-title-container">
      <span class="subtitle-label">对标企业配置</span>
    </div>
    <div class="search-form">
      <el-form :model="searchForm" inline label-width="68px">
        <jr-form-item label="企业名称">
          <jr-combobox
            v-model="searchForm.bmEntId"
            style="width:400px;"
            filterable
            clearable
            remote
            reserve-keyword
            placeholder="请输入内容"
            :remote-method="queryBenchmarkCompanyList"
            :loading="loading"
            :data="compList"
            option-value="bmEntId"
            option-label="bmEntName"
          />
        </jr-form-item>
        <ws-button
          type="primary"
          icon="el-icon-plus"
          :disabled="!searchForm.bmEntId"
          @click="addCompToTag"
        >
          添加
        </ws-button>
        <div class="comp-tags">
          <el-tag
            v-for="tag in compTags"
            :key="tag.bmEntId"
            class="comp-tags-item"
            closable
            :disable-transitions="false"
            @close="deleteCompTag(tag)"
          >
            {{ tag.bmEntName }}
          </el-tag>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script>
import * as API from '@/api/benchmark-analysis/benchmark-analysis.js'
import wsButton from '@/components/ws-button'

export default {
  components: {
    wsButton
  },
  provide() {
    return { parant: this }
  },
  props: {
    // 权限相关
    permitdetail: {
      type: Object,
      default: () => ({})
    },
    menuinfo: {
      type: Object,
      default: () => ({})
    },
    params: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      modalConfig: {
        isVisible: false,
        itemData: {}
      },
      searchForm: {
        bmEntId: ''
      },
      compList: [],
      compMap: new Map(),
      compTags: [],
      loading: false
    }
  },
  created() {
    // 查询获取已配置的对标企业
    this.queryBenchmarkCompanyConfigList()
    // 查询获取已配置的对标企业下拉选择信息
    // this.queryBenchmarkCompanySelectList()
  },
  methods: {
    setVisible() {
      this.modalConfig.isVisible = false
    },
    /**
     * 查询获取已配置的对标企业
     */
    queryBenchmarkCompanyConfigList() {
      const { entId, entOrgCode, groupId } = this.params
      this.loading = true
      API.getBenchmarkCompanyConfigList({
        entId, entOrgCode, groupId
      }).then(data => {
        this.loading = false
        this.compTags = Array.isArray(data) ? data : []
      }).catch(() => {
        this.loading = false
        this.compTags = []
      })
    },
    // /**
    //  * 查询获取已配置的对标企业下拉选择信息
    //  */
    // queryBenchmarkCompanySelectList() {
    //   const { entId, entOrgCode, groupId } = this.params
    //   this.loading = true
    //   API.getBenchmarkCompanySelectList({
    //     entId, entOrgCode, groupId
    //   }).then(data => {
    //     this.loading = false
    //     this.compSelectList = Array.isArray(data) ? data : []
    //   }).catch(() => {
    //     this.loading = false
    //     this.compSelectList = []
    //   })
    // },
    /**
     * 查询获取对标企业下拉框信息
     * @param {String} value 搜索的企业名称
     */
    queryBenchmarkCompanyList(value) {
      if (value) {
        this.loading = true
        const { entId } = this.params
        API.getBenchmarkCompanyList({
          entId, compName: value
        }).then(data => {
          this.loading = false
          this.compList = Array.isArray(data) ? data : []
          this.compMap = new Map(this.compList.map(item => [item.bmEntId, item]))
        }).catch(() => {
          this.loading = false
          this.compList = []
        })
      }
    },
    /**
     * 添加企业名称到Tag标签中显示
     */
    addCompToTag() {
      // 获取选中的企业ID
      const { bmEntId } = this.searchForm || {}

      // 超过5家不允许添加
      if (Array.isArray(this.compTags) && this.compTags.length >= 5) {
        this.$message.warning('对标企业最多选择5家')
        return false
      }

      // 不允许添加重复的企业
      if (Array.isArray(this.compTags)) {
        const index = this.compTags.findIndex(item => item.bmEntId === bmEntId)
        if (index > 0) {
          this.$message.warning('该企业已添加，不允许重复添加')
          return false
        }
      }

      // 处理参数并保存
      if (this.compMap && this.compMap.size > 0 && bmEntId) {
        const addedCompObj = this.compMap.get(bmEntId) || {}
        const { entId, entName, entOrgCode } = this.params
        const { bmEntName, bmEntOrgCode, bmCompSname } = addedCompObj
        this.compTags.push({
          entId, entName, entOrgCode, bmEntId, bmEntName, bmEntOrgCode, bmCompSname
        })
        // 保存添加的对标企业
        this.saveBenchmarkCompanyConfig()
      }
    },
    /**
     * 删除已配置的对标企业
     * @param {Object} tag 需删除的标签
     */
    deleteCompTag(tag) {
      this.$confirm('是否确认删除？')
        .then(() => {
          if (Array.isArray(this.compTags) && Object.prototype.toString.call(tag) === '[object Object]') {
            const index = this.compTags.findIndex(item => item.bmEntId === tag.bmEntId)
            index > -1 && this.compTags.splice(index, 1)
            // 保存添加的对标企业
            this.saveBenchmarkCompanyConfig()
          }
        })
    },
    /**
     * 保存对标企业配置信息
     */
    saveBenchmarkCompanyConfig() {
      const { entId, entOrgCode, groupId } = this.params
      this.loading = true
      API.updateBenchmarkingConfig({
        entId, entOrgCode, groupId, list: [...this.compTags]
      }, (isSuccess) => {
        this.loading = false
        if (isSuccess) {
          // 查询获取已配置的对标企业下拉选择信息
          this.$emit('saveConfingCallbackMethod')
          this.searchForm.bmEntId = ''
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.benchmark-company-config {
    padding: 0px 16px;
    background: #fff;
    .el-table {
        height: 300px !important;
    }

    .formItem {
        display: inline-block;
        width: 300px
    }
    .btn {
        display: inline-block;
        margin-left: 10px;
    }
    .content {
        display: flex;
        .leftContent {
            flex: 1;
        }
        .rightContent {
            flex: 1;
        }
    }
}
</style>

