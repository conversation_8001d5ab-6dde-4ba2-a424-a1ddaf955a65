<template>
  <div>
    <collapse-panel title="基本信息">
      <div class="collapseBody">
        <jr-form-item-create :validate-rules="validateRules" :data="baseFileds" :disabled="disabled" :model="data" />
      </div>
    </collapse-panel>
  </div>
</template>

<script>
import { getModalProps, getModalComputed } from '@/systems/mixins'
import moment from 'moment'
export default {
  mixins: [getModalProps, getModalComputed],
  props: {
    itemData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      baseFileds: []
    }
  },
  created() {
    this.itemData && this.$emit('setFieldsValue', {
      ...this.itemData,
      qxr: this.itemData.qxr ? moment(this.itemData.qxr).format('YYYY-MM-DD') : '',
      dqr: this.itemData.dqr ? moment(this.itemData.dqr).format('YYYY-MM-DD') : ''
    })

    this.initData()
  },
  methods: {

    initData() {
      this.baseFileds = [
        {
          title: '企业名称',
          prop: 'qymc',
          required: true,
          expr: {
            disabled: '!!this.isModify'
          }
        },
        {
          title: '融资类别',
          prop: 'rzlb_zqjc',
          type: 'select',
          required: true,
          options: [],
          expr: {
            disabled: '!!this.isModify'
          }
        },
        {
          title: '借款银行',
          prop: 'jkyh_zcxs',
          type: 'select',
          required: true,
          options: [],
          expr: {
            disabled: '!!this.isModify'
          }
        },
        {
          title: '利率(%)',
          prop: 'll',
          type: 'amount',
          required: true,
          expr: {
            disabled: '!!this.isModify'
          }
        },
        {
          title: '本金(元)',
          prop: 'bj',
          type: 'amount',
          required: true,
          expr: {
            disabled: '!!this.isModify'
          }
        },
        {
          title: '利息(元)',
          prop: 'lx',
          type: 'amount'
        },
        {
          title: '已使用总计(元)',
          prop: 'ysyzj',
          type: 'amount',
          expr: {
            disabled: '!!this.isModify'
          }
        },
        {
          title: '已使用本金(元)',
          prop: 'ysybj',
          type: 'amount'
        },
        {
          title: '已使用利息(元)',
          prop: 'ysylx',
          type: 'amount'
        },
        {
          title: '未使用利息(元)',
          prop: 'wsylx',
          type: 'amount',
          expr: {
            disabled: '!!this.isModify'
          }
        },
        {
          title: '未使用本金(元)',
          prop: 'wsybj',
          type: 'amount',
          expr: {
            disabled: '!!this.isModify'
          }
        },
        {
          title: '起息日期',
          prop: 'qxr',
          type: 'date',
          expr: {
            disabled: '!!this.isModify'
          }
        },
        {
          title: '到期日期',
          prop: 'dqr',
          type: 'date',
          expr: {
            disabled: '!!this.isModify'
          }
        }
      ]
    },
    async preSaveHandel() {
      return true
    }

  }
}
</script>

<style lang="scss" scoped>
</style>
