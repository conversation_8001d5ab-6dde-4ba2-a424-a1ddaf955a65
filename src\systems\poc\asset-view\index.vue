<!--
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023-10-24 17:11:49
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2023-11-06 15:20:46
 * @Description: 资产看板
-->
<template>
  <div class="poc-asset-view">
    <div class="top">
      <PocView />
      <PocConfig />
      <PocTrend />
    </div>
    <div class="bottom">
      <PocTop20 />
      <PocMonitor />
    </div>
  </div>
</template>

<script>
import PocView from './modules/view'
import PocConfig from './modules/config'
import PocMonitor from './modules/monitor'
import PocTrend from './modules/trend'
import PocTop20 from './modules/top20'
export default {
  components: {
    PocView,
    PocConfig,
    PocMonitor,
    PocTrend,
    PocTop20
  }
}
</script>

<style lang="scss">
.poc-asset-view {
  height: 100%;
  overflow: auto;
  display: flex;
  width: 100%;
  flex-direction: column;
  article {
    padding: 0 16px;
  }
  .header {
    border-bottom: 1px solid #E6E6E6;
    height: 30px;
    display: flex;
    justify-content: space-between;
    .el-radio-button--mini {
      margin-top: 3px;
      .el-radio-button__inner {
        padding: 4px 10px;
      }
    }
    .search-list {
      padding-top: 3px;
      .jr-combobox {
        width: 120px;
        .el-input__inner {
          height: 22px!important;
        }
      }
      label {
        margin-left: 6px;
      }
      .jr-svg-icon {
        margin-left: 6px;
        cursor: pointer;
      }
    }
    .title {
      line-height: 29px;
      display: inline-block;
      font-size: 14px;
      color: #333;
      border-top: 1px solid var(--theme--color);
    }
  }
  .body {
    height: calc(100% - 30px);
  }
  .top {
    height: 300px;
    display: flex;
    & > article {
      width: calc(33.3% - 5px);
      background: #fff;
      margin-right: 8px;
      &:last-child {
        margin-right: 0;
      }
    }
  }
  .bottom {
    height: calc(100% - 308px);
    min-height: 300px;
    margin-top: 8px;
    display: flex;
    & > article {
      width: calc(50% - 4px);
      background: #fff;
      margin-right: 8px;
      &:last-child {
        margin-right: 0;
      }
    }
  }
}
</style>
