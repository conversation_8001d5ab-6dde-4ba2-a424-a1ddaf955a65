<!--
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023-10-25 10:54:32
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2023-10-26 10:52:01
 * @Description: 已转组合表现
-->
<template>
  <article>
    <div class="header">
      <span class="title">已转组合表现</span>
      <div class="search-list">
        <label>组合：</label><jr-combobox v-model="indexType" :data="indexTypeList" />
        <label>指标：</label><jr-combobox v-model="status" :data="statusList" />
        <label>日期：</label><el-date-picker v-model="rangeDate" type="daterange" />
        <el-radio-group v-model="combo" size="mini">
          <el-radio-button v-for="com in comboxList" :key="com" :label="com" />
        </el-radio-group>
      </div>
    </div>
    <section class="body">
      <echarts :options="chartOptions" />
    </section>
  </article>
</template>

<script>
import echarts from '@jupiterweb/components/echarts'
export default {
  components: { echarts },
  data() {
    const platDate = JSON.parse(sessionStorage.getItem('platDate'))
    const getDateList = () => {
      const count = Number(platDate.substring(8, 10))
      let i = 1
      const ret = []
      while (i <= count) {
        ret.push(platDate.substring(0, 8) + i.toString().padStart(2, '0'))
        i++
      }
      return ret
    }
    return {
      height: null,
      rangeDate: [platDate.substring(0, 8) + '01', platDate],
      indexType: '组合001',
      indexTypeList: ['组合001', '组合004'].map(t => ({ text: t, value: t })),
      status: '单位净值',
      statusList: ['单位净值', '其他'].map(t => ({ text: t, value: t })),
      combo: '图',
      comboxList: ['图', '表'],
      chartOptions: {

        tooltip: {
          trigger: 'axis',
          axisPointer: { type: 'cross' }
        },
        grid: {
          right: 80,
          left: 80,
          top: 30
        },
        legend: {
          bottom: 10,
          data: ['单位净值', '净值涨跌幅']
        },
        xAxis: [
          {
            type: 'category',
            axisTick: {
              alignWithLabel: true
            },
            data: getDateList()
          }
        ],
        yAxis: [
          {
            type: 'value',
            name: '',
            min: 1.1004,
            max: 1.1024,
            position: 'left',
            axisLabel: {
              formatter: '{value} '
            }
          },
          {
            type: 'value',
            name: '',
            min: -0.0500,
            max: 0.0500,
            position: 'right',
            axisLabel: {
              formatter: '{value} %'
            }
          }
        ],
        series: [
          {
            name: '单位净值',
            type: 'bar',
            barWidth: 20,
            data: [
              1.1011,
              1.1012,
              1.1016,
              1.1021,
              1.1023,
              1.1023,
              1.1023,
              1.1019,
              1.1018,
              1.101881,
              1.101855,
              1.101812,
              1.101812,
              1.101812,
              1.101671,
              1.101805
            ]
          },
          {
            name: '净值涨跌幅',
            type: 'line',
            yAxisIndex: 1,
            data: [
              -0.0182,
              0.0091,
              0.0363,
              0.0454,
              0.0181,
              0.0000,
              0.0000,
              -0.0363,
              -0.0091,
              0.0074,
              -0.0024,
              -0.0039,
              0.0000,
              0.0000,
              -0.0128,
              0.0122
            ]
          }
        ]
      }
    }
  }
}
</script>

<style lang="scss" scoped></style>
