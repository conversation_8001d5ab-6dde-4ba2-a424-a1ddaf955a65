<template>
  <div class="issue-spread">
    <div class="issue-spread-container">
      <div class="issue-spread-container-left">
        <div class="public-radio-group">
          <el-radio-group v-model="headerBtns" @input="changeType">
            <el-radio-button label="mtn">标准利率曲线</el-radio-button>
            <el-radio-button label="lgfv">自定义利率曲线</el-radio-button>
          </el-radio-group>
        </div>
        <el-form
          inline
          :model="form"
          label-width="80"
          style="display: flex; align-items: center; margin-top: 8px; height: 32px"
        >
          <jr-form-item label="日期区间">
            <el-date-picker
              v-model="form.customDateRange"
              style="width: 346px; height: 32px"
              type="daterange"
              unlink-panels
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="yyyy-MM-dd"
              class="date-picker"
              @change="handleCustomDateRangeChange"
            />
          </jr-form-item>
        </el-form>

        <div v-if="headerBtns === 'mtn'" class="issue-spread-container-left-search">
          <CustomSearchFormItem
            v-for="item in standardSettings.multipleSelectList"
            :key="item.label"
            :selectlist="item.selectlist"
            :checked.sync="item.checked"
            :label="item.label"
            :subtext="item.subtext"
            :showtooltip="item.showtooltip"
            :tiptext="item.tiptext"
            :value.sync="item.value"
            :iconcolor="item.iconColor"
            @change="getChartData"
          />

          <customSingleSearchForm
            v-for="(item, index) in standardSettings.changeList1"
            :key="item.label + index"
            :selectlist="item.selectlist"
            :checked.sync="item.checked"
            :label="item.label"
            :subtext="item.subtext"
            :showtooltip="item.showtooltip"
            :tiptext="item.tiptext"
            :value.sync="item.value"
            :otherselectlist="item.otherselectlist"
            :othervalue.sync="item.othervalue"
            :index="index"
            liststr="changeList1"
            :iconcolor="item.iconColor"
            @add="addSearchItem"
            @delete="deleteSearchItem"
            @change="getChartData"
          />

          <customSingleSearchForm
            v-for="(item, index) in standardSettings.changeList2"
            :key="item.label + index"
            :selectlist="item.selectlist"
            :checked.sync="item.checked"
            :label="item.label"
            :subtext="item.subtext"
            :showtooltip="item.showtooltip"
            :tiptext="item.tiptext"
            :value.sync="item.value"
            :otherselectlist="item.otherselectlist"
            :othervalue.sync="item.othervalue"
            :index="index"
            liststr="changeList2"
            :iconcolor="item.iconColor"
            @add="addSearchItem"
            @delete="deleteSearchItem"
            @change="getChartData"
          />

          <customSingleSearchForm
            v-for="(item, index) in standardSettings.changeList3"
            :key="item.label + index"
            :selectlist="item.selectlist"
            :checked.sync="item.checked"
            :label="item.label"
            :subtext="item.subtext"
            :showtooltip="item.showtooltip"
            :tiptext="item.tiptext"
            :value.sync="item.value"
            :otherselectlist="item.otherselectlist"
            :othervalue.sync="item.othervalue"
            :index="index"
            liststr="changeList3"
            :iconcolor="item.iconColor"
            @add="addSearchItem"
            @delete="deleteSearchItem"
            @change="getChartData"
          />
        </div>

        <div v-else class="issue-spread-container-left-search">
          <CustomSaveSearchForm
            curvename="平均发行利率曲线"
            tiptext="在所选择时间范围内，计算每一日负荷配置条件的成功发行债券的平均票面利率，形成平均利率曲线。统计的债券范围为协会债、公司债、企业债各细分品种"
            :outsidelist="searchParamsList"
            @getAllList="getParamsList"
          />
        </div>
      </div>

      <div class="issue-spread-container-right">
        <CustomSpreadCurve
          :chartdata="chartOptions"
          :height="360"
          :title="headerBtns === 'mtn' ? '标准利率曲线' : '自定义利率曲线'"
          chartTitle="票面利率VS基准曲线"
        />
      </div>
    </div>

    <div class="issue-spread-table">
      <div class="public-tabs-container">
        <el-tabs v-model="tabActiveName" class="issue-spread-table-tabs" @tab-click="changeTabs">
          <el-tab-pane
            :label="$store.getters.sysVersion === $dict.COMPANY_VER_group ? '集团债券' : '我司债券'"
            name="CreditSpread"
          />
          <el-tab-pane label="参考债券" name="IssueSpread" />
          <el-tab-pane label="自选债券" name="ValuationSpread" />
        </el-tabs>
      </div>
      <div>
        <div class="issue-spread-table-header">
          <div
            v-if="tabActiveName === 'IssueSpread' && searchParamsList.length > 0"
            class="issue-spread-table-header-btns"
          >
            <div :class="showAllForm ? 'left-area' : 'left-area-single'">
              <el-button v-for="(item, index) in searchParamsList" :key="'btns' + index" @click="setOtherParams(item)">
                平均发行利率曲线{{ numberToChinese(index + 1) }}
              </el-button>
            </div>

            <div class="right-ctrl" v-if="searchParamsList.length > 9">
              <el-button v-if="!showAllForm" type="text" @click="changeShowAllForm(true)">
                <jr-svg-icon icon-class="down" />
                展开
              </el-button>
              <el-button v-else type="text" @click="changeShowAllForm(false)">
                <jr-svg-icon icon-class="up" />
                收起
              </el-button>
            </div>
          </div>

          <el-form inline :model="form" label-width="90" class="issue-spread-table-header-form">
            <jr-form-item label="发行截止日">
              <el-date-picker
                v-model="form.customDate"
                style="width: 100%"
                type="date"
                unlink-panels
                placeholder="请选择日期"
                clearable
                format="yyyy-MM-dd"
                class="date-picker"
                :picker-options="pickerOptions"
                :disabled="
                  (Array.isArray(form.customDateRange) && form.customDateRange.length === 0) || !form.customDateRange
                "
              />
            </jr-form-item>
            <jr-form-item v-if="tabActiveName !== 'ValuationSpread'" label="债券简称">
              <el-autocomplete
                v-model="form.s_info_name"
                :fetch-suggestions="querySearchAsync"
                placeholder="请输入债券简称"
                clearable
                style="width: 100%"
                value-key="text"
              />
            </jr-form-item>

            <jr-form-item v-if="$store.getters.sysVersion === $dict.COMPANY_VER_group" label="企业名称">
              <el-autocomplete
                v-model="form.compName"
                style="width: 100%"
                placeholder="请输入企业全称"
                clearable
                remote
                :fetch-suggestions="remoteSearch"
                :loading="loading"
                @clear="handleClear"
                @select="handleSelect"
              />
            </jr-form-item>

            <el-button type="primary" @click="submit">查询</el-button>
            <el-checkbox v-model="form.checked">含已到期</el-checkbox>

            <div class="issue-spread-table-header-form-left">
              <el-button
                v-if="tabActiveName === 'ValuationSpread'"
                type="text"
                style="height: 32px; border: 1px solid #ccc; background-color: #fff"
                @click="visible = true"
              >
                <jr-svg-icon icon-class="bars" />
                自选
              </el-button>

              <SelfDefinedList
                v-if="visible"
                :visible="visible"
                :permitdetail="permitdetail"
                :menuinfo="menuinfo"
                module="issue"
                @close="closemodal"
              />

              <el-button
                type="text"
                style="height: 32px; border: 1px solid #ccc; background-color: #fff"
                @click="resetSelectRows"
              >
                <jr-svg-icon icon-class="delete" />
                清空所选
              </el-button>
            </div>
          </el-form>
        </div>
        <jr-decorated-table
          ref="jrTable"
          :key="`${headerBtns}_${tabActiveName}`"
          :params="tableParams"
          style="height: 516px"
          :custom-id="ccid"
          v-bind="{ ...$attrs, ...$props }"
          :permitdetail="{
            ...permitdetail,
            export: { icon: 'upload', name: '导出', componenturl: 'export', btnkey: 'export' }
          }"
          :handleexport="exportData"
          :default-page-size="10"
          :initPagination="{
            pageSizeOptions: [10, 20, 50, 100]
          }"
          @handleSelectionChange="getSelectRows"
          @refreshed="callFn"
        />
      </div>
    </div>
  </div>
</template>

<script>
import moment from 'moment'
import CustomSearchFormItem from './custom-search-form.vue'
import customSingleSearchForm from './custom-single-search-form.vue'
import CustomSaveSearchForm from './custom-save-search-form'
import SelfDefinedList from './self-defined-tables'
import CustomSpreadCurve from './custom-spread-curve'
import { queryBondShortName } from '@/api/bonds/bonds'
import { debounce } from 'lodash'
import {
  queryCompName,
  spreadAnalysisQueryStandardYieldCurve,
  queryCurveRequestdata,
  saveCurveRequestdata,
  spreadAnalysisQueryCustomizeYieldCurve,
  spreadAnalysisQueryYield
} from '@/api/bonds/bonds'
import {
  getDictOptionsApi,
  multipleDefaultSearchSetting,
  singleDefaultSearchSetting,
  setChartOptions,
  getStandard,
  getDefaultSettings,
  formatterDateString
} from '../get-dictionary'
import { exportExcelByCustomColumn } from '@/api/public/public'
import tabParams from '@/mixins/tab-params'
const DICTIONARYARRAY = [
  'issueTermCDB',
  'issueTermGB',
  'issueTermMTN',
  'issueTermLGFV',
  'issueTermCB',
  'issueRatingMTN',
  'issueRatingLGFV',
  'issueRatingCB',
  'issueTermMy'
]
/**
 * 字典项数组对应值
 *
 *发行利差期限(国开债)
 *发行利差期限(国债)
 *发行利差期限(中短期票据)
 *发行利差期限(城投债)
 *发行利差期限(企业债)
 *发行利差期限(自定义利率曲线)
 *发行利差主体评级(中短期票票据)
 *发行利差主体评级(城投债)
 *发行利差主体评级(企业债)
 */
export default {
  components: {
    CustomSearchFormItem,
    CustomSaveSearchForm,
    SelfDefinedList,
    CustomSpreadCurve,
    customSingleSearchForm
  },
  props: {
    // 权限相关
    permitdetail: {
      type: Object,
      default: () => ({})
    },
    menuinfo: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      showAllForm: false, // 是否展示所有曲线
      ccid: 'e676658ce9654672a2f20e13333fff62',
      series: [],
      xAxis: [],
      selectRows: [],
      visible: false,
      tableParams: {
        ownedModuleid: '1352315117735067648'
      },
      tabActiveName: 'CreditSpread',
      headerBtns: 'mtn',
      form: {
        customDate: '',
        checked: true,
        compName: '',
        compId: '',
        customDateRange: null
      },
      myChart: null,
      standardSettings: {
        multipleSelectList: [],
        changeList1: [],
        changeList2: [],
        changeList3: []
      },
      standardSettingsId: null,
      customizeSettingsId: null,
      scatterSettings: [],
      scatterSettingsId: null,
      searchParamsList: [],
      loading: false, // 远程搜索加载状态
      chartOptions: {},
      pickerOptions: {},
      dictionaryObject: {},
      refresh: true,
      columns: [],
      rateLeftData: {
        xAxis: [],
        series: []
      },
      rateRightData: {
        xAxis: [],
        series: []
      },
      rateBottomData: {
        xAxis: [],
        series: []
      }
    }
  },
  mounted() {
    // 监听表格数据请求完成，设置默认选中行数据
    this.$watch(
      () => this.$refs.jrTable.$children[0].listData,
      (newData) => {
        const selectRowIds = this.scatterSettings.map((item) => item.uuid)

        this.setTableDefaultSelected(selectRowIds, newData)
      },
      { immediate: true, deep: true } // 立即监听 + 深度监听
    )
  },
  async created() {
    // 默认查询本年数据
    this.form.customDateRange = this.getYearStartAndEnd()
    this.pickerOptions.disabledDate = this.disabledDate
    this.submit()

    this.dictionaryObject = await getDictOptionsApi(DICTIONARYARRAY)

    // 初始化存储参数设置
    this.initSearchLists()

    // 页面请求操作过多，进行防抖处理
    this.debouncedRemoteSearch = debounce(this.rawRemoteSearch, 500)
    this.getChartData = debounce(this.getChartData, 500)
    this.getSelectRows = debounce(this.getSelectRows, 500)

    this.getChartData()
  },
  methods: {
    changeShowAllForm(val) {
      this.showAllForm = val
    },
    // 获取表格tab标题
    getFileName() {
      let name = ''

      switch (this.tabActiveName) {
        case 'CreditSpread':
          name = this.$store.getters.sysVersion === this.$dict.COMPANY_VER_group ? '集团债券' : '我司债券'
          break
        case 'IssueSpread':
          name = '参考债券'
          break
        case 'ValuationSpread':
          name = '自选债券'
          break
      }

      return `${name}发行利差`
    },
    // 导出
    async exportData() {
      const params = {
        params: {
          filename: this.getFileName(),
          column: this.columns,
          selectData: Array.isArray(this.selectRows) && this.selectRows.length > 0 ? this.selectRows : null,
          ccid: this.ccid,
          ownedModuleid: '1352315117735067648'
        },
        page: {
          sort: this.sort,
          direction: this.direction
        }
      }
      await exportExcelByCustomColumn(params)
    },
    // 切换tab页
    changeTabs() {
      switch (this.tabActiveName) {
        case 'CreditSpread':
          this.ccid = 'e676658ce9654672a2f20e13333fff62'
          break
        case 'IssueSpread':
          this.ccid = 'ab4eb491166c4306bc1912533468255a'
          break
        case 'ValuationSpread':
          this.ccid = '191e9498193c4d429acf9674e43793dd'
          break
      }
      this.tableParams = {}
      this.form.customDate = ''
      this.form.checked = true
      this.form.compName = ''
      this.form.compId = ''

      this.submit()

      this.getChartData()
    },
    // 默认发行截止日为本年
    getYearStartAndEnd() {
      // 获取当前年份
      const currentYear = new Date().getFullYear()

      // 创建本年1月1日的日期对象
      const startOfYear = new Date(currentYear, 0, 1)

      // 创建本年12月31日的日期对象
      const endOfYear = new Date(currentYear, 11, 31)

      // 返回开始和结束的日期
      return [moment(startOfYear).format('YYYY-MM-DD'), moment(endOfYear).format('YYYY-MM-DD')]
    },
    // 设置禁止选择区间
    disabledDate(time) {
      if (
        !this.form.customDateRange ||
        (Array.isArray(this.form.customDateRange) && this.form.customDateRange.length === 0)
      ) {
        return false
      }

      const start = new Date(this.form.customDateRange[0])
      const end = new Date(this.form.customDateRange[1])
      const current = new Date(time)

      // 不在范围内则禁用
      return current < start || current > end
    },
    // 发行截止日改变，发行截止日清空
    handleCustomDateRangeChange() {
      this.form.customDate = null
      this.getChartData()
    },
    // 表格数据勾选事件
    getSelectRows(rows, listData) {
      const currentUnselectedIds = []
      const currentSelectedIds = []
      for (let index = 0; index < rows.length; index++) {
        const subItem = rows[index]
        currentSelectedIds.push(subItem.uuid)
      }
      for (let index = 0; index < listData.length; index++) {
        const item = listData[index]
        if (!currentSelectedIds.includes(item.uuid)) {
          currentUnselectedIds.push(item.uuid)
        }
      }

      this.selectRows = rows
      const selectParams = [...this.scatterSettings]
      for (let index = 0; index < this.selectRows.length; index++) {
        const item = this.selectRows[index]
        selectParams.push({
          uuid: item.uuid,
          queryDate: moment(item.bIssueLastissue).format('YYYYMMDD'),
          bondCode: item.sInfoWindcode
        })
      }

      this.scatterSettings = selectParams.filter((item) => {
        return !currentUnselectedIds.includes(item.uuid)
      })

      this.getChartData()
    },
    // 表格组件渲染回调
    callFn(data) {
      this.columns = data.config.columns
      this.sort = data.sort
      this.direction = data.direction
      let getTableDataTimeInterval = null
      let retryCount = 0
      const MAX_RETRIES = 30

      getTableDataTimeInterval = setInterval(() => {
        console.log('轮询')

        const tableData = [...data.listData]
        console.log(tableData, 'tableData')

        if (tableData.length > 0) {
          clearInterval(getTableDataTimeInterval)
          const selectRowIds = this.scatterSettings.map((item) => item.uuid)

          this.setTableDefaultSelected(selectRowIds, tableData)
        } else {
          retryCount++
          if (retryCount >= MAX_RETRIES) {
            clearInterval(getTableDataTimeInterval)
            console.log('轮询超时，未获取到数据')
          }
        }
      }, 100)
    },
    // 设置表格默认勾选
    setTableDefaultSelected(selectRowIds, tableData) {
      const elTableRef = this.getElTableRef()

      this.$nextTick(() => {
        tableData.forEach((row) => {
          if (selectRowIds.includes(row.uuid)) {
            elTableRef?.toggleRowSelection(row, true)
          }
        })
      })
    },
    // 获取el-table ref对象
    getElTableRef() {
      if (
        this.$refs.jrTable &&
        this.$refs.jrTable.$children &&
        Array.isArray(this.$refs.jrTable.$children) &&
        this.$refs.jrTable.$children.length > 0 &&
        this.$refs.jrTable.$children[0] &&
        this.$refs.jrTable.$children[0].$el &&
        this.$refs.jrTable.$children[0].$el.__vue__ &&
        this.$refs.jrTable.$children[0].$el.__vue__.$children &&
        Array.isArray(this.$refs.jrTable.$children[0].$el.__vue__.$children) &&
        this.$refs.jrTable.$children[0].$el.__vue__.$children.length > 0 &&
        this.$refs.jrTable.$children[0].$el.__vue__.$children[0] &&
        this.$refs.jrTable.$children[0].$el.__vue__.$children[0].$el &&
        this.$refs.jrTable.$children[0].$el.__vue__.$children[0].$el.__vue__ &&
        this.$refs.jrTable.$children[0].$el.__vue__.$children[0].$el.__vue__.$children
      ) {
        return this.$refs.jrTable.$children[0].$el.__vue__.$children[0].$el.__vue__.$el.__vue__.$children[0]
      } else {
        return null
      }
    },
    // 重置表格复选款已选
    resetSelectRows() {
      this.$nextTick(() => {
        const elTableRef = this.getElTableRef()
        this.selectRows.forEach((row) => {
          elTableRef?.toggleRowSelection(row, false)
        })
        this.scatterSettings = []
        this.getChartData()
      })
    },
    // 获取图表数据
    async getChartData() {
      if (this.headerBtns === 'mtn') {
        this.saveCustomCurveRequestdata(
          'standardSettings',
          JSON.stringify(this.standardSettings),
          this.standardSettingsId
        )
        const params = {
          tradeDtStart: Array.isArray(this.form.customDateRange)
            ? moment(this.form.customDateRange[0]).format('YYYYMMDD')
            : '',
          tradeDtEnd: Array.isArray(this.form.customDateRange)
            ? moment(this.form.customDateRange[1]).format('YYYYMMDD')
            : '',
          standard: getStandard(this.standardSettings)
        }
        const res = await spreadAnalysisQueryStandardYieldCurve(params)

        this.rateLeftData = this.formatLeftChartData(res)
      } else if (this.headerBtns === 'lgfv') {
        const checkedParams = []

        for (let index = 0; index < this.searchParamsList.length; index++) {
          const item = this.searchParamsList[index]
          if (item.checked) {
            checkedParams.push({
              bondRating:
                Array.isArray(item.bondRating) && item.bondRating.length > 0 ? item.bondRating : item.bondRatingAll,
              bondType: Array.isArray(item.bondType) && item.bondType.length > 0 ? item.bondType : item.bondTypeAll,
              compProperty:
                Array.isArray(item.compProperty) && item.compProperty.length > 0
                  ? item.compProperty
                  : item.compPropertyAll,
              curvename: item.curvename,
              district: item.district || [],
              implyRating:
                Array.isArray(item.implyRating) && item.implyRating.length > 0 ? item.implyRating : item.implyRatingAll,
              isguarantee: item.isguarantee || [],
              isright: item.isright || '',
              termStr: Array.isArray(item.termStr) && item.termStr.length > 0 ? item.termStr : item.termStreAll,
              tradeDtEnd: item.tradeDtEnd,
              tradeDtStart: item.tradeDtStart
            })
          }
        }

        this.saveCustomCurveRequestdata(
          'customizeSettings',
          JSON.stringify(this.searchParamsList),
          this.customizeSettingsId
        )
        const res = await spreadAnalysisQueryCustomizeYieldCurve(checkedParams)

        this.rateRightData = this.formatRightChartData(res)
      }

      if (Array.isArray(this.scatterSettings)) {
        this.saveCustomCurveRequestdata('scatterSettings', JSON.stringify(this.scatterSettings), this.scatterSettingsId)
        const res = await spreadAnalysisQueryYield(this.scatterSettings)
        this.rateBottomData = this.processScatterData(res)
      }

      const xAxis = Array.from(
        new Set([...this.rateLeftData.xAxis, ...this.rateRightData.xAxis, ...this.rateBottomData.xAxis])
      ).sort((a, b) => new Date(a) - new Date(b))
      const series = [...this.rateRightData.series, ...this.rateLeftData.series, ...this.rateBottomData.series]
      this.chartOptions = setChartOptions(xAxis, series)
    },
    setDefaultCurveColor(curvename) {
      console.log(curvename, 'curvename')

      let color = ''
      if (curvename.includes('我司发行定价曲线')) {
        color = '#5B8FF9'
      } else if (curvename.includes('中债国开债到期收益率')) {
        color = '#269A99'
      } else if (curvename.includes('中债国债到期收益率')) {
        color = '#5AD8A6'
      } else if (curvename.includes('中债中短期票据到期收益率')) {
        color = '#E1B01E'
      } else if (curvename.includes('中债城投债到期收益率')) {
        color = '#E8684A'
      } else if (curvename.includes('中债企业债到期收益率')) {
        color = '#66BDFF'
      }
      if (color) {
        return {
          itemStyle: {
            color: color
          }
        }
      } else {
        return {}
      }
    },
    // 前端处理标准利率曲线数据
    formatLeftChartData(originalData) {
      if (!originalData || !Array.isArray(originalData))
        return {
          xAxis: [],
          series: []
        }
      // 1. 去重处理（保留同日期同曲线的最新数据）
      const uniqueMap = new Map()
      originalData.forEach((item) => {
        const key = `${formatterDateString(item.trade_dt)}_${item.b_anal_curvename}`
        uniqueMap.set(key, item)
      })
      const uniqueData = Array.from(uniqueMap.values())

      // 2. 获取所有唯一日期并排序
      const allDates = [...new Set(uniqueData.map((item) => formatterDateString(item.trade_dt)))]
      const sortedDates = allDates.sort((a, b) => new Date(a) - new Date(b))

      // 3. 按曲线名称分组
      const curveGroups = {}
      uniqueData.forEach((item) => {
        if (!curveGroups[item.b_anal_curvename]) {
          curveGroups[item.b_anal_curvename] = []
        }
        curveGroups[item.b_anal_curvename].push(item)
      })

      // 4. 为每条曲线创建数据系列
      const seriesData = []
      Object.keys(curveGroups).forEach((curveName) => {
        const curveItems = curveGroups[curveName]
        const dateValueMap = {}

        // 创建日期到值的映射
        curveItems.forEach((item) => {
          dateValueMap[formatterDateString(item.trade_dt)] = item.b_anal_yield
        })

        // 为当前曲线创建数据点数组
        const dataPoints = sortedDates.map((date) => ({
          name: date,
          value: dateValueMap[date] !== undefined ? dateValueMap[date] : null
        }))

        seriesData.push({
          name: curveName,
          type: 'line',
          data: dataPoints,
          symbol: 'circle',
          smooth: true,
          symbolSize: 6,
          lineStyle: {
            width: 2
          },
          connectNulls: true,
          ...this.setDefaultCurveColor(curveName)
        })
      })

      console.log(seriesData, 'seriesData')

      return {
        xAxis: sortedDates,
        series: seriesData
      }
    },
    // 前端处理自定义利率曲线数据
    formatRightChartData(rateData) {
      const series = []
      const xAxisSet = new Set()

      // 首先收集所有可能的日期
      Object.values(rateData).forEach((curve) => {
        curve.forEach((item) => {
          xAxisSet.add(formatterDateString(item.b_issue_lastissue))
        })
      })

      // 将日期排序
      const xAxisData = Array.from(xAxisSet).sort((a, b) => {
        return new Date(a) - new Date(b)
      })

      // 处理每条曲线
      Object.entries(rateData).forEach(([curveName, curveData]) => {
        // 创建日期到利率的映射
        const dateRateMap = {}
        curveData.forEach((item) => {
          dateRateMap[formatterDateString(item.b_issue_lastissue)] = item.couponrate
        })

        // 为当前曲线生成数据点
        const seriesData = xAxisData.map((date) => {
          return {
            name: date,
            value: dateRateMap[date] || null // 没有数据时设为null
          }
        })

        series.push({
          name: curveName,
          type: 'line',
          data: seriesData,
          symbol: 'circle',
          smooth: true,
          symbolSize: 8,
          lineStyle: {
            width: 3
          },
          // 处理null值，使曲线断开
          connectNulls: false
        })
      })

      return {
        xAxis: xAxisData,
        series: series
      }
    },
    // 前端处理散点数据
    processScatterData(rawData) {
      // 1. 按日期排序
      const sortedData = [...rawData].sort((a, b) => new Date(a.lastissuedate) - new Date(b.lastissuedate))

      // 2. 提取x轴数据（去重排序后的日期）
      const xAxis = [...new Set(sortedData.map((item) => formatterDateString(item.lastissuedate)))]

      // 3. 生成series数据
      const series = sortedData.map((item) => ({
        data: [
          {
            name: item.abbrname,
            value: [formatterDateString(item.lastissuedate), item.couponrate],
            symbolSize: 10
          }
        ],
        name: item.abbrname,
        type: 'scatter'
      }))

      return {
        xAxis,
        series
      }
    },
    // 关闭自选弹窗
    closemodal() {
      this.visible = false
      this.submit()
    },
    // 企业名称选择回调
    handleSelect(value) {
      if (value) {
        // 保存选中的数据
        this.form.compId = value.compId
        this.form.compName = value.compName
      }
    },
    // 企业名称清空回调
    handleClear() {
      // 清空选中的数据
      this.form.compId = ''
      this.form.compName = ''
    },
    // 查询企业名称
    remoteSearch(query, cb) {
      if (!query) return cb([])
      this.loading = true
      this.debouncedRemoteSearch(query, cb)
    },
    rawRemoteSearch(query, cb) {
      const compId = '502036608'
      queryCompName(query, compId) // 企业id 暂时为空
        .then((res) => {
          // 转换数据格式，确保每个选项有value字段
          const suggestions = res.map((item) => ({
            value: item.compName, // 显示文本
            ...item // 保留其他字段
          }))
          cb(suggestions)
        })
        .finally(() => {
          this.loading = false
        })
    },
    // 自定义利率曲线监听
    getParamsList(arr) {
      for (let index = 0; index < arr.length; index++) {
        const v = arr[index]
        v.curvename = `平均发行利率曲线${this.numberToChinese(index + 1)}`
        v.tradeDtEnd = Array.isArray(this.form.customDateRange)
          ? moment(this.form.customDateRange[1]).format('YYYYMMDD')
          : ''
        v.tradeDtStart = Array.isArray(this.form.customDateRange)
          ? moment(this.form.customDateRange[0]).format('YYYYMMDD')
          : ''
      }
      this.searchParamsList = arr
      this.getChartData()
    },
    // 数字转汉字
    numberToChinese(num) {
      if (isNaN(num) || num > 9999999999999999) {
        return '数字超出范围'
      }

      if (num === 0) {
        return '零'
      }

      // 定义数字对应的汉字
      const chineseNums = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九']
      // 定义单位
      const units = ['', '十', '百', '千']
      const bigUnits = ['', '万', '亿', '兆']

      // 处理负数
      let prefix = ''
      if (num < 0) {
        prefix = '负'
        num = Math.abs(num)
      }

      // 将数字转为字符串并分割为4位一组
      const numStr = Math.floor(num).toString()
      const segments = []
      for (let i = numStr.length; i > 0; i -= 4) {
        segments.unshift(numStr.slice(Math.max(0, i - 4), i))
      }

      let result = ''

      segments.forEach((segment, index) => {
        let segmentStr = ''
        let hasZero = false

        for (let i = 0; i < segment.length; i++) {
          const digit = parseInt(segment[i])
          const pos = segment.length - 1 - i

          if (digit === 0) {
            hasZero = true
          } else {
            if (hasZero) {
              segmentStr += chineseNums[0]
              hasZero = false
            }
            segmentStr += chineseNums[digit] + units[pos]
          }
        }

        if (segmentStr !== '') {
          segmentStr += bigUnits[segments.length - 1 - index]
        }

        result += segmentStr
      })

      // 处理连续的零
      result = result.replace(/零+/g, '零')
      // 去除末尾的零
      result = result.replace(/零+$/, '')
      // 处理一十开头的特殊情况
      if (result.startsWith('一十')) {
        result = result.substring(1)
      }

      return prefix + result
    },
    /**
     * 债券简称远程搜索
     */
    querySearchAsync(queryString, cb) {
      if (queryString) {
        queryBondShortName({
          text: queryString
        }).then((data) => {
          if (data && Object.keys(data).length) {
            cb(data)
          } else {
            cb([])
          }
        })
      } else {
        cb([])
      }
    },
    // 切换tab
    changeType() {
      this.getChartData()
    },
    // 保存企业用户页面设置参数
    async saveCustomCurveRequestdata(tabType, requestData, id) {
      const res = await saveCurveRequestdata([
        { tabType: tabType, requestData: requestData, id: id, tab: 'issue' + this.$store.getters.sysVersion }
      ])

      if (Object.hasOwnProperty.call(res, 'standardSettings')) {
        this.standardSettingsId = res.standardSettings || null
      }
      if (Object.hasOwnProperty.call(res, 'customizeSettings')) {
        this.customizeSettingsId = res.customizeSettings || null
      }

      if (Object.hasOwnProperty.call(res, 'scatterSettings')) {
        this.scatterSettingsId = res.scatterSettings || null
      }
    },
    // 初始化设置参数
    initSearchLists() {
      queryCurveRequestdata({ tab: 'issue' + this.$store.getters.sysVersion }).then(async (res) => {
        console.log(res, '设置')

        if (Object.hasOwnProperty.call(res, 'standardSettings')) {
          this.standardSettingsId = res['standardSettings'].id

          this.standardSettings = JSON.parse(res['standardSettings'].requestData)
        } else {
          this.standardSettings = await getDefaultSettings(this.dictionaryObject)
        }

        if (Object.hasOwnProperty.call(res, 'customizeSettings')) {
          this.customizeSettingsId = res['customizeSettings'].id

          this.searchParamsList = JSON.parse(res['customizeSettings'].requestData)
        } else {
          this.searchParamsList = []
        }

        if (Object.hasOwnProperty.call(res, 'scatterSettings')) {
          this.scatterSettingsId = res['scatterSettings'].id
          this.scatterSettings = JSON.parse(res['scatterSettings'].requestData)
        } else {
          this.scatterSettings = []
        }
      })
    },
    // 新增
    addSearchItem(liststr) {
      switch (liststr) {
        case 'changeList1':
          if (Array.isArray(this.standardSettings[liststr]) && this.standardSettings[liststr].length <= 2) {
            this.standardSettings[liststr].push(singleDefaultSearchSetting(this.dictionaryObject, 1))
          } else {
            this.$message({
              type: 'warning',
              message: '最多拥有三条中债中短期票据到期收益率(主体评级)(期限)'
            })
          }
          break
        case 'changeList2':
          if (Array.isArray(this.standardSettings[liststr]) && this.standardSettings[liststr].length <= 2) {
            this.standardSettings[liststr].push(singleDefaultSearchSetting(this.dictionaryObject, 2))
          } else {
            this.$message({
              type: 'warning',
              message: '最多拥有三条中债城投债到期收益率(期限)'
            })
          }
          break
        case 'changeList3':
          if (Array.isArray(this.standardSettings[liststr]) && this.standardSettings[liststr].length <= 2) {
            this.standardSettings[liststr].push(singleDefaultSearchSetting(this.dictionaryObject, 3))
          } else {
            this.$message({
              type: 'warning',
              message: '最多拥有三条中债企业债到期收益率(期限)'
            })
          }
          break
      }
    },
    // 删除
    deleteSearchItem(liststr, index) {
      if (Array.isArray(this.standardSettings[liststr]) && this.standardSettings[liststr].length >= 2) {
        this.standardSettings[liststr].splice(index, 1)
      } else {
        this.$message({
          type: 'warning',
          message: `至少拥有一条${
            liststr === 'changeList1'
              ? '中债中短期票据到期收益率(主体评级)(期限)'
              : liststr === 'changeList2'
              ? '中债城投债到期收益率(期限)'
              : '中债企业债到期收益率(期限)'
          }`
        })
      }
      this.getChartData()
    },
    // 处理表格查询参数
    handleSearchParams() {
      const obj = {
        issue_status: this.form.checked ? '1' : '0', // 含已到期
        b_issue_lastissue: this.form.customDate ? moment(new Date(this.form.customDate)).format('YYYYMMDD') : '', // 截止日期
        s_info_name: this.form.s_info_name, // 债券简称
        b_info_issuercode: this.form.compId,
        webTime: new Date().getTime(),
        tradeDtStart: Array.isArray(this.form.customDateRange)
          ? moment(this.form.customDateRange[0]).format('YYYYMMDD')
          : '',
        tradeDtEnd: Array.isArray(this.form.customDateRange)
          ? moment(this.form.customDateRange[1]).format('YYYYMMDD')
          : ''
      }

      return { ...this.tableParams, ...obj }
    },
    submit() {
      this.tableParams = this.handleSearchParams()
    },
    setOtherParams(item) {
      this.tableParams = { ...this.handleSearchParams(), ...item }
    }
  }
}
</script>

<style lang="scss" scoped>
@import '../css/valuation-spread-temporary.scss';
</style>
