<template>
  <div v-if="visible" class="bond-detail">
    <div class="bond-detail-top">
      详情
      <i class="el-icon-close" @click="handleCancel()" />
    </div>
    <div class="bond-detail-content">
      <el-descriptions :column="2" :size="size" border>
        <el-descriptions-item>
          <template slot="label">债券代码</template>
          {{ form.sinfoWindcode }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">债券简称</template>
          {{ form.sinfoName }}
        </el-descriptions-item>
        <el-descriptions-item :span="2">
          <template slot="label">债券全称</template>
          {{ form.binfoFullname }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">债券类型</template>
          {{ form.bondTypeName2 }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">创新专项品种</template>
          {{ form.productName }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">中债隐含评级</template>
          {{ form.cnbdCreditrating }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">发行价格(元)</template>
          {{ form.binfoIssueprice }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">规模(亿)</template>
          {{ form.bissueAmountact }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">发行期限</template>
          {{ form.term }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">票面利率(%)</template>
          {{ form.latestCouponrate }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">票面与首次估值利差(BP)</template>
          {{ form.interestMargin }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">中债估值(行权/到期)</template>
          {{ form.exerciseValuation }}
          {{ form.exerciseValuation && form.maturityValuation ? '/' : null }}
          {{ form.maturityValuation }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">剩余期限</template>
          {{ form.remainTerm }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">余额(亿)</template>
          {{ form.binfoOutstandingbalance }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">发行起始日</template>
          {{ form.bissueFirstissue }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">发行截止日</template>
          {{ form.bissueLastissue }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">发行方式</template>
          {{ form.binfoIssuetype }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">公告日</template>
          {{ form.bissueAnnouncement }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">上市日期</template>
          {{ form.binfoListdate }}
        </el-descriptions-item>
        <el-descriptions-item :span="2">
          <template slot="label">主承销商</template>
          {{ form.allLu }}
        </el-descriptions-item>
        <el-descriptions-item :span="2">
          <template slot="label">牵头主承销商</template>
          {{ form.leaderLu }}
        </el-descriptions-item>
        <el-descriptions-item :span="2">
          <template slot="label">联席主承销商</template>
          {{ form.jointLu }}
        </el-descriptions-item>
        <el-descriptions-item :span="2">
          <template slot="label">存续期管理机构</template>
          {{ form.managementAgency }}
        </el-descriptions-item>
        <el-descriptions-item :span="2">
          <template slot="label">簿记管理人</template>
          {{ form.bookAgency }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">内含特殊条款</template>
          {{ form.binfoProvisiontype }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">下一行权日</template>
          {{ form.nextRightDate }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">下一债权登记日</template>
          {{ form.nextRecorddate }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">交易市场</template>
          {{ form.sinfoExchmarketname }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">发行时债项评级</template>
          {{ form.bondRating }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">发行时主体评级</template>
          {{ form.binfoCreditrating }}
        </el-descriptions-item>
        <el-descriptions-item :span="2">
          <template slot="label">发行人委托评级机构</template>
          {{ form.binfoCreditratingagency }}
        </el-descriptions-item>
        <el-descriptions-item :span="2">
          <template slot="label">注册文号</template>
          {{ form.registerFileNumber }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">利率类型</template>
          {{ form.binfoInteresttypename }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">息票品种</template>
          {{ form.binfoCouponname }}
        </el-descriptions-item>
        <el-descriptions-item :span="2">
          <template slot="label">利率说明</template>
          {{ form.binfoCoupontxt }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">计息方式</template>
          {{ form.binfoPaymenttypename }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">付息频率</template>
          {{ form.binfoInterestfrequency }}
        </el-descriptions-item>
        <el-descriptions-item :span="2">
          <template slot="label">付息日说明</template>
          {{ form.binfoCoupondatetxt }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">计息基准</template>
          {{ form.binfoActualbenchmark }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">起息日期</template>
          {{ form.binfoCarrydate }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">到期日期</template>
          {{ form.binfoMaturitydate }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">兑付日期</template>
          {{ form.binfoPaymentdate }}
        </el-descriptions-item>
        <el-descriptions-item :span="2">
          <template slot="label">发行人</template>
          {{ form.binfoIssuer }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">主体性质</template>
          {{ form.compProperty }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">是否城投</template>
          {{ form.isWindInvest }}
        </el-descriptions-item>
        <el-descriptions-item :span="2">
          <template slot="label">行业</template>
          {{ form.sinfoCompindName4 }}
        </el-descriptions-item>
        <el-descriptions-item :span="2">
          <template slot="label">所在地区</template>
          {{ form.province }}{{ form.city }}{{ form.district }}
        </el-descriptions-item>
        <el-descriptions-item :span="2">
          <template slot="label">托管机构</template>
          {{ form.hostAgency }}
        </el-descriptions-item>
        <el-descriptions-item :span="2">
          <template slot="label">担保人</template>
          {{ form.guarantorlist }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">增信情况</template>
          {{ form.binfoGuarintroduction }}
        </el-descriptions-item>
      </el-descriptions>
    </div>
  </div>
</template>

<script>
import { queryBondDescriptionByWindCode } from '@/api/bonds/bonds'
export default {
  data() {
    return {
      visible: false,
      title: '',
      form: {},
      size: ''
    }
  },
  computed: {},
  methods: {
    /**
     * 弹框打开
     * @param {Object} row 行内数据
     * @param {String} title 展示表格名称
     */
    open(data) {
      this.visible = true
      queryBondDescriptionByWindCode(data).then((res) => {
        this.form = res
      })
    },
    /**
     * 弹框关闭
     */
    handleCancel() {
      this.visible = false
      this.form = {}
    }
  }
}
</script>

<style lang="scss" scoped>
.bond-detail {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 4px 16px 16px;
  background: rgb(245, 245, 245);
  z-index: 8;

  .bond-detail-top {
    position: relative;
    padding: 0 16px;
    height: 48px;
    line-height: 48px;
    font-size: var(--el-font-size-extra-large);
    font-weight: bold;
    background: #ffffff;
    border-bottom: 1px solid rgb(238, 238, 238);

    i {
      position: absolute;
      top: 16px;
      right: 16px;
      font-size: var(--el-font-size-medium);
      cursor: pointer;
    }
  }

  .bond-detail-content {
    padding: 16px;
    background: #ffffff;
    height: calc(100% - 49px);

    .el-descriptions {
      height: 100%;
      overflow: auto;

      th.el-descriptions-item__cell.el-descriptions-item__label.is-bordered-label {
        width: 12.5%;
        background: #f7f8fa;
      }

      td.el-descriptions-item__cell.el-descriptions-item__content {
        width: 12.5%;
      }
    }
  }
}
</style>
