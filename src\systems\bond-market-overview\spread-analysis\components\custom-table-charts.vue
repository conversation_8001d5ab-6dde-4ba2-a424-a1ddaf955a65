<template>
  <div class="table-chart-container">
    <div class="public-title-container">
      <div
        class="flex table-chart-container-header"
        style="justify-content: space-between; flex: 1; height: 22px !important"
      >
        <div class="flex" style="gap: 8px">
          <span style="font-size: var(--el-font-size-base); font-weight: 600; color: #000">{{ title }}</span>

          <span style="font-size: var(--el-font-size-base)">更新日期 {{ updateDate }}</span>
        </div>

        <ws-button class="el-dropdown-link" @click="exportTableToImage">
          <jr-svg-icon icon-class="upload" />
          导出
        </ws-button>
      </div>
    </div>
    <div ref="chart" class="table-chart" :style="{ height: height }"></div>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import wsButton from '@/components/ws-button'
export default {
  components: {
    wsButton
  },
  props: {
    max: {
      type: Number,
      default: 50
    },
    height: {
      type: String,
      default: '500px'
    },
    headers: {
      type: Array,
      default: () => ['', '1Y', '2Y', '3Y', '5Y', '7Y']
    },
    rows: {
      type: Array,
      default: () => [],
      validator: (value) => {
        // 确保每行都有name和values属性
        return value.every((row) => row.hasOwnProperty('name') && row.hasOwnProperty('values'))
      }
    },
    emptySymbol: {
      type: String,
      default: '--'
    },
    updateDate: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      chart: null
    }
  },
  mounted() {
    this.initChart()
    window.addEventListener('resize', this.handleResize)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize)
    if (this.chart) {
      this.chart.dispose()
    }
  },
  methods: {
    exportTableToImage(v) {
      // 下载图片
      const exportOptions = {
        backgroundColor: '#ffffff',
        pixelRatio: 2 // 提高导出清晰度（可选）
      }
      // // 生成图片 URL 并触发下载
      const chart = this.chart
      const imgUrl = chart.getDataURL(exportOptions)
      const link = document.createElement('a')
      link.href = imgUrl
      link.download = `${this.title}.png`
      link.click()
    },
    progressPercentage(value) {
      if (this.max <= 0) return 0
      if (value === 0) return 0
      const percentage = (value / Math.ceil(this.max / 0.8)) * 100
      return Math.min(100, Math.max(0, percentage.toFixed(1)))
    },
    initChart() {
      this.chart = echarts.init(this.$refs.chart)
      this.updateChart()
    },
    updateChart() {
      const option = this.getChartOption()
      this.chart.setOption(option, true)
    },
    getChartOption() {
      // 处理空值并反转行数据顺序
      const processedRows = this.rows
        .map((row) => ({
          name: row.name || '',
          values: Array.isArray(row.values)
            ? row.values.map((v) => (v === null || v === undefined || v === 0 ? this.emptySymbol : v))
            : new Array(this.headers.length - 1).fill(this.emptySymbol)
        }))
        .reverse()

      return {
        grid: {
          top: 54,
          left: 100,
          right: 50,
          bottom: 0,
          containLabel: true
        },
        xAxis: {
          type: 'value',
          show: false,
          max: this.calculateMaxValue()
        },
        yAxis: {
          type: 'category',
          axisLine: { show: false },
          axisTick: { show: false },
          data: [],
          axisLabel: {
            margin: 30,
            fontSize: 14,
            align: 'left'
          }
        },
        series: [this.getHeaderSeries(), this.getDataSeries(processedRows)]
      }
    },
    getHeaderSeries() {
      return {
        type: 'custom',
        renderItem: (params, api) => {
          const cellWidth = api.getWidth() / Math.max(1, this.headers.length)
          const headerHeight = 42
          const startY = 0

          const children = []

          if (Array.isArray(this.headers) && this.headers.length > 0) {
            // 表头背景
            children.push({
              type: 'rect',
              shape: {
                x: 0,
                y: startY,
                width: api.getWidth(),
                height: headerHeight
              },
              style: {
                fill: '#F5F5FB'
              },
              silent: true
            })

            // 表头文字和分隔线
            for (let index = 0; index < this.headers.length; index++) {
              const text = this.headers[index] || ''

              children.push({
                type: 'text',
                style: {
                  text: text,
                  x: index === 0 ? 20 : cellWidth * index + 30,
                  y: startY + headerHeight / 2,
                  fill: '#333',
                  textAlign: index === 0 ? 'left' : 'center',
                  textVerticalAlign: 'middle',
                  fontSize: 14,
                  fontWeight: 'bold'
                },
                silent: true
              })

              if (index > 0) {
                children.push({
                  type: 'line',
                  shape: {
                    x1: cellWidth * index,
                    y1: startY,
                    x2: cellWidth * index,
                    y2: startY + headerHeight
                  },
                  style: {
                    stroke: '#fff',
                    lineWidth: 4
                  },
                  silent: true
                })
              }
            }

            // 底部边框
            children.push({
              type: 'line',
              shape: {
                x1: 0,
                y1: startY + headerHeight,
                x2: api.getWidth(),
                y2: startY + headerHeight
              },
              style: {
                stroke: '#F5F5FB',
                lineWidth: 1
              },
              silent: true
            })
          }

          return {
            type: 'group',
            children: children,
            silent: true
          }
        },
        data: [0], // 必须有数据项
        z: 100
      }
    },
    getStartY(dataIndex, dataLength, rowHeight = 42, gap = 4) {
      return rowHeight * (dataLength - dataIndex) + gap * (dataLength - dataIndex) + rowHeight / 2
    },
    getDataSeries(rows) {
      const len = rows.length
      return {
        type: 'custom',
        renderItem: (params, api) => {
          // 更安全的数据获取方式
          const dataIndex = params.dataIndex
          const rowData = rows[dataIndex] || { name: '', values: [] }
          const values = rowData.values || []
          const cellWidth = api.getWidth() / Math.max(1, this.headers.length)
          const rowHeight = 42
          const y = this.getStartY(dataIndex, len)

          const children = []

          // 单元格背景
          children.push({
            type: 'rect',
            shape: {
              x: 0,
              y: y - rowHeight / 2,
              width: cellWidth - 2,
              height: rowHeight
            },
            style: {
              fill: '#F5F5FB', // 背景色
              stroke: '#F5F5FB', // 边框色
              lineWidth: 0
            },
            z: 1
          })

          // 行名称
          children.push({
            type: 'text',
            style: {
              text: rowData.name,
              x: 32,
              y: y,
              fill: '#333', // 文字颜色
              textAlign: 'center',
              textVerticalAlign: 'middle',
              fontSize: 14
            },
            z: 2
          })

          children.push({
            type: 'line',
            shape: {
              x1: cellWidth,
              y1: y,
              x2: cellWidth,
              y2: y + rowHeight
            },
            style: {
              stroke: '#fff',
              lineWidth: 4
            },
            silent: true
          })

          // 数据单元格
          for (let index = 0; index < values.length; index++) {
            const value = values[index]
            const x = cellWidth * (index + 1)

            // 单元格背景1
            children.push({
              type: 'rect',
              shape: {
                x: x + 2,
                y: y - rowHeight / 2,
                width: cellWidth - 4,
                height: rowHeight
              },
              style: {
                fill: '#fff',
                stroke: '#EAE9E9',
                lineWidth: 1
              },
              z: 1
            })

            // 单元格背景2
            if (value !== 0) {
              // 进度条背景
              children.push({
                type: 'rect',
                shape: {
                  x: x + 16,
                  y: y - rowHeight / 2 + 8,
                  width: cellWidth - 32,
                  height: rowHeight - 16,
                  r: [4, 4, 4, 4]
                },
                style: {
                  fill: '#F4F4F4',
                  stroke: '#F4F4F4',
                  lineWidth: 1
                },
                z: 2
              })
              children.push({
                type: 'rect',
                shape: {
                  x: x + 16,
                  y: y - rowHeight / 2 + 8,
                  width:
                    ((cellWidth - 32) *
                      this.progressPercentage(value === this.emptySymbol ? 0 : this.formatValue(value))) /
                    100,
                  height: rowHeight - 16,
                  r: [4, 4, 4, 4]
                },
                style: {
                  fill: {
                    type: 'linear',
                    x: 0,
                    y: 0,
                    x2: 1,
                    y2: 0,
                    colorStops: [
                      {
                        offset: 0,
                        color: '#FFCC74' // 渐变起始颜色
                      },
                      {
                        offset: 0.5,
                        color: '#ecc479' // 渐变中间颜色
                      },
                      {
                        offset: 1,
                        color: '#FFAE68' // 渐变结束颜色
                      }
                    ]
                  },
                  stroke: '#F5F5FB',
                  lineWidth: 1
                },
                z: 3
              })
            }

            // 单元格内容
            children.push({
              type: 'text',
              style: {
                text: value === this.emptySymbol ? this.emptySymbol : this.formatValue(value),
                x: x + 44,
                y: y,
                fill: '#333',
                textAlign: 'center',
                textVerticalAlign: 'middle',
                fontSize: 14
              },
              z: 4
            })
          }

          return {
            type: 'group',
            children: children
          }
        },
        data: rows,
        encode: {
          // 明确指定数据映射
          y: (params) => params.dataIndex // 使用数据索引作为y轴坐标
        }
      }
    },
    formatValue(value) {
      return String(value)
    },
    calculateMaxValue() {
      let max = 1 // 默认最小值
      this.rows.forEach((row) => {
        if (Array.isArray(row.values)) {
          row.values.forEach((value) => {
            if (typeof value === 'number' && value > max) {
              max = value
            }
          })
        }
      })
      return Math.ceil(max / 0.8)
    },
    handleResize() {
      if (this.chart) {
        this.chart.resize()
      }
    }
  },
  watch: {
    headers: {
      handler() {
        if (this.chart) {
          this.updateChart()
        }
      },
      deep: true
    },
    rows: {
      handler() {
        if (this.chart) {
          this.updateChart()
        }
      },
      deep: true
    }
  }
}
</script>

<style scoped lang="scss">
@import '../css/credit-spread-temporary.scss';
</style>
