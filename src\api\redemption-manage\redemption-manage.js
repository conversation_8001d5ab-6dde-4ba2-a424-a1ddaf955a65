import { GetListInfo, ExportFn } from '@jupiterweb/utils/api'

// 新增修改承销费
export const feeUnderwritingInfoSaveOrModify = (params) => GetListInfo('/paymentManagerment/feeUnderwritingInfo/saveOrModify', params)

// 新增修改中介费
export const feeAgencyInfoSaveOrModify = (params) => GetListInfo('/paymentManagerment/feeAgencyInfo/saveOrModify', params)

// 查询承销费支付计划
export const queryFeePlanForAll = (params) =>
  GetListInfo('/paymentManagerment/feeUnderwritingInfo/queryFeePlanForAll', params)

// 查询中介费支付计划
export const feeAgencyInfoQueryFeePlanForAll = (params) =>
  GetListInfo('/paymentManagerment/feeAgencyInfo/queryFeePlanForAll', params)

// 根据债卷简称查询规模和发行期限
export const queryBondInfo = (params) => GetListInfo('/paymentManagerment/feeUnderwritingInfo/queryBondInfo', params)

// 承销费计算
export const saveBath = (params) => GetListInfo('/paymentManagerment/feeUnderwritingInfo/saveBath', params)

// 删除承销费支付计划
export const deletePayPlan = (params) => GetListInfo('/paymentManagerment/feeUnderwritingInfo/delete', params)

// 删除中介费支付计划
export const deleteFeeAgencyInfoPayPlan = (params) => GetListInfo('/paymentManagerment/feeAgencyInfo/delete', params)

// 承销费支付计划状态修改
export const changePaymentStatus = (params) =>
  GetListInfo('/paymentManagerment/feeUnderwritingInfo/paymentStatus', params)

// 中介费支付计划状态修改
export const changeFeeAgencyInfoPaymentStatus = (params) =>
  GetListInfo('/paymentManagerment/feeAgencyInfo/paymentStatus', params)

// 计算支付费用 
export const calPaymentAmt = (params) =>
  GetListInfo('/paymentManagerment/feeUnderwritingInfo/calPaymentAmt', params)

// 承销费导出 
export const serviceExport = (params) =>
  ExportFn('/paymentManagerment/feeUnderwritingInfo/export', params)

// 中介费和其他服务费用导出
export const otherExport = (params) =>
  ExportFn('/paymentManagerment/feeAgencyInfo/export', params)

// 兑付管理-债项付息兑付-保存现金流信息
export const bondRedemptionSavePaymentInfo = (params) =>
  GetListInfo('/paymentManagerment/bondRedemption/savePaymentInfo', params)
// 兑付管理-债项付息兑付-修改支付状态
export const bondRedemptionUpdatePaymentStatus = (params) =>
  GetListInfo('/paymentManagerment/bondRedemption/updatePaymentStatus', params)

// 兑付管理-未来偿付现金流-分析汇总信息
export const futureCashFlowQueryAnalyseInfo = (params) =>
  GetListInfo('/paymentManagerment/futureCashFlow/queryAnalyseInfo', params)

// 兑付管理-未来偿付现金流-本金利息图
export const futureCashFlowQueryPrincipalAndInterestInfo = (params) =>
  GetListInfo('/paymentManagerment/futureCashFlow/queryPrincipalAndInterestInfo', params)