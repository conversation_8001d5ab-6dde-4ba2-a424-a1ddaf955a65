<!-- 债券持仓分析 -> 平均到期收益率 -->
<template>
  <div class="home-poc-item maturity-yield-page">
    <div class="home-poc-item--header">平均到期收益率
      <el-form :model="form" style="flex: 1;padding-left: 20%;padding-right: 29px; margin-top: -1px;">
        <jr-form-item-create :data="cols" :model="form" :column="3" style="line-height: 2;" />
      </el-form>
      <fullscreen @fullscreen="fullscreen" />
    </div>

    <TemplateModule
      ref="TemplateModule"
      class="home-poc-item--body"
      chart-seq="ffc70b7afc0a4d9d80198630aa8c84b1"
      chart-type="MULTILINE"
      style="padding-top: 10px;"
      :params="queryParams"
    />
  </div>
</template>

<script>
const format = 'YYYY-MM-DD'
import dayjs from 'dayjs'
import { getInit } from '@/systems/mixins'
import fullscreen from '../../common/fullscreen'
import TemplateModule from '@jupiterweb/components/template-module'

export default {
  components: {
    fullscreen,
    TemplateModule
  },
  mixins: [getInit('/invest/portfolio/bondposanalysis/BondPosAnalysis001')],
  props: {
    params: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      cols: [],
      form: {
        timeLength: '01'
      }
    }
  },
  computed: {
    queryParams() {
      const { timeLength } = this.form
      const [vDate, mDate] = this.convertDate(timeLength)
      return { vDate, mDate, ...this.params }
    }
  },
  created() {
    this.init()
  },
  methods: {
    init() {
      const { TIME_LENGTH } = this.getInit

      this.cols = [{
        type: 'select',
        prop: 'timeLength',
        options: TIME_LENGTH,
        optionValue: 'id',
        clearable: false
      }]
    },
    convertDate(timeLength) {
      const sysDate = { ...this.params }.mDate || this.$store.getters.systemTime
      const plateDate = dayjs(sysDate).format(format)

      switch (timeLength) {
        case '04':
          return [dayjs(sysDate).subtract(7, 'day').format(format), plateDate]
        case '03':
          return [dayjs(sysDate).subtract(30, 'day').format(format), plateDate]
        case '02':
          return [dayjs(sysDate).subtract(180, 'day').format(format), plateDate]
        case '01':
          return [dayjs(sysDate).subtract(365, 'day').format(format), plateDate]
      }
    },
    fullscreen(v) {
      this.$emit('fullscreen', v)

      const { chartInstance } = this.$refs.TemplateModule.$children[0]
      chartInstance && this.$nextTick(chartInstance.resize)
    }
  }
}
</script>

<style lang="scss" scoped>
@import "../../common/poc.scss";
</style>
