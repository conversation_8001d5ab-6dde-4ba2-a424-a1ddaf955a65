{"name": "butler-web", "version": "1.0.0", "description": "投行智管家", "homepage": "/", "scripts": {"serve": "jupiter-service serve --open", "start": "npm run serve", "build": "jupiter-service build", "lint": "eslint --ext .js,.vue src", "lintfix": "eslint --ext .js,.vue src --fix"}, "dependencies": {"dayjs": "^1.11.13", "jupiterweb": "4.1.1-4"}, "license": "ISC", "vuePlugins": {"resolveFrom": "node_modules/jupiterweb"}, "baseModules": [], "browserslist": ["> 1%", "last 2 versions"]}