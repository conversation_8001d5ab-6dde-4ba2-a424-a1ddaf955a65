::v-deep .el-table__cell {
  .cell {
    height: 40px !important;
    display: flex;
    align-items: center;
    padding: 5px !important;
    line-height: 40px !important;
  }
}

.credit-spread {
  background: #fff;
  padding-bottom: 16px;

  &-table {
    padding: 16px 16px 0 16px !important;
    background: #fff !important;
  }

  &-chart {
    background-color: #fff;
    padding: 16px !important;
  }

  &-header {
    display: flex;
    width: 100%;
    align-items: center;
    padding-left: 16px;
    height: 40px !important;

    ::v-deep .el-radio-group {
      height: 40px !important;
    }

    ::v-deep .el-radio-button__inner {
      font-size: var(--el-font-size-large) !important;
      height: 40px !important;
    }

    &-btn {
      background-color: #f5f5f5;
      color: #000;
    }

    &-btn:active,
    &-btn.is-active,
    &-btn:focus {
      background-color: var(--theme--color);
      border-color: var(--theme--color);
      color: white;
    }
  }
}
