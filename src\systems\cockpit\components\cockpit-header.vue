<template>
  <div class="cockpitHeader" :class="type">
    <img src="@/assets/cockpit/cockpitHeadIcon.png" alt="">
    <p v-if="title">{{ title }}</p>
    <slot />
    <img src="@/assets/cockpit/cockpitHeadIcon.png" alt="">
  </div>
</template>

<script>
export default {
  name: 'CockpitHeader',
  props: {
    title: {
      type: String,
      default: ''
    },
    type:{
      type:String,
      default: 'deep'
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~assets/styles/portcss.scss';
.cockpitHeader {
  margin: 0 auto;
  width: 100%;
  height: 100%;
  background-size: 100% 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: vw(7);
  img {
    width: vw(26);
    height: vh(26);
  }
  p {
    margin-bottom: 0;
    height: vh(19);
    font-size: vh(16);
    color: #e6f6ff;
    line-height: vh(19);
  }
}
.deep{
  background-image: url('../../../assets/cockpit/cockpitHeadBac.png');
  & > p {
    font-weight: 600;
  }
}
.light{
  background-image: url('../../../assets/cockpit/cockpitHeadLightBac.png');
}
</style>
