<template>
  <div class="group-manage-container">
    <div class="group-manage-header">
      <label>企业名称</label>
      <el-input v-model="companyName" placeholder="请输入企业名称" clearable />
      <el-button type="primary" @click="handleSearch">搜索</el-button>
      <el-checkbox v-model="showOnlyBondCompany" @change="handleSearch">仅展示发债公司</el-checkbox>
    </div>
    <div class="group-manage-content">
      <div class="group-manage-content-title">集团成员信息
        <el-button v-if="canEdit && !editStatus" plain type="primary" @click="handleEdit">
          <img src="~@/assets/images/personal/cfg.png">
          配置
        </el-button>
      </div>
      <el-tree
        ref="tree"
        v-loading="loading"
        element-loading-background="transparent"
        :data="treeData"
        default-expand-all
        check-strictly
        show-checkbox
        node-key="id"
        :default-checked-keys="checkedKeys"
        :render-content="renderContent"
        @check-change="handleSelectChange"
      />
      <div v-if="editStatus" class="group-manage-content-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleSave">确定</el-button>
      </div>
    </div>
    <jr-modal
      ref="messageModal"
      :width="modalConfig.width"
      :ok-text="modalConfig.okText"
      :height="modalConfig.height"
      :modal-class="modalConfig.modalClass"
      :handle-cancel="modalConfig.handleCancel"
      :handle-ok="modalConfig.handleOk"
      :visible="modalConfig.visible"
    >
      <span>{{ modalConfig.title }}</span>
      <template #body>
        <el-form ref="messageModalForm" :model="addCompanyForm">
          <jr-form-item-create
            :data="modalConfigList"
            :model="addCompanyForm"
            :column="1"
          />
        </el-form>
      </template>
    </jr-modal>
  </div>
</template>

<script>
import { ConvertPlainToTreeWithoutTop } from '@jupiterweb/utils/common'
import { Loading } from 'element-ui'
import { getGroupMemberConfig, saveGroupMemberConfig, queryShareholding } from '@/api/personal'

export default {
  props: {
    permitdetail: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      editStatus: false,
      companyName: '',
      showOnlyBondCompany: false,
      treeData: [],
      checkedKeys: [],
      loading: false,
      addCompanyForm: {
        status: 'N',
        pid: '',
        pCompanyName: '',
        companyCode: '',
        companyName: '',
        title: '',
        value: '',
        lev: 0
      },
      modalConfigList: [
        {
          title: '上级企业名称',
          prop: 'pCompanyName',
          type: 'text',
          disabled: true
        },
        {
          title: '企业名称',
          prop: 'companyCode',
          type: 'remoteSelect',
          required: true,
          api: '/center/groupcfg/findAllCompany',
          optionValue: 'companyCode',
          optionLabel: 'companyName',
          showCode: false,
          change: async(val, option) => {
            if (val) {
              const loading = Loading.service({
                lock: true, // 是否锁定屏幕的滚动
                text: '加载中……', // loading下面的文字
                background: 'rgba(0, 0, 0, 0.7)', // loading的背景色
                target: 'body' // loading显示在容器
              })
              this.$set(this.addCompanyForm, 'key', val + '_' + this.addCompanyForm.lev)
              this.$set(this.addCompanyForm, 'id', val + '_' + this.addCompanyForm.lev)
              this.$set(this.addCompanyForm, 'label', option?.companyName)
              this.$set(this.addCompanyForm, 'companyName', option?.companyName)
              this.$set(this.addCompanyForm, 'title', option?.companyName)
              await this.handleShareholding(this.addCompanyForm.pCompanyName, this.addCompanyForm.pid, option?.companyCode)
              loading.close()
            }
          }
        }
      ],
      modalConfig: {
        visible: false,
        title: '添加集团成员',
        width: '640px',
        height: 135,
        okText: '确定',
        modalClass: 'personal-modal group-manage-modal',
        handleCancel: this.handleCloseMessageModal,
        handleOk: this.handleSaveMessageConfigModal
      }
    }
  },
  computed: {
    canEdit() {
      return !!this.permitdetail.edit
    }
  },
  created() {
    this.getGroupMemberConfig()
  },
  methods: {
    handleSearch() {
      this.getGroupMemberConfig()
    },
    async getGroupMemberConfig() {
      const { companyName, showOnlyBondCompany, canEdit } = this
      this.loading = true
      const treeData = await getGroupMemberConfig({ permitFlag: canEdit ? 'Y' : 'N', companyName, issuerFlag: showOnlyBondCompany ? 'Y' : 'N' })
      if (treeData.length) {
        treeData.forEach((item) => {
          this.$set(item, 'disabled', !this.editStatus || !canEdit)
        })
      }
      this.checkedKeys = treeData.filter((item) => item.status === 'Y').map((item) => item.id)
      this.treeData = ConvertPlainToTreeWithoutTop(treeData, 'id', 'pid', 'companyName')
      this.$refs.tree?.setCheckedKeys?.(this.checkedKeys)
      this.loading = false
    },
    renderContent(h, { node, data }) {
      const self = this
      return (
        <span class={'group-manage-custom-tree-node'}>
          <span class={'node-text'}>
            <span class={'node-text-name' + ' lev-' + data.lev} title={data.label || data.companyName}>
              {data.label || data.companyName}
            </span>
            {data.bondFlag === 'Y' && (
              <span class={'node-text-bond'}>
                [债]
              </span>
            )}
            {data.shareholding !== null && (
              <span class={'node-text-holding'}>
                {(data.shareholding * 100).toFixed(2)}%
              </span>
            )}
            {data.periodFlag === 'Y' && (
              <span class={'node-text-period'}>
                无存续期债券
              </span>
            )}
          </span>
          {self.editStatus && <span class='table-action-box'>
            {data.lev <= 4 && (
              <span title='添加' onClick={self.openModal.bind(self, data)}>
                <img src={require(`@/assets/images/personal/plus.png`)} />
              </span>
            )}
            {data.lev > 1 && (
              <span title='删除' onClick={self.handleDelete.bind(self, data)}>
                <img src={require(`@/assets/images/personal/delete.png`)} />
              </span>
            )}
          </span>}
        </span>
      )
    },
    handleSelectChange(data, checked) {
      this.$set(data, 'status', checked ? 'Y' : 'N')
      // this.$nextTick(this.handleSave)
    },
    handleEdit() {
      this.editStatus = true
      this.getGroupMemberConfig()
    },
    handleCancel() {
      this.editStatus = false
      this.getGroupMemberConfig()
    },
    handleSave() {
      if (this.loading) return
      this.loading = true
      saveGroupMemberConfig(this.treeData[0], (isSuccess) => {
        if (isSuccess) {
          this.getGroupMemberConfig()
          this.editStatus = false
          this.msgSuccess('配置成功')
        } else {
          this.loading = false
        }
      })
    },
    // 查询持股比例额
    handleShareholding(pName, pCode, code) {
      queryShareholding({ pName, pCode, code }).then(res => {
        this.$set(this.addCompanyForm, 'shareholding', res)
      })
    },
    // 打开添加集团成员弹窗
    openModal(data, e) {
      e.stopPropagation()
      this.$set(this.addCompanyForm, 'pid', data.id)
      this.$set(this.addCompanyForm, 'lev', data.lev + 1)
      this.$set(this.addCompanyForm, 'pCompanyName', data.companyName)
      Object.assign(this.modalConfig, {
        visible: true
      })
    },
    // 关闭添加集团成员弹窗
    handleCloseMessageModal() {
      Object.assign(this.addCompanyForm, {
        pid: '',
        companyName: '',
        companyCode: '',
        id: '',
        key: '',
        label: '',
        title: '',
        value: ''
      })
      Object.assign(this.modalConfig, {
        visible: false
      })
    },
    // 保存添加集团成员
    handleSaveMessageConfigModal() {
      const self = this
      this.$refs.messageModalForm.validate((valid) => {
        if (valid) {
          self.$refs.tree?.append({ ...self.addCompanyForm, status: 'N', children: [] }, self.addCompanyForm.pid)
          self.$nextTick(() => {
            // this.handleSave()
            self.handleCloseMessageModal()
          })
        }
      })
    },
    // 删除树节点
    handleDelete(data, e) {
      e.stopPropagation()
      const self = this
      this.confirm('确定删除条数据吗？', {
        type: 'error'
      }).then(() => {
        self.$refs.tree?.remove(data)
        // self.$nextTick(self.handleSave)
      })
    }
  }
}
</script>

<style lang="scss">
.group-manage-modal {
  .el-form {
    width: 325px;
    margin: auto;
  }
}
.group-manage-container {
  height: 100%;
  .group-manage-header {
    display: flex;
    align-items: center;
    padding: 0 16px 8px;
    .el-button {
      margin-right: 10px;
      width: 88px;
    }
    .el-checkbox__label {
      padding-left: 8px;
    }
    .el-input {
      width: 300px;
      margin-right: 10px;
      margin-left: 5px;
    }
  }
  .group-manage-content {
    padding: 0 16px;
    height: calc(100% - 70px);
    padding-bottom: 20px;
    overflow-y: auto;
    width: 100%;
    position: relative;
    .group-manage-content-title {
      display: flex;
      align-items: center;
      line-height: 28px;
      color: rgba(0, 0, 0, 0.9);
      // font-size: var(--el-font-size-medium);
      margin-bottom: 16px;
      font-weight: 600;
      .el-button {
        height: 28px;
        line-height: 26px;
        margin-left: 16px;
        img {
          vertical-align: -2px;
        }
        &:hover {
          color: var(--theme--color);
          background-color: var(--theme--color-light-9);
        }
      }
    }
    .group-manage-content-footer {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 100%;
      height: 56px;
      background-color: var(--el-fill-color-blank);
      position: absolute;
      bottom: 0;
      left: 0;
      border-top: 1px solid rgba(0, 0, 0, 0.06);
      .el-button {
        width: 88px;
      }
    }
    .el-tree {
      width: fit-content;
      min-width: 620px;
      height: calc(100% - 56px - 44px);
    }
  }
  .el-tree-node {
    margin-top: 8px;
    .el-checkbox__inner {
      width: var(--el-font-size-medium);
      height: var(--el-font-size-medium);
      &::after {
        height: 9px;
        left: 5px;
      }
    }
  }
  .group-manage-custom-tree-node {
    display: flex;
    align-items: center;
    justify-content: space-between;
    // font-size: var(--el-font-size-large);
    width: 100%;
    .node-text {
      white-space: nowrap;
    }
    // 节点过长显示省略号
    .node-text-name {
      text-overflow: ellipsis;
      overflow: hidden;
      &.lev-1 {
        font-weight: 600;
      }
    }
    .node-text-bond {
      color: var(--theme--color);
      font-size: var(--el-font-size-base);
      margin-left: 10px;
    }
    .node-text-holding {
      margin-left: 10px;
    }
    .node-text-period {
      color: var(--theme--color);
      background-color: var(--theme--color-light-9);
      font-size: var(--el-font-size-base);
      padding: 3px 5px;
      margin-left: 10px;
    }
    .table-action-box {
      width: 60px;
      position: absolute;
      right: -60px;
      background: #fff;
        span + span {
        margin-left: 10px;
        cursor: pointer;
      }
    }
  }
}
</style>
