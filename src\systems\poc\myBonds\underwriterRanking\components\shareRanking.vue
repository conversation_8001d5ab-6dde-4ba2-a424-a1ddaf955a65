<!-- 承销份额排名 -->
<template>
  <div class="shareRanking">
    <div class="shareRankingTitle">
      <span>承销份额排名</span>
      <span class="line" />
      <span class="otherTitle">承销笔数排名</span>
    </div>
    <div class="content">
      <TemplateModule chart-type="BAR" chart-seq="bbc3d062d8754c0d88f8b947703ab756" style="height: 400px; width: 100%" />
    </div>

  </div>
</template>

<script>
import TemplateModule from '@jupiterweb/components/template-module'
export default {
  components: { TemplateModule },
  data() {
    return {
    }
  },
  methods: {}
}
</script>
<style lang="scss">
.shareRanking {
    margin-top: 10px;
    padding: 6px 10px 6px 10px;
    background: #fff;
    min-height: 400px;
    .el-table {
        height: 300px !important;
    }
    .shareRankingTitle {
        font-size: 16px;
        color: #303133;
        font-weight: 700;
        padding-bottom: 6px;
        border-bottom: 1px solid #EBEEF5;
        .line {
            display: inline-block;
            height: 13px;
            width: 1px;
            background-color: #ccc;
            margin: 0 20px;
        }
        .otherTitle {
            color: var(--theme--color);
        }
    }
    .content {
        display: flex;
        .leftContent {
            flex: 1;
        }
        .rightContent {
            flex: 1.5;
        }
    }
}
</style>
