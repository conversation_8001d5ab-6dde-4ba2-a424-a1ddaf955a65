<!-- 组合归因分析 -> 头部公共查询 -->
<template>
  <div class="ascribe-search header-search-panel">
    <el-form ref="form" :model="form">
      <jr-form-item-create :data="cols" :model="form" :column="3" style="line-height: 0;" />
    </el-form>

    <el-button type="primary" class="search-panel" @click="query">
      查询
    </el-button>
  </div>
</template>

<script>
export default {
  data() {
    return {
      form: {},
      cols: [{
        title: '组合名称',
        prop: 'portfolioId',
        type: 'remoteSelect',
        required: true,
        optionValue: 'id',
        api: '/invest/portfolio/ptlincomeanalysis/PtlIncomeAnalysis001/getPortfolioId'
      }, {
        title: '起始日期',
        prop: 'startDate',
        type: 'date',
        required: true
      }, {
        title: '截止日期',
        prop: 'endDate',
        type: 'date',
        required: true
      }]
    }
  },
  methods: {
    query() {
      this.$refs.form.validate(valid => {
        if (valid) {
          const { portfolioId, startDate, endDate } = this.form
          const p = {
            portfolioId,
            startDate,
            endDate
          }

          this.$emit('setTargetParams', {
            'e53d2679fdb64fa2ba926cf5a919c9a5': p, // 组合收益分解
            '5d891d7e205c4d0ab92590498f2c763d': p, // 债券归因
            '5c175f49fd7d4387b3e91a91c4e62f86': p // N大收益贡献
          })
        }
      })
    }
  }
}
</script>

<style lang="scss">
.ascribe-search {
  background: #fff;
  display: flex !important;
  padding: 0 10px;

  .el-form {
    width: 100%;
    flex: 1;
  }

  .search-panel {
    width: 56px;
    margin-top: 4px;
    margin-left: 15px;
  }
}
</style>
