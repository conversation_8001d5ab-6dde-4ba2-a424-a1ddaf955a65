<template>
  <jr-modal
    modal-class="personal-modal update-password-dialog"
    :loading="confirmLoading"
    :handle-cancel="() => handleCancel(false)"
    :visible="visible"
    :handle-ok="handleOk"
    width="640px"
    ok-text="确定"
    :height="193"
  >
    <slot name="title">修改密码</slot>
    <template v-slot:body>
      <el-form ref="updateForm" :model="form" label-width="80px">
        <jr-form-item label="原密码" prop="pwd" :rules="rules.pwd">
          <el-input v-model="form.pwd" type="password" :maxlength="24" placeholder="请输入原密码" />
        </jr-form-item>
        <jr-form-item label="新密码" prop="new1Pwd" :rules="rules.new1Pwd">
          <el-input v-model="form.new1Pwd" type="password" :maxlength="24" placeholder="请输入新密码" />
        </jr-form-item>
        <jr-form-item label="确认密码" prop="new2Pwd" :rules="rules.new2Pwd">
          <el-input v-model="form.new2Pwd" type="password" :maxlength="24" placeholder="请确认密码" />
        </jr-form-item>
      </el-form>
    </template>
  </jr-modal>
</template>
<script>
import PassWordModal from '@jupiterweb/layout/components/settings/update-password'
export default {
  extends: PassWordModal,
  methods: {
    handleCancel(isSuccess) {
      const self = this
      this.confirmLoading = false
      this.userInfo.isFirstLogin !== 'Y' && (this.form = {})
      this.closeModal(isSuccess)
      isSuccess && setTimeout(() => {
        self.$router.push({ path: '/login' })
      }, 2000)
    }
  }
}
</script>
<style lang="scss">
.update-password-dialog {
  .el-form {
    width: 400px;
    margin: 0 auto;
  }
  .el-form-item {
    margin-bottom: 10px !important;
    .el-form-item__error {
      top: 97% !important;
      display: block !important;
      padding-top: 0px !important;
    }
  }
}
</style>
