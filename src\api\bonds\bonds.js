import { GetListInfo, ExportFn } from '@jupiterweb/utils/api'

// 主体评级
export const getBondRatingInfo = (params) => GetListInfo('/myBonds/ratingInfo/companyRatingLine', params)

// 中债隐含评级
export const getImpliedRatingChartData = (params) =>
  GetListInfo('/myBonds/ratingInfo/queryCompanyImpliedRatingLine', params)

// 发行人
export const getBondIssuerInfo = (params) => GetListInfo('/system/organ/list', params)
// 评级机构
export const getBondRatingAgencyInfo = (params) => GetListInfo('/dict/detail/page', params)

// 导出
export const exportBondRatingInfo = (params) => ExportFn('/tmCustomColumns/export', params)

// 我司估值导出
export const exportBondMyEstimateInfo = (params) => ExportFn('/myBonds/companyValuation/exportExcelByModule', params)

// 模糊查询未关联的企业名称
export const queryCompName = (compName, compId) =>
  GetListInfo(`/myBonds/relatedCompanyBonds/queryCompName?compName=${compName}&compId=${compId}`)

// 新增关联关系
export const addBondRelated = (params) => GetListInfo('/myBonds/relatedCompanyBonds/addRelated', params)

// 修改关联关系
export const updateBondRelated = (params) => GetListInfo('/myBonds/relatedCompanyBonds/changeRelated', params)

// 删除关联关系
export const deleteBondRelated = (params) => GetListInfo('/myBonds/relatedCompanyBonds/deleteRelated', params)
// 承销排名
export const queryBondRank = (params) => GetListInfo('/myBonds/underwritingDetails/queryBondRank', params)

// 根据发行人代码查询债券类型
export const queryBondTypeByIssuerCode = (params) => GetListInfo('/common/queryBondType/queryBondType', params)

// 我的债券-债券明细-编辑时查询付息兑付计划现金流列表
export const queryBondRepayCalculator = (params) => GetListInfo('/myBonds/bondDetail/calculateCashFlow', params)
// 我的债券-债券明细-债券录入
export const saveBondDetail = (params) => GetListInfo('/myBonds/bondDetail/saveBondDetails', params)

// 债市一览-利差分析-信用利差 表格数据查询
export const queryCreditSpread = (params) => GetListInfo('/bondMarketOverview/spreadAnalysis/queryCreditSpread', params)

// 债券评级机构
export const queryBondRatingAgency = (params) => GetListInfo('/common/baseinfo/orgInfoOption', params)
// 我的债券-集团发债-债券简称下拉项
export const queryBondShortName = (params) => GetListInfo('/web/common/bondInfoOption', params)
// 我的债券-债券明细-平均统计信息
export const mineBondsAverageInfo = (params) => GetListInfo('/myBonds/bondDetail/queryAvgInfo', params)

// 债市一览-利差分析-发行利差收益率曲线(自定义利率)
export const spreadAnalysisQueryCustomizeYieldCurve = (params) =>
  GetListInfo('/bondMarketOverview/spreadAnalysis/queryCustomizeYieldCurve', params)
// 债市一览-利差分析-发行利差收益率曲线(标准利率)
export const spreadAnalysisQueryStandardYieldCurve = (params) =>
  GetListInfo('/bondMarketOverview/spreadAnalysis/queryStandardYieldCurve', params)
// 债市一览-利差分析-发行利差-债券票面利率
export const spreadAnalysisQueryYield = (params) => GetListInfo('/bondMarketOverview/spreadAnalysis/queryYield', params)

// 债市一览-利差分析-发行利差-自选债券保存
export const saveBatchCustomizeBond = (params) => GetListInfo('/bondMarketOverview/spreadAnalysis/saveBatchCustomizeBond', params)

// 债市一览-利差分析-发行利差-自定义已选择债券查询
export const querySelectedBondList = (params) => GetListInfo('/bondMarketOverview/spreadAnalysis/querySelectedBondList', params)

// 债市一览-利差分析-发行利差-左自定义债券查询 
export const queryCustomizeBondForAll = (params) => GetListInfo('/bondMarketOverview/spreadAnalysis/queryCustomizeBondForAll', params)

// 债市一览-利差分析-发行利差-删除已选择债券
export const deleteCustomizeBond = (params) => GetListInfo('/bondMarketOverview/spreadAnalysis/deleteCustomizeBond', params)

// 债市一览-利差分析-发行利差-曲线参数表查询
export const queryCurveRequestdata = (params) => GetListInfo('/bondMarketOverview/spreadAnalysis/queryCurveRequestdata', params)

// 债市一览-利差分析-发行利差-曲线参数表保存
export const saveCurveRequestdata = (params) => GetListInfo('/bondMarketOverview/spreadAnalysis/saveCurveRequestdata', params)

// 债市一览-利差分析-估值利差曲线(上市首日估值偏离度)
export const queryValuationDeviation = (params) => GetListInfo('/bondMarketOverview/spreadAnalysis/queryValuationDeviation', params)

// 债市一览-利差分析-估值利差曲线(信用利差曲线)
export const queryValuationSpread = (params) => GetListInfo('/bondMarketOverview/spreadAnalysis/queryValuationSpread', params)

// 债市一览-利差分析-上市首日估值偏离度
export const queryDeviation = (params) => GetListInfo('/bondMarketOverview/spreadAnalysis/queryDeviation', params)

// 获取发行时主体评级
export const queryCompanyAuthInfo = (params) => GetListInfo('/system/common/companyAuthInfo', params)

// 我的债券-债券明细-保存时查询付息兑付计划现金流列表
export const initBondDetailCashFlow = (params) => GetListInfo('/myBonds/bondDetail/initCashFlow', params)

// 债市一览-发行查询-行业下拉框
export const queryBondCompindDict = (params) => GetListInfo('bond/bondDescription/queryBondCompindDict', params)

// 债市一览-发行查询-债券创新专项品种
export const queryBondSpecialProductDict = (params) => GetListInfo('bond/bondDescription/queryBondSpecialProductDict', params)

// 债市一览-发行查询-发行汇总查询
export const getBondSumTotal = (params) => GetListInfo('bond/bondDescription/getBondSumTotal', params)

// 债市一览-发行查询-债券规模分布按债券类型查询
export const getBondTypeTotal = (params) => GetListInfo('bond/bondDescription/getBondTypeTotal', params)

// 债市一览-发行查询-债券规模分布按期限查询
export const getBondTermTotal = (params) => GetListInfo('bond/bondDescription/getBondTermTotal', params)

// 债市一览-发行查询-债券规模分布按评级查询
export const getBondRatingTotal = (params) => GetListInfo('bond/bondDescription/getBondRatingTotal', params)

// 债市一览-发行查询-债券利率分布按发行时间查询
export const getBondRateFirstDateTotal = (params) => GetListInfo('bond/bondDescription/getBondRateFirstDateTotal', params)

// 债市一览-发行查询-债券利率分布按期限查询
export const getBondRateTermTotal = (params) => GetListInfo('bond/bondDescription/getBondRateTermTotal', params)

// 债市一览-发行查询-发行详情查询
export const queryBondDescriptionByWindCode = (params) => GetListInfo('bond/bondDescription/queryBondDescriptionByWindCode', params)

// 债市一览-发行查询-债券品种
export const queryDictBondType = (params) => GetListInfo('bond/bondDescription/queryDictBondType', params)

// 债市一览-利差分析-信用利差曲线
export const spreadAnalysisQueryCreditSpreadCurve = (params) => GetListInfo('bondMarketOverview/spreadAnalysis/queryCreditSpreadCurve', params)

// 债市一览-利差分析-信用利差曲线导出
export const exportSpreadAnalysisQueryCreditSpreadCurve = (params) => ExportFn('/bondMarketOverview/spreadAnalysis/export', params)

// 利差走势债券列表
export const queryBondList = (params) => GetListInfo('/myBonds/bondSpread/queryBondList', params)

// 发行利差、估值利差上市首日估值偏离度图
export const queryyieldtimage = (params) => GetListInfo('/myBonds/bondSpread/queryyieldtimage', params)

// 估值利差走势
export const queryBondyieldhist = (params) => GetListInfo('/myBonds/bondSpread/queryBondyieldhist', params)

// 导出利差走势列表
export const exportBondList = (params) => ExportFn('/myBonds/bondSpread/exportBondList', params)

// 我的债券-利差走势-新估值日期估值日期
export const queryyieldtradeDt = (params) => GetListInfo('/myBonds/bondSpread/queryyieldtradeDt', params)

// 区域经济-最新数据年份
export const queryRegionEeconomicYear = (params) => GetListInfo('/bond/region/queryRegionEeconomicYear', params)
