<template>
  <div class="template-module-chart">
    <Echart id="futureDebt" ref="componentsCharts" :options="options" :styles="{ height: height + 'px' }" />
  </div>
</template>
<script>
import Echart from '@jupiterweb/components/echarts'
import * as echarts from 'echarts'
export default {
  name: 'futureDebt',
  components: {
    Echart
  },
  props: {
    height: {
      type: Number,
      default() {
        return 400
      }
    },
    chartdata: {
      type: Object,
      default: () => ({})
    },
    title: {
      type: String,
      default() {
        return ''
      }
    }
  },
  data() {
    return {
      options: {}
    }
  },
  computed: {
    chartOptions() {
      return JSON.stringify(this.chartdata)
    }
  },
  watch: {
    chartOptions(newVal, oldVal) {
      console.log('发生了变化')
      this.init()
    }
  },

  created() {
    this.init()
  },
  methods: {
    exportTableToImage(v) {
      // 下载图片
      const exportOptions = {
        backgroundColor: '#ffffff',
        pixelRatio: 2 // 提高导出清晰度（可选）
      }
      // // 生成图片 URL 并触发下载
      const chart = this.$refs.componentsCharts.myChart

      const imgUrl = chart.getDataURL(exportOptions)
      const link = document.createElement('a')
      link.href = imgUrl
      link.download = `${this.title}.png`
      link.click()
    },
    init() {
      this.$nextTick(() => {
        this.options = JSON.parse(JSON.stringify(this.chartdata))
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.template-module-chart {
  height: 472px;
  &-header {
    justify-content: space-between;
    height: 64px;
    padding: 21px;
    display: flex;

    &-text {
      height: 22px;
      font-family: MicrosoftYaHeiSemibold;
      font-size: var(--el-font-size-base);
      color: rgba(0, 0, 0, 0.85);
      line-height: 22px;
      text-align: left;
      font-style: normal;
      margin-left: 8px;
    }
  }
}
</style>
