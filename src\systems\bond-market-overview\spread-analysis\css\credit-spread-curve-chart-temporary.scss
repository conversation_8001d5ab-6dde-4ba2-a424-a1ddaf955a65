.template-module-chart {
  position: relative;
  background-color: #fff;
  min-height: 500px;

  .custom-actions {
    position: absolute;
    top: 4px;
    left: 16px;
    width: calc(100% - 120px);
    z-index: 1;
  }

  .chart-content {
    top: 16px;
    margin-top: 0 !important;
  }

  .el-dropdown {
    position: absolute;
    right: 0px;
    top: 4px;
    z-index: 1;
    .el-dropdown-link {
      cursor: pointer;
      font-size: var(--el-font-size-base);
      color: #333333;
    }
  }
}

::v-deep .el-form-item {
  display: flex !important;
  width: 25%;

  .el-form-item__label {
    padding-top: 8px !important;
    width: 72px;
  }

  .el-form-item__content {
    display: flex;
    align-items: center;
    width: calc(100% - 72px);
  }

  &.no-label {
    width: auto;

    .el-form-item__content {
      width: 100%;
    }
  }
}

::v-deep .el-radio {
  margin-right: 0 !important;
  min-width: 80px !important;
}