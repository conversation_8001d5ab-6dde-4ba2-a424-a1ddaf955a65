<template>
  <!-- POC专用 我司估值 -->
  <div class="page-wsgz">
    <Yield v-bind="{...$attrs, ...$props}" @currentRow="(row) => currentRow = row" />
    <div class="flex-row">
      <Chart style="width: calc(100% - 300px);" v-bind="{ chartSeq, ...$attrs, ...$props}" />
      <Term v-bind="{...$attrs, ...$props}" :current-row="currentRow" @chartSeq="(v) => chartSeq = v" />
    </div>
  </div>
</template>

<script>
import Yield from './modules/yield.vue'
import Chart from './modules/chart.vue'
import Term from './modules/term.vue'
export default {
  components: {
    Yield,
    Chart,
    Term
  },
  data() {
    return {
      chartSeq: 'ef9bb382aad9467eb2b755438450ab87',
      currentRow: {}
    }
  }
}
</script>

<style lang="scss">
.page-wsgz {
  height: 100%;
  width: 100%;
  &>div {
    background-color: #fff;
  }
  .flex-row {
    display: flex;
    width: 100%;
    height: calc(100% - 300px);
  }
}
</style>
