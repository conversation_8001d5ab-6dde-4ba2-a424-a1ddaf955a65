// 关联企业发债
<template>
  <div class="associated-enterprise">
    <div class="public-table-search-container associated-enterprise-select">
      <label @click.stop="test">企业名称</label>
      <el-input v-model="searchForm.related_comp_name" style="width: 246px" placeholder="请输入企业全称" />
      <el-button type="primary" @click="submit">查询</el-button>
      <el-checkbox v-model="searchForm.issue_status">所有债券</el-checkbox>
    </div>
    <div class="associated-enterprise-content">
      <div class="public-table-search-container associated-enterprise-content-top">
        <el-button @click="openPage">关联配置</el-button>
        <el-button
          @click="exportData"
          :disabled="
            (Array.isArray(multipleSelectRows) && multipleSelectRows.length === 0) || !Array.isArray(multipleSelectRows)
          "
        >
          <jr-svg-icon class="el-icon--left" icon-class="upload" />
          批量导出
        </el-button>
      </div>
      <div class="associated-enterprise-content-table">
        <jr-decorated-table
          ref="jrTable"
          :custom-render="customRender"
          stripe
          :menuinfo="menuinfo"
          custom-id="0b3d5f66ae9f4662a2accb9dd980406f"
          :params="{
            ...tableParams,
            ownedModuleid: menuinfo.moduleid
          }"
          style="padding-bottom: 16px"
          @refreshed="callFn"
          @handleSelectionChange="getSelectRows"
          v-bind="{ ...$props }"
        />
      </div>
      <decorTable v-if="false" />
    </div>
  </div>
</template>
<script>
import { exportExcelByCustomColumn } from '@/api/public/public'
import decorTable from './components/decorTable.vue'
import { getMultipleRowsAndIds, setDefaultSelected } from '@/assets/js/multipleExport'
import { debounce } from 'lodash'
export default {
  name: 'AssociatedEnterprise',
  components: {
    decorTable
  },
  props: {
    // 权限相关
    permitdetail: {
      type: Object,
      default: () => ({})
    },
    menuinfo: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      allBond: false,
      columns: [],
      selectRows: [],
      // 自定义列id
      tableId: '0b3d5f66ae9f4662a2accb9dd980406f',
      // 菜单id
      ownedModuleid: '1352320268273270784',
      // 查询参数
      tableParams: {
        issue_status: 0
      },
      searchForm: {
        ccid: '0b3d5f66ae9f4662a2accb9dd980406f',
        ownedModuleid: '1352320268273270784',
        related_comp_name: '',
        issue_status: false
      },
      isExpanded: {}, // 记录每行的展开状态
      detailTableParams: {}, // 新增：存储详情表格的查询参数
      compName: '',
      compId: '',
      customRender: {
        relatedCompName: (h, { rowIndex, row }) => {
          return (
            <el-popover
              placement='top-start'
              trigger='click'
              class='custom-popover-item'
              onShow={() => {
                this.handlePopoverShow(rowIndex)
              }}
            >
              <span
                slot='reference'
                style='color:var(--theme--color);cursor:pointer;'
                popperClass='detail-popover'
                width='800'
                onClick={(e) => {
                  e.stopPropagation() // 阻止事件冒泡
                  this.openPopover(row)
                }}
              >
                {row.relatedCompName}
              </span>
              {row.relatedCompId === this.compId ? (
                <decorTable
                  bInfoIssuercode={this.compId}
                  permitdetail={this.permitdetail}
                  menuinfo={this.menuinfo}
                  issue_status={this.tableParams.issue_status}
                  customId={'dc9571652c1f4ea6923400d136c0e46f'}
                ></decorTable>
              ) : null}
            </el-popover>
          )
        }
      },
      multipleSelectRows: [], // 批量导出多选存储数组，最多50个
      multipleSelectIds: [] // 批量导出多选id集合
    }
  },
  created() {
    this.getSelectRows = debounce(this.getSelectRows, 500)
  },
  methods: {
    test() {
      console.log(this.menuinfo)
    },
    submit() {
      this.compId = ''
      this.tableParams = {
        ...this.searchForm
      }
      if (this.tableParams.issue_status) {
        this.tableParams.issue_status = 1
      } else {
        this.tableParams.issue_status = 0
      }
    },
    // 这是全部导出 -暂不支持导出选中的列
    async exportData() {
      const params = {
        params: {
          pageInfo: {},
          filename: '关联企业发债',
          column: this.columns,
          ccid: '2642e060df324cddbcc58b88f22b472c',
          ownedModuleid: this.menuinfo.moduleid,
          selectData:
            Array.isArray(this.multipleSelectRows) && this.multipleSelectRows.length > 0
              ? this.multipleSelectRows
              : null
        },
        page: {
          sort: this.sort,
          direction: this.direction
        }
      }
      await exportExcelByCustomColumn(params)
    },
    callFn(data) {
      this.columns = data.config.columns
      this.sort = data.sort
      this.direction = data.direction

      let getTableDataTimeInterval = null
      let retryCount = 0
      const MAX_RETRIES = 30
      const that = this

      getTableDataTimeInterval = setInterval(() => {
        console.log('轮询')

        const tableData = [...data.listData]
        console.log(tableData, 'tableData')

        if (tableData.length > 0) {
          clearInterval(getTableDataTimeInterval)

          setDefaultSelected(this.multipleSelectIds, tableData, that)
        } else {
          retryCount++
          if (retryCount >= MAX_RETRIES) {
            clearInterval(getTableDataTimeInterval)
            console.log('轮询超时，未获取到数据')
          }
        }
      }, 100)
    },
    getSelectRows(rows, listData) {
      this.selectRows = rows
      const that = this
      const res = getMultipleRowsAndIds(rows, listData, this.multipleSelectRows, this.multipleSelectIds, that)
      this.multipleSelectRows = res.rows
      this.multipleSelectIds = res.ids
    },
    // 打开关联配置
    openPage() {
      console.log('打开关联配置')
      // 1361284917287215104 菜单id
      // 往mate中添加参数？ 需要带参数跳转吗
      this.$store.dispatch('tagsView/updateCurrentTab', {
        path: '/1361284917287215104',
        meta: { params: { id: 1 }, query: { id: 1 } }
      })
    },
    /**
     * 获取数据详情
     */
    openPopover(row) {
      console.log('被点击')

      this.compId = row.relatedCompId
    },
    handlePopoverShow(index) {
      this.$nextTick(() => {
        const elList = document.querySelectorAll('.custom-popover-item')

        for (let i = 0; i < elList.length; i++) {
          const popoverInstance = elList[i].__vue__

          if (i !== index) {
            if (popoverInstance?.doClose) popoverInstance.doClose()
          }
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-checkbox__label {
  font-size: var(--el-font-size-base);
}
::v-deep .jr-decorated-table--top-search .jr-decorated-table--header-left {
  display: none !important;
}
::v-deep .associated-enterprise-select label {
  display: flex;
  align-items: center;
}
::v-deep .jr-tags-view.tabs-view-container .comm-tabs > .el-tabs__header .el-tabs__nav .el-tabs__item {
  font-size: var(--el-font-size-base) !important;
}
::v-deep body .jr-tags-view.tabs-view-container {
  padding: 4px 16px 16px !important;
}
::v-deep .jr-decorated-table > div {
  padding-left: 16px;
  padding-right: 16px;
}
::v-deep .jr-decorated-table--top-search {
  background-color: #ffffff;
}
.associated-enterprise {
  height: 100%;
  &-select {
    width: 100%;
    height: 48px;
    background-color: #ffffff;
    padding: 8px 16px;
    display: flex;
    align-items: center;
    label {
      height: 22px;
      font-family: MicrosoftYaHei;
      font-size: var(--el-font-size-base);
      color: rgba(0, 0, 0, 0.85);
      line-height: 22px;
      margin-right: 8px;
    }
    button {
      margin-left: 13px;
      margin-right: 22px;
    }

    ::v-deep .el-checkbox__inner {
      margin-top: 3px;
    }
  }
  &-content {
    height: calc(100% - 56px);
    margin-top: 1px;
    &-top {
      background-color: #ffffff;
      display: flex;
      justify-content: space-between;
      border: none !important;
      & > button:nth-of-type(1) {
        height: 32px;
        background: #ff8e2b;
        border-radius: 2px;
        color: #ffffff;
        border: none;
      }
    }
    &-table {
      height: calc(100% - 54px);
      .jr-decorated-table--header-left,
      .jr-decorated-table--header {
        display: none;
      }
      .jr-decorated-table--body {
        padding: 0;
      }
    }
  }
}
</style>
<style lang="scss">
.detail-popover {
  // max-height: 300px !important;
  max-width: 1000px;
  // width: 80%;
  // min-width: 1000px;
}
</style>
