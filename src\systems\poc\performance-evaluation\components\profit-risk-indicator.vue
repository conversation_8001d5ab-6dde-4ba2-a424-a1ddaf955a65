<!--
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2024-01-13 19:49:54
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2024-06-06 14:29:35
 * @Description: 收益风险指标
-->
<template>
  <div class="profit-risk-indicator">
    <h2>收益风险指标</h2>
    <jr-decorated-table v-if="total" custom-id="52e1bd84096043588e88f765682b6141" :params="defaultValues" v-bind="{ menuinfo: {pageId: 'ProfitRiskIndicator001', btnList: []}, noPagination: true, ...$attrs, ...$props }" @refreshed="handleRefreshed" />
    <jr-empty v-else />
  </div>
</template>
<script>
export default {
  props: {
    data: {
      type: Object,
      default: () => ({})
    },
    params: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      total: 10
    }
  },
  computed: {
    defaultValues() {
      return { 'PORTFOLIO_ID': this.data.PORTFOLIOID, PERIOD: this.params.period }
    }
  },
  watch: {
    params: {
      handler() {
        this.total = 10
      },
      deep: true
    }
  },
  methods: {
    handleRefreshed(ins) {
      this.total = ins.pagination.total
    }
  }
}
</script>
<style lang="scss">
.profit-risk-indicator {
  height: 400px;
  .jr-decorated-table {
    height: calc(100% - 60px);
  }
  // ,.el-table__header-wrapper
  .jr-decorated-table--header {
    display: none;
  }
  .jr-table {
    border: 1px solid #e8e8e8 !important;
    border-left: none !important;
    td,th {
      border-left: 1px solid #e8e8e8 !important;
    }
  }
  .jr-decorated-table--body .jr-table .el-table--border tr > th.el-table__cell.is-leaf:first-child,
  .jr-decorated-table--body .jr-table .el-table--border tr > th.el-table__cell:first-child {
    background-color: #fff !important;
    border-right: none !important;
    border-bottom: none !important;
    &> div {
    visibility: hidden;
    }
  }
}
</style>
