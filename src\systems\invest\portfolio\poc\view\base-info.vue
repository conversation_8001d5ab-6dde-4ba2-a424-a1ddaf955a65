<!--
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023-04-20 19:49:22
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2023-06-17 14:38:59
 * @Description: 组合简要
-->
<template>
  <div class="home-poc-item has-fullscreen plt-base-info-page">
    <div class="home-poc-item--header float-left">组合简要 <fullscreen v-on="{ ...$listeners }" /></div>
    <jr-decorated-table
      custom-id="ae8b739239ca4402a82d9a201aaa616f"
      :custom-render="{
        ptlResidueTerm: ptlResidueTermRender,
        netvalueUpdown: netvalueUpdownRender
      }"
      :row-click="handleRowClick"
      v-bind="{ ...$attrs, params: queryParams, initDisplayMode: 'card', noPagination: true, menuinfo: { pageId: 'BaseInfo' } }"
      @refreshed="queryListEnd"
    />
  </div>
</template>

<script>
import fullscreen from '../common/fullscreen'
import { FormatDate } from '@jupiterweb/utils/common'

export default {
  components: {
    fullscreen
  },
  data() {
    return {
      isInit: true,
      totalCount: 0,
      queryParams: {},
      platDate: JSON.parse(sessionStorage.getItem('platDate'))
    }
  },
  methods: {
    queryListEnd(ins) {
      // this.queryParams = { ...ins.getCommonParams('exportParams') }
      if (ins.listData.length) {
        this.handleRowClick(ins.listData[0])

        this.isInit && this.$nextTick(() => {
          const card = this.$el.querySelector('.el-card')

          this.isInit = false
          card && card.click()
        })
      }
    },
    // 组合剩余期限
    ptlResidueTermRender(h, { row }) {
      return <span>{row.ptlResidueTerm}天</span>
    },
    // 净值涨跌幅
    netvalueUpdownRender(h, { row }) {
      const v = (Number(row.netvalueUpdown || 0) * 100).toFixed(4)
      const cn = row.netvalueUpdown > 0 ? 'up' : row.netvalueUpdown === 0 ? 'gray' : 'down'
      return <span class={'color-' + cn}>{v}%</span>
    },
    handleRowClick(item) {
      const p = {
        cdate: FormatDate(item.cdate, 'yyyy-MM-dd'),
        portfolioId: item.portfolioId,
        portfolioName: item.portfolioName
      }
      // 收益贡献：e02cb31f6a5f4ba7a28b0cddd34dc419
      // 持仓：dca8968a87b446cda499ff32583e8358
      this.$emit('setTargetParams', {
        'f4ab86230e764141acb2dadbb8097bb6': p, // 组合净值走势
        '36ecf6030f764238a761c482a0adc4a2': p, // 组合久期
        '99ee0eb3eaf6477b92d7fb434074917e': p, // 组合头寸
        'dca8968a87b446cda499ff32583e8358': p // 组合持仓
      })
    }
  }
}
</script>
<style lang="scss">
$up: red;
$down: green;
$gray: gray;
@import "../common/poc.scss";
.plt-base-info-page {
  .jr-decorated-table--card-switch {
    display: none;
  }
  .jr-decorated-table--body {
    height: calc(100% - 45px);
    .jr-table,
    .table-card-list {
      height: 100% !important;
    }
  }
  .jr-decorated-table--header-center {
    padding-right: 30px;
    .el-form--inline .el-form-item {
      margin-right: 0;
    }
  }
  .el-card:hover {
    transform: scale(1.02);
  }
  .el-card .el-card__body {
    position: relative;
  }
  .el-card [class*="color-"] {
    &::after {
      content: " ";
      height: 3px;
      width: 100%;
      position: absolute;
      bottom: 0;
      left: 0;
    }
  }
  .el-card .color-up {
    color: $up;
     &::after {
      background: $up;
     }
  }
  .el-card .color-down {
    color: $down;
     &::after {
      background: $down;
     }
  }
  .el-card .color-gray {
    color: $gray;
     &::after {
      background: $gray;
     }
  }

  .el-card.current-row {
    &:has(.color-up) {
      border: 1px solid $up;
    }

    &:has(.color-down) {
      border: 1px solid $down;
    }

    &:has(.color-gray) {
      border: 1px solid $gray;
    }
  }
}
</style>
