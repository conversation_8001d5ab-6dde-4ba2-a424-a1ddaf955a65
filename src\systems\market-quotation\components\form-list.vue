<template>
  <z-search ref="search" class="form-list" :form="form" :form-old="formOld" @submit="submit" @reset="reset">
    <jr-form-item v-if="timeFlag" label="日期区间">
      <div class="z-row">
        <div class="z-col">
          <jr-radio-group
            v-model="form.radioDate"
            :data="checkboxList"
            option-label="label"
            option-value="value"
            @change="change"
          />
        </div>
        <div class="z-col">
          <el-date-picker
            v-model="form.date"
            type="daterange"
            ref="datePicker"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd"
            format="yyyy-MM-dd"
            unlink-panels
            :picker-options="pickerOptions()"
            @change="dateChange"
          />
        </div>
      </div>
    </jr-form-item>
    <slot />

    <template v-if="tipsFlag" v-slot:search-left>
      <span class="tips">
        <jr-svg-icon icon-class="exclamation-circle" />
        发行期限最多选择10条
      </span>
    </template>
  </z-search>
</template>

<script>
import zSearch from '@/components/search'
import { date } from '@/utils/common'
import { GetViewData } from '@jupiterweb/api/config-platform/params-type'
export default {
  components: {
    zSearch
  },
  props: {
    form: {
      type: Object,
      default: () => ({})
    },
    checkChange: {
      type: Function,
      default: () => {}
    },
    reset: {
      type: Function,
      default: () => {}
    },
    submit: {
      type: Function,
      default: () => {}
    },
    dictType: {
      type: String,
      default: 'BOND_TERM_MARKET_CURVE_RATIO'
    },
    tipsFlag: {
      type: Boolean,
      default: true
    },
    termFlag: {
      type: Boolean,
      default: true
    },
    timeFlag: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      list: [],
      pickTimes: {
        maxDate: '',
        minDate: ''
      },
      checkboxList: [
        { label: '近1年', value: 12 },
        { label: '近6月', value: 6 },
        { label: '近3月', value: 3 },
        { label: '近1月', value: 1 }
      ],
      formOld: {}
    }
  },
  created() {
    this.getDictList()
    this.setFormReset()
  },
  methods: {
    dateChange() {
      this.form.radioDate = ''
    },
    pickerOptions() {
      const _this = this
      return {
        disabledDate(time) {
          const { minDate, maxDate } = _this.pickTimes
          const curDateStr = date(time).format()
          if (minDate) {
            return curDateStr < minDate || curDateStr > maxDate
          }
        },
        onPick(time) {
          const { minDate, maxDate } = time
          if (!maxDate) {
            const minDateStr = date(minDate).format()
            _this.pickTimes.minDate = minDateStr
            _this.pickTimes.maxDate = date(minDateStr).add(1, 'y')
          } else {
            _this.pickTimes.minDate = null
            _this.pickTimes.maxDate = null
          }
        }
      }
    },
    setFormReset() {
      this.$nextTick(() => {
        this.formOld = JSON.parse(JSON.stringify(this.form))
      })
    },
    async getDictList() {
      if (!this.termFlag) return
      const { dictList } = await GetViewData(this.dictType)
      this.list = dictList || []
      this.$emit('dictFn', dictList)
    },
    change(v) {
      if (v === 'daterange') return (this.form.radioDate = null)
      const nowD = date().now()
      switch (this.form.radioDate) {
        case 12:
          this.form.date = [date().subtract(1, 'y'), nowD]
          break
        case 6:
          this.form.date = [date().subtract(6, 'M'), nowD]
          break
        case 3:
          this.form.date = [date().subtract(3, 'M'), nowD]
          break
        case 1:
          this.form.date = [date().subtract(1, 'M'), nowD]
          break

        default:
          break
      }
    },
    checkboxChange() {
      this.checkChange()
    }
  }
}
</script>

<style lang="scss" scoped>
.tips {
  color: #969799;
  font-size: 12px;
  position: absolute;
  top: 50%;
  left: 10px;
  transform: translate(0, -50%);
}

.form-list {
  padding: 8px 16px;
  background: #f6f8fd;
  border-radius: 4px;

  ::v-deep .el-form-item {
    margin-bottom: 8px;

    .el-form-item__label {
      padding-top: 10px;
    }

    .el-form-item__content {
      line-height: 32px;
    }
  }
}

::v-deep .el-range-editor.el-date-editor--daterange.el-input__inner {
  width: 240px !important;
}

.z-row {
  display: flex;
  align-items: center;
}
</style>
<style>
.el-radio input[aria-hidden='true'] {
  display: none !important;
}

.el-radio:focus:not(.is-focus):not(:active):not(.is-disabled) .el-radio__inner {
  box-shadow: none !important;
}
</style>
