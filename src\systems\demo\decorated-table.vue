<template>
  <jr-layout-horizontal left-title="左侧标题">
    <template v-slot:left>
      <div style="margin: 15px 10px 0 0;">
        <el-input v-model="configQuery.ceshi" />
        <br><br>
        <el-button style="width: 100%;" type="primary" @click="handleQuery">触发右侧自定义列数据请求</el-button>
      </div>
    </template>

    <template v-slot:right>
      <!-- 示例自定义列 custom-id 为(交易所费用设置) -->
      <jr-decorated-table
        :date="1662022042"
        :params="params"
        custom-id="72022bed54c74a9ea3dd8576359fde78"
        :row-click="clickFunc"
        :row-dbl-click="dblClickFunc"
        :menuinfo="{moduleid: 'xxxx'}"
      />
    </template>
  </jr-layout-horizontal>
</template>

<script>
export default {
  data() {
    return {
      params: {
        ceshi: ''
      },
      configQuery: {
        ceshi: ''
      }
    }
  },
  methods: {
    handleQuery() {
      this.params.ceshi = this.configQuery.ceshi
    },
    clickFunc(row) {
      console.log(row, 'clickFunc')
    },
    dblClickFunc(row) {
      console.log(row, 'dblClickFunc')
    }
  }
}
</script>
