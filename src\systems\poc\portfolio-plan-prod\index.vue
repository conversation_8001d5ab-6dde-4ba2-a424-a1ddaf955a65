<!--
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023-11-06 15:48:25
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2023-12-05 09:36:00
 * @Description: 产品组合方案
-->
<template>
  <flat-index class="poc-portfolio-plan" :query="handleQuery" :reset="handleReset" :pagination="configTable.pagination">
    <template v-slot:form>
      <el-form ref="elForm" :model="configForm.model">
        <jr-form-item-create :column="0" :data="configForm.data" :model="configForm.model" />
      </el-form>
    </template>

    <template v-slot:right-button>
      <el-button>
        <jr-svg-icon icon-class="export" />
      </el-button>
    </template>

    <template v-slot:table-header-button>
      <el-button type="primary" @click="handleOpen('/portfolio.html?update', '投资组合构建方式')">{{ InitialMessage('common.system.button.add') }}组合</el-button>
      <el-button type="primary" @click="() => $store.dispatch('tagsView/updateCurrentTab', '/PTL_MANAGEMENT_001_003_001')">模拟试算与转指令</el-button>
      <el-button type="primary" @click="handleOpen('/backtest.html', '组合回测', 'full')">回测</el-button>
      <el-button type="primary" @click="handleOpen('/compare.html', '组合对比', 'full')">对比</el-button>
    </template>

    <template v-slot:table-list="{ height }">
      <jr-table
        :height="height"
        :columns="configTable.columns"
        :data-source="configTable.data"
        row-key="名称"
        :expand-row-keys="['净值型', '投资组合1218', '股票多头组合', '货币型']"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
        :loading="configTable.loading"
        :pagination="configTable.pagination"
        :on-change="handleQuery"
        :cell-style="handleCellStyle"
        :row-style="handleRowStyle"
        border
      />
    </template>

    <!-- 详情弹框(全局注册的组件)-->
    <modal-non-process
      ref="modal"
      :size="configModal.size"
      :close-modal="closeModal"
      :save="configModal.save"
      :visible="configModal.visible"
      :modal-type="configModal.modalType"
      :modal-type-name="configModal.modalTypeName"
      :modal-title="configModal.modalTitle"
      :module-id="configModal.moduleId"
      :businessno="configModal.businessno"
      :menuinfo="configModal.menuinfo"
      :item-data="configModal.itemData"
      join-type
    />
  </flat-index>
</template>

<script>
export default {
  props: {
    // 平台日期
    date: {
      type: [Number, String],
      default: null
    },
    // 权限相关
    permitdetail: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    const self = this
    return {
      // 查询区域
      configForm: {
        model: {
          field2: '全部',
          field3: '2022-08-16',
          field5: '1'
        },
        data: [
          {
            title: '组合代码',
            prop: 'field1',
            type: 'select',
            placeholder: '请选择组合',
            options: [
              { text: '1111', value: '1111' },
              { text: '2222', value: '2222' },
              { text: '3333', value: '3333' }
            ]
          },
          {
            title: '收益类型',
            prop: 'field2',
            type: 'select',
            options: [
              { text: '全部', value: '全部' },
              { text: '净值型', value: '净值型' },
              { text: '货币型', value: '货币型' },
              { text: '预期收益型', value: '预期收益型' }
            ]
          },
          {
            title: '查询日期',
            prop: 'field3',
            required: true,
            type: 'date'
          },
          {
            title: '是否穿透',
            prop: 'field5',
            required: true,
            type: 'radio',
            options: [
              { text: '不穿透', value: '0' },
              { text: '穿透', value: '1' }
            ]
          }
        ]
      },
      // 表格数据
      configTable: {
        loading: false,
        columns: [
          '名称',
          '资产一级类型',
          '资产二级类型',
          '目标权重（%）',
          '持仓权重（%）',
          '市值（元）',
          '成本（元）',
          '当日盈亏（元）',
          '累计盈亏（元）',
          '最新净值/价格',
          '净值/价格日期',
          '七日年化收益率（%）',
          '成立以来年化收益率（%）',
          '年化标准差（%）',
          '最大回撤（%）',
          '夏普比率',
          '索提诺比率',
          '构建方式',
          '创建人',
          '创建日期',
          '更新日期'
        ]
          .map((k, index) => {
            return {
              title: k,
              prop: k,
              minWidth: index === 0 ? 200 : index > 4 && index < 9 ? 200 : 120,
              className: index > 3 && index < 9 ? 'linear-cell' : '',
              align: /元|价格|%|比率/.test(k) ? 'right' : 'left'
            }
          })
          .concat({
            title: '操作',
            prop: '操作',
            width: 120,
            fixed: 'right',
            align: 'center',
            render: (h, { row }) => {
              if (row.名称.includes('投资组合')) {
                return (
                  <span class='table-action-box'>
                    <el-tooltip content='调整组合'>
                      <jr-svg-icon icon-class='merge-cells' onClick={self.handleOpen.bind(self, '/portfolio.html?adjust', '调整组合方案')}/>
                    </el-tooltip>
                    <el-tooltip content='调成记录'>
                      <jr-svg-icon icon-class='log' onClick={self.handleOpen.bind(self, '/portfolio.html?log', '调成记录')}/>
                    </el-tooltip>
                    <el-tooltip content='组合再平衡'>
                      <jr-svg-icon icon-class='skill' onClick={self.handleOpen.bind(self, '/portfolio.html?log', '投资组合设置')}/>
                    </el-tooltip>
                  </span>
                )
              }
              return <span />
            }
          }),
        data: [
          {
            '名称': '净值型',
            '资产一级类型': '',
            '资产二级类型': '',
            '目标权重（%）': '',
            '持仓权重（%）': '',
            '市值（元）': '14,888,530,721.36',
            '成本（元）': '13,285,661,609.25',
            '当日盈亏（元）': '200,219,767.50',
            '累计盈亏（元）': '865,634,790.22',
            '最新净值/价格': '',
            '净值/价格日期': '',
            '七日年化收益率（%）': '',
            '成立以来年化收益率（%）': '',
            '年化标准差（%）': '',
            '最大回撤（%）': '',
            '夏普比率': '',
            '索提诺比率': '',
            '构建方式': '',
            '创建人': '',
            '创建日期': '',
            '更新日期': '',
            'children': [
              {
                '名称': '投资组合1218',
                'hasChidren': true,
                '资产一级类型': '',
                '资产二级类型': '',
                '目标权重（%）': '100.00',
                '持仓权重（%）': '100.00',
                '市值（元）': '2,481,074,226.06',
                '成本（元）': '2,354,654,479.70',
                '当日盈亏（元）': '169,392,334.19',
                '累计盈亏（元）': '387,182,573.15',
                '最新净值/价格': '1.14',
                '净值/价格日期': '2022/8/16',
                '七日年化收益率（%）': '5.79',
                '成立以来年化收益率（%）': '5.21',
                '年化标准差（%）': '3.09',
                '最大回撤（%）': '13.09',
                '夏普比率': '0.89',
                '索提诺比率': '1.15',
                '构建方式': '自上而下',
                '创建人': 'zlp',
                '创建日期': '2019/10/9',
                '更新日期': '2022/8/10',
                'children': [
                  {
                    '名称': '股票多头组合',
                    '资产一级类型': '股票权益类策略',
                    'rate': true,
                    '资产二级类型': '量化选股',
                    '目标权重（%）': '50.55',
                    '持仓权重（%）': '40.31',
                    '市值（元）': '1,000,186,760.00',
                    '成本（元）': '992,996,759.94',
                    '当日盈亏（元）': '25,036,312.91',
                    '累计盈亏（元）': '83,098,411.05',
                    '最新净值/价格': '1.12',
                    '净值/价格日期': '2022/8/16',
                    '七日年化收益率（%）': '6.35',
                    '成立以来年化收益率（%）': '5.18',
                    '年化标准差（%）': '10.09',
                    '最大回撤（%）': '15.43',
                    '夏普比率': '1.03',
                    '索提诺比率': '1.29',
                    '构建方式': '',
                    '创建人': '',
                    '创建日期': '',
                    '更新日期': '',
                    'children': [
                      {
                        '名称': '奥泰生物',
                        '资产一级类型': '',
                        '资产二级类型': '',
                        '目标权重（%）': '',
                        '持仓权重（%）': '8.28',
                        '市值（元）': '205,331,110.00',
                        '成本（元）': '204,512,380.75',
                        '当日盈亏（元）': '8,143,873.11',
                        '累计盈亏（元）': '24,431,619.34',
                        '最新净值/价格': '5.51',
                        '净值/价格日期': '2022/8/16',
                        '七日年化收益率（%）': '5.29',
                        '成立以来年化收益率（%）': '4.12',
                        '年化标准差（%）': '11.30',
                        '最大回撤（%）': '8.61',
                        '夏普比率': '1.58',
                        '索提诺比率': '1.61',
                        '构建方式': '',
                        '创建人': '',
                        '创建日期': '',
                        '更新日期': ''
                      },
                      {
                        '名称': '精致达',
                        '资产一级类型': '',
                        '资产二级类型': '',
                        '目标权重（%）': '',
                        '持仓权重（%）': '6.10',
                        '市值（元）': '151,411,550.00',
                        '成本（元）': '150,592,820.75',
                        '当日盈亏（元）': '7,743,873.11',
                        '累计盈亏（元）': '23,231,619.34',
                        '最新净值/价格': '8.82',
                        '净值/价格日期': '2022/8/16',
                        '七日年化收益率（%）': '5.01',
                        '成立以来年化收益率（%）': '3.84',
                        '年化标准差（%）': '15.51',
                        '最大回撤（%）': '10.59',
                        '夏普比率': '0.91',
                        '索提诺比率': '0.93',
                        '构建方式': '',
                        '创建人': '',
                        '创建日期': '',
                        '更新日期': ''
                      },
                      {
                        '名称': '云南城投',
                        '资产一级类型': '',
                        '资产二级类型': '',
                        '目标权重（%）': '',
                        '持仓权重（%）': '5.86',
                        '市值（元）': '145,333,800.00',
                        '成本（元）': '135,435,070.75',
                        '当日盈亏（元）': '7,643,873.11',
                        '累计盈亏（元）': '22,931,619.34',
                        '最新净值/价格': '9.21',
                        '净值/价格日期': '2022/8/16',
                        '七日年化收益率（%）': '4.89',
                        '成立以来年化收益率（%）': '3.72',
                        '年化标准差（%）': '10.01',
                        '最大回撤（%）': '9.73',
                        '夏普比率': '0.85',
                        '索提诺比率': '0.69',
                        '构建方式': '',
                        '创建人': '',
                        '创建日期': '',
                        '更新日期': ''
                      },
                      {
                        '名称': '康尼机电',
                        '资产一级类型': '',
                        '资产二级类型': '',
                        '目标权重（%）': '',
                        '持仓权重（%）': '5.41',
                        '市值（元）': '134,313,100.00',
                        '成本（元）': '140,201,829.25',
                        '当日盈亏（元）': '-9,384,794.89',
                        '累计盈亏（元）': '-9,381,794.89',
                        '最新净值/价格': '15.55',
                        '净值/价格日期': '2022/8/16',
                        '七日年化收益率（%）': '-2.01',
                        '成立以来年化收益率（%）': '1.16',
                        '年化标准差（%）': '16.22',
                        '最大回撤（%）': '15.71',
                        '夏普比率': '0.13',
                        '索提诺比率': '0.52',
                        '构建方式': '',
                        '创建人': '',
                        '创建日期': '',
                        '更新日期': ''
                      },
                      {
                        '名称': '津投诚开',
                        '资产一级类型': '',
                        '资产二级类型': '',
                        '目标权重（%）': '',
                        '持仓权重（%）': '4.84',
                        '市值（元）': '120,114,100.00',
                        '成本（元）': '122,332,829.19',
                        '当日盈亏（元）': '-5,384,794.89',
                        '累计盈亏（元）': '-10,760,308.77',
                        '最新净值/价格': '9.28',
                        '净值/价格日期': '2022/8/16',
                        '七日年化收益率（%）': '-2.85',
                        '成立以来年化收益率（%）': '-0.68',
                        '年化标准差（%）': '17.43',
                        '最大回撤（%）': '21.29',
                        '夏普比率': '-0.25',
                        '索提诺比率': '-0.13',
                        '构建方式': '',
                        '创建人': '',
                        '创建日期': '',
                        '更新日期': ''
                      },
                      {
                        '名称': '江苏吴中',
                        '资产一级类型': '',
                        '资产二级类型': '',
                        '目标权重（%）': '',
                        '持仓权重（%）': '4.17',
                        '市值（元）': '103,534,910.00',
                        '成本（元）': '105,653,639.25',
                        '当日盈亏（元）': '-884,794.89',
                        '累计盈亏（元）': '-1,676,661.77',
                        '最新净值/价格': '10.85',
                        '净值/价格日期': '2022/8/16',
                        '七日年化收益率（%）': '-1.69',
                        '成立以来年化收益率（%）': '-3.86',
                        '年化标准差（%）': '18.64',
                        '最大回撤（%）': '25.43',
                        '夏普比率': '0.08',
                        '索提诺比率': '0.12',
                        '构建方式': '',
                        '创建人': '',
                        '创建日期': '',
                        '更新日期': ''
                      },
                      {
                        '名称': '腾龙股份',
                        '资产一级类型': '',
                        '资产二级类型': '',
                        '目标权重（%）': '',
                        '持仓权重（%）': '3.32',
                        '市值（元）': '82,414,520.00',
                        '成本（元）': '84,533,249.25',
                        '当日盈亏（元）': '-384,794.89',
                        '累计盈亏（元）': '-766,658.77',
                        '最新净值/价格': '20.55',
                        '净值/价格日期': '2022/8/16',
                        '七日年化收益率（%）': '1.09',
                        '成立以来年化收益率（%）': '-4.08',
                        '年化标准差（%）': '8.89',
                        '最大回撤（%）': '8.51',
                        '夏普比率': '0.05',
                        '索提诺比率': '0.21',
                        '构建方式': '',
                        '创建人': '',
                        '创建日期': '',
                        '更新日期': ''
                      },
                      {
                        '名称': '泉阳泉',
                        '资产一级类型': '',
                        '资产二级类型': '',
                        '目标权重（%）': '',
                        '持仓权重（%）': '2.33',
                        '市值（元）': '57,733,670.00',
                        '成本（元）': '49,734,940.75',
                        '当日盈亏（元）': '17,543,873.11',
                        '累计盈亏（元）': '35,088,977.23',
                        '最新净值/价格': '19.55',
                        '净值/价格日期': '2022/8/16',
                        '七日年化收益率（%）': '10.93',
                        '成立以来年化收益率（%）': '7.76',
                        '年化标准差（%）': '10.10',
                        '最大回撤（%）': '9.03',
                        '夏普比率': '1.79',
                        '索提诺比率': '1.81',
                        '构建方式': '',
                        '创建人': '',
                        '创建日期': '',
                        '更新日期': ''
                      }
                    ]
                  },
                  {
                    '名称': '信用债优选策略',
                    'children': [{ '名称': '12229' }],
                    'rate': true,
                    '资产一级类型': '固收类策略',
                    '资产二级类型': '债券优选',
                    '目标权重（%）': '30.4',
                    '持仓权重（%）': '40.29',
                    '市值（元）': '999,579,586.41',
                    '成本（元）': '900,289,713.26',
                    '当日盈亏（元）': '99,928,810.64',
                    '累计盈亏（元）': '199,858,852.28',
                    '最新净值/价格': '1.00',
                    '净值/价格日期': '2022/8/16',
                    '七日年化收益率（%）': '4.19',
                    '成立以来年化收益率（%）': '3.02',
                    '年化标准差（%）': '4.36',
                    '最大回撤（%）': '6.28',
                    '夏普比率': '0.69',
                    '索提诺比率': '0.75',
                    '构建方式': '',
                    '创建人': '',
                    '创建日期': '',
                    '更新日期': ''
                  },
                  {
                    '名称': '外贸信托产品',
                    '资产一级类型': '资管产品',
                    '资产二级类型': '标品信托',
                    'children': [{ '名称': '122210' }],
                    'rate': true,
                    '目标权重（%）': '19.05',
                    '持仓权重（%）': '17.91',
                    '市值（元）': '444,463,586.41',
                    '成本（元）': '424,523,713.26',
                    '当日盈亏（元）': '44,427,210.64',
                    '累计盈亏（元）': '88,855,652.28',
                    '最新净值/价格': '1.00',
                    '净值/价格日期': '2022/8/16',
                    '七日年化收益率（%）': '4.91',
                    '成立以来年化收益率（%）': '3.86',
                    '年化标准差（%）': '1.09',
                    '最大回撤（%）': '5.39',
                    '夏普比率': '0.73',
                    '索提诺比率': '0.86',
                    '构建方式': '',
                    '创建人': '',
                    '创建日期': '',
                    '更新日期': ''
                  },
                  {
                    '名称': '现金',
                    '资产一级类型': '现金',
                    '资产二级类型': '现金',
                    // 'children': [{ '名称': '122211' }],
                    'rate': true,
                    '目标权重（%）': '0',
                    '持仓权重（%）': '1.49',
                    '市值（元）': '36,844,293.24',
                    '成本（元）': '36,844,293.24',
                    '当日盈亏（元）': '',
                    '累计盈亏（元）': '',
                    '最新净值/价格': '',
                    '净值/价格日期': '',
                    '七日年化收益率（%）': '',
                    '成立以来年化收益率（%）': '',
                    '年化标准差（%）': '',
                    '最大回撤（%）': '',
                    '夏普比率': '',
                    '索提诺比率': '',
                    '构建方式': '',
                    '创建人': '',
                    '创建日期': '',
                    '更新日期': ''
                  }
                ]
              },

              {
                '名称': '投资组合1021',
                'children': [{ '名称': '1222' }],
                '资产一级类型': '',
                '资产二级类型': '',
                '目标权重（%）': '100.00',
                '持仓权重（%）': '100.00',
                '市值（元）': '2,480,744,355.06',
                '成本（元）': '2,381,454,481.91',
                '当日盈亏（元）': '2,047,604.22',
                '累计盈亏（元）': '34,819,609.06',
                '最新净值/价格': '1.11',
                '净值/价格日期': '2022/8/16',
                '七日年化收益率（%）': '2.53',
                '成立以来年化收益率（%）': '1.36',
                '年化标准差（%）': '2.89',
                '最大回撤（%）': '5.03',
                '夏普比率': '0.52',
                '索提诺比率': '0.58',
                '构建方式': '自上而下',
                '创建人': 'zlp',
                '创建日期': '2020/1/21',
                '更新日期': '2022/8/9'
              },
              {
                '名称': '投资组合1022',
                'children': [{ '名称': '12223' }],
                '资产一级类型': '',
                '资产二级类型': '',
                '目标权重（%）': '100.00',
                '持仓权重（%）': '100.00',
                '市值（元）': '2,480,087,007.06',
                '成本（元）': '2,280,797,133.91',
                '当日盈亏（元）': '6,093,582.78',
                '累计盈亏（元）': '101,352,189.88',
                '最新净值/价格': '1.11',
                '净值/价格日期': '2022/8/16',
                '七日年化收益率（%）': '2.68',
                '成立以来年化收益率（%）': '5.90',
                '年化标准差（%）': '5.79',
                '最大回撤（%）': '9.01',
                '夏普比率': '0.83',
                '索提诺比率': '0.85',
                '构建方式': '自上而下',
                '创建人': 'zlp',
                '创建日期': '2020/4/13',
                '更新日期': '2022/8/10'
              },
              {
                '名称': '投资组合1229',
                'children': [{ '名称': '12224' }],
                '资产一级类型': '',
                '资产二级类型': '',
                '目标权重（%）': '100.00',
                '持仓权重（%）': '100.00',
                '市值（元）': '2,483,372,356.06',
                '成本（元）': '1,984,082,482.91',
                '当日盈亏（元）': '11,440,666.17',
                '累计盈亏（元）': '171,395,457.68',
                '最新净值/价格': '1.11',
                '净值/价格日期': '2022/8/16',
                '七日年化收益率（%）': '3.05',
                '成立以来年化收益率（%）': '7.22',
                '年化标准差（%）': '7.01',
                '最大回撤（%）': '7.57',
                '夏普比率': '0.98',
                '索提诺比率': '0.99',
                '构建方式': '自上而下',
                '创建人': 'zlp',
                '创建日期': '2020/4/16',
                '更新日期': '2022/8/3'
              },
              {
                '名称': '投资组合4',
                'children': [{ '名称': '12225' }],
                '资产一级类型': '',
                '资产二级类型': '',
                '目标权重（%）': '100.00',
                '持仓权重（%）': '100.00',
                '市值（元）': '2,481,984,610.06',
                '成本（元）': '1,942,694,736.91',
                '当日盈亏（元）': '11,083,895.32',
                '累计盈亏（元）': '168,444,355.44',
                '最新净值/价格': '1.11',
                '净值/价格日期': '2022/8/16',
                '七日年化收益率（%）': '3.01',
                '成立以来年化收益率（%）': '6.89',
                '年化标准差（%）': '10.93',
                '最大回撤（%）': '11.04',
                '夏普比率': '0.91',
                '索提诺比率': '1.07',
                '构建方式': '自下而上',
                '创建人': 'zlp',
                '创建日期': '2020/4/10',
                '更新日期': '2022/8/12'
              },
              {
                '名称': '投资组合5',
                'children': [{ '名称': '12226' }],
                '资产一级类型': '',
                '资产二级类型': '',
                '目标权重（%）': '100.00',
                '持仓权重（%）': '100.00',
                '市值（元）': '2,481,268,167.06',
                '成本（元）': '2,341,978,293.91',
                '当日盈亏（元）': '161,684.82',
                '累计盈亏（元）': '2,440,605.01',
                '最新净值/价格': '1.04',
                '净值/价格日期': '2022/8/16',
                '七日年化收益率（%）': '1.43',
                '成立以来年化收益率（%）': '6.60',
                '年化标准差（%）': '25.65',
                '最大回撤（%）': '21.36',
                '夏普比率': '0.51',
                '索提诺比率': '0.68',
                '构建方式': '自下而上',
                '创建人': 'zlp',
                '创建日期': '2021/9/16',
                '更新日期': '2022/8/1'
              }
            ]
          },
          {
            '名称': '货币型',
            '资产一级类型': '',
            '资产二级类型': '',
            '目标权重（%）': '',
            '持仓权重（%）': '',
            '市值（元）': '',
            '成本（元）': '',
            '当日盈亏（元）': '',
            '累计盈亏（元）': '',
            '最新净值/价格': '',
            '净值/价格日期': '',
            '七日年化收益率（%）': '',
            '成立以来年化收益率（%）': '',
            '年化标准差（%）': '',
            '最大回撤（%）': '',
            '夏普比率': '',
            '索提诺比率': '',
            '构建方式': '',
            '创建人': '',
            '创建日期': '',
            '更新日期': '',
            'children': [
              {
                '名称': '投资组合49',
                'children': [{ '名称': '12227' }],
                '资产一级类型': '',
                '资产二级类型': '',
                '目标权重（%）': '100.00',
                '持仓权重（%）': '100.00',
                '市值（元）': '182,018,935.91',
                '成本（元）': '181,734,592.91',
                '当日盈亏（元）': '127,151.82',
                '累计盈亏（元）': '349,243.46',
                '最新净值/价格': '1.00',
                '净值/价格日期': '2022/8/16',
                '七日年化收益率（%）': '2.39',
                '成立以来年化收益率（%）': '2.46',
                '年化标准差（%）': '1.68',
                '最大回撤（%）': '',
                '夏普比率': '1.51',
                '索提诺比率': '1.53',
                '构建方式': '自下而上',
                '创建人': 'zlp',
                '创建日期': '2022/8/1',
                '更新日期': '2022/8/12'
              },
              {
                '名称': '投资组合77',
                'children': [{ '名称': '12228' }],
                '资产一级类型': '',
                '资产二级类型': '',
                '目标权重（%）': '100.00',
                '持仓权重（%）': '100.00',
                '市值（元）': '140,643,199.91',
                '成本（元）': '140,346,846.91',
                '当日盈亏（元）': '67,841.82',
                '累计盈亏（元）': '171,313.46',
                '最新净值/价格': '1.00',
                '净值/价格日期': '2022/8/16',
                '七日年化收益率（%）': '2.05',
                '成立以来年化收益率（%）': '2.28',
                '年化标准差（%）': '1.91',
                '最大回撤（%）': '',
                '夏普比率': '1.32',
                '索提诺比率': '1.69',
                '构建方式': '自下而上',
                '创建人': 'zlp',
                '创建日期': '2022/7/21',
                '更新日期': '2022/8/9'
              }
            ]
          }
        ],
        pagination: {
          pageNo: 1,
          pageSize: window.screen.height > 1000 ? 25 : 15,
          pageSizeOptions: [15, 25, 50, 100],
          total: 0,
          showSizeChanger: true
        }
      },
      // 弹框配置项
      configModal: {
        save: '/invest/basicdata/mstcommissionset/MstCommissionSet002', // 初始化 init 的接口地址，必传(这里随便写的，开发需填写真实接口地址)
        visible: false,
        itemData: {},
        // businessno: DICT.SYSCONSTANT.BUSINSSNO.SYSTEMADJUSTDEAL,
        modalTitle: '投资组合构建方式',
        modalType: 'iframe',
        modalTypeName: '',
        size: 'normal',
        moduleId: '',
        menuinfo: {
          componenturl: location.origin + '/portfolio.html?update'
        }
      }
    }
  },
  methods: {
    handleOpen(page, title, size = 'normal') {
      Object.assign(this.configModal, {
        visible: true,
        modalTitle: title,
        size,
        menuinfo: {
          componenturl: location.origin + page
        }
      })
    },
    handleRowStyle({ row }) {
      if (row['名称'].includes('投资组合')) {
        return {
          fontWeight: 'bold'
        }
      }
    },
    handleCellStyle({ row, column }) {
      const prop = column.columnKey
      const val = (typeof row[prop] === 'string' ? row[prop] : '').replace(/,/g, '')
      if (!val) return {}
      if (prop === '持仓权重（%）' && row.rate) {
        return {
          '--wdh': Number(val) + '%',
          '--bg': 'linear-gradient(90deg, #70ade9, transparent)'
        }
      }
      if (['市值（元）',
        '成本（元）',
        '当日盈亏（元）',
        '累计盈亏（元）'].includes(prop)) {
        const maxMap = {
          '市值（元）': '14888530721.36',
          '成本（元）': '13285661609.25',
          '当日盈亏（元）': '200219767.50',
          '累计盈亏（元）': '865634790.22'
        }
        const ratio = parseInt(Number(val) / Number(maxMap[prop]) * 100)
        return {
          '--wdh': Math.abs(ratio) + '%',
          '--left': ratio > 0 ? '8%' : 'unset',
          '--right': ratio > 0 ? 'unset' : '92%',
          '--bg': val > 0 ? 'linear-gradient(90deg, #57bf57, transparent)' : 'linear-gradient(90deg, transparent, red)'
        }
      }
      if (
        [
          '七日年化收益率（%）',
          '成立以来年化收益率（%）',
          '年化标准差（%）',
          '最大回撤（%）',
          '夏普比率',
          '索提诺比率'
        ].includes(prop)
      ) {
        return Number(val) < 0
          ? {
            color: 'red'
          }
          : ''
      }
      return {}
    },
    closeModal() {
      this.configModal.visible = false
    },
    // 查询
    handleQuery() {},
    // 重置
    handleReset() {}
  }
}
</script>

<style lang="scss">
.poc-portfolio-plan {
  .linear-cell {
    .cell::before {
      content: ' ';
      height: 14px;
      margin-top: 3px;
      width: var(--wdh);
      background: var(--bg);
      position: absolute;
      left: var(--left);
      right: var(--right);
    }
  }
}
</style>
