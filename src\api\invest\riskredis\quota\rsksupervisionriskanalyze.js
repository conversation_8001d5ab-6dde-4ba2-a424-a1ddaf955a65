// 监控值查询
import { GetListInfo, ExportFn } from '@jupiterweb/utils/api'
import request from '@jupiterweb/utils/request'
// 查询监控值明细信息
export const getInfoDetailList = params => request.post('/invest/riskredis/quota/rsksupervisionriskanalyze/RskSupervisionRiskAnalyze002/findPage', JSON.stringify(params))
// 查询连续违规列表数据
export const getViolationList = params => request.post('/invest/riskredis/quota/rsksupervisionriskanalyze/RskSupervisionRiskAnalyze003/findPage', JSON.stringify(params))
// 查询资产持仓明细数据
export const getHoldDetailList = params => request.post('/invest/riskredis/quota/rsksupervisionriskanalyze/RskSupervisionRiskAnalyze004/findPage', JSON.stringify(params))
// 获取字典数据
export const GetDict = params => GetListInfo('/framework/dictionary/select', params)
// 监控值明细信息导出
export const InfoDetailExport = params => ExportFn('/invest/riskredis/quota/rsksupervisionriskanalyze/RskSupervisionRiskAnalyze002/exportMx', params)
// 资产持仓明细导出
export const holdDetailExport = params => ExportFn('/invest/riskredis/quota/rsksupervisionriskanalyze/RskSupervisionRiskAnalyze004/exportNx', params)
// 连续违规明细导出
export const violationDetailExport = params => ExportFn('/invest/riskredis/quota/rsksupervisionriskanalyze/RskSupervisionRiskAnalyze003/exportFu', params)
