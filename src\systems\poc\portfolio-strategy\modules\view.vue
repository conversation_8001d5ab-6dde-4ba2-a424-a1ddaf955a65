<!--
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023-10-25 10:54:01
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2023-10-27 13:38:44
 * @Description: 概览
-->
<template>
  <article>
    <div class="header">
      <span class="title">概览</span>
      <el-radio-group v-model="combo" size="mini">
        <el-radio-button v-for="com in comboxList" :key="com" :label="com" />
      </el-radio-group>
    </div>
    <div class="body">
      <section v-for="data in dataList" :key="data.text">
        <span>{{ data.value }}</span>
        <p>{{ data.text }}</p>
      </section>
    </div>
  </article>
</template>

<script>
export default {
  data() {
    return {
      combo: '整体',
      comboxList: ['整体', '固收', '权益', '混合'],
      dataList: [
        {
          value: 199.99,
          text: '资产净值'
        },
        {
          value: 194.78,
          text: '投资成本'
        },
        {
          value: -2.5,
          text: '累计收益'
        },
        {
          value: 3.6,
          text: '权益占比'
        },
        {
          value: '-1.29',
          text: '累计收益率(%)'
        },
        {
          value: '-1.40',
          text: '年化收益率(%)'
        },
        {
          value: '5.15',
          text: '年化波动率(%)'
        },
        {
          value: '-1.69',
          text: '最大回撤'
        }
      ]
    }
  }
}
</script>

<style lang="scss" scoped>
// .header {}
.body {
  display: grid;
  grid-template-columns: repeat(4, 25%);
  section {
    text-align: center;
    padding-top: 24px;
    span {
      font-size: 30px;
      line-height: 60px;
      color: var(--theme--color);
    }
    p {
      font-weight: bold;
    }
  }
}
</style>
