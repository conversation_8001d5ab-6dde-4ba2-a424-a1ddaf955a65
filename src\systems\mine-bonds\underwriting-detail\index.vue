// 承销明细
<template>
  <div class="underwriting-detail">
    <jr-layout-vertical :height="48" disabled>
      <template v-slot:top>
        <el-form inline :model="form" class="underwriting-detail-form" label-width="80">
          <jr-form-item v-sysversion="'group'" label="发行人">
            <jr-combobox
              v-model="form.issuerId"
              style="max-width: 285px"
              placeholder="请选择"
              clearable
              filterable
              :data="peopleList"
              option-value="value"
              option-label="text"
            />
          </jr-form-item>
          <jr-form-item label="债券类型">
            <SelectAutoSetCollapseTages
              style="max-width: 285px"
              :options="bondTypeList"
              placeholder="请选择债券类型"
              @emitConfirmData="setBondTypes"
            />
          </jr-form-item>
          <jr-form-item label="发布日期">
            <el-date-picker
              v-model="form.s_lu_issuedate"
              style="max-width: 285px"
              type="daterange"
              range-separator="至"
              unlink-panels
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              class="date-picker"
            />
          </jr-form-item>
          <jr-form-item label="机构类型">
            <jr-combobox
              v-model="form.institutionType"
              style="max-width: 285px"
              placeholder="请选择"
              clearable
              multiple
              filterable
              collapse-tags
              :data="companyTypeList"
            />
          </jr-form-item>
          <jr-form-item>
            <el-button type="primary" @click="submit">查询</el-button>
          </jr-form-item>
        </el-form>
      </template>

      <template v-slot:bottom>
        <div class="underwriting-detail-content">
          <div class="underwriting-detail-content-item he500">
            <!--  左侧表格-->
            <div class="underwriting-detail-content-item-left">
              <div class="underwriting-detail-content-item-top">
                <span class="underwriting-detail-content-item-top-title">
                  <span class="underwriting-detail-content-item-top-title-tip" />
                  主承排名
                </span>
              </div>
              <div class="underwriting-detail-content-item-table">
                <jr-table
                  stripe
                  :border="false"
                  :data-source="configTable.data"
                  :loading="configTable.loading"
                  :span-method="objectSpanMethod"
                >
                  <el-table-column prop="ranking" label="排名" width="64" align="center">
                    <template #default="{ row }">
                      <span class="ranking" :style="{ color: row.ranking <= 3 ? '#FF8E2B' : '' }">
                        <template v-if="row.ranking <= 3">
                          <img
                            :src="require(`@/assets/images/rank${row.ranking}@2x.png`)"
                            style="width: 18px; height: 22px"
                          >
                        </template>
                        <template v-else>
                          <span style="width: 18px; height: 22px; display: inline-block; text-align: center">
                            {{ row.ranking }}
                          </span>
                        </template>
                      </span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="sLuName" label="承销商名称" />
                  <el-table-column prop="netCollection" align="right" label="总承销份额（亿元）" width="164" />
                  <el-table-column prop="cnt" label="只数" width="60" align="center" />
                </jr-table>
              </div>
            </div>
            <!-- 右侧图表 -->
            <div class="underwriting-detail-content-item-right">
              <div class="underwriting-detail-content-item-top just-end">
                <el-dropdown trigger="click" @command="handleCommand">
                  <el-button>
                    <jr-svg-icon class="el-icon--right" icon-class="upload" />
                    导出
                    <i class="el-icon-caret-bottom" />
                  </el-button>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item command="tp">
                      <jr-svg-icon icon-class="picture" />
                      导出图片
                    </el-dropdown-item>
                    <el-dropdown-item command="bg">
                      <jr-svg-icon icon-class="table" />
                      导出表格
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
              </div>
              <!-- template-module -->
              <template-module
                chart-seq="2ca9ebe151104a0abdfdd512cfe47a86"
                :height="508"
                :params="chartParams"
                :callback="chartCallback"
                :custom-options="customOptions"
              />
            </div>
          </div>
          <div class="underwriting-detail-content-item no-flex mt-42 he640">
            <div class="underwriting-detail-content-item-top">
              <span class="underwriting-detail-content-item-top-title">
                <span class="underwriting-detail-content-item-top-title-tip" />
                承销明细
              </span>
            </div>
            <el-row>
              <el-form inline :model="detailForm" class="underwriting-detail-form" label-width="68">
                <jr-form-item label="关键字">
                  <el-input
                    v-model="detailForm.name"
                    clearable
                    style="max-width: 285px"
                    placeholder="请输入承销商名称/债券简称"
                  />
                </jr-form-item>
                <jr-form-item label="债券类型">
                  <SelectAutoSetCollapseTages
                    style="max-width: 285px"
                    :options="bondTypeList"
                    placeholder="请选择债券类型"
                    @emitConfirmData="setDeatilBondTypes"
                  />
                </jr-form-item>
                <jr-form-item>
                  <el-button type="primary" style="margin-right: 10px" @click="searchDetail">查 询</el-button>
                </jr-form-item>
              </el-form>
            </el-row>
            <div class="underwriting-detail-content-item-table">
              <!-- 接自定义列表 tableId换成自己的id -->
              <jr-decorated-table
                stripe
                :menuinfo="{ moduleid: '1352320216532336640' }"
                :params="detailTableParams"
                custom-id="a57acc4187e4464daf83c00d4bdafec3"
                :default-page-size="10"
                :permitdetail="{...permitdetail, export: { icon: 'upload', name: '导出', componenturl: 'export', btnkey: 'export' }}"
                :handleexport="exportData"
                @refreshed="callFn"
              />
              <span class="underwriting-detail-tips">
                <jr-svg-icon icon-class="info-circle" />
                中债隐含评级来源于债券的隐含评级（剔除ABS、ABN），也同时代表了主体的隐含评级。
              </span>
            </div>
          </div>
          <div class="he16" />
        </div>
      </template>
    </jr-layout-vertical>
  </div>
</template>
<script>
import {
  exportBondRatingInfo,
  queryBondRank,
  queryBondTypeByIssuerCode,
  queryBondRatingAgency
} from '@/api/bonds/bonds'
import { GetComboboxList, exportExcelByModule } from '@/api/home'
import templateModule from '@/components/template-module'
import SelectAutoSetCollapseTages from '@/components/selectAutoSetCollapseTages'
export default {
  name: 'AssociatedEnterprise',
  components: { templateModule, SelectAutoSetCollapseTages },
  props: {
    permitdetail: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      allBond: false,
      columns: [],
      // 自定义列id
      tableId: '9784744570374c0abd53ea0e6e28d968',
      // 菜单id
      ownedModuleid: '1352320268273270784',
      configTable: {
        loading: false,
        search: '',
        data: []
      },
      // s_lu_issuedate
      form: {
        issuerId: null, // 发行人id
        bondTypes: [],
        s_lu_issuedate: [new Date().getFullYear() + '-01-01', new Date().getFullYear() + '-12-31'],
        // s_lu_issuedate: [moment().subtract(1, 'years').format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')],
        institutionType: [] // 机构类型
      },
      peopleList: [],
      bondTypeList: [],
      companyTypeList: [],
      // 这块默认查一年？
      chartParams: {
        issueStartDate: new Date().getFullYear() + '-01-01',
        issueEndDate: new Date().getFullYear() + '-12-31'
      },
      customOptions: {
        grid: {
          top: '56',
          left: '16',
          right: '24',
          bottom: '24'
        },
        toolbox: {
          show: false
        },
        legend: {
          show: false
        },
        // 设置柱状图的颜色 为渐变色 linear-gradient( 180deg, #FFCC74 0%, #FFAE68 100%);
        color: [
          {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 1,
            y2: 0,
            colorStops: [
              {
                offset: 0,
                color: '#FFCC74'
              },
              {
                offset: 1,
                color: '#FFAE68'
              }
            ]
          }
        ],

        // 设置y轴的线为虚线
        yAxis: [
          {
            splitLine: {
              lineStyle: {
                type: 'dashed' // 设置y轴网格线为虚线
              }
            }
          }
        ],
        // 设置x轴颜色color: rgba(0,0,0,0.45);
        xAxis: [
          {
            axisLabel: {
              color: 'rgba(0,0,0,0.45)', // 设置x轴标签颜色
              overflow: 'truncate', // 文字溢出时截断
              ellipsis: '...', // 显示省略号
              width: 90, // 设置标签宽度
              interval: 0, // 强制显示所有标签
              rotate: 45 // 设置标签旋
            },
            axisLine: {
              lineStyle: {
                color: 'rgba(0,0,0,0.45)' // 设置x轴颜色
              }
            },
            axisTick: {
              alignWithLabel: true // 保证刻度线和标签对齐
            },
            name: ''
          }
        ]
      },
      customRender: {
        ranking: (h, { row: { ranking }}) => {
          return ranking <= 3
            ? h('img', {
              attrs: { src: require(`@/assets/images/rank${ranking}@2x.png`) },
              style: { width: '18px', height: '22px' }
            })
            : h('span', ranking)
        }
      },
      //  承销明细查询项目
      detailForm: {
        name: '',
        bondTypes: ''
      },
      detailTableParams: {
        ccid: 'a57acc4187e4464daf83c00d4bdafec3',
        ownedModuleid: '1352320216532336640',
        name: '',
        bondTypes: ''
      },
      myChart: null,
      pngName: '承销明细-主承排名', // 图片名称
      issuerCodes: [] // 发行人id集合 -先写死后续改动动态
    }
  },
  created() {
    const sysVersion = localStorage.getItem('sysVersion')
    if (sysVersion === 'group' && this.$store.getters.personInfo.outCompCode) {
      this.chartParams.issuerId = [this.$store.getters.personInfo.outCompCode]
    }
    this.getBondIssuerInfo()
    this.getComboboxList()
    this.getBondRank()
    this.getBondTypes()
  },
  methods: {
    submit() {
      const obj = {
        issuerId: this.form.issuerId ? [this.form.issuerId] : [],
        bondTypes: this.form.bondTypes,
        // s_lu_issuedate: this.form.s_lu_issuedate,
        institutionType: this.form.institutionType
      }
      if (Array.isArray(this.form.s_lu_issuedate) && this.form.s_lu_issuedate.length > 0) {
        obj.issueStartDate = this.form.s_lu_issuedate[0]
        obj.issueEndDate = this.form.s_lu_issuedate[1]
      } else {
        obj.issueStartDate = ''
        obj.issueEndDate = ''
      }
      this.chartParams = {
        ...this.chartParams,
        ...obj
      }
      this.getBondRank()
    },
    async exportData() {
      const params = {
        sort: null,
        direction: null,
        filename: '承销明细_承销明细',
        title: '承销明细',
        params: {
          ownedModuleid: this.ownedModuleid,
          ccid: this.tableId,
          ...this.detailTableParams
        },
        page: -1,
        count: 15,
        column: this.columns,
        total: 0
      }
      await exportBondRatingInfo(params)
    },
    callFn(data) {
      // 获取列表的列
      this.columns = data.config.columns
    },
    chartCallback(refData) {
      this.myChart = refData
    },
    // 承销明细查询
    searchDetail() {
      this.detailTableParams = { ...this.detailTableParams, ...this.detailForm }
    },
    // 查询字典
    async getComboboxList() {
      const data = await GetComboboxList(['LUTYPE'])
      if (data && data.LUTYPE) {
        this.companyTypeList = data.LUTYPE
      }
    },
    // 查询主承排名
    async getBondRank() {
      this.configTable.loading = true
      const res = await queryBondRank({
        ...this.chartParams
      })
      this.configTable.loading = false
      this.configTable.data = res
    },
    // 导出下拉菜单点击事件
    handleCommand(type) {
      if (type === 'tp') {
        // 下载图片
        const exportOptions = {
          backgroundColor: '#ffffff',
          pixelRatio: 2 // 提高导出清晰度（可选）
        }
        // // 生成图片 URL 并触发下载
        const chart = this.myChart
        const imgUrl = chart.getDataURL(exportOptions)
        const link = document.createElement('a')
        link.href = imgUrl
        link.download = `${this.pngName}.png`
        link.click()
      } else {
        const params = {
          ...this.chartParams,
          chartSeq: '2ca9ebe151104a0abdfdd512cfe47a86'
        }
        exportExcelByModule(params).then((res) => {})
      }
    },
    // 合并单元格
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0 || columnIndex === 1) {
        if (row.ranking === null && (row.isOther || row.isTotal)) {
          // 合并排名和承销商两列
          return {
            rowspan: 1,
            colspan: columnIndex === 0 ? 0 : 2
          }
        } else {
          // 正常显示
          return {
            rowspan: 1,
            colspan: 1
          }
        }
      }
    },
    async getBondIssuerInfo() {
      const data = {
        text: ''
      }
      const res = await queryBondRatingAgency(data)
      const list = []
      for (const i of res) {
        if (i) {
          list.push(i)
        }
      }
      this.peopleList = list
      const sysVersion = localStorage.getItem('sysVersion')
      if (sysVersion === 'group' && this.$store.getters.personInfo.outCompCode) {
        this.form.issuerId = this.$store.getters.personInfo.outCompCode
      }
    },
    // 查询债券类型
    async getBondTypes() {
      const params = this.issuerCodes
      const res = await queryBondTypeByIssuerCode(params)
      this.bondTypeList = res.reduce((pre, current) => {
        if (current) {
          const index = pre.findIndex((item) => item.value === current.bondTypeCode)
          if (index === -1) {
            pre.push({
              label: current.bondTypeName,
              value: current.bondTypeCode,
              children: []
            })
          } else {
            pre[index].children.push({
              label: current.bondTypeName2,
              value: current.bondTypeName2
            })
          }
        }
        return pre
      }, [])
    },
    setBondTypes(data) {
      this.form.bondTypes = data
    },
    setDeatilBondTypes(data) {
      this.detailForm.bondTypes = data
    }
  }
}
</script>
<style lang="scss">
.underwriting-detail {
  height: 100%;

  .vertical-layout {
    background: #fff;
    padding: 0;

    &--top-content {
      padding: 0 16px;
    }

    &--resize {
      height: 1px;
      color: #eae9e9;
    }

    &--bottom {
      margin-top: 16px;
      padding: 0 16px;
    }
  }

  &-form {
    display: flex;
    align-items: center;
    padding-top: 16px;

    .el-form-item {
      width: 100%;
      max-width: 352px;

      .el-form-item__label {
        padding: 11px 8px 0 0;
      }

      .el-form-item__content {
        width: calc(100% - 68px);

        .el-button {
          margin-left: 16px;
        }
      }
    }
  }

  &-content {
    height: 100%;

    &-item {
      display: flex;
      justify-content: space-between;
      padding: 0 16px 0;
      border-radius: 4px;
      border: 1px solid #eae9e9;

      &-left {
        width: 580px;
        padding: 21px 20px 0 0;
        border-right: 1px solid #eae9e9;
      }

      &-right {
        flex: 1;
        padding: 21px 0 0 16px;
      }

      &-top {
        display: flex;
        justify-content: space-between;
        align-items: center;

        &-title {
          font-size: var(--el-font-size-medium);
          line-height: var(--el-font-size-medium);
          color: rgba(0, 0, 0, 0.85);
          font-weight: 600;
          display: flex;
          align-items: center;

          &-tip {
            display: inline-block;
            width: 3px;
            height: 16px;
            background-color: #ff8e2b;
            margin-right: 13px;
          }
        }
      }

      .just-end {
        justify-content: flex-end;

        .el-button {
          padding: 0 !important;
          width: 92px;
          height: 32px;
          color: rgba(0, 0, 0, 0.9);
        }
      }

      &-table {
        position: relative;
        padding-top: 20px;
        height: calc(100% - 30px);
        
        .jr-decorated-table--header {
          position: absolute;
          top: -40px;
          right: 0;

          .jr-decorated-table--header-left,
          .jr-decorated-table--header-right {
            width: auto;
          }
        }

        .jr-decorated-table--body {
          padding: 0;
        }

        .jr-pagination {
          .el-pagination {
            transform: translateY(6px);
          }
        }
      }
    }

    .he500 {
      height: 580px;
    }

    .no-flex {
      display: block;
    }

    .mt-42 {
      margin-top: 42px;
    }

    .he640 {
      padding-top: 21px;
      height: 640px;

      .underwriting-detail-content-item-table {
        height: calc(100% - 80px);
      }
    }

    .he16 {
      height: 16px;
    }
  }

  &-tips {
    position: absolute;
    left: 0;
    bottom: 12px;
    font-size: var(--el-font-size-base);
    color: rgba(0, 0, 0, 0.6);
  }
}
</style>
