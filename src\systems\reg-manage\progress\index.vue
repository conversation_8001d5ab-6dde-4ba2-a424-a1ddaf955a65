<template>
  <!-- 注册进度 -->
  <!--  公司债自定义列 051e23932f3e46f3946191431b8f1538-->
  <!-- 协会债自定义列 3aed47702325497eb8667f81f61a315c-->
  <div class="registration-progress">
    <div class="public-tabs-container">
      <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="协会债" name="first" />
        <el-tab-pane label="公司债" name="second" />
      </el-tabs>
    </div>
    <jr-decorated-table
      v-if="activeName === 'first'"
      ref="table"
      key="firstTable"
      class="registration-progress-table"
      stripe
      :params="tableParams"
      custom-id="3aed47702325497eb8667f81f61a315c"
      v-bind="{ ...$attrs, ...$props, menuinfo, customRender }"
      @handleSelectionChange="getSelectRows"
      @refreshed="callFn"
    />
    <jr-decorated-table
      v-else
      ref="table"
      class="registration-progress-table"
      stripe
      key="otherTable"
      :params="tableParams"
      custom-id="051e23932f3e46f3946191431b8f1538"
      v-bind="{ ...$attrs, ...$props, menuinfo, customRender }"
      @handleSelectionChange="getSelectRows"
      @refreshed="callFn"
    />
    <custonTimeLine v-if="false" />

    <span class="registration-progress-tips">
      <jr-svg-icon icon-class="info-circle" />
      仅展示进度中的项目信息、注册完成后可前往额度管理查看。
    </span>
  </div>
</template>
<script>
import { getProjectStatus } from '@/api/reg/reg'
import custonTimeLine from '../components/custom-time-line'
export default {
  components: { custonTimeLine },
  props: {
    menuinfo: {
      type: Object,
      default: () => ({})
    },
    permitdetail: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      compId: '',
      activeName: 'first',
      timelineData: {}, // 存储时间线数据
      customRender: {
        progress: (h, { rowIndex, row }) => {
          return (
            <el-popover
              placement='bottom'
              trigger='click'
              class='custom-popover-item'
              onShow={() => {
                this.handlePopoverShow(rowIndex)
                if (!this.timelineData[row.bInfoFullname]) {
                  this.loadTimelineData(row)
                }
              }}
            >
              <span
                slot='reference'
                style='color:var(--theme--color);cursor:pointer;'
                popperClass='detail-popover'
                width='800'
                onClick={() => {
                  this.openPopover(row, rowIndex)
                }}
              >
                {row.progress}
              </span>
              {rowIndex === this.compId ? (
                <custonTimeLine datasourse={this.timelineData[row.bInfoFullname]}></custonTimeLine>
              ) : null}
            </el-popover>
          )
        }
      },
      tableParams: {},
      columns: [], // 新增：存储详情表格的查询参数
      selectRows: []
    }
  },
  methods: {
    openPopover(row, index) {
      this.compId = index
    },
    callFn(data) {
      this.columns = data.config.columns
    },
    getSelectRows(rows, listData) {
      this.selectRows = rows
    },
    async loadTimelineData(row) {
      const res = await getProjectStatus({ bInfoFullname: row.bInfoFullname, sInfoCompcode: row.compOrgid })
      console.log(res, 'res')

      this.$set(
        this.timelineData,
        row.bInfoFullname,
        res.map((item, index) => ({
          date: item.updateDate,
          statusName: item.statusName,
          // updateDate:item.updateDate,
          // 数组的第一项设置isCurrent为true
          isCurrent: index === 0
        }))
      )
      console.log(this.timelineData, 'this.timelineData')
    },
    handleClick(tab, event) {
      // console.log(tab, event, 'tab, event')
      // this.activeName = tab.name

      this.tableParams = {
        webTime: new Date().getTime()
      }
    },
    handlePopoverShow(index) {
      this.$nextTick(() => {
        const elList = document.querySelectorAll('.custom-popover-item')

        for (let i = 0; i < elList.length; i++) {
          const popoverInstance = elList[i].__vue__

          if (i !== index) {
            if (popoverInstance?.doClose) popoverInstance.doClose()
          }
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .jr-decorated-table--header {
  display: none !important;
}

.registration-progress {
  height: 100%;

  &-tabs {
    ::v-deep .el-tabs__header {
      background: #fff;
      margin: 0;
      .el-tabs__nav-wrap::after {
        height: 2px !important;
      }
      .el-tabs__nav-scroll {
        margin-left: 16px;
      }
      .el-tabs__item {
        height: 56px;
        line-height: 56px;
        font-family: MicrosoftYaHeiSemibold;
        font-size: 20px;
        color: rgba(0, 0, 0, 0.6);
        text-align: left;
        font-style: normal;
      }
      .el-tabs__item.is-active {
        color: var(--theme--color) !important;
      }
    }
  }
  &-table {
    background-color: #fff;
    height: calc(100% - 56px);
    .jr-decorated-table--header-left {
      width: 100%;
      padding: 3px 16px;
    }

    ::v-deep .jr-pagination {
      position: absolute !important;
      right: 32px !important;
      bottom: 38px !important;
    }
  }
  &-tips {
    position: absolute;
    left: 32px;
    bottom: 44px;

    font-size: 14px;
    color: rgba(0, 0, 0, 0.6);
  }
}
.progress-popover {
  padding-left: 8px !important;
  font-size: 12px !important;
  .el-timeline {
    .el-timeline-item {
      padding-bottom: 15px;
    }
    .el-timeline-item__wrapper {
      padding-left: 15px !important;
    }
    .current-item {
      .el-timeline-item__wrapper {
        .el-timeline-item__timestamp,
        .el-timeline-item__content {
          color: var(--theme--color) !important;
        }
      }
    }
    .normal-item {
      .el-timeline-item__wrapper {
        .el-timeline-item__timestamp,
        .el-timeline-item__content {
          color: #bfbfbf !important;
        }
      }
    }
  }
}
</style>
