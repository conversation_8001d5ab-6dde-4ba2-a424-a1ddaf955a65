<!-- 财务对标 -->
<template>
  <div class="financialBenchmarking">
    <div class="financialBenchmarkingTitle">
      财务对标
      <span class="radio">
        <el-radio-group v-model="searchForm.benchmarkingType">
          <el-radio :label="0">指标对比</el-radio>
          <el-radio :label="1">趋势分析</el-radio>
        </el-radio-group>
      </span>
    </div>
    <div class="searchForm">
      <el-form :model="searchForm" label-width="90px">
        <jr-form-item label="报告期" class="formItem">
          <el-date-picker
            v-model="searchForm.bgq"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          />
        </jr-form-item>
        <jr-form-item label="对标企业" class="formItem">
          <jr-combobox
            v-model="searchForm.enterprise"
            placeholder="请选择"
            clearable
            filterable
            multiple
            collapse-tags
            :data="enterpriseList"
            option-value="id"
            option-label="text"
          />
        </jr-form-item>
        <jr-form-item label="币种" class="formItem">
          <jr-combobox
            v-model="searchForm.bz"
            placeholder="请选择"
            clearable
            filterable
            :data="ccyList"
            option-value="id"
            option-label="text"
          />
        </jr-form-item>
        <jr-form-item label="指标自定义" class="formItem">
          <jr-combobox
            v-model="searchForm.zdyzb"
            placeholder="请选择"
            clearable
            filterable
            multiple
            collapse-tags
            :data="indicatorsList"
            option-value="id"
            option-label="text"
            @change="zdyzbChange"
          />
        </jr-form-item>
        <el-checkbox v-model="searchForm.wsjqy" style="margin-left: 10px">过滤无数据企业</el-checkbox>
        <el-button type="primary" class="btn" @click="queryData">查询</el-button>
      </el-form>
      <el-tag v-for="tag in indicatorsTags" :key="tag.name">
        {{ tag.name }}
      </el-tag>
    </div>
    <div class="content">
      <TemplateModule v-if="searchForm.benchmarkingType === 0" chart-type="BAR" chart-seq="9117c320645b4bcf9965c748e1fe3430" />
      <TemplateModule v-else chart-type="BAR" chart-seq="53c4706d4f324ea1b333c24bbecef763" />
    </div>
  </div>
</template>

<script>
import TemplateModule from '@jupiterweb/components/template-module'
export default {
  components: {
    TemplateModule
  },
  data() {
    return {
      searchForm: {
        bgq: [],
        enterprise: [],
        zdyzb: ['ROA', '资产负债率', 'ROE', 'EBIT', '短期借款'],
        bz: '',
        benchmarkingType: 0,
        wsjqy: false
      },
      enterpriseList: [
        { id: '雪佛兰', text: '雪佛兰' },
        { id: '英国石油', text: '英国石油' },
        { id: '道达尔', text: '道达尔' }
      ],
      bondTypeList: [],
      ccyList: [
        { id: 'cny', text: '人名币' }
      ],
      indicatorsList: [
        { id: 'ROA', text: 'ROA' },
        { id: '资产负债率', text: '资产负债率' },
        { id: 'ROE', text: 'ROE' },
        { id: 'EBIT', text: 'EBIT' },
        { id: '短期借款', text: '短期借款' },
        { id: '总资产', text: '总资产' }
      ],
      indicatorsTags: [
        { name: 'ROA', text: 'ROA' },
        { name: '资产负债率', text: '资产负债率' },
        { name: 'ROE', text: 'ROE' },
        { name: 'EBIT', text: 'EBIT' },
        { name: '短期借款', text: '短期借款' }
      ],
      customRender: {
        fxr: (h, { row }) => {
          return <el-link type='primary' onClick={this.rowClick.bind(this, row)}>{row.fxr}</el-link>
        }
      }
    }
  },
  methods: {
    queryData() {
      //
    },
    rowClick(row) {
      //
    },
    zdyzbChange(val) {
      console.log(val, 'nnnnbbbb')
      this.indicatorsTags = val.map(item => ({
        name: item
      }))
    }
  }
}
</script>
<style lang="scss">
.financialBenchmarking {
    margin-top: 10px;
    padding: 6px 10px 6px 10px;
    background: #fff;
    .radio {
        display: inline-block;
        margin-left: 30px;
    }
    .el-table {
        height: 300px !important;
    }
    .financialBenchmarkingTitle {
        font-size: 16px;
        color: #303133;
        font-weight: 700;
        padding-bottom: 6px;
        border-bottom: 1px solid #EBEEF5;
    }
    .formItem {
        display: inline-block;
        width: 300px
    }
    .btn {
        display: inline-block;
        margin-left: 10px;
    }
    .searchForm {
        padding: 10px 0 0 0;
        background: #fff;
        margin-bottom: 0;
        .el-tag {
            margin: 10px;
        }
    }
    .content {
        width: 100%;
        height: 300px;
    }
}
</style>

