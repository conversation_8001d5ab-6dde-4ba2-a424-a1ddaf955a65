<!--
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023-04-20 19:49:22
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2023-05-12 11:36:28
 * @Description: 新债发行
-->
<template>
  <div class="home-poc-item has-fullscreen">
    <div class="home-poc-item--header float-left">新债发行 (<font color="red">{{ totalCount }}<fullscreen v-on="{ ...$listeners }" /></font>)
      <jr-combobox v-model="form.range" style="top: 8px;position: absolute;width: 200px;right: 116px;" type="button" size="mini" :data="RANGE_LIST" @change="queryList" />
    </div>
    <jr-decorated-table
      custom-id="6e812934d8f44d99913f18084566115b"
      :handlebidding="goBidding"
      v-bind="{ ...$attrs, params: queryParams, noPagination: true, menuinfo: { pageId: 'Monitor', btnList: [{btnkey: 'bidding', btnPosition: 'LIST', icon: 'flag', btnnm: '投标'}]} }"
      @refreshed="queryEnd"
    />
  </div>
</template>

<script>
import dayjs from 'dayjs'
import fullscreen from './fullscreen'
// 近一周、近一月、近三月、近半年、年初至今、近一年
const RANGE_LIST = [
  {
    text: '近一周',
    value: '01'
  },
  {
    text: '近一个月',
    value: '02'
  },
  {
    text: '近三个月',
    value: '03'
  },
  {
    text: '近半年',
    value: '04'
  },
  {
    text: '年初至今',
    value: '05'
  },
  {
    text: '近一年',
    value: '06'
  }
]
export default {
  components: {
    fullscreen
  },
  data() {
    return {
      RANGE_LIST,
      form: {
        range: '05'
      },
      totalCount: 0,
      queryParams: {},
      platDate: JSON.parse(sessionStorage.getItem('platDate'))
    }
  },
  created() {
    this.queryList()
  },
  methods: {
    queryList() {
      const [issueDateStart, issueDateEnd] = this.convertDate(this.form.range)
      this.queryParams = { issueDateStart, issueDateEnd }
    },
    convertDate(range) {
      const format = 'YYYY-MM-DD'
      const sysDate = this.$store.getters.systemTime
      const plateDate = dayjs(sysDate).format(format)
      switch (range) {
        case '01':
          return [dayjs(sysDate).subtract(7, 'day').format(format), plateDate]
        case '02':
          return [dayjs(sysDate).subtract(30, 'day').format(format), plateDate]
        case '03':
          return [dayjs(sysDate).subtract(90, 'day').format(format), plateDate]
        case '04':
          return [dayjs(sysDate).subtract(180, 'day').format(format), plateDate]
        case '05':
          return [dayjs(new Date(new Date(sysDate).getFullYear(), 0, 1)).format(format), plateDate]
        case '06':
          return [dayjs(sysDate).subtract(365, 'day').format(format), plateDate]
      }
    },
    goBidding(s, i, row) {
      this.$store.dispatch('tagsView/updateCurrentTab', {
        path: '/SETTLE_MANAGEMENT_001_001_006',
        meta: { refresh: true, params: { ...row }}
      })
    },
    queryEnd(ins) {
      this.totalCount = ins.pagination.total
    }
  }
}
</script>

<style lang="scss">
</style>

<style lang="scss" scoped>
@import "./poc.scss";
</style>
