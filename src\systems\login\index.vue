<template>
  <div class="b-login-page" :class="{ 'is-login': flagConfig.login, 'is-register': flagConfig.register }">
    <div class="b-login-left">
      <div class="b-login-left-chart" />
    </div>
    <div class="b-body" :class="{'is-forget': flagConfig.forgot }">
      <Register v-if="flagConfig.register" @close="close" @openCaptcha="openCaptcha" />
      <Forgot v-else-if="flagConfig.forgot" @close="close" @openCaptcha="openCaptcha" />
      <Login v-else @open="open" @openCaptcha="openCaptcha" />
    </div>
    <slider-captcha
      :visible="showCaptcha"
      @close="handleClose"
      @success="handleSuccess"
    />
    <ThemePicker v-show="false" />
  </div>
</template>

<script>
import Login from './login.vue'
import Register from './register.vue'
import Forgot from './forgot.vue'
import ThemePicker from '@jupiterweb/components/theme-picker/index.vue'
import SliderCaptcha from './sliderCaptcha.vue'
export default {
  name: 'LoginPage',
  components: {
    Login,
    Register,
    Forgot,
    ThemePicker,
    SliderCaptcha
  },
  data() {
    return {
      showCaptcha: false,
      captchaCb: null,
      flagConfig: {
        login: true,
        register: false,
        forgot: false
      }
    }
  },
  methods: {
    handleClose() {
      this.showCaptcha = false
      this.captchaCb = null
    },
    handleSuccess() {
      this.showCaptcha = false
      this.$message.closeAll()
      this.captchaCb && this.captchaCb()
    },
    openCaptcha(cb) {
      this.captchaCb = cb
      this.showCaptcha = true
    },
    open(type) {
      Object.keys(this.flagConfig).forEach(key => {
        this.flagConfig[key] = false
      })
      this.flagConfig[type] = true
    },
    close(type) {
      this.flagConfig[type] = false
      this.flagConfig.login = true
    }
  }
}
</script>

<style lang="scss" scoped>
.b-login-page {
  width: 100%;
  height: 100%;
  background: url('~@/assets/images/login/bg.png') no-repeat center center;
  background-size: 100% 100%;
  position: relative;
  .b-login-left {
    background: url('~@/assets/images/login/left-bg.png') no-repeat center center;
    background-size: 100% 100%;
    width: 57.6%;
    height: 100%;
    &-chart {
      background: url('~@/assets/images/login/left-top.png') no-repeat center center;
      background-size: 100% 100%;
      width: 46.88vw;
      height: 100vh;
    }
  }
  &.is-register {
    .b-login-left-chart {
      background: url('~@/assets/images/login/left-top-reg.png') no-repeat center center;
      background-size: 100% 100%;
    }
  }
  .b-body {
    --el-component-size: 40px;
    width: 27.5vw;
    height: 100vh;
    position: absolute;
    overflow: auto;
    top: 0;
    left: 61.25%;
    display: flex;
    justify-content: center;
    ::v-deep .el-form-item__error {
      display: block;
    }

    ::v-deep .b-login--logo {
      line-height: 1;
      text-align: center;
      background: url(~@/assets/images/login/logo.png) no-repeat center center;
      background-size: contain;
      height: 59px;
      width: 480px;
      max-width: calc(100% - 48px);
      margin: 0 auto;
    }
    &>div {
      margin: auto 0;
    }
    ::v-deep .b-block-button.el-button {
      width: 100%;
      font-size: var(--el-font-size-large);
    }
    ::v-deep .el-form-item--medium .el-form-item__content,
    ::v-deep .el-form-item--medium .el-form-item__label {
      line-height: var(--el-component-size);
    }
    ::v-deep .el-input {
      .el-input__inner {
        padding: 15px 25px 15px 40px;
      }
      &.b-verify--form-code {
        .el-input__inner {
          border-right: none !important;
        }
      }
      .el-input-group__append {
        background: transparent;
        padding-right: 12px !important;
      }
      .el-input__prefix,
      .el-input__suffix {
        top: 8px;
      }
      .el-input__suffix {
        right: 12px;
      }
      .el-input__prefix {
        left: 12px;
      }
      img {
        width: 24px;
        height: 25px;
      }
    }
    ::v-deep .el-form-item + .el-form-item {
      margin-top: 24px;
    }
  }
}
</style>

