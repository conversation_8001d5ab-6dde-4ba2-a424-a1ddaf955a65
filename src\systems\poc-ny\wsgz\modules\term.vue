<template>
  <div class="page-wsgz--term">
    <div class="page-wsgz--term-title">
      <span>债券简称/代码</span>
    </div>
    <div class="page-wsgz--term-content">
      <el-input v-model="currentRow.zqjc" />
    </div>
    <div class="page-wsgz--term-title">
      <span>估值类型</span>
    </div>
    <div class="page-wsgz--term-content">
      <jr-radio-group v-model="form.termSelected" :data="termList" :max="10" @change="(v) => $emit('chartSeq', v)" />
    </div>
  </div>
</template>

<script>
export default {
  props: {
    currentRow: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      termList: [{
        text: '估价收益率（%）',
        value: 'ef9bb382aad9467eb2b755438450ab87'
      }, {
        text: '估价净价（元）',
        value: '86636317df1944aeaf4ca33be34b6c46'
      }, {
        text: '估价全价（元）',
        value: 'f73f8072f89f4f48a4ca863188d0054d'
      }],
      form: {
        zqdm: '',
        termSelected: 'ef9bb382aad9467eb2b755438450ab87'
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.page-wsgz--term {
  width: 300px;
  height: 100%;
  padding: 0 12px;
  background-color: #f5f5f5;
  overflow: auto;
  .page-wsgz--term-title {
    display: flex;
    line-height: 40px;
    justify-content: space-between;
    align-items: center;
    span {
      font-size: 14px;
    }
  }
  .color-warning {
    color: var(--el-theme-color-warning);
  }
  .page-wsgz--term-content {
    width: 100%;
    min-width: 268px;
    overflow-x: hidden;
    .jr-radio-group {
      width: 100%;
      display: flex;
      flex-direction: column;
      ::v-deep .el-radio {
        background-color: #fff;
        border: 1px solid #e8e8e8;
        margin-right: 0;
        padding: 4px 12px;
        width: 100%;
        margin-bottom: 10px;
      }
    }
  }
}
</style>
