<template>
  <flat-index :query="handleQuery" :reset="handleReset" :pagination="configTable.pagination">
    <template v-slot:form>
      <el-form ref="elForm" :model="configForm.model">
        <jr-form-item-create :column="0" :data="configForm.data" :model="configForm.model" />
      </el-form>
    </template>

    <template v-slot:right-button>
      <el-button>
        <jr-svg-icon icon-class="export" />
      </el-button>
    </template>

    <template v-slot:table-list="{ height }">
      <jr-table
        :height="height"
        :columns="configTable.columns"
        :data-source="configTable.data"
        :loading="configTable.loading"
        :pagination="configTable.pagination"
        :on-change="handleQuery"
        border
      >
        <template v-slot:index>
          <el-table-column
            type="index"
            width="50px"
            align="center"
            :label="InitialMessage('common.columns.index')"
          >
            <template slot-scope="scope">
              <span>{{ (configTable.pagination.pageNo - 1) * configTable.pagination.pageSize + scope.$index +1 }}</span>
            </template>
          </el-table-column>
        </template>
      </jr-table>
    </template>

  </flat-index>
</template>

<script>
import * as API from '@/api/demo/flat-index'

export default {
  props: {
    // 平台日期
    date: {
      type: [Number, String],
      default: null
    },
    // 权限相关
    permitdetail: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      // 查询区域
      configForm: {
        model: {},
        data: [
          {
            title: '发行状态',
            prop: 'field1',
            type: 'radio',
            options: [
              { text: '近期已发行', value: '1' },
              { text: '今日发行', value: '2' },
              { text: '即将发行', value: '3' },
              { text: '推迟发行', value: '4' },
              { text: '取消发行', value: '5' }
            ],
            change(value, row) {
              console.log('--发行状态 change --：', value)
            }
          },
          {
            title: '发行起始日',
            prop: 'field2',
            type: 'rangeDate'
          },
          {
            title: '主体层级',
            prop: 'field3',
            type: 'select',
            options: [
              { text: '1111', value: '1111' },
              { text: '2222', value: '2222' },
              { text: '3333', value: '3333' }
            ]
          },
          {
            title: '主体评级',
            prop: 'field4',
            type: 'select',
            options: [
              { text: '1111', value: '1111' },
              { text: '2222', value: '2222' },
              { text: '3333', value: '3333' }
            ]
          },
          {
            title: '地区',
            prop: 'field5',
            type: 'select',
            options: [
              { text: '1111', value: '1111' },
              { text: '2222', value: '2222' },
              { text: '3333', value: '3333' }
            ]
          },
          {
            title: '行业',
            prop: 'field6',
            type: 'select',
            options: [
              { text: '1111', value: '1111' },
              { text: '2222', value: '2222' },
              { text: '3333', value: '3333' }
            ]
          },
          {
            title: '债项评级',
            prop: 'field7',
            type: 'select',
            options: [
              { text: '1111', value: '1111' },
              { text: '2222', value: '2222' },
              { text: '3333', value: '3333' }
            ]
          },
          {
            title: '债券类型',
            prop: 'field8',
            type: 'select',
            options: [
              { text: '1111', value: '1111' },
              { text: '2222', value: '2222' },
              { text: '3333', value: '3333' }
            ]
          },
          {
            title: '发行期限',
            prop: 'field9',
            type: 'select',
            options: [
              { text: '1111', value: '1111' },
              { text: '2222', value: '2222' },
              { text: '3333', value: '3333' }
            ]
          },
          {
            title: '关键字',
            prop: 'field10',
            type: 'text'
          }
        ]
      },
      // 表格数据
      configTable: {
        loading: false,
        columns: [
          {
            title: '债券代码',
            prop: 'code'
          },
          {
            title: '债券简称',
            prop: 'name'
          },
          {
            title: '牵头主承销商/牵头经办人',
            prop: 'user'
          },
          {
            title: '联席主承销商',
            prop: 'agent'
          },
          {
            title: '牵头承销占比(%)',
            prop: 'rate1',
            type: 'rate'
          },
          {
            title: '承销明细',
            prop: 'detail1'
          },
          {
            title: '牵头包销占比(%)',
            prop: 'rate2',
            type: 'rate'
          },
          {
            title: '联席包销占比(%)',
            prop: 'rate3',
            type: 'rate'
          },
          {
            title: '包销明细',
            prop: 'detail2'
          }
        ],
        data: [
          {
            code: '012383884.IB',
            name: '23淄博城运SCP003',
            user: '中国光大银行股份有限公司',
            agent: '光大证券股份有限公司',
            rate1: 50.00,
            detail1: '光大证券股份有限公司:10亿',
            rate2: 0.00,
            rate3: 0.00,
            detail2: '光大证券股份有限公司'
          },
          {
            code: '012383884.IB',
            name: '23淄博城运SCP003',
            user: '中国光大银行股份有限公司',
            agent: '光大证券股份有限公司',
            rate1: 50.00,
            detail1: '光大证券股份有限公司:10亿',
            rate2: 0.00,
            rate3: 0.00,
            detail2: '光大证券股份有限公司'
          },
          {
            code: '012383884.IB',
            name: '23淄博城运SCP003',
            user: '中国光大银行股份有限公司',
            agent: '光大证券股份有限公司',
            rate1: 50.00,
            detail1: '光大证券股份有限公司:10亿',
            rate2: 0.00,
            rate3: 0.00,
            detail2: '光大证券股份有限公司'
          },
          {
            code: '012383884.IB',
            name: '23淄博城运SCP003',
            user: '中国光大银行股份有限公司',
            agent: '光大证券股份有限公司',
            rate1: 50.00,
            detail1: '光大证券股份有限公司:10亿',
            rate2: 0.00,
            rate3: 0.00,
            detail2: '光大证券股份有限公司'
          },
          {
            code: '012383884.IB',
            name: '23淄博城运SCP003',
            user: '中国光大银行股份有限公司',
            agent: '光大证券股份有限公司',
            rate1: 50.00,
            detail1: '光大证券股份有限公司:10亿',
            rate2: 0.00,
            rate3: 0.00,
            detail2: '光大证券股份有限公司'
          }
        ],
        pagination: {
          pageNo: 1,
          pageSize: window.screen.height > 1000 ? 25 : 15,
          pageSizeOptions: [15, 25, 50, 100],
          total: 0,
          showSizeChanger: true
        }
      }
    }
  },
  methods: {
    // 查询
    handleQuery() {
      const self = this
      const { configForm, configTable } = self

      self.$refs.elForm.validate(async valid => {
        if (valid) {
          configTable.loading = true

          const params = {
            data: { ...configForm },
            page: { ...configTable.pagination }
          }

          const { list = [], total = 0 } = { ...await API.GetListData(params) }

          if (list.length) {
            configTable.loading = false
            configTable.data = list
            configTable.pagination.total = total
          }
        }
      })
    },
    // 重置
    handleReset() {

    }
  }
}
</script>

<style lang="scss">

</style>

