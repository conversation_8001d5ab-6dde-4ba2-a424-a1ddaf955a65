<template>
  <div class="bazaar">
    <el-tabs v-model="active" class="bazaar-tabs">
      <el-tab-pane label="LPR" name="first">
        <rate-lpr />
      </el-tab-pane>

      <el-tab-pane label="同业存单" name="second" :lazy="true">
        <rate-cd />
      </el-tab-pane>

      <el-tab-pane label="SHIBOR" name="three" :lazy="true">
        <rate-shibor />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import rateCd from './modules/rate-ncds.vue'
import rateLpr from './modules/rate-lpr.vue'
import rateShibor from './modules/rate-shibor.vue'
export default {
  name: 'PolicyRate',
  components: {
    rateCd,
    rateLpr,
    rateShibor
  },
  data() {
    return {
      active: 'first',
      url: ''
    }
  },
  created() {
  },
  methods: {
  }
}
</script>

<style lang="scss" scoped>
@import '@/assets/styles/market.scss';

::v-deep .jr-radio-group .el-radio {
  min-width: 96px;
  margin-right: 16px;
}

::v-deep .jr-checkbox-group .el-checkbox {
  min-width: 80px;
  margin-right: 32px;
}
</style>
