<template>
  <div class="thing-todo">
    <cockpit-header :style="{ width: px2vw(362), height: px2vh(40) }" title="待办" />
    <div class="thing-todo-content">
      <div class="thing-todo-content-item" v-for="(item, index) in todoList" :key="index">
        <p class="thing-todo-content-item-title">
          <span>{{ item.title }}</span>
          <span>共{{ item.total }}条，待处理{{ item.pending }}条</span>
        </p>
        <div class="thing-todo-content-item-percent">
          <div class="thing-todo-content-item-percent-bar" :style="{ width: getProgressPercent(item) + '%' }"></div>
        </div>
        <p class="thing-todo-content-item-handle">去处理</p>
      </div>
    </div>
  </div>
</template>

<script>
import cockpitHeader from '../../components/cockpit-header.vue'
import { px2vw, px2vh } from '../../utils/portcss'
export default {
  name: 'ThingTodo',
  components: {
    cockpitHeader
  },
  data() {
    return {
      todoList: [
        {
          title: '重大事项排查',
          total: 102,
          pending: 100
        },
        {
          title: '付息兑付公告披露',
          total: 102,
          pending: 100
        },
        {
          title: '财务报告披露',
          total: 102,
          pending: 100
        }
      ]
    }
  },
  methods: {
    px2vw,
    px2vh,
    /**
     * 计算进度百分比
     */
    getProgressPercent(item) {
      if (item.total === 0) return 0
      const processedCount = item.total - item.pending
      return Math.round((processedCount / item.total) * 100)
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~assets/styles/portcss.scss';
.thing-todo {
  width: 100%;
  height: vh(446);
  background-image: url('../../../../assets/cockpit/cockpit_pro_normal_bac.png');
  background-size: 100% 100%;
  padding: 0px vw(24);
  &-content {
    width: 100%;
    height: vh(396);
    overflow-y: scroll;
    &-item {
      width: 100%;
      height: vh(110);
      margin-top: vh(16);
      border-radius: vh(8);
      backdrop-filter: blur(0px);
      padding: 24px vw(16) 16px;
      position: relative;
      overflow: hidden;
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(to top, rgba(88, 154, 255, 0) 0%, rgba(44, 144, 255, 0.2) 100%);
        border-radius: vh(8);
        z-index: -1;
      }
      &::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(180deg, rgba(255, 255, 255, 0.45) 0%, rgba(132, 164, 255, 0.51) 100%);
        border-radius: vh(8);
        padding: 1px;
        mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
        mask-composite: xor;
        -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
        -webkit-mask-composite: xor;
        z-index: -1;
      }
      box-shadow: inset 0px 7px 16px 0px rgba(102, 156, 255, 0.16);
      & > p {
        margin-bottom: 0px;
      }
      &-title {
        display: flex;
        justify-content: space-between;
        & > span:nth-of-type(1) {
          height: vh(19);
          font-family: MicrosoftYaHeiSemibold;
          font-size: vh(14);
          color: #ffffff;
          line-height: vh(19);
        }
        & > span:nth-of-type(2) {
          height: vh(19);
          font-family: MicrosoftYaHei;
          font-size: vh(14);
          color: rgba(255, 255, 255, 0.7);
          line-height: vh(19);
        }
      }
      &-percent {
        width: 100%;
        height: vh(7);
        background: rgba(0, 0, 0, 0.06);
        border-radius: vh(4);
        margin-top: vh(8);
        position: relative;
        overflow: hidden;
        &-bar {
          height: 100%;
          background: linear-gradient( 270deg, #3B84FF 0%, #A3C5FF 100%);;
          border-radius: vh(4);
          transition: width 0.3s ease;
        }
      }
      &-handle {
        width: vw(58);
        height: vh(28);
        background: #2668DB;
        border-radius: 2px;
        font-family: MicrosoftYaHei;
        font-size: vh(14);
        color: rgba(255, 255, 255, 0.9);
        line-height: vh(28);
        text-align: center;
        margin-top: vh(8);
        cursor: pointer;
      }
    }
  }
}
</style>
