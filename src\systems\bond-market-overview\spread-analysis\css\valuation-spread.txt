::v-deep .jr-decorated-table--header {
  display: none !important;
}

.issue-spread {
  background: #fff;
  padding-bottom: 40px;
  padding: 0 16px;
  min-height: 100%;

  ::v-deep .el-radio-button__inner {
    font-size: var(--el-font-size-large) !important;
  }

  &-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 16px;
    border: 1px solid #ccc;
    height: 484px;

    &-left {
      padding: 16px;
      height: 100%;
      &-header {
        display: flex;
        width: 100%;
        align-items: center;
        height: 40px;
      }

      &-search {
        margin-top: 16px;
        height: calc(100% - 104px) !important;
        overflow: scroll;
        width: 802px;

        ::v-deep .el-input__suffix-inner {
          height: 100% !important;
          display: flex;
          align-items: center;
        }
      }
    }

    &-right {
      // width: 810px;
      margin-right: 16px;
      flex: 1;
      height: 472px;
      &-header {
        justify-content: space-between;
        height: 64px;
        padding: 21px;
        display: flex;
      }
    }
  }

  &-table {
    padding: 16px 0;
    background-color: #fff;

    ::v-deep .jr-decorated-table--header-left {
      display: none !important;
    }

    &-header {
      &-btns {
        padding-top: 16px;
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        gap: 16px;
      }

      &-form {
        height: 64px;
        padding: 16px;
        display: flex;
        align-items: center;
        gap: 16px;

        ::v-deep .el-form-item__label {
          padding-top: 10px !important;
        }

        &-left {
          flex: 1;
          display: flex;
          align-items: center;
          justify-content: flex-end;

          ::v-deep .el-button--text {
            font-size: var(--el-font-size-base) !important;
            color: rgba(0, 0, 0, 0.9) !important;
          }
        }
      }
    }

    &-tabs {
      width: 100%;
      height: 36px;
      box-sizing: border-box;
      background-color: #ffffff;

      ::v-deep .el-tabs__item {
        font-size: var(--el-font-size-medium) !important;
      }
    }
  }
}
