<template>
  <div class="save-form">
    <div class="save-form-header">
      <jr-svg-icon icon-class="funds-box-fill" />
      <span class="save-form-header-title">配置{{ curvename }}</span>
      <el-tooltip placement="right" effect="dark">
        <jr-svg-icon icon-class="info-circle" />
        <div slot="content" v-html="tiptext" style="max-width: 300px" />
      </el-tooltip>
    </div>
    <div class="save-form-contant">
      <el-form ref="updateForm" :model="form" label-width="90px">
        <jr-form-item-create
          :prop-path="''"
          :validate-rules="{}"
          :data="infoFormFields"
          :model="form"
          :column="2"
          :disabled="false"
        />
      </el-form>

      <div class="add" @click="handleAdd">
        <i class="el-icon-circle-plus-outline" />
        生成曲线
      </div>
    </div>
    <div v-if="quickSearchList.length > 0" class="save-form-list">
      <div v-for="(item, index) in quickSearchList" :key="'quick' + index" class="save-form-list-item">
        <el-checkbox :label="index" v-model="item.checked" @change="searchParamsChange">
          <div class="save-form-list-item-content">
            <div class="save-form-list-item-content-left">
              <jr-svg-icon icon-class="funds-box-fill" />
              <span class="save-form-list-item-content-left-title">
                {{ curvename }}{{ numberToChinese(index + 1) }}
              </span>

              <el-tooltip placement="right" effect="dark" style="margin-top: 3px">
                <jr-svg-icon icon-class="info-circle" />
                <div slot="content">
                  <div class="tooltip-text">债券类型：{{ item.bondTypeName ? `${item.bondTypeName}` : '全部' }}</div>
                  <div class="tooltip-text">
                    债项评级：{{ item.bondRatingName ? `${item.bondRatingName}` : '全部' }}
                  </div>
                  <div class="tooltip-text">
                    中债隐含评级：{{ item.implyRatingName ? `${item.implyRatingName}` : '全部' }}
                  </div>
                  <div class="tooltip-text">发行期限：{{ item.termName ? `${item.termName}` : '全部' }}</div>
                  <div class="tooltip-text">
                    有无担保：{{ item.isguaranteeName ? `${item.isguaranteeName}` : '全部' }}
                  </div>
                  <div class="tooltip-text">含权债：{{ item.isrightName ? `${item.isrightName}` : '全部' }}</div>
                  <div class="tooltip-text">
                    主体性质：{{ item.compPropertyName ? `${item.compPropertyName}` : '全部' }}
                  </div>
                  <div class="tooltip-text">地区：{{ item.areaName ? `${item.areaName}` : '全部' }}</div>
                </div>
              </el-tooltip>
            </div>
          </div>
        </el-checkbox>

        <jr-svg-icon icon-class="delete" color="#E6A23C" class="save-form-list-item-right" @click="deleteItem(index)" />
      </div>
    </div>
    <durationIssuanceRangeTable v-if="false" />
  </div>
</template>

<script>
import { getdictionarybystr, getDictOptionsApi } from '../get-dictionary'
import { queryAllBondType, regionLink } from '@/api/public/public'
import durationIssuanceRangeTable from './duration-issuance-range-table.vue'
import { issuanceQueryBondTypeList } from '@/api/issuance/issuance'
const DICTIONARYARRAY = [
  'isright',
  'isguarantor',
  'bondRatingCustomized',
  'implyRatingCustomized',
  'issueTermCustomized',
  'COMPPROPERTY'
]
export default {
  components: { durationIssuanceRangeTable },
  props: {
    tiptext: {
      type: String,
      default() {
        return ''
      }
    },
    curvename: {
      type: String,
      default() {
        return ''
      }
    },
    outsidelist: {
      type: Array,
      default() {
        return []
      }
    },
    type: {
      type: String,
      default() {
        return 'issue'
      }
    }
  },
  data() {
    return {
      form: {
        checked: false
      },
      infoFormFields: [],
      quickSearchList: [],
      dictionaryObject: {},
      areaList: [],
      bondTypeOptions: []
    }
  },
  beforeDestroy() {
    this.quickSearchList = []
  },
  async created() {
    let defaultSetting = [
      {
        title: '债券类型',
        prop: 'bondType',
        type: 'cascader',
        options: [],
        required: false,
        trigger: 'change',
        props: {
          label: 'label',
          value: 'value'
        },
        uiProps: {
          showAllLevels: false
        },
        showCode: false,
        multiple: true,
        change: (e) => {
          this.form.bondTypeName = this.geCascadertName(this.bondTypeOptions, e).join(',')
        }
      },
      {
        title: '债项评级',
        required: false,
        prop: 'bondRating',
        type: 'select',
        disabled: false,
        options: [],
        optionValue: 'itemcode',
        optionLabel: 'cnname',
        showCode: false,
        multiple: true,
        change: (e) => {
          this.form.bondRatingName = this.getName(
            getdictionarybystr(this.dictionaryObject, 'bondRatingCustomized'),
            e,
            'itemcode',
            'cnname'
          )
        }
      },
      {
        title: '',
        customLabel: true,
        required: false,
        prop: 'implyRating',
        implyRatingprepend: () => {
          return (
            <div style='display:flex;align-items:center;gap:5px;'>
              <span>隐含评级</span>
              <el-tooltip content='中债隐含评级' placement='right' effect='dark' style='margin-top:2px'>
                <jr-svg-icon iconClass='info-circle' />
              </el-tooltip>
            </div>
          )
        },
        type: 'select',
        disabled: false,
        options: [],
        optionValue: 'itemcode',
        optionLabel: 'cnname',
        showCode: false,
        multiple: true,
        change: (e) => {
          this.form.implyRatingName = this.getName(
            getdictionarybystr(this.dictionaryObject, 'implyRatingCustomized'),
            e,
            'itemcode',
            'cnname'
          )
        }
      },
      {
        required: false,
        prop: 'termStr',
        type: 'select',
        disabled: false,
        options: [],
        optionValue: 'itemcode',
        optionLabel: 'cnname',
        showCode: false,
        multiple: true,
        termStrprepend: () => {
          return (
            <div style='display:flex;align-items:center;gap:5px;'>
              <span>发行期限</span>
              <el-tooltip placement='right' effect='dark' style='margin-top:2px'>
                <jr-svg-icon iconClass='info-circle' />
                <div slot='content'>
                  <durationIssuanceRangeTable />
                </div>
              </el-tooltip>
            </div>
          )
        },
        change: (e) => {
          this.form.termName = this.getName(
            getdictionarybystr(this.dictionaryObject, 'issueTermCustomized'),
            e,
            'itemcode',
            'cnname'
          )
        }
      },
      {
        title: '有无担保',
        required: false,
        prop: 'isguarantee',
        type: 'select',
        disabled: false,
        options: [],
        optionValue: 'itemcode',
        optionLabel: 'cnname',
        showCode: false,
        multiple: true,
        change: (e) => {
          this.form.isguaranteeName = this.getName(
            getdictionarybystr(this.dictionaryObject, 'isguarantor'),
            e,
            'itemcode',
            'cnname'
          )
        }
      },
      {
        title: '',
        customLabel: true,
        required: false,
        prop: 'isright',
        isrightprepend: () => {
          return (
            <div style='display:flex;align-items:center;gap:5px;'>
              <span>含权债</span>
              <el-tooltip
                content='此处含权债为含权条款类型为赎回、回售、延期三种条款的债券'
                placement='right'
                effect='dark'
                style='margin-top:2px'
              >
                <jr-svg-icon iconClass='info-circle' />
              </el-tooltip>
            </div>
          )
        },
        type: 'select',
        disabled: false,
        options: [],
        optionValue: 'itemcode',
        optionLabel: 'cnname',
        showCode: false,
        multiple: false,
        change: (e) => {
          this.form.isrightName = this.getName(
            getdictionarybystr(this.dictionaryObject, 'isright'),
            e,
            'itemcode',
            'cnname'
          )
        }
      },
      {
        title: '主体性质',
        required: false,
        prop: 'compProperty',
        type: 'select',
        disabled: false,
        options: [],
        optionValue: 'itemcode',
        optionLabel: 'cnname',
        showCode: false,
        multiple: true,
        change: (e) => {
          this.form.compPropertyName = this.getName(
            getdictionarybystr(this.dictionaryObject, 'COMPPROPERTY'),
            e,
            'itemcode',
            'cnname'
          )
        }
      },
      {
        title: '地区',
        required: false,
        prop: 'districtsub',
        type: 'cascader',
        disabled: false,
        options: [],
        multiple: true,
        props: {
          label: 'cname'
        },
        change: (e) => {
          this.form.district = this.getDistrict(e)
          this.form.areaName = this.getAreaNames(this.areaList, e).join(',')
        }
      }
    ]

    this.infoFormFields = defaultSetting.filter((item) => {
      if (this.type === 'issue') {
        return true
      } else {
        return item.title !== '主体性质'
      }
    })
    this.dictionaryObject = await getDictOptionsApi(DICTIONARYARRAY)
    await this.getArea()
    await this.getBondTypeOptionsApi()

    if (Array.isArray(this.outsidelist) && this.outsidelist.length > 0) {
      this.quickSearchList = this.outsidelist
    }

    if (this.type === 'valuation' && this.quickSearchList.length === 0) {
      // this.$message({
      //   type: 'warning',
      //   message: '请配置上市首日估值偏离度曲线'
      // })
    }
    this.form.bondTypeAll = this.getCascaderAllSelected(this.bondTypeOptions)
    this.form.bondRatingAll = this.getAllSelected(
      getdictionarybystr(this.dictionaryObject, 'bondRatingCustomized'),
      'itemcode'
    )
    this.form.implyRatingAll = this.getAllSelected(
      getdictionarybystr(this.dictionaryObject, 'implyRatingCustomized'),
      'itemcode'
    )
    this.form.termStreAll = this.getAllSelected(
      getdictionarybystr(this.dictionaryObject, 'issueTermCustomized'),
      'itemcode'
    )

    // 发行利差才有主体性质
    if (this.type === 'issue') {
      this.form.compPropertyAll = this.getAllSelected(
        getdictionarybystr(this.dictionaryObject, 'COMPPROPERTY'),
        'itemcode'
      )
    }

    defaultSetting = [
      {
        title: '债券类型',
        required: false,
        prop: 'bondType',
        type: 'cascader',
        options: this.bondTypeOptions,
        trigger: 'change',
        props: {
          label: 'label',
          value: 'value'
        },
        uiProps: {
          showAllLevels: false
        },
        showCode: false,
        multiple: true,
        change: (e) => {
          this.form.bondTypeName = this.geCascadertName(this.bondTypeOptions, e).join(',')
        }
      },
      {
        title: '债项评级',
        required: false,
        prop: 'bondRating',
        type: 'select',
        disabled: false,
        options: getdictionarybystr(this.dictionaryObject, 'bondRatingCustomized'),
        optionValue: 'itemcode',
        optionLabel: 'cnname',
        showCode: false,
        multiple: true,
        change: (e) => {
          this.form.bondRatingName = this.getName(
            getdictionarybystr(this.dictionaryObject, 'bondRatingCustomized'),
            e,
            'itemcode',
            'cnname'
          )
        }
      },
      {
        title: '',
        customLabel: true,
        required: false,
        prop: 'implyRating',
        implyRatingprepend: () => {
          return (
            <div style='display:flex;align-items:center;gap:5px;'>
              <span>隐含评级</span>
              <el-tooltip content='中债隐含评级' placement='right' effect='dark' style='margin-top:2px'>
                <jr-svg-icon iconClass='info-circle' />
              </el-tooltip>
            </div>
          )
        },
        type: 'select',
        disabled: false,
        options: getdictionarybystr(this.dictionaryObject, 'implyRatingCustomized'),
        optionValue: 'itemcode',
        optionLabel: 'cnname',
        showCode: false,
        multiple: true,
        change: (e) => {
          this.form.implyRatingName = this.getName(
            getdictionarybystr(this.dictionaryObject, 'implyRatingCustomized'),
            e,
            'itemcode',
            'cnname'
          )
        }
      },
      {
        required: false,
        prop: 'termStr',
        type: 'select',
        disabled: false,
        options: getdictionarybystr(this.dictionaryObject, 'issueTermCustomized'),
        optionValue: 'itemcode',
        optionLabel: 'cnname',
        showCode: false,
        multiple: true,
        termStrprepend: () => {
          return (
            <div style='display:flex;align-items:center;gap:5px;'>
              <span>发行期限</span>
              <el-tooltip placement='right' effect='dark' style='margin-top:2px'>
                <jr-svg-icon iconClass='info-circle' />
                <div slot='content'>
                  <durationIssuanceRangeTable />
                </div>
              </el-tooltip>
            </div>
          )
        },
        change: (e) => {
          this.form.termName = this.getName(
            getdictionarybystr(this.dictionaryObject, 'issueTermCustomized'),
            e,
            'itemcode',
            'cnname'
          )
        }
      },
      {
        title: '有无担保',
        required: false,
        prop: 'isguarantee',
        type: 'select',
        disabled: false,
        options: getdictionarybystr(this.dictionaryObject, 'isguarantor'),
        optionValue: 'itemcode',
        optionLabel: 'cnname',
        showCode: false,
        multiple: true,
        change: (e) => {
          this.form.isguaranteeName = this.getName(
            getdictionarybystr(this.dictionaryObject, 'isguarantor'),
            e,
            'itemcode',
            'cnname'
          )
        }
      },
      {
        title: '',
        customLabel: true,
        required: false,
        prop: 'isright',
        isrightprepend: () => {
          return (
            <div style='display:flex;align-items:center;gap:5px;'>
              <span>含权债</span>
              <el-tooltip
                content='此处含权债为含权条款类型为赎回、回售、延期三种条款的债券'
                placement='right'
                effect='dark'
                style='margin-top:2px'
              >
                <jr-svg-icon iconClass='info-circle' />
              </el-tooltip>
            </div>
          )
        },
        type: 'select',
        disabled: false,
        options: getdictionarybystr(this.dictionaryObject, 'isright'),
        optionValue: 'itemcode',
        optionLabel: 'cnname',
        showCode: false,
        multiple: false,
        change: (e) => {
          this.form.isrightName = this.getName(
            getdictionarybystr(this.dictionaryObject, 'isright'),
            e,
            'itemcode',
            'cnname'
          )
        }
      },
      {
        title: '主体性质',
        required: false,
        prop: 'compProperty',
        type: 'select',
        disabled: false,
        options: getdictionarybystr(this.dictionaryObject, 'COMPPROPERTY'),
        optionValue: 'itemcode',
        optionLabel: 'cnname',
        showCode: false,
        multiple: true,
        change: (e) => {
          this.form.compPropertyName = this.getName(
            getdictionarybystr(this.dictionaryObject, 'COMPPROPERTY'),
            e,
            'itemcode',
            'cnname'
          )
        }
      },
      {
        title: '地区',
        required: false,
        prop: 'districtsub',
        type: 'cascader',
        disabled: false,
        options: this.areaList,
        multiple: true,
        props: {
          label: 'cname'
        },
        change: (e) => {
          this.form.district = this.getDistrict(e)
          this.form.areaName = this.getAreaNames(this.areaList, e).join(',')
        }
      }
    ]

    this.infoFormFields = defaultSetting.filter((item) => {
      if (this.type === 'issue') {
        return true
      } else {
        return item.title !== '主体性质'
      }
    })
  },
  methods: {
    async getBondTypeOptionsApi() {
      const data = await issuanceQueryBondTypeList()
      this.bondTypeOptions = data.reduce((pre, current) => {
        if (current) {
          const index = pre.findIndex((item) => item.value === current.bondTypeCode)
          if (index === -1) {
            pre.push({
              label: current.bondTypeName,
              value: current.bondTypeCode,
              children: []
            })
          } else {
            pre[index].children.push({
              label: current.bondTypeName2,
              value: current.bondTypeCode2
            })
          }
        }
        return pre
      }, [])
      console.log(this.bondTypeOptions)
    },
    getAllSelected(options, valueStr) {
      if (Array.isArray(options) && options.length === 0) return ''
      return options.map((option) => option[valueStr]).join(',')
    },
    getCascaderAllSelected(options) {
      if (!Array.isArray(options) || (Array.isArray(options) && options.length === 0)) return ''

      const selectedValues = []

      function pushSelectedValues(options) {
        if (Object.hasOwnProperty.call(options, 'children')) {
          if (Array.isArray(options.children) && options.children.length > 0) {
            for (let index = 0; index < options.children.length; index++) {
              const element = options.children[index]
              pushSelectedValues(element)
            }
          }
        } else {
          selectedValues.push(options.value)
        }
      }

      for (let index = 0; index < options.length; index++) {
        const element = options[index]
        pushSelectedValues(element)
      }

      return selectedValues.join(',')
    },
    geCascadertName(options, selectedValues) {
      if (!Array.isArray(selectedValues) || (Array.isArray(selectedValues) && selectedValues.length === 0)) return []
      const result = []

      // 遍历所有选中项
      selectedValues.forEach((path) => {
        // 从根节点开始查找
        let currentLevel = options
        let lastLabel = ''

        // 按照路径值逐级查找
        path.forEach((value, index) => {
          const foundItem = currentLevel.find((item) => item.value === value)
          if (foundItem) {
            lastLabel = foundItem.label
            if (foundItem.children && index < path.length - 1) {
              currentLevel = foundItem.children
            }
          }
        })

        if (lastLabel) {
          result.push(lastLabel)
        }
      })

      return result
    },
    getName(list, select, valueStr = 'itemcode', labelStr = 'cnname') {
      const arr = []
      if (Array.isArray(select)) {
        console.log(1)

        for (let index = 0; index < list.length; index++) {
          const element = list[index]
          if (select.includes(element[valueStr])) {
            arr.push(element[labelStr])
          }
        }
      } else {
        for (let index = 0; index < list.length; index++) {
          const element = list[index]
          if (select === element[valueStr]) {
            arr.push(element[labelStr])
          }
        }
      }

      return arr.join(',')
    },
    getDistrict(districtsub) {
      const district = []
      if (Array.isArray(districtsub) && districtsub.length > 0) {
        for (let index = 0; index < districtsub.length; index++) {
          const element = districtsub[index]
          if (Array.isArray(element) && element[2]) {
            district.push(element[2])
          }
        }
      }
      return district
    },
    getAreaNames(areaTrees, selectedCodes) {
      if (Array.isArray(selectedCodes) && selectedCodes.length > 0) {
        // 创建省份查找表 {id: province}
        const provinceMap = {}
        areaTrees.forEach((province) => {
          provinceMap[province.id] = province
        })

        return selectedCodes.map((codes) => {
          const [provinceCode, cityCode, districtCode] = codes

          // 查找省份
          const province = provinceMap[provinceCode]
          const provinceName = province?.cname || '未知省份'

          // 查找城市
          let cityName = '未知城市'
          let districtName = '未知区县'

          if (province && province.children) {
            const city = province.children.find((c) => c.id === cityCode)
            if (city) {
              cityName = city.cname || '未知城市'

              // 查找区县
              if (city.children) {
                const district = city.children.find((d) => d.id === districtCode)
                districtName = district?.cname || '未知区县'
              }
            }
          }

          return `${provinceName}/${cityName}/${districtName}`
        })
      } else {
        return []
      }
    },
    handleAdd() {
      this.quickSearchList.push(JSON.parse(JSON.stringify(this.form)))
      console.log('this.quickSearchList', this.quickSearchList)

      this.form = {
        checked: false
      }
      this.form.bondTypeAll = this.getCascaderAllSelected(this.bondTypeOptions)
      this.form.bondRatingAll = this.getAllSelected(
        getdictionarybystr(this.dictionaryObject, 'bondRatingCustomized'),
        'itemcode'
      )
      this.form.implyRatingAll = this.getAllSelected(
        getdictionarybystr(this.dictionaryObject, 'implyRatingCustomized'),
        'itemcode'
      )
      this.form.termStreAll = this.getAllSelected(
        getdictionarybystr(this.dictionaryObject, 'issueTermCustomized'),
        'itemcode'
      )
      this.form.compPropertyAll = this.getAllSelected(
        getdictionarybystr(this.dictionaryObject, 'COMPPROPERTY'),
        'itemcode'
      )
      this.$emit('getAllList', this.quickSearchList)
    },
    deleteItem(index) {
      this.quickSearchList.splice(index, 1)

      this.$emit('getAllList', this.quickSearchList)
    },
    // 数字转汉字
    numberToChinese(num) {
      if (isNaN(num) || num > 9999999999999999) {
        return '数字超出范围'
      }

      if (num === 0) {
        return '零'
      }

      // 定义数字对应的汉字
      const chineseNums = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九']
      // 定义单位
      const units = ['', '十', '百', '千']
      const bigUnits = ['', '万', '亿', '兆']

      // 处理负数
      let prefix = ''
      if (num < 0) {
        prefix = '负'
        num = Math.abs(num)
      }

      // 将数字转为字符串并分割为4位一组
      const numStr = Math.floor(num).toString()
      const segments = []
      for (let i = numStr.length; i > 0; i -= 4) {
        segments.unshift(numStr.slice(Math.max(0, i - 4), i))
      }

      let result = ''

      segments.forEach((segment, index) => {
        let segmentStr = ''
        let hasZero = false

        for (let i = 0; i < segment.length; i++) {
          const digit = parseInt(segment[i])
          const pos = segment.length - 1 - i

          if (digit === 0) {
            hasZero = true
          } else {
            if (hasZero) {
              segmentStr += chineseNums[0]
              hasZero = false
            }
            segmentStr += chineseNums[digit] + units[pos]
          }
        }

        if (segmentStr !== '') {
          segmentStr += bigUnits[segments.length - 1 - index]
        }

        result += segmentStr
      })

      // 处理连续的零
      result = result.replace(/零+/g, '零')
      // 去除末尾的零
      result = result.replace(/零+$/, '')
      // 处理一十开头的特殊情况
      if (result.startsWith('一十')) {
        result = result.substring(1)
      }

      return prefix + result
    },
    searchParamsChange() {
      this.$emit('getAllList', this.quickSearchList)
    },
    async getArea() {
      const res = await regionLink() // 企业id 暂时为空
      this.areaList = this.formatAreaList(res)
      console.log(this.areaList)
    },

    // 格式化省市区列表
    formatAreaList(areaList) {
      let arr = []
      arr = JSON.parse(JSON.stringify(areaList))

      const idMapping = arr.reduce((acc, el, i) => {
        acc[el.id] = i
        return acc
      }, {})

      const root = []
      arr.forEach((el) => {
        // 判断根节点
        if (!el.pid) {
          root.push(el)
          return
        }
        // 用映射表找到父元素
        const parentEl = arr[idMapping[el.pid]]
        if (parentEl) {
          // 把当前元素添加到父元素的`children`数组中
          parentEl.children = [...(parentEl.children || []), el]
        }
      })
      return root
    }
  }
}
</script>

<style lang="scss" scoped>
@import '../css/custom-save-search-form-temporary.scss';
</style>
