/* eslint-disable no-undef */
/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

(function(root, factory) {
  if (typeof define === 'function' && define.amd) {
    // AMD. Register as an anonymous module.
    define(['exports', 'echarts'], factory)
  } else if (typeof exports === 'object' && typeof exports.nodeName !== 'string') {
    // CommonJS
    factory(exports, require('echarts/lib/echarts'))
  } else {
    // Browser globals
    factory({}, root.echarts)
  }
})(this, function(exports, echarts) {
  var log = function(msg) {
    if (typeof console !== 'undefined') {
      console && console.error && console.error(msg)
    }
  }
  if (!echarts) {
    log('ECharts is not Loaded')
    return
  }

  var colorPalette = [
    '#E01F54',
    '#001852',
    '#f5e8c8',
    '#b8d2c7',
    '#c6b38e',
    '#a4d8c2',
    '#f3d999',
    '#d3758f',
    '#dcc392',
    '#2e4783',
    '#82b6e9',
    '#ff6347',
    '#a092f1',
    '#0a915d',
    '#eaf889',
    '#6699FF',
    '#ff6666',
    '#3cb371',
    '#d5b158',
    '#38b6b6'
  ]

  var theme = {
    color: colorPalette,

    visualMap: {
      color: ['#e01f54', '#e7dbc3'],
      textStyle: {
        color: '#333'
      }
    },

    candlestick: {
      itemStyle: {
        color: '#e01f54',
        color0: '#001852'
      },
      lineStyle: {
        width: 1,
        color: '#f5e8c8',
        color0: '#b8d2c7'
      },
      areaStyle: {
        color: '#a4d8c2',
        color0: '#f3d999'
      }
    },

    graph: {
      itemStyle: {
        color: '#a4d8c2'
      },
      linkStyle: {
        color: '#f3d999'
      }
    },

    gauge: {
      startAngle: 180,
      endAngle: 0,
      center: ['50%', '80%'],
      legend: {
        show: false
      },
      progress: {
        show: true,
        width: 20
      },
      pointer: {
        icon: 'path://M2090.36389,615.30999 L2090.36389,615.30999 C2091.48372,615.30999 2092.40383,616.194028 2092.44859,617.312956 L2096.90698,728.755929 C2097.05155,732.369577 2094.2393,735.416212 2090.62566,735.56078 C2090.53845,735.564269 2090.45117,735.566014 2090.36389,735.566014 L2090.36389,735.566014 C2086.74736,735.566014 2083.81557,732.63423 2083.81557,729.017692 C2083.81557,728.930412 2083.81732,728.84314 2083.82081,728.755929 L2088.2792,617.312956 C2088.32396,616.194028 2089.24407,615.30999 2090.36389,615.30999 Z',
        show: true,
        width: 6,
        backgroundColor: 'inherit'
      },
      axisLine: {
        lineStyle: {
          color: [
            [0.2, '#E01F54'],
            [0.8, '#b8d2c7'],
            [1, '#001852']
          ],
          width: 20
        }
      },
      axisTick: {
        distance: -35,
        splitNumber: 5,
        lineStyle: {
          width: 0.4,
          color: '#333'
        }
      },
      splitLine: {
        distance: -42,
        length: 5,
        lineStyle: {
          width: 1,
          color: '#333'
        }
      },
      axisLabel: {
        distance: 0,
        color: 'inherit',
        fontSize: 12
      },
      detail: {
        valueAnimation: true,
        color: 'inherit',
        fontSize: 14,
        offsetCenter: [0, '-25%']
      }
    }
  }

  echarts.registerTheme('roma', theme)
})
