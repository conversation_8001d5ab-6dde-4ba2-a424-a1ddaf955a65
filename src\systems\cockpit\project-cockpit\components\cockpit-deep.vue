<template>
  <div class="cockpit-deep">
    <div class="cockpit-deep-left">
      <quota-switching />
      <cash-flow />
    </div>
    <div class="cockpit-deep-middle">
      <bond-overview />
    </div>
    <div class="cockpit-deep-right">
      <company-announce />
      <mainbody-rating />
    </div>
  </div>
</template>

<script>
import quotaSwitching from './quota-switching.vue'
import cashFlow from "./cash-flow.vue"
import bondOverview from "./bond-overview.vue"
import companyAnnounce from "./company-announce.vue"
import mainbodyRating from "./mainbody-rating.vue"
export default {
  components: {
    quotaSwitching,
    cashFlow,
    bondOverview,
    companyAnnounce,
    mainbodyRating
  }
}
</script>

<style lang="scss" scoped>
@import '~assets/styles/portcss.scss';
.cockpit-deep {
  width: 100%;
  height: vh(908);
  margin-top: vh(16);
  margin-bottom: vh(16);
  padding-left: vw(19);
  padding-right: vw(18);
  display: flex;
  justify-content: space-between;
  &-left {
    width: vw(490);
    height: 100%;
  }
  &-middle {
    width: vw(868);
    height: 100%;
  }
  &-right {
    width: vw(490);
    height: 100%;
  }
  & > div {
    & > div:nth-of-type(2){
      margin-top: vh(16);
    }
  }
}
</style>
