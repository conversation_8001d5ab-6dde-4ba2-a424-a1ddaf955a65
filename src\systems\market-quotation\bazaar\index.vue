<template>
  <div class="bazaar">
    <el-tabs v-model="active" :class="{ 'bazaar-tabs': true, 'bazaar-tabs-three': active === 'three' }">
      <el-tab-pane label="国债" name="first">
        <z-national-debt />
      </el-tab-pane>

      <el-tab-pane label="国开债" name="second" :lazy="true">
        <z-national-k-debt />
      </el-tab-pane>

      <el-tab-pane name="three" :lazy="true">
        <template slot="label">
          <div class="interest-rate">
            利率债面板
            <i class="el-icon-s-marketing" />
          </div>
        </template>
        <nationalRate />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import zNationalDebt from './modules/national-debt.vue'
import zNationalKDebt from './modules/national-k-debt.vue'
import nationalRate from './modules/national-rate.vue'
export default {
  name: 'Bazaar',
  components: {
    zNationalDebt,
    zNationalKDebt,
    nationalRate
  },
  props: {
    menuinfo: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      active: 'first',
      url: ''
    }
  },
  created() {
    const params = this.menuinfo.meta.params
    console.log('params', params)

    this.active = params.tabName || 'first'
  },
  methods: {}
}
</script>

<style lang="scss" scoped>
@import '@/assets/styles/market.scss';

::v-deep .jr-radio-group .el-radio {
  min-width: 96px;
  margin-right: 16px;
}

::v-deep .jr-checkbox-group .el-checkbox {
  min-width: 80px;
  margin-right: 32px;
}
</style>
