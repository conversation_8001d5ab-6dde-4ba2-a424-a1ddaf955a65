<!--
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023-04-20 19:49:22
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2023-10-13 17:26:08
 * @Description: 组合单位净值走势
-->
<template>
  <div class="home-poc-item">
    <div class="home-poc-item--header">组合单位净值走势
      <el-form :model="form" style="flex: 1;padding-left: 20%;padding-right: 10px;">
        <jr-form-item-create :data="cols" :model="form" :column="2" style="line-height: 2;" />
      </el-form>
    </div>
    <TemplateModule
      class="home-poc-item--body"
      chart-seq="e5e9aa25d61343218d5d1d4b26d1cb47"
      chart-type="MULTILINE"
      style="padding-top: 10px;"
      :params="params"
    />
  </div>
</template>

<script>
import dayjs from 'dayjs'
import * as API from '@/api/home'
import TemplateModule from '@jupiterweb/components/template-module'
// 近一周、近一月、近三月、近半年、年初至今、近一年
const RANGE_LIST = [
  {
    text: '近一周',
    value: '01'
  },
  {
    text: '近一个月',
    value: '02'
  },
  {
    text: '近三个月',
    value: '03'
  },
  {
    text: '近半年',
    value: '04'
  },
  {
    text: '年初至今',
    value: '05'
  },
  {
    text: '近一年',
    value: '06'
  }
]
export default {
  components: {
    TemplateModule
  },
  data() {
    return {
      cols: [{
        type: 'select',
        options: RANGE_LIST,
        prop: 'range'
      }, {
        type: 'remoteSelect',
        api: '/invest/portfolio/ptloverview/PtlOverview001/getPortfolioId',
        prop: 'portFolioIds',
        placeholder: '请选择组合',
        // multiple: true,
        class: 'poc-portFolioIds-item',
        optionValue: 'id'
      }],
      form: {
        portFolioIds: '',
        range: '05'
      }
    }
  },
  computed: {
    params() {
      const { portFolioIds, range } = this.form
      const [vDate, mDate] = this.convertDate(range)
      return {
        mDate, vDate, portFolioId: portFolioIds// .toString()
      }
    }
  },
  created() {
    // this.initPortfolio()
  },
  methods: {
    async initPortfolio() {
      const res = await API.GetPortfolioList({
        length: 5,
        page: 1,
        order: 'PORTFOLIO_ID',
        sort: 'desc'
      })
      if (res && res.length) {
        this.cols && this.$set(this.cols[1], 'options', res)
        Object.assign(this.form, { portFolioIds: res.map(r => r.id) })
      }
    },
    convertDate(range) {
      const format = 'YYYY-MM-DD'
      const sysDate = this.$store.getters.systemTime
      const plateDate = dayjs(sysDate).format(format)
      switch (range) {
        case '01':
          return [dayjs(sysDate).subtract(7, 'day').format(format), plateDate]
        case '02':
          return [dayjs(sysDate).subtract(30, 'day').format(format), plateDate]
        case '03':
          return [dayjs(sysDate).subtract(90, 'day').format(format), plateDate]
        case '04':
          return [dayjs(sysDate).subtract(180, 'day').format(format), plateDate]
        case '05':
          return [dayjs(new Date(new Date(sysDate).getFullYear(), 0, 1)).format(format), plateDate]
        case '06':
          return [dayjs(sysDate).subtract(365, 'day').format(format), plateDate]
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import "./poc.scss";
</style>

<style lang="scss">
.poc-portFolioIds-item {
  // width: 150% !important;
  // margin-left: -50%;
  width: 200px;
  margin-left: 10px;
}
</style>
