<template>
  <el-dialog
    :visible.sync="dialogVisible"
    :show-close="false"
    width="320px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    custom-class="slide-captcha-dialog"
    @close="handleClose"
  >
    <div class="slide-captcha-container">
      <div class="slide-captcha-header">
        <span class="slide-captcha-title">安全验证</span>
        <i class="el-icon-close slide-captcha-close" @click="handleClose" />
      </div>

      <div class="slide-captcha-content">
        <div class="captcha-image-container">
          <!-- 背景图 -->
          <img class="background-image" :src="backgroundImage" />

          <!-- 滑块缺口 -->
          <div class="puzzle-hole" :style="{ left: targetLeft + 'px', top: puzzleTop + 'px' }" />

          <!-- 滑块 -->
          <div class="puzzle-piece" :style="{ left: puzzleLeft + 'px', top: puzzleTop + 'px' }" />

          <!-- 刷新按钮 -->
          <el-button class="refresh-btn" type="text" icon="el-icon-refresh" circle @click="refreshCaptcha" />
        </div>

        <!-- 滑动条 -->
        <div class="slider-container">
          <div class="slider-track">
            <div class="slider-mask" :style="{ width: sliderLeft + 'px' }" />
            <div
              class="slider-button"
              :style="{ left: sliderLeft + 'px' }"
              @mousedown.prevent="sliderTouchStart"
              @mousemove.prevent="sliderTouchMove"
              @mouseup.prevent="sliderTouchEnd"
            >
              <i class="el-icon-d-arrow-right" />
            </div>
          </div>
          <div class="slider-text">请向右拖动滑块完成拼图</div>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script>
const getImgSrc = () => {
  // 生成随机图片序号(1-5之间的整数)
  const randomImageNum = Math.floor(Math.random() * 4) + 1
  // 重新获取背景图片（添加时间戳防止缓存）
  return `https://butler.joyintech.com/miniprogram/${randomImageNum}.png?t=${new Date().getTime()}`
}
export default {
  name: 'SliderCaptcha',

  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },

  data() {
    return {
      dialogVisible: false,
      backgroundImage: getImgSrc(),
      puzzleSize: 40,
      puzzleLeft: 0,
      puzzleTop: 60,
      targetLeft: 200,
      sliderLeft: 0,
      isDragging: false,
      startX: 0,
      sliderText: '请向右拖动滑块完成拼图',
      tolerance: 10
    }
  },

  watch: {
    visible: {
      immediate: true,
      handler(val) {
        this.dialogVisible = val
        if (val) {
          this.resetCaptcha()
        }
      }
    }
  },

  mounted() {
    this.generateRandomPosition()
  },

  methods: {
    generateRandomPosition() {
      this.puzzleTop = Math.floor(Math.random() * (120 - this.puzzleSize)) + 20
      this.targetLeft = Math.floor(Math.random() * (240 - this.puzzleSize - 40)) + 80
    },

    resetCaptcha() {
      this.sliderLeft = 0
      this.puzzleLeft = 0
      this.backgroundImage = getImgSrc()
      this.generateRandomPosition()
      this.isDragging = false
    },

    refreshCaptcha() {
      this.resetCaptcha()
    },

    handleClose() {
      this.$emit('close')
    },

    sliderTouchStart(e) {
      this.isDragging = true
      this.startX = e.clientX
    },

    sliderTouchMove(e) {
      if (!this.isDragging) return

      const moveX = e.clientX - this.startX
      let newLeft = this.sliderLeft + moveX
      newLeft = Math.max(0, Math.min(280 - 40, newLeft))

      this.sliderLeft = newLeft
      this.startX = e.clientX
      this.puzzleLeft = this.sliderLeft
    },

    sliderTouchEnd() {
      if (!this.isDragging) return

      this.isDragging = false

      if (Math.abs(this.puzzleLeft - this.targetLeft) <= this.tolerance) {
        this.msgSuccess('验证成功')

        setTimeout(() => {
          this.$emit('success')
        }, 500)
      } else {
        this.msgError('验证失败，请重试')

        setTimeout(() => {
          this.resetCaptcha()
        }, 500)
      }
    }
  }
}
</script>

<style lang="scss">
.slide-captcha-dialog {
  .el-dialog__header {
    display: none;
  }

  .el-dialog__body {
    padding: 0;
  }
}

.slide-captcha-container {
  background-color: #ffffff;
  border-radius: 4px;
  overflow: hidden;
}

.slide-captcha-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #ebeef5;
}

.slide-captcha-title {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

.slide-captcha-close {
  font-size: 20px;
  color: #909399;
  cursor: pointer;

  &:hover {
    color: #409eff;
  }
}

.slide-captcha-content {
  padding: 0;
}

.captcha-image-container {
  position: relative;
  width: 100%;
  height: 160px;
  overflow: hidden;
}

.background-image {
  width: 100%;
  height: 100%;
  display: block;
  object-fit: cover;
}

.puzzle-hole {
  position: absolute;
  width: 40px;
  height: 40px;
  background-color: rgba(0, 0, 0, 0.3);
  border-radius: 4px;
}

.puzzle-piece {
  position: absolute;
  width: 40px;
  height: 40px;
  background-color: rgba(64, 158, 255, 0.8);
  border-radius: 4px;
  box-shadow: 0 0 6px rgba(0, 0, 0, 0.3);
}

.refresh-btn {
  position: absolute;
  top: 10px;
  right: 10px;
  padding: 8px;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  z-index: 10;

  &:hover {
    background-color: #fff;
  }
}

.slider-container {
  padding: 20px 10px;
  background-color: #fff;
}

.slider-track {
  position: relative;
  height: 40px;
  background-color: #f5f7fa;
  border-radius: 20px;
  border: 1px dashed #dcdfe6;
}

.slider-mask {
  position: absolute;
  height: 100%;
  background-color: rgba(64, 158, 255, 0.1);
  border-radius: 20px 0 0 20px;
}

.slider-button {
  position: absolute;
  top: 0;
  width: 40px;
  height: 38px;
  background-color: #fff;
  border-radius: 2px;
  border: 1px solid #409eff;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: background-color 0.3s;

  &:hover {
    background-color: #ecf5ff;
  }

  i {
    color: #409eff;
    font-size: 20px;
  }
}

.slider-text {
  text-align: center;
  margin-top: 12px;
  font-size: 14px;
  color: #606266;
}
</style>
