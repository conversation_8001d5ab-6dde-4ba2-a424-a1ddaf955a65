<!-- 承销商排名列表 -->
<template>
  <div class="rankingList">
    <div class="rankingListTitle">承销商排名列表</div>
    <jr-decorated-table
      style="height: 100%"
      custom-id="784a4c77c36c499e833a9e8f41cc8cf0"
      :custom-render="customRender"
      v-bind="{...$attrs, ...$props}"
    />
  </div>
</template>

<script>
export default {
  data() {
    return {
      customRender: {
        tqpmbd: (h, { row }) => {
          return <span> { row.tqpmbd}
            { row.tqpmbd > 0
              ? <jr-svg-icon icon-class='caret-up'/> : row.tqpmbd === 0 ? '' : <jr-svg-icon icon-class='caret-down'/>
            }
          </span>
        }
      }
    }
  },
  mounted() {

  },
  methods: {

  }
}
</script>
<style lang="scss">
.rankingList {
    margin-top: 10px;
    padding: 6px 10px 6px 10px;
    background: #fff;
    min-height: 400px;
    .el-table {
        height: 300px !important;
    }
    .rankingListTitle {
        font-size: 16px;
        color: #303133;
        font-weight: 700;
        padding-bottom: 6px;
        border-bottom: 1px solid #EBEEF5;
    }
    .jr-svg-icon--caret-up {
        color: red;
    }
    .jr-svg-icon--caret-down {
        color: green;
    }
}
</style>
