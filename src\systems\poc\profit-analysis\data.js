export const xdata = [
  '2022-10-12',
  '2022-10-13',
  '2022-10-14',
  '2022-10-17',
  '2022-10-18',
  '2022-10-19',
  '2022-10-20',
  '2022-10-21',
  '2022-10-24',
  '2022-10-25',
  '2022-10-26',
  '2022-10-27',
  '2022-10-28',
  '2022-10-31',
  '2022-11-01',
  '2022-11-02',
  '2022-11-03',
  '2022-11-04',
  '2022-11-07',
  '2022-11-08',
  '2022-11-09',
  '2022-11-10',
  '2022-11-11',
  '2022-11-14',
  '2022-11-15',
  '2022-11-16',
  '2022-11-17',
  '2022-11-18',
  '2022-11-21',
  '2022-11-22',
  '2022-11-23',
  '2022-11-24',
  '2022-11-25',
  '2022-11-28',
  '2022-11-29',
  '2022-11-30',
  '2022-12-01',
  '2022-12-02',
  '2022-12-05',
  '2022-12-06',
  '2022-12-07',
  '2022-12-08',
  '2022-12-09',
  '2022-12-12',
  '2022-12-13',
  '2022-12-14',
  '2022-12-15',
  '2022-12-16',
  '2022-12-19',
  '2022-12-20',
  '2022-12-21',
  '2022-12-22',
  '2022-12-23',
  '2022-12-26',
  '2022-12-27',
  '2022-12-28',
  '2022-12-29',
  '2022-12-30',
  '2023-01-02',
  '2023-01-03',
  '2023-01-04',
  '2023-01-05',
  '2023-01-06',
  '2023-01-09',
  '2023-01-10',
  '2023-01-11',
  '2023-01-12',
  '2023-01-13',
  '2023-01-16',
  '2023-01-17',
  '2023-01-18',
  '2023-01-19',
  '2023-01-20',
  '2023-01-23',
  '2023-01-24',
  '2023-01-25',
  '2023-01-26',
  '2023-01-27',
  '2023-01-30',
  '2023-01-31',
  '2023-02-01',
  '2023-02-02',
  '2023-02-03',
  '2023-02-06',
  '2023-02-07',
  '2023-02-08',
  '2023-02-09',
  '2023-02-10',
  '2023-02-13',
  '2023-02-14',
  '2023-02-15',
  '2023-02-16',
  '2023-02-17',
  '2023-02-20',
  '2023-02-21',
  '2023-02-22',
  '2023-02-23',
  '2023-02-24',
  '2023-02-27',
  '2023-02-28',
  '2023-03-01',
  '2023-03-02',
  '2023-03-03',
  '2023-03-06',
  '2023-03-07',
  '2023-03-08',
  '2023-03-09',
  '2023-03-10',
  '2023-03-13',
  '2023-03-14',
  '2023-03-15',
  '2023-03-16',
  '2023-03-17',
  '2023-03-20',
  '2023-03-21',
  '2023-03-22',
  '2023-03-23',
  '2023-03-24',
  '2023-03-27',
  '2023-03-28',
  '2023-03-29',
  '2023-03-30',
  '2023-03-31',
  '2023-04-03',
  '2023-04-04',
  '2023-04-05',
  '2023-04-06',
  '2023-04-07',
  '2023-04-10',
  '2023-04-11',
  '2023-04-12',
  '2023-04-13',
  '2023-04-14',
  '2023-04-17',
  '2023-04-18',
  '2023-04-19',
  '2023-04-20',
  '2023-04-21',
  '2023-04-24',
  '2023-04-25',
  '2023-04-26',
  '2023-04-27',
  '2023-04-28',
  '2023-05-01',
  '2023-05-02',
  '2023-05-03',
  '2023-05-04',
  '2023-05-05',
  '2023-05-08',
  '2023-05-09',
  '2023-05-10',
  '2023-05-11',
  '2023-05-12',
  '2023-05-15',
  '2023-05-16',
  '2023-05-17',
  '2023-05-18',
  '2023-05-19',
  '2023-05-22',
  '2023-05-23',
  '2023-05-24',
  '2023-05-25',
  '2023-05-26',
  '2023-05-29',
  '2023-05-30',
  '2023-05-31',
  '2023-06-01',
  '2023-06-02',
  '2023-06-05',
  '2023-06-06',
  '2023-06-07',
  '2023-06-08',
  '2023-06-09',
  '2023-06-12',
  '2023-06-13',
  '2023-06-14',
  '2023-06-15',
  '2023-06-16',
  '2023-06-19',
  '2023-06-20',
  '2023-06-21',
  '2023-06-22',
  '2023-06-23',
  '2023-06-26',
  '2023-06-27',
  '2023-06-28',
  '2023-06-29',
  '2023-06-30',
  '2023-07-03',
  '2023-07-04',
  '2023-07-05',
  '2023-07-06',
  '2023-07-07',
  '2023-07-10',
  '2023-07-11',
  '2023-07-12',
  '2023-07-13',
  '2023-07-14',
  '2023-07-17',
  '2023-07-18',
  '2023-07-19',
  '2023-07-20',
  '2023-07-21',
  '2023-07-24',
  '2023-07-25',
  '2023-07-26',
  '2023-07-27',
  '2023-07-28',
  '2023-07-31',
  '2023-08-01',
  '2023-08-02',
  '2023-08-03',
  '2023-08-04',
  '2023-08-07',
  '2023-08-08',
  '2023-08-09',
  '2023-08-10',
  '2023-08-11',
  '2023-08-14',
  '2023-08-15',
  '2023-08-16',
  '2023-08-17',
  '2023-08-18',
  '2023-08-21',
  '2023-08-22',
  '2023-08-23',
  '2023-08-24',
  '2023-08-25',
  '2023-08-28',
  '2023-08-29',
  '2023-08-30',
  '2023-08-31',
  '2023-09-01',
  '2023-09-04',
  '2023-09-05',
  '2023-09-06',
  '2023-09-07',
  '2023-09-08',
  '2023-09-11',
  '2023-09-12',
  '2023-09-13',
  '2023-09-14',
  '2023-09-15',
  '2023-09-18',
  '2023-09-19',
  '2023-09-20',
  '2023-09-21',
  '2023-09-22',
  '2023-09-25',
  '2023-09-26',
  '2023-09-27',
  '2023-09-28',
  '2023-09-29',
  '2023-10-02',
  '2023-10-03',
  '2023-10-04',
  '2023-10-05',
  '2023-10-06',
  '2023-10-09',
  '2023-10-10',
  '2023-10-11',
  '2023-10-12'
]
export const ydata1 = [
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  -0.0001,
  -0.0001,
  -0.0001,
  0,
  -0.0001,
  0,
  0,
  0,
  0,
  -0.0001,
  0,
  0,
  0,
  0,
  -0.0001,
  -0.0001,
  -0.0001,
  -0.0001,
  -0.0001,
  -0.0001,
  -0.0001,
  -0.0001,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0.0001,
  0.0001,
  0,
  0,
  0,
  0,
  0,
  0,
  0.0001,
  0.0001,
  0.0001,
  0.0001,
  0.0001,
  0.0001,
  0.0001,
  0.0001,
  0.0001,
  0.0001,
  0.0001,
  0.0001,
  0.0001,
  0.0001,
  0.0002,
  0.0002,
  0.0002,
  0.0002,
  0.0002,
  0.0002,
  0.0002,
  0.0002,
  0.0002,
  0.0002,
  0.0002,
  0.0002,
  0.0002,
  0.0002,
  0.0002,
  0.0001,
  0.0001,
  0.0001,
  0.0002,
  0.0002,
  0.0002,
  0.0002,
  0.0002,
  0.0002,
  0.0001,
  0.0002,
  0.0002,
  0.0002,
  0.0002,
  0.0001,
  0.0001,
  0.0002,
  0.0002,
  0.0002,
  0.0002,
  0.0002,
  0.0002,
  0.0002,
  0.0001,
  0.0001,
  0.0001,
  0.0001,
  0.0001,
  0.0001,
  0.0001,
  0.0001,
  0.0001,
  0.0002,
  0.0002,
  0.0002,
  0.0001,
  0.0001,
  0.0001,
  0.0002,
  0.0002,
  0.0002,
  0.0002,
  0.0002,
  0.0002,
  0.0002,
  0.0002,
  0.0002,
  0.0002,
  0.0002,
  0.0002,
  0.0002,
  0.0002,
  0.0002,
  0.0002,
  0.0002,
  0.0001,
  0.0001,
  0.0001,
  0.0001,
  0.0002,
  0.0002,
  0.0002,
  0.0002,
  0.0002,
  0.0002,
  0.0002,
  0.0001,
  0.0001,
  0.0001,
  0.0001,
  0.0001,
  0.0001,
  0.0001,
  0.0001,
  0.0001,
  0.0001,
  0.0001,
  0.0001,
  0.0001,
  0.0001,
  0.0001,
  0.0001,
  0.0001,
  0.0001,
  0.0001,
  0.0001,
  0.0001,
  0.0001,
  0.0001,
  0.0001,
  0.0001,
  0.0001,
  0.0001,
  0.0002,
  0.0002,
  0.0001,
  0.0001,
  0.0001,
  0.0001,
  0.0001,
  0.0001,
  0.0001,
  0.0001,
  0.0001,
  0.0001,
  0.0001,
  0.0001,
  0.0001,
  0.0001,
  0.0001,
  0.0001,
  0.0001,
  0.0001,
  0.0001,
  0.0001,
  0.0001,
  0.0001,
  0.0001,
  0.0001,
  0.0001,
  0.0001,
  0.0001,
  0.0001,
  0.0001,
  0.0002,
  0.0002,
  0.0002,
  0.0002,
  0.0002,
  0.0002,
  0.0002,
  0.0002,
  0.0002,
  0.0002,
  0.0001,
  0.0001,
  0.0001,
  0.0001,
  0.0001,
  0.0001,
  0.0001,
  0.0001,
  0.0001,
  0.0001,
  0.0001,
  0.0001,
  0.0001,
  0.0001,
  0.0001,
  0.0001,
  0.0001,
  0.0001,
  0.0001,
  0.0001,
  0.0001,
  0.0001,
  0.0001,
  0.0001,
  0.0001,
  0.0001,
  0.0001,
  0.0001,
  0,
  0,
  0.0001,
  0.0001,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0
]
export const ydata2 = [
  0.0152,
  0.0067,
  0.0308,
  0.0319,
  0.0297,
  0.0131,
  0.0073,
  0.0041,
  -0.0253,
  -0.0269,
  -0.019,
  -0.0259,
  -0.05,
  -0.0587,
  -0.0251,
  -0.0134,
  -0.0214,
  0.0106,
  0.0128,
  0.0058,
  -0.0036,
  -0.0113,
  0.0163,
  0.0178,
  0.0371,
  0.0286,
  0.0244,
  0.0198,
  0.0111,
  0.0112,
  0.0123,
  0.0078,
  0.0129,
  0.0015,
  0.0324,
  0.0336,
  0.0448,
  0.0384,
  0.0588,
  0.0645,
  0.0619,
  0.0621,
  0.0726,
  0.0606,
  0.0585,
  0.0609,
  0.0602,
  0.0608,
  0.0444,
  0.0272,
  0.0276,
  0.0291,
  0.027,
  0.0311,
  0.043,
  0.0385,
  0.0346,
  0.0386,
  0.0386,
  0.043,
  0.0443,
  0.0646,
  0.0679,
  0.0766,
  0.0777,
  0.0757,
  0.0778,
  0.093,
  0.1101,
  0.1099,
  0.108,
  0.1149,
  0.1217,
  0.1217,
  0.1217,
  0.1217,
  0.1217,
  0.1217,
  0.1271,
  0.1151,
  0.1256,
  0.1216,
  0.111,
  0.0964,
  0.0983,
  0.0935,
  0.1082,
  0.1016,
  0.1116,
  0.112,
  0.1062,
  0.0981,
  0.0823,
  0.1089,
  0.1118,
  0.1017,
  0.1009,
  0.0894,
  0.0848,
  0.0917,
  0.1071,
  0.1046,
  0.1081,
  0.1023,
  0.0862,
  0.0822,
  0.0784,
  0.0642,
  0.0754,
  0.0689,
  0.0695,
  0.0567,
  0.062,
  0.0567,
  0.0683,
  0.0729,
  0.0835,
  0.0803,
  0.0764,
  0.0729,
  0.0747,
  0.0834,
  0.0867,
  0.0973,
  0.1007,
  0.1007,
  0.099,
  0.1061,
  0.1012,
  0.0999,
  0.0991,
  0.0916,
  0.0977,
  0.1131,
  0.1165,
  0.1065,
  0.1034,
  0.0818,
  0.0684,
  0.063,
  0.0621,
  0.0699,
  0.0809,
  0.0809,
  0.0809,
  0.0809,
  0.0812,
  0.0776,
  0.0899,
  0.0805,
  0.0722,
  0.0705,
  0.0564,
  0.0727,
  0.0672,
  0.0624,
  0.0613,
  0.0582,
  0.0648,
  0.0498,
  0.0352,
  0.0329,
  0.0331,
  0.0285,
  0.0295,
  0.019,
  0.0212,
  0.036,
  0.0313,
  0.0216,
  0.0165,
  0.0248,
  0.0292,
  0.0313,
  0.0368,
  0.0366,
  0.0531,
  0.0632,
  0.0545,
  0.0527,
  0.0366,
  0.0366,
  0.0366,
  0.022,
  0.0316,
  0.0303,
  0.0253,
  0.0308,
  0.0443,
  0.046,
  0.0379,
  0.0309,
  0.0263,
  0.0313,
  0.038,
  0.031,
  0.0458,
  0.046,
  0.0374,
  0.0341,
  0.033,
  0.0258,
  0.0253,
  0.0208,
  0.0503,
  0.0481,
  0.0468,
  0.0711,
  0.077,
  0.0725,
  0.065,
  0.0744,
  0.0786,
  0.0704,
  0.0676,
  0.0643,
  0.0665,
  0.042,
  0.0344,
  0.0319,
  0.0243,
  0.0277,
  0.0151,
  0.0005,
  0.0082,
  -0.0083,
  -0.0011,
  -0.005,
  0.0067,
  0.0167,
  0.0163,
  0.0101,
  0.0171,
  0.0325,
  0.0248,
  0.0226,
  0.0083,
  0.0033,
  0.0107,
  0.0088,
  0.0024,
  0.0016,
  -0.0051,
  0,
  -0.0019,
  -0.0059,
  -0.0148,
  0.003,
  -0.0035,
  -0.0093,
  -0.0073,
  -0.0102,
  -0.0102,
  -0.0102,
  -0.0102,
  -0.0102,
  -0.0102,
  -0.0102,
  -0.0115,
  -0.0189,
  -0.0161,
  -0.0068
]
export const ydata3 = [
  -0.0152,
  -0.0067,
  -0.0308,
  -0.0318,
  -0.0296,
  -0.0131,
  -0.0073,
  -0.0041,
  0.0253,
  0.0268,
  0.0189,
  0.0259,
  0.0499,
  0.0587,
  0.025,
  0.0133,
  0.0214,
  -0.0106,
  -0.0128,
  -0.0058,
  0.0036,
  0.0112,
  -0.0163,
  -0.0178,
  -0.0371,
  -0.0287,
  -0.0245,
  -0.0199,
  -0.0112,
  -0.0113,
  -0.0124,
  -0.0079,
  -0.013,
  -0.0016,
  -0.0324,
  -0.0337,
  -0.0449,
  -0.0385,
  -0.0588,
  -0.0645,
  -0.0619,
  -0.0621,
  -0.0726,
  -0.0606,
  -0.0585,
  -0.0609,
  -0.0601,
  -0.0607,
  -0.0444,
  -0.0272,
  -0.0276,
  -0.029,
  -0.0269,
  -0.031,
  -0.0429,
  -0.0385,
  -0.0346,
  -0.0386,
  -0.0386,
  -0.0429,
  -0.0443,
  -0.0645,
  -0.0678,
  -0.0765,
  -0.0776,
  -0.0756,
  -0.0777,
  -0.0929,
  -0.1099,
  -0.1097,
  -0.1079,
  -0.1147,
  -0.1216,
  -0.1216,
  -0.1216,
  -0.1216,
  -0.1216,
  -0.1216,
  -0.1269,
  -0.115,
  -0.1254,
  -0.1215,
  -0.1109,
  -0.0962,
  -0.0982,
  -0.0933,
  -0.108,
  -0.1014,
  -0.1114,
  -0.1118,
  -0.1061,
  -0.098,
  -0.0822,
  -0.1087,
  -0.1116,
  -0.1016,
  -0.1007,
  -0.0893,
  -0.0847,
  -0.0915,
  -0.1069,
  -0.1045,
  -0.1079,
  -0.1021,
  -0.086,
  -0.082,
  -0.0782,
  -0.0641,
  -0.0752,
  -0.0688,
  -0.0694,
  -0.0566,
  -0.0619,
  -0.0566,
  -0.0682,
  -0.0727,
  -0.0834,
  -0.0801,
  -0.0763,
  -0.0728,
  -0.0746,
  -0.0832,
  -0.0865,
  -0.0972,
  -0.1005,
  -0.1005,
  -0.0988,
  -0.1059,
  -0.101,
  -0.0997,
  -0.099,
  -0.0914,
  -0.0975,
  -0.1129,
  -0.1163,
  -0.1063,
  -0.1032,
  -0.0816,
  -0.0683,
  -0.0629,
  -0.062,
  -0.0698,
  -0.0807,
  -0.0807,
  -0.0807,
  -0.0807,
  -0.081,
  -0.0774,
  -0.0897,
  -0.0804,
  -0.0721,
  -0.0704,
  -0.0562,
  -0.0726,
  -0.0671,
  -0.0622,
  -0.0611,
  -0.058,
  -0.0647,
  -0.0496,
  -0.0352,
  -0.0329,
  -0.033,
  -0.0284,
  -0.0294,
  -0.0189,
  -0.0212,
  -0.0359,
  -0.0312,
  -0.0215,
  -0.0165,
  -0.0247,
  -0.0291,
  -0.0312,
  -0.0367,
  -0.0364,
  -0.0529,
  -0.0631,
  -0.0544,
  -0.0526,
  -0.0365,
  -0.0365,
  -0.0365,
  -0.0219,
  -0.0315,
  -0.0302,
  -0.0252,
  -0.0307,
  -0.0442,
  -0.0458,
  -0.0377,
  -0.0308,
  -0.0262,
  -0.0312,
  -0.0379,
  -0.0309,
  -0.0457,
  -0.0459,
  -0.0373,
  -0.034,
  -0.0329,
  -0.0257,
  -0.0252,
  -0.0207,
  -0.0501,
  -0.048,
  -0.0467,
  -0.0709,
  -0.0768,
  -0.0723,
  -0.0648,
  -0.0742,
  -0.0784,
  -0.0702,
  -0.0674,
  -0.0642,
  -0.0664,
  -0.0419,
  -0.0343,
  -0.0318,
  -0.0242,
  -0.0276,
  -0.015,
  -0.0004,
  -0.0081,
  0.0084,
  0.0012,
  0.005,
  -0.0066,
  -0.0166,
  -0.0162,
  -0.01,
  -0.017,
  -0.0324,
  -0.0247,
  -0.0225,
  -0.0082,
  -0.0032,
  -0.0106,
  -0.0087,
  -0.0023,
  -0.0015,
  0.0051,
  0.0001,
  0.002,
  0.006,
  0.0149,
  -0.0029,
  0.0036,
  0.0094,
  0.0073,
  0.0103,
  0.0103,
  0.0103,
  0.0103,
  0.0103,
  0.0103,
  0.0103,
  0.0116,
  0.019,
  0.0162,
  0.0068
]
export const ydata4 = [
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0.0001,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0,
  0
]
