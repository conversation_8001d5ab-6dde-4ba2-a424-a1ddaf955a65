<!-- 发行明细 -->
<template>
  <div class="bondsDetail">
    <div class="searchForm">
      <el-form :model="searchForm" label-width="90px">
        <jr-form-item label="主体口径" class="formItem">
          <jr-combobox
            v-model="searchForm.mainCaliber"
            placeholder="请选择"
            clearable
            filterable
            :data="mainCaliberList"
            option-value="id"
            option-label="text"
          />
        </jr-form-item>
        <jr-form-item label="追溯时间点" class="formItem">
          <el-date-picker
            v-model="searchForm.backTime"
            placeholder="请选择"
            type="date"
            value-format="yyyy-MM-dd"
          />
        </jr-form-item>
        <el-button type="primary" class="btn" @click="queryData">查询</el-button>
      </el-form>
    </div>
    <BondBalanceAnalysis />
    <BondDetailsList v-bind="{...$attrs, ...$props}" />
  </div>
</template>

<script>
import BondBalanceAnalysis from './components/bondBalanceAnalysis/index.vue'
import BondDetailsList from './components/bondDetailsList.vue'
export default {
  components: { BondBalanceAnalysis, BondDetailsList },
  data() {
    return {
      searchForm: {
        mainCaliber: '',
        backTime: ''
      },
      mainCaliberList: []
    }
  },
  methods: {
    queryData() {
      console.log('222222')
    }
  }
}
</script>
<style lang="scss" scoped>
.bondsDetail {
  height: 100%;
  .el-form .el-form-item__label {
    padding-top: 12px;
  }
  .formItem {
    display: inline-block;
    width: 300px
  }
  .btn {
    display: inline-block;
    margin-left: 10px;
  }
  .searchForm {
    padding: 10px 0 10px 0;
    background: #fff;
    margin-bottom: 10px;
  }
}
</style>
