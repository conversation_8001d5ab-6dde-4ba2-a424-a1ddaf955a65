/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023-10-10 15:38:12
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2023-10-10 15:47:10
 * @Description: 公共方法、参数
 */
import { OPERATE_TYPES } from '@jupiterweb/constants'

/*
* 获取公共弹窗的 props
*/
export const getModalProps = {
  props: {
    // 表单数据对象(弹窗内业务组件 v-model 须绑定此对象，点击保存活提交时会传给后端)
    data: {
      type: Object,
      default: () => ({})
    },
    // 代办表单数据
    businessData: {
      type: Object,
      default: null
    },
    // 行数据(比如一览列表点击编辑某行)
    itemData: {
      type: Object,
      default: null
    },
    formID: {
      type: String,
      default: ''
    },
    // 自定义脚本
    validateRules: {
      type: Object,
      default: () => ({})
    },
    /* 操作类型，详见 import { OPERATE_TYPES } from '@jupiterweb/constants'
    * new // 新增
    * modify // 修改
    * import // 导入
    * export // 导出
    * delete // 删除
    * detail // 查看
    * revoke // 撤销
    */
    modalType: {
      type: String,
      default: ''
    },
    operateTypes: {
      type: Object,
      default: () => ({})
    },
    // 用户信息
    userInfo: {
      type: Object,
      default: () => ({})
    },
    moduleId: {
      type: String,
      default: ''
    },
    // 按钮权限相关
    permitdetail: {
      type: Object,
      default: () => ({})
    },
    // 菜单信息
    menuinfo: {
      type: Object,
      default: () => ({})
    },
    // 平台日期
    date: {
      type: Number,
      default: 0
    },
    // 选中的数据(按钮标识需配置 select/batch 前缀标识,例如: batch_anniu)
    selectedRows: {
      type: Array,
      default: () => ([])
    },
    // 标识为自定义列 固定弹窗打开的页面
    joinType: {
      type: Boolean,
      default: false
    },
    // 是否是代办中打开
    isProcess: {
      type: Boolean,
      default: false
    },
    // 代办中打开为 false 表示不可编辑
    editable: {
      type: Boolean,
      default: true
    },
    // 代办中打开参数
    initParams: {
      type: Object,
      default: () => ({})
    },
    closeModal: {
      type: Function,
      default: () => {}
    }
  }
}

/*
* 获取手写自定义列的 props
*/
export const getIndexProps = {
  props: {
    // 用户信息
    userInfo: {
      type: Object,
      default: () => ({})
    },
    // 按钮权限相关
    permitdetail: {
      type: Object,
      default: () => ({})
    },
    // 菜单信息
    menuinfo: {
      type: Object,
      default: () => ({})
    },
    // 操作类型枚举值
    operateTypes: {
      type: Object,
      default: () => ({})
    },
    // 按钮集合(查询区域位置)
    queryButtons: {
      type: Array,
      default: () => ([])
    },
    // 按钮集合(表头位置)
    headButtons: {
      type: Array,
      default: () => ([])
    },
    // 按钮集合(表格操作列位置)
    listButtons: {
      type: Array,
      default: () => ([])
    },
    // 平台日期，
    date: {
      type: Number,
      default: 0
    }
  }
}

/*
* 获取公共弹窗的 computed
*/
export const getModalComputed = {
  computed: {
    // 是否新增
    isCreate() {
      return this.modalType === OPERATE_TYPES.create
    },
    // 是否编辑
    isModify() {
      return this.modalType === OPERATE_TYPES.modify
    },
    // 是否查看
    isDetail() {
      return this.modalType === OPERATE_TYPES.detail
    },
    // 是否办理
    isDeal() {
      return this.modalType === OPERATE_TYPES.deal
    },
    // 是否维护
    isMountain() {
      return this.modalType === OPERATE_TYPES.mountain
    },
    // 是否撤销
    isBackout() {
      return this.modalType === OPERATE_TYPES.backout
    },
    // 是否禁用
    isDisabled() {
      return this.modalType === OPERATE_TYPES.detail
    }
  }
}
