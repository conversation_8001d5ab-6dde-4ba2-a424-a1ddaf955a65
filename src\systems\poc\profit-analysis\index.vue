<template>
  <div class="var-poc has-fullscreen">
    <div class="search-form">
      <el-form class="form-items-container four-columns">
        <!-- 组合名称 -->
        <jr-form-item label="组合名称">
          <jr-combobox
            v-model="searchForm.portfolioId"
            :data="portfolioList"
            option-value="id"
            show-code
            collapse-tags
            remote
            :remote-method="getPortfolioList"
          />
        </jr-form-item>

        <!-- 维度 -->
        <jr-form-item label="维度">
          <jr-combobox
            v-model="searchForm.dimension"
            :data="dimensionList"
          />
        </jr-form-item>

        <!-- 业绩基准 -->
        <jr-form-item label="业绩基准">
          <jr-combobox
            v-model="searchForm.benchmarks"
            :data="benchmarksList"
          />
        </jr-form-item>

        <!-- 查询日期 -->
        <jr-form-item
          label="查询日期"
        >
          <el-date-picker v-model="searchForm.cdate" value-format="yyyy-MM-dd" />
        </jr-form-item>

        <!-- 产品经理 -->
        <jr-form-item label="产品经理">
          <el-input
            v-model="searchForm.manager"
          />
        </jr-form-item>

        <!-- 投资经理 -->
        <jr-form-item label="投资经理">
          <el-input
            v-model="searchForm.tzmanager"
          />
        </jr-form-item>

        <!-- 是否穿透 -->
        <jr-form-item
          label="是否穿透"
        >
          <jr-radio-group
            v-model="searchForm.isPene"
            :data="YNList"
          />
        </jr-form-item>
      </el-form>
      <div class="btn-div">
        <el-button type="primary" @click="handleQuery">查询</el-button>
        <el-button>重置</el-button>
      </div>
    </div>
    <div>
      <!-- <div class="home-poc-item--header float-left">VaR
      </div> -->
      <jr-decorated-table
        ref="table"
        custom-id="c3d0fab56f6e489583859f7091be2fe1"
        v-bind="{ ...$attrs, params: queryParams, defaultExpandAll: true, noPagination: true, menuinfo: { pageId: 'Monitor', btnList: [{btnkey: 'bidding', btnPosition: 'LIST', icon: 'flag', btnnm: '投标'}]} }"
        @refreshed="queryEnd"
      />
    </div>

    <div class="echarts-box">
      <el-form class="form-items-container four-columns">
        <!-- 图表 -->
        <jr-form-item label="图表">
          <jr-combobox
            v-model="searchForm.chartstype"
            :data="chartstypeList"
          />
        </jr-form-item>

        <!-- 区间 -->
        <jr-form-item label="区间">
          <el-date-picker
            v-model="searchForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          />
        </jr-form-item>

        <!-- 周期 -->
        <jr-form-item label="周期">
          <jr-combobox
            v-model="searchForm.cycle"
            :data="cycleList"
          />
        </jr-form-item>

        <!-- 交易日历 -->
        <jr-form-item
          label="交易日历"
        >
          <jr-combobox
            v-model="searchForm.calendar"
            :data="calendarList"
          />
        </jr-form-item>
      </el-form>
      <div v-if="searchForm.portfolioId" class="echarts-item">
        <!-- <h4>持仓权重</h4> -->
        <!-- <echarts :options="options1" :styles="{ height: '100%' }" /> -->
        <TemplateModule ref="echartItem" style="height: 300px;" chart-seq="2cd23fe7be1b414e9687e612dd34bb33" chart-type="MULTILINE" :params="queryParams" />
      </div>
    </div>

  </div>
</template>

<script>
// import fullscreen from './fullscreen'
// import echarts from '@jupiterweb/components/echarts'
import { GetInfoFn } from 'jupiterweb/src/utils/api'
import { xdata, ydata1, ydata2, ydata3, ydata4 } from './data'
import TemplateModule from '@jupiterweb/components/template-module'
export default {
  components: {
    TemplateModule
    // fullscreen
    // echarts
  },
  data() {
    return {
      searchForm: {
        portfolioId: '',
        cdate: JSON.parse(sessionStorage.getItem('platDate')),
        isPene: 'N',
        dimension: 'ZCYJFL',
        benchmarks: 'POCJZ',
        chartstype: 'ZHB',
        dateRange: ['2023-07-12', '2023-08-12'],
        cycle: 'D',
        calendar: 'RLR'
      },
      portfolioList: [],
      chartstypeList: [{
        text: '总回报',
        value: 'ZHB'
      }],
      cycleList: [
        {
          text: '日',
          value: 'D'
        }
      ],
      calendarList: [{
        text: '日历日（不含周末）',
        value: 'RLR'
      }],
      benchmarksList: [{
        text: '沪深300',
        value: 'HS300'
      }, {
        value: 'POCJZ',
        text: '中债新综合指数(5-7年)财富指数'
      }],
      dimensionList: [{
        text: '资产一级分类',
        value: 'ZCYJFL'
      },
      {
        text: '组合',
        value: 'ZH'
      }],
      YNList: [{
        text: '不穿透',
        value: 'N'
      },
      {
        text: '穿透',
        value: 'Y'
      }],
      queryParams: {
        portfolioId: '',
        cdate: JSON.parse(sessionStorage.getItem('platDate')),
        isPene: 'N',
        dimension: 'ZCYJFL',
        benchmarks: 'POCJZ',
        chartstype: 'ZHB',
        dateRange: ['2023-07-12', '2023-08-12'],
        cycle: 'D',
        calendar: 'RLR'
      },
      options1: {}
    }
  },
  created() {
    this.getPortfolioList('')
  },
  mounted() {
    this.setOptions()
  },
  methods: {
    handleQuery() {
      Object.assign(this.queryParams, {
        ...this.searchForm
      })
      requestAnimationFrame(() => {
        this.$refs.table?.triggerTableRefresh(true, false)
        this.$refs.echartItem?.getChartTmpl('MULTILINE')
      })
    },
    setOptions() {
      this.options1 = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        legend: {},
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        dataZoom: {
          show: true
          // start: 0,
          // end: 50,
          // maxSpan: 80
        // zoomLock: true
        },
        xAxis: [{
          type: 'category',
          data: xdata
          // axisLine: {
          //   show: true,
          //   interval: 0,
          //   lineStyle: {
          //     width: 1,
          //     type: 'solid'
          //   }
          // },
          // axisTick: {
          //   show: false
          // },
          // axisLabel: {
          //   show: true
          // }
        }],
        yAxis: [{
          type: 'value'
          // axisTick: {
          //   show: false
          // },
          // axisLine: {
          //   show: false,
          //   lineStyle: {
          //     width: 1,
          //     type: 'solid'
          //   }
          // },
          // splitLine: {
          //   lineStyle: {
          //   }
          // }
        }],
        series: [{
          name: '总回报（%）',
          type: 'line',
          data: ydata1
        }, {
          name: '中债新综合指数(5-7年)财富指数',
          type: 'line',
          data: ydata2
        }, {
          name: '超额收益（%）',
          type: 'line',
          data: ydata3
        }, {
          name: '日回报（%）',
          type: 'line',
          data: ydata4
        }]
      }
    },
    filterSeries(data, flag) {
      const arr = []
      if (flag === 1) {
        let xdata = []
        const nameList = [{
          text: '持仓集中度%',
          value: 'positionConcentration'
        }, {
          text: '持仓权重%',
          value: 'positionWeight'
        }]
        nameList.map((item) => {
          xdata = (data.map(v => { return v[item.value] }))
          arr.push({ name: item.text, data: xdata })
        })
      }
      console.log(arr)
      return arr
    },
    filterXaxis(data) {
      const list = []
      data.map(item => list.push(item.finprodName))
      return list
    },
    queryEnd(ins) {
      // this.options1 = this.setOptions()
    },
    getPortfolioList(search) {
      const params = {
        search,
        page: 1,
        length: 100
      }
      GetInfoFn('/invest/portfolio/ptloverview/PtlOverview001/getPortfolioId', params).then((res) => {
        this.portfolioList = res.root
        if (res.root?.length > 0 && !this.searchForm.portfolioId) {
          this.queryParams.portfolioId = res.root[0].id
          this.searchForm.portfolioId = res.root[0].id
        }
      })
    }
  }
}
</script>

<style lang="scss">
</style>

<style lang="scss" scoped>
.var-poc{
  .search-form{
    display: flex;
    background: #fff;
    margin-bottom: 10px;
    padding: 8px 10px;
    .el-form{
      flex: 1;
      padding-right: 20px;
    }
    .btn-div{
      width: 122px;
      padding-top: 3px;
      position: relative;
      .el-link{
        position: absolute;
        bottom: 0;
        right: 0;
      }
    }
  }
  ::v-deep .el-tabs__header{
    background: #fff;
    padding: 0 10px;
    margin: 0;
  }
  .echarts-box{
    background: #fff;
    height: 400px;
    // display: flex;
    // justify-content: space-between;
    padding: 10px;
    .el-form{
      padding: 10px 0;
    }
    .echarts-item{
      width: 100%;
      height: 400px;
      border: 1px solid RGB(241,241,241);
      // h4{
      //   margin-bottom: 0;
      //   padding: 0 10px;
      //   line-height: 36px;
      //   height: 36px;
      //   font-size: 14px;
      //   background: RGB(250,250,250);
      //   border-bottom: 1px solid RGB(241,241,241);
      // }
      & > div{
        padding: 10px 10px 30px 10px;
      }
    }
  }
  ::v-deep .jr-radio-group .el-radio{
    min-width: auto !important;
  }
  ::v-deep .jr-decorated-table--body{
    height: 300px;
    .jr-table .el-table{
      height: 100%;
    }
  }
}
// @import "./poc.scss";

</style>
