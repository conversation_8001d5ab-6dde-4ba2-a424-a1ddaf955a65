<!--
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2024-01-10 16:54:13
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2024-01-19 11:37:03
 * @Description: brinson分析
-->
<template>
  <jr-decorated-table class="brinson-decorated-table" :params="{...queryParams}" custom-id="5b5dd007d0a64e6388efb8a0d84205e9" :menuinfo="{pageId: 'Brinson'}" v-bind="{...$attrs, ...$props}" :row-click="handleRowClick" />
</template>

<script>
export default {
  props: {
    params: {
      type: Object,
      default: () => ({})
    }
  },
  computed: {
    queryParams() {
      return { portfolioId: 'HYZZL01', ...this.params }
    }
  },
  mounted() {
    this.$el.parentNode.parentNode.style.background = '#fff'
  },
  methods: {
    handleRowClick(row) {
      row.industryLevelCode && row.industryLevelCode !== 'null' && this.$emit('setTargetParams', {
        'ea90e1d1987f43d0bb75d8e30d47d059': {
          assetType3: row.industryLevelCode
        }
      })
    }
  }
}
</script>

<style lang="scss">
.brinson-decorated-table {
  .jr-decorated-table--header {
    display: none;
  }
  &.jr-decorated-table--top-search {
    padding-top: 12px;
    background: #fff;
    overflow: hidden;
    .jr-pagination--right {
      display: none;
    }
  }
}
</style>
