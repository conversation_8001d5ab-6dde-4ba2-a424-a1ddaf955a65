<template>
  <div class="b-navbar navbar" :style="{ background: isVertical ? theme : '#fff' }">
    <div class="left-menu">
      <span class="el-dropdown-link user-company">
        <img src="~@/assets/images/home/<USER>" alt="公司">
        <span>{{ $store.getters.personInfo.companyName }}</span>
      </span>
      <el-dropdown @command="changeSysVersion">
        <span class="el-dropdown-link color-primary">
          <img src="~@/assets/images/home/<USER>" alt="切换">
          <span>{{ $store.getters.sysVersionName }}</span>
          <img src="~@/assets/images/home/<USER>" alt="切换">
        </span>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item
            v-for="item in $store.getters.personInfo.companyVerList || []"
            :key="item.id"
            :command="item.id"
          >
            {{ item.text }}
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>
    <div class="right-menu">
      <!-- 搜索 -->
      <el-popover
        v-if="$store.getters.showSearch && !(isTypeMenu && !isVertical)"
        v-model="isShowMenuTree"
        :width="320"
        trigger="hover"
        popper-class="right-menu__popover"
        class="right-menu__icon-item"
      >
        <jr-svg-icon slot="reference" icon-class="search" />
        <div v-clickoutside.native="() => isShowMenuTree = false">
          <el-input
            ref="menuKeywordRef"
            v-model="menuKeyword"
            size="mini"
            prefix-icon="el-icon-search"
            placeholder="输入关键字进行搜索"
          />
          <el-scrollbar>
            <el-tree
              ref="navTree"
              :data="menuTreeList"
              :props="{
                label: 'title',
                children: 'children'
              }"
              :filter-node-method="filterNavItem"
              @node-click="onClickNavItem"
            />
          </el-scrollbar>
        </div>
      </el-popover>
      <el-popover
        v-if="$store.getters.showThemeSwitch"
        placement="bottom"
        width="290"
        :offset="20"
        class="right-menu__icon-item"
        trigger="hover"
      >
        <setting />
        <jr-svg-icon slot="reference" icon-class="color" />
      </el-popover>
      <el-dropdown class="avatar-container right-menu-item hover-effect" trigger="hover">
        <div class="avatar-wrapper">
          <div class="avatar-wrapper-user">
            <img src="~@/assets/images/home/<USER>" alt="头像">
            <span>{{ userInfo.username || userInfo.userid }}</span>
          </div>
          <img src="~@/assets/images/home/<USER>" alt="头像">
        </div>
        <template v-slot:dropdown>
          <el-dropdown-menu class="avatar-container-menu">
            <el-dropdown-item class="avatar-container-menu-item">
              <img src="~@/assets/images/home/<USER>" alt="头像">
              <span>{{ userInfo.username }}</span>
            </el-dropdown-item>
            <el-dropdown-item divided @click.native="handlePersonal">个人中心</el-dropdown-item>
            <el-dropdown-item @click.native="logout">安全退出</el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
    <div 
      ref='cockpitEntry'
      class="cockpit-entry"
      :style="{ 
        left: `${posX}px`, 
        top: `${posY}px` 
      }"
      @mousedown.stop="startDrag"
    >
      <img
        src="@/assets/cockpit/cockpit_entry.png" 
        alt=""
        style="width: 100%;height:100%;"
      >
    </div>
    <ThemePicker v-show="false" />
  </div>
</template>

<script>
import Navbar from '@jupiterweb/layout/components/navbar'
import { getUserInfo } from '@/api/home'
import router, { createRouter } from '@jupiterweb/router'
import Paths from '@jupiterweb/utils/systopath'
import ThemePicker from '@jupiterweb/components/theme-picker/index.vue'
import { resizeAllECharts } from '@/assets/js/echartsResize.js'
export default {
  name: 'JrNavbar',
  components: {
    ThemePicker
  },
  extends: Navbar,
  data() {
    return {
      posX: 0,
      posY: 0,
      isDragging: false,
      offsetX: 0,
      offsetY: 0,
      originX: 0
    }
  },
  computed: {
    currentTab() {
      // 获取当前激活的标签页
      return this.$store.getters.currentTab
    }
  },
  watch: {
    'theme': {
      handler(val) {
        const defaultTheme = (window.S_CONFIG.DEFAULT_THEME || this.variables?.theme || '').toLowerCase()
        if (val !== defaultTheme) {
          document.body.style.setProperty('--theme-mix-color', 'var(--theme--color)')
          document.body.style.setProperty('--theme-mix-color-light', 'var(--theme--color-light-9)')
        } else {
          document.body.style.setProperty('--theme-mix-color', '#856ffe')
          document.body.style.setProperty('--theme-mix-color-light', `rgba(133, 111, 254, 0.1)`)
        }
      }
    },
    // 进入当前页触发所以echarts的resize事件
    currentTab(val) {
      console.log('currentTab',this.$store.getters.currentTab);
      this.$nextTick(()=>{
        resizeAllECharts(val)
      })
    }
  },
  created() {
    this.getExtUserInfo()
  },
  mounted() {
    this.initPosition()
    window.addEventListener('resize', this.initPosition)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.initPosition)
  },
  methods: {
    async getExtUserInfo() {
      const res = await getUserInfo()
      if (res) {
        this.$store.dispatch('system/setPersonInfo', res)
      }
    },
    async changeSysVersion(companyVer) {
      const self = this
      if (self.$store.getters.personInfo?.companyVerList?.length <= 1) return
      self.$store.commit('system/CHANGE_SYS_VERSION', companyVer)
      router.matcher = createRouter().matcher
      self.$store.dispatch('GetMenuAuth').then((res) => {
        if (Object.keys(res).length && JSON.stringify(this.$store.getters.currentSystem) !== '{}') {
          const { sysId, routerflag: sysflag } = this.$store.getters.currentSystem
          const dashboard = Paths()[sysId]
          const plainMenus = this.$store.getters.permissions[sysId] || []
          self.$store.dispatch('GenerateRoutes', { menus: plainMenus, dashboard, sysflag }).then((accessRoutes) => {
            // 根据menus权限生成可访问的路路由表
            router.addRoutes(accessRoutes) // 动态添加可访问路由表
            self.$store.dispatch('tagsView/delOthersViews', self.$store.getters.showDashboard ? self.visitedViews[0] : [])
            self.$store.dispatch('tagsView/updateCurrentTab', '/ports/index')
          })
        }
      })
    },
    handlePersonal() {
      // 个人中心菜单
      this.$store.dispatch('tagsView/updateCurrentTab', {
        path: '/1366051139997949952'
        // component: (resolve) => require(['@/systems/personal'], resolve),
        // meta: {
        //   title: '个人中心'
        // }
      })
    },
    /**
     * 初始化熊猫位置
     */
    initPosition() {
      let cockpitEntry = this.$refs.cockpitEntry.getBoundingClientRect()
      this.posX = window.innerWidth - cockpitEntry.width
      this.posY = window.innerHeight * 0.6 - cockpitEntry.height
      this.originX = this.posX
    },
    /**
     * 开始拖拽
     */
    startDrag(event) {
      cancelAnimationFrame(this.animationId)
      this.isDragging = true;
      // 计算鼠标点击位置与 div 左上角的偏移
      this.offsetX = event.clientX - this.posX;
      this.offsetY = event.clientY - this.posY;
      
      // 添加全局事件监听
      document.addEventListener('mousemove', this.onDrag);
      document.addEventListener('mouseup', this.stopDrag);
    },
    /**
     * 拖拽中
     */
    onDrag(event) {
      if (this.isDragging) {
        // 更新 div 位置
        this.posX = event.clientX - this.offsetX;
        this.posY = event.clientY - this.offsetY;
        console.log(event)
      }
    },
    /**
     * 停止拖拽
     */
    stopDrag() {
      this.isDragging = false;
      // 移除全局事件监听
      document.removeEventListener('mousemove', this.onDrag);
      document.removeEventListener('mouseup', this.stopDrag);
      this.animationId = requestAnimationFrame(this.clearDragRight)
      if(this.posX === this.originX){
        this.$store.dispatch('tagsView/updateCurrentTab', {
          path: '/1369319537827659776',
          // 跳转需要刷新的话 refresh传true
          meta: {
            refresh: true,
            params: {}
          }
        })
      }
    },
    /**
     * 让熊猫靠右
     */
    clearDragRight() {
      if(this.posX >= this.originX){
        this.posX = this.originX
        cancelAnimationFrame(this.animationId)
      }else{
        this.posX += 10
        this.animationId = requestAnimationFrame(this.clearDragRight)
      }
    }
  },
}
</script>
<style lang="scss">
.b-navbar {
  display: flex;
  justify-content: space-between;
  padding-left: calc(var(--jr-sidebar-width) + 50px);
  align-items: center;
  .left-menu .el-dropdown-link {
    font-size: var(--el-font-size-base);
    cursor: pointer;
    color: var(--jr-sidebar-active-submenu-text-color);
    background-color: var(--jr-sidebar-active-submenu-bg-color);
    padding: 5px 9px;
    border-radius: 4px;
    line-height: 22px;
    display: inline-flex;
    align-items: center;
    span {
      margin-left: 10px;
      font-weight: 600;
      & + img {
        margin-left: 10px;
      }
    }
    &.color-primary {
      color: var(--theme--color);
      background-color: var(--theme--color-light-9);
      margin-left: 16px;
    }
  }
}
.avatar-container-menu.el-dropdown-menu--medium {
  padding: 8px 12px;
  min-width: 150px;
  .el-dropdown-menu__item {
    line-height: 32px;
    &.el-dropdown-menu__item--divided {
      margin-top: 8px;
      margin-bottom: 8px;

    }
  }
  .avatar-container-menu-item {
    img {
      margin-right: 10px;
      vertical-align: middle;
    }
  }
}
.hideSidebar .b-navbar {
  padding-left: calc(var(--jr-sidebar-hide-size) + 50px);
}
.b-navbar .right-menu {
  height: 36px;
  .avatar-container {
    background-color: var(--theme--color) !important;
    border-radius: 8px;
    color: #fff;
    margin-left: 16px;
  }
  .avatar-container .avatar-wrapper > img {
    margin-left: 6px;
  }
  .right-menu-item .avatar-wrapper-user {
    color: #fff;
    font-weight: 600;
  }
  .avatar-wrapper-user img {
    height: 28px;
    width: 28px;
    margin-left: -30px;
  }
  .el-dropdown-menu {
    .el-dropdown-item {
      img {
        margin-right: 10px;
      }
    }
  }
}
.cockpit-entry {
  width: 55px;
  height: 66px;
  position: fixed;
  cursor: pointer;
  z-index: 9;
  user-select:none;
  -moz-user-select:none;/*火狐*/
  -webkit-user-select:none;/*webkit浏览器*/
  -ms-user-select:none;/*IE10*/
  -khtml-user-select:none;/*早期浏览器*/
  img{
      width: 100px;
      height: 100px;
      user-select:none;
      pointer-events: none;
  }
}
</style>
