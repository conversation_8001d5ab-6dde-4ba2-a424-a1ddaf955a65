<template>
  <!-- POC专用 -->
  <!-- 债券概览 -->
  <div class="poc-home">
    <h3>债券概览</h3>
    <div>
      <div class="poc-home--block">
        <div v-for="(item, index) in blockList" :key="index" class="poc-home--block-item">
          <div class="poc-home--block-item-name">{{ item.name }}</div>
          <div class="poc-home--block-item-value-item color-primary">
            <span>{{ item.value1 }}</span>
            <span>{{ item.value2 }}</span>
          </div>
          <div class="poc-home--block-item-value-item">
            <span>{{ item.label1 }}</span>
            <span>{{ item.label2 }}</span>
          </div>
        </div>
      </div>
      <div class="jr-decorated-table--body" style="display: flex; height: calc(100% - 140px);">
        <template-module style="width: 70%" chart-seq="8825bd90a4524ceb84b0e5e251655a0b" chart-type="STACKEDBAR" />
        <template-module style="width: 30%" chart-seq="b60a2b7ab14446a6b4ba1b73b2022f95" chart-type="TABLE" />
      </div>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      blockList: [
        {
          name: '存续期债券',
          label1: '债券数',
          label2: '余额(亿元)',
          value1: '59',
          value2: '500.96'
        },
        {
          name: '近期付息兑付',
          label1: '债券数',
          label2: '现金流(亿元)',
          value1: '13',
          value2: '46.01'
        },
        {
          name: '近期新发债券',
          label1: '债券数',
          label2: '规模(亿元)',
          value1: '0',
          value2: '0.00'
        },
        {
          name: '综合成本',
          label1: '债券数',
          label2: '票面加权(%)',
          value1: '59',
          value2: '3.88'
        }
      ]
    }
  }
}
</script>
<style lang="scss" scoped>
.poc-home--block {
  display: flex;
  flex-wrap: nowrap;
  margin-top: 10px;
  margin-bottom: 30px;
  height: 100px;
  justify-content: space-between;
}
.poc-home--block-item {
  width: 24%;
  padding: 12px 14px;
  border-radius: 4px;
  background-color: #fff;
  border: 1px solid #e5e5e5;
}
.poc-home--block-item-name {
  font-size: 14px;
  font-weight: 700;
  color: #333;
  margin-bottom: 6px;
}
.poc-home--block-item-value-item {
  display: flex;
  justify-content: space-between;
}
.color-primary {
  font-weight: bold;
  color: var(--theme--color);
  font-size: 14px;
  line-height: 28px;
}
</style>
