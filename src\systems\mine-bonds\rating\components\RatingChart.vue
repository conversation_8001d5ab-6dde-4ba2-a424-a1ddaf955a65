<template>
  <div ref="chartContainer" class="rating-chart">
    <canvas
      ref="ratingCanvas"
      style="width: 100%; height: 100%; display: block"
      :style="{ cursor: isDragging ? 'grabbing' : 'grab' }"
      @mousedown="startDrag"
      @mousemove="onDrag"
      @mouseup="endDrag"
      @mouseleave="endDrag"
    />
    <!-- 添加横向滚动提示 -->
    <div v-if="canScroll" class="scroll-hint">
      <span>← 横向拖动查看更多 →</span>
    </div>
  </div>
</template>

<script>
export default {
  name: 'RatingChart',
  props: {
    // 评级数据
    ratingData: {
      type: Array,
      required: true,
      default: () => []
    },
    // 图例显示状态
    legendStatus: {
      type: Object,
      default: () => ({})
    },
    // 图表高度
    chartHeight: {
      type: Number,
      default: 270
    },
    // 弹窗标题
    tooltipTitle: {
      type: String,
      default: '评级变更'
    },
    // 评级颜色配置
    ratingColors: {
      type: Object,
      default: () => ({})
    },
    // 图表模式: 'agency'(按评级机构) 或 'timeline'(按时间序列)
    chartMode: {
      type: String,
      default: 'agency',
      validator: (value) => ['agency', 'timeline'].includes(value)
    },
    // 简化模式，不显示标注点、图例、Y轴轴线和纵轴网格线
    simpleMode: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      // 活动提示信息
      activeTooltip: null,
      // 存储时间轴位置信息，用于纵轴线检测
      datePositions: {},
      // 存储时间点有效日期列表
      validDates: [],
      // 记录当前悬停的日期（用于临时显示日期标签）
      hoverDate: null,
      // 存储图例区域信息
      legendAreas: [],
      // 存储点位置信息
      ratingPointsData: [],
      // 用于ResizeObserver
      resizeObserver: null,
      // 拖动相关状态
      isDragging: false,
      dragStartX: 0,
      offsetX: 0,
      maxOffsetX: 0,
      // 是否可以滚动
      canScroll: false,
      // 日期总宽度
      totalWidth: 0,
      // 可视区域宽度
      viewportWidth: 0,
      // 性能优化：缓存相关
      canvasRef: null,
      ctxRef: null,
      requestAnimationFrameId: null,
      timeoutIds: new Set(),
      isDestroyed: false,
      // 缓存计算结果
      cachedAgencyList: null,
      cachedValidDates: null,
      dataHashCache: null,
      // 组件激活状态
      isComponentActive: true,
      // 重绘尝试次数
      redrawAttempts: 0,
      // 最大重绘尝试次数
      maxRedrawAttempts: 1
    }
  },
  computed: {
    // 计算数据哈希，用于检测数据变化
    dataHash() {
      return JSON.stringify(this.ratingData) + JSON.stringify(this.legendStatus)
    }
  },
  watch: {
    ratingData: {
      handler() {
        this.invalidateCache()
        this.scheduleRedraw()
      },
      deep: true
    },
    legendStatus: {
      handler() {
        this.scheduleRedraw()
      },
      deep: true
    }
  },
  mounted() {
    this.initChart()
    // 添加窗口大小变化监听，重绘图表
    window.addEventListener('resize', this.handleResize)

    // 使用ResizeObserver监听容器大小变化
    if (typeof ResizeObserver !== 'undefined') {
      this.resizeObserver = new ResizeObserver(() => {
        this.scheduleRedraw()
      })
      this.resizeObserver.observe(this.$refs.chartContainer)
    }
  },
  // 处理keep-alive组件重新激活时的canvas尺寸问题
  activated() {
    this.isComponentActive = true
    // 重置重绘计数器，确保重新激活时能正常重绘
    this.redrawAttempts = 0
    // 使用$nextTick确保DOM已经完全渲染
    this.$nextTick(() => {
      // 延迟一下确保容器尺寸已经恢复
      const timeoutId = setTimeout(() => {
        if (!this.isDestroyed && this.isComponentActive) {
          this.initChart()
        }
      }, 100)
      this.timeoutIds.add(timeoutId)
    })
  },
  // 组件失活时清理tooltip等状态
  deactivated() {
    this.isComponentActive = false
    this.clearStates()
    // 清理所有定时器，防止在非激活状态下继续重绘
    this.timeoutIds.forEach((id) => clearTimeout(id))
    this.timeoutIds.clear()
    // 清理requestAnimationFrame
    if (this.requestAnimationFrameId) {
      cancelAnimationFrame(this.requestAnimationFrameId)
      this.requestAnimationFrameId = null
    }
  },
  beforeDestroy() {
    this.isDestroyed = true
    this.cleanup()
  },
  methods: {
    // 清理资源，防止内存泄漏
    cleanup() {
      // 移除窗口大小变化监听
      window.removeEventListener('resize', this.handleResize)

      // 移除ResizeObserver
      if (this.resizeObserver) {
        this.resizeObserver.disconnect()
        this.resizeObserver = null
      }

      // 清理setTimeout
      this.timeoutIds.forEach((id) => clearTimeout(id))
      this.timeoutIds.clear()

      // 清理requestAnimationFrame
      if (this.requestAnimationFrameId) {
        cancelAnimationFrame(this.requestAnimationFrameId)
        this.requestAnimationFrameId = null
      }

      // 清理canvas事件监听器
      const canvas = this.canvasRef
      if (canvas) {
        // 克隆节点以移除所有事件监听器
        const newCanvas = canvas.cloneNode(true)
        canvas.parentNode?.replaceChild(newCanvas, canvas)
      }

      // 清理引用
      this.canvasRef = null
      this.ctxRef = null
      this.clearStates()
      this.invalidateCache()
    },

    // 清理状态
    clearStates() {
      this.activeTooltip = null
      this.hoverDate = null
      this.isDragging = false
      // 重置重绘计数器
      this.redrawAttempts = 0
    },

    // 使cache失效
    invalidateCache() {
      this.cachedAgencyList = null
      this.cachedValidDates = null
      this.dataHashCache = null
    },

    // 优化的重绘调度
    scheduleRedraw() {
      // 如果组件未激活，不进行重绘
      if (!this.isComponentActive) {
        return
      }

      if (this.requestAnimationFrameId) {
        cancelAnimationFrame(this.requestAnimationFrameId)
      }

      this.requestAnimationFrameId = requestAnimationFrame(() => {
        if (!this.isDestroyed && this.isComponentActive) {
          this.drawRatingChart()
        }
        this.requestAnimationFrameId = null
      })
    },

    // 获取canvas引用（缓存）
    getCanvas() {
      if (!this.canvasRef) {
        this.canvasRef = this.$refs.ratingCanvas
      }
      return this.canvasRef
    },

    // 获取context引用（缓存）
    getContext() {
      const canvas = this.getCanvas()
      if (!canvas) return null

      if (!this.ctxRef) {
        this.ctxRef = canvas.getContext('2d')
      }
      return this.ctxRef
    },

    // 通用导出图表方法，确保纯白色背景
    exportChart(filename = '评级图表', format = 'jpg') {
      const canvas = this.getCanvas()
      if (!canvas) return null

      try {
        // 创建一个离屏画布，确保背景为白色
        const offscreenCanvas = document.createElement('canvas')
        offscreenCanvas.width = canvas.width
        offscreenCanvas.height = canvas.height

        const offCtx = offscreenCanvas.getContext('2d')

        // 填充纯白色背景
        offCtx.fillStyle = '#ffffff'
        offCtx.fillRect(0, 0, offscreenCanvas.width, offscreenCanvas.height)

        // 将原始画布内容绘制到新画布上
        offCtx.drawImage(canvas, 0, 0)

        // 设置导出选项
        const exportOptions = {
          type: format.toLowerCase() === 'png' ? 'image/png' : 'image/jpeg',
          quality: format.toLowerCase() === 'png' ? 1 : 0.95
        }

        // 从离屏画布创建数据URL
        const dataURL = offscreenCanvas.toDataURL(exportOptions.type, exportOptions.quality)

        // 创建下载链接
        const link = document.createElement('a')
        link.download = `${filename}.${format.toLowerCase()}`
        link.href = dataURL

        // 触发下载
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)

        return dataURL
      } catch (error) {
        console.error('导出图表失败:', error)
        return null
      }
    },

    // 初始化图表
    initChart() {
      this.$nextTick(() => {
        // 清理之前的状态
        this.clearStates()

        // 确保容器存在且有有效尺寸
        const container = this.$refs.chartContainer
        if (!container) {
          console.warn('图表容器不存在，无法初始化')
          return
        }

        const rect = container.getBoundingClientRect()
        if (rect.width === 0 || rect.height === 0) {
          console.warn('图表容器尺寸为0，延迟初始化')
          // 延迟重试
          const timeoutId = setTimeout(() => {
            if (!this.isDestroyed) {
              this.initChart()
            }
          }, 100)
          this.timeoutIds.add(timeoutId)
          return
        }

        this.drawRatingChart()
        this.initMouseEvents()
      })
    },

    // 窗口大小变化处理
    handleResize() {
      this.scheduleRedraw()
    },

    // 强制刷新图表 - 提供给外部调用
    forceRefresh() {
      // 如果组件未激活，不进行刷新
      if (!this.isComponentActive) {
        return
      }

      // 清理状态
      this.clearStates()
      this.invalidateCache()

      // 延迟执行，确保DOM已更新
      this.$nextTick(() => {
        const timeoutId = setTimeout(() => {
          if (!this.isDestroyed && this.isComponentActive) {
            this.drawRatingChart()
          }
        }, 50)
        this.timeoutIds.add(timeoutId)
      })
    },

    // 绘制评级图表
    drawRatingChart() {
      const canvas = this.getCanvas()
      if (!canvas) return

      const ctx = this.getContext()
      if (!ctx) return

      // 获取画布的实际宽高
      const dpr = window.devicePixelRatio || 1
      const rect = canvas.getBoundingClientRect()

      // 检查容器尺寸是否有效，如果宽高为0则延迟重绘
      if (rect.width === 0 || rect.height === 0) {
        console.warn('Canvas容器尺寸为0，延迟重绘图表')
        // 检查重绘次数是否超过限制
        if (this.redrawAttempts >= this.maxRedrawAttempts) {
          console.warn(`Canvas容器尺寸异常，已重试${this.redrawAttempts}次，停止重绘`)
          return
        }
        
        // 只有在组件处于激活状态时才尝试重绘
        if (this.isComponentActive) {
          this.redrawAttempts++
          // 延迟100ms后重试
          const timeoutId = setTimeout(() => {
            if (!this.isDestroyed && this.isComponentActive) {
              this.drawRatingChart()
            }
          }, 100)
          this.timeoutIds.add(timeoutId)
        }
        return
      }

      // 重绘成功，重置计数器
      this.redrawAttempts = 0

      // 设置canvas元素的尺寸（样式尺寸）
      canvas.style.width = `${rect.width}px`
      canvas.style.height = `${rect.height}px`

      // 设置canvas的实际尺寸（考虑DPR）
      canvas.width = rect.width * dpr
      canvas.height = rect.height * dpr

      // 根据DPR缩放绘图上下文
      ctx.scale(dpr, dpr)

      // 清空画布
      ctx.clearRect(0, 0, rect.width, rect.height)

      // 记录可视区域宽度
      this.viewportWidth = rect.width

      // 根据图表模式选择不同的绘制方法
      if (this.chartMode === 'timeline') {
        this.drawTimelineChart(ctx, rect)
      } else {
        this.drawAgencyChart(ctx, rect)
      }

      // 如果存在活动的工具提示，则显示它
      if (this.activeTooltip) {
        // 绘制十字线
        if (this.activeTooltip.crosshairX !== undefined) {
          this.drawCrosshair(ctx, this.activeTooltip.crosshairX, rect)
        }

        // 绘制tooltip
        this.drawTooltip(ctx, this.activeTooltip.x, this.activeTooltip.y, this.activeTooltip.timePointData)
      }
    },

    // 简化机构名称，最多显示5个字符
    simplifyAgencyName(name) {
      if (!name) return ''
      // 如果长度大于5的，添加...表示被截断
      if (name.length <= 5) return name
      return name.substring(0, 4) + '…'
    },

    // 绘制按评级机构显示的图表
    drawAgencyChart(ctx, rect) {
      // 动态获取评级机构列表
      const agencyList = this.getAgencyList()

      // 如果没有数据，则不绘制任何内容
      if (this.ratingData.length === 0) {
        // 清空图例区域
        this.legendAreas = []
        return
      }

      // 设置文字样式，用于测量文本宽度
      ctx.font = '12px Arial'
      ctx.textBaseline = 'alphabetic' // 设置默认基线

      // 计算左侧padding，根据最长机构名称动态调整
      let maxAgencyNameWidth = 0
      agencyList.forEach((agency) => {
        const simplifiedName = this.simplifyAgencyName(agency)
        const textWidth = ctx.measureText(simplifiedName).width
        maxAgencyNameWidth = Math.max(maxAgencyNameWidth, textWidth)
      })

      // 设置最小左侧内边距为50px，并根据文本长度增加，额外添加20px的空间确保不被截断
      const paddingLeft = Math.max(50, maxAgencyNameWidth + 20)

      // 设置一些常量
      const padding = { top: 30, right: 30, bottom: 40, left: paddingLeft }
      const chartWidth = rect.width - padding.left - padding.right
      const chartHeight = rect.height - padding.top - padding.bottom

      // 绘制时间轴
      const timelineY = padding.top + chartHeight
      ctx.beginPath()
      ctx.moveTo(padding.left, timelineY)
      ctx.lineTo(padding.left + chartWidth, timelineY)
      ctx.strokeStyle = '#ADADAD'
      ctx.lineWidth = 1
      ctx.stroke()

      // 添加Y轴起点0标记
      ctx.textAlign = 'right'
      ctx.textBaseline = 'middle'
      ctx.fillStyle = '#666'
      ctx.fillText('0', padding.left - 5, timelineY)
      ctx.textBaseline = 'alphabetic' // 恢复默认基线

      // 获取所有日期并排序
      const allDates = this.getValidDates()

      // 筛选要显示标签的日期（只显示第一个和最后一个）
      const displayLabelDates = this.filterDatesToDisplay(allDates, chartWidth)

      // 清空日期位置对象并重新填充
      this.datePositions = {}
      // 存储有效日期
      this.validDates = allDates

      ctx.font = '12px Arial'
      ctx.fillStyle = '#666'

      // 计算每个日期在X轴上的位置
      const dateToPositionMap = this.calculateDatePositions(allDates, padding.left, chartWidth)

      // 绘制所有日期点的刻度线
      allDates.forEach((date, index) => {
        const x = dateToPositionMap[date]
        this.datePositions[date] = x

        // 应用偏移量获取实际绘制位置
        const adjustedX = x + this.offsetX

        // 如果调整后的位置在可见区域外，跳过绘制
        if (adjustedX < padding.left - 10 || adjustedX > padding.left + chartWidth + 10) {
          return
        }

        // 为所有日期点绘制刻度线
        ctx.beginPath()
        ctx.moveTo(adjustedX, timelineY)
        ctx.lineTo(adjustedX, timelineY + 5)
        ctx.strokeStyle = '#ADADAD'
        ctx.lineWidth = 1
        ctx.stroke()

        // 判断是否绘制日期标签：
        // 1. 日期在需要显示标签的列表中，或
        // 2. 日期是当前鼠标悬停的日期
        const shouldDisplayLabel = displayLabelDates.includes(date) || date === this.hoverDate

        if (shouldDisplayLabel) {
          // 绘制日期文本，根据位置调整对齐方式
          ctx.save()

          // 所有日期标签居中对齐
          ctx.textAlign = 'center'

          if (date === this.hoverDate && !displayLabelDates.includes(date)) {
            // 悬停日期使用不同样式
            ctx.fillStyle = '#FF8E2B'
            ctx.font = 'bold 12px Arial'
          }
          ctx.fillText(date, adjustedX, timelineY + 20)
          ctx.restore()
        }
      })

      // 填充所有日期的位置信息，即使没有显示标签
      allDates.forEach((date) => {
        if (!this.datePositions[date]) {
          this.datePositions[date] = dateToPositionMap[date]
        }
      })

      // 绘制评级机构
      const agencyPositions = {}

      ctx.textAlign = 'right'
      ctx.fillStyle = '#333'

      agencyList.forEach((agency, index) => {
        const y = padding.top + (chartHeight / (agencyList.length + 1)) * (index + 1)
        agencyPositions[agency] = y

        // 绘制机构名称(简称)
        ctx.textBaseline = 'middle'
        ctx.fillText(this.simplifyAgencyName(agency), padding.left - 10, y)

        // 绘制水平辅助线
        ctx.beginPath()
        ctx.moveTo(padding.left, y)
        ctx.lineTo(padding.left + chartWidth, y)
        ctx.setLineDash([4, 4])
        ctx.strokeStyle = '#E9E9E9'
        ctx.stroke()
        ctx.setLineDash([])
      })

      // 恢复默认基线
      ctx.textBaseline = 'alphabetic'

      // 定义评级颜色
      const ratingColors = this.ratingColors

      // 存储图例区域信息，用于点击检测
      this.legendAreas = []

      // 绘制图例
      // 水平排布图例靠右侧
      const legendWidth = 60 // 单个图例宽度，从80减小到60
      const legendGap = 10 // 图例间距
      const legendStartX = padding.left + chartWidth - 10 // 从右侧留出一定的边距
      const legendY = padding.top - 15 // 调高图例位置

      // 对评级进行排序，从小到大（从低评级到高评级）
      const sortedRatings = Object.keys(ratingColors).sort((a, b) => {
        // 使用getRatingOrder方法获取评级序号，并按从小到大排序
        return this.getRatingOrder(a) - this.getRatingOrder(b)
      })

      sortedRatings.forEach((rating, index) => {
        // 修改为从右到左排列，基于右侧固定边距
        const legendX =
          legendStartX - (sortedRatings.length - index) * legendWidth - (sortedRatings.length - index - 1) * legendGap

        // 根据图例状态设置透明度
        const alpha = this.legendStatus[rating] ? 1 : 0.3

        // 使用新的颜色系统
        const ratingColor = this.getRatingColor(rating)

        // 绘制图例颜色框
        if (this.legendStatus[rating]) {
          ctx.fillStyle = ratingColor // 使用新的颜色
        } else {
          // 创建半透明颜色
          const rgbValues = this.hexToRgb(ratingColor) // 使用新的颜色
          ctx.fillStyle = `rgba(${rgbValues.r}, ${rgbValues.g}, ${rgbValues.b}, ${alpha})`
        }
        ctx.beginPath()
        ctx.arc(legendX + 8, legendY + 8, 4, 0, Math.PI * 2)
        ctx.fill()

        // 绘制图例文字
        ctx.fillStyle = this.legendStatus[rating] ? '#333' : '#999'
        ctx.textAlign = 'left'
        ctx.fillText(rating, legendX + 20, legendY + 12)

        // 存储图例区域信息，用于点击检测
        this.legendAreas.push({
          x: legendX,
          y: legendY,
          width: 50, // 包括文字的整个区域宽度
          height: 15,
          rating: rating
        })
      })

      // 清空点数据数组
      this.ratingPointsData = []

      // 存储需要绘制的点的信息
      const pointsToDraw = []

      // 创建裁剪区域，确保线段不会超出图表网格
      ctx.save()
      ctx.beginPath()
      ctx.rect(padding.left, padding.top, chartWidth, chartHeight)
      ctx.clip()

      // 按机构分组并按日期排序处理数据
      const agencyRatingData = {}

      // 首先按机构分组
      this.ratingData.forEach((item) => {
        if (!agencyRatingData[item.agency]) {
          agencyRatingData[item.agency] = []
        }
        agencyRatingData[item.agency].push(item)
      })

      // 对每个机构的数据按日期排序
      Object.keys(agencyRatingData).forEach((agency) => {
        agencyRatingData[agency].sort((a, b) => {
          // 按开始日期排序
          return new Date(a.startDate) - new Date(b.startDate)
        })
      })

      // 为每个机构绘制评级线段
      Object.entries(agencyRatingData).forEach(([agency, items]) => {
        const y = agencyPositions[agency]
        if (!y) return // 如果没有有效的y坐标，则跳过

        // 首先添加所有要绘制的点和交互数据点
        items.forEach((item) => {
          const startX = this.datePositions[item.startDate]
          if (!startX) return

          const adjustedStartX = startX + this.offsetX
          // 使用新的评级颜色系统
          const color = this.getRatingColor(item.rating)

          // 只处理在可视区域内的点
          if (adjustedStartX >= padding.left && adjustedStartX <= padding.left + chartWidth) {
            // 图例状态检查
            if (this.legendStatus[item.rating]) {
              // 添加到要绘制的点列表
              pointsToDraw.push({
                x: adjustedStartX,
                y: y,
                color: color
              })
            }

            // 无论图例状态如何，都添加到交互点数据
            this.ratingPointsData.push({
              x: adjustedStartX,
              y: y,
              radius: 4,
              date: item.startDate,
              agency: agency,
              rating: item.rating,
              enabled: this.legendStatus[item.rating],
              tooltip: `${item.startDate} ${agency} ${item.rating}`
            })
          }

          // 如果有结束日期且在可视区域内，也添加结束点
          if (item.endDate) {
            const endX = this.datePositions[item.endDate]
            if (endX) {
              const adjustedEndX = endX + this.offsetX
              if (adjustedEndX >= padding.left && adjustedEndX <= padding.left + chartWidth) {
                // 图例状态检查
                if (this.legendStatus[item.rating]) {
                  // 添加到要绘制的点列表
                  pointsToDraw.push({
                    x: adjustedEndX,
                    y: y,
                    color: color
                  })
                }

                // 添加到交互点数据
                this.ratingPointsData.push({
                  x: adjustedEndX,
                  y: y,
                  radius: 4,
                  date: item.endDate,
                  agency: agency,
                  rating: item.rating,
                  enabled: this.legendStatus[item.rating],
                  tooltip: `${item.endDate} ${agency} ${item.rating}`
                })
              }
            }
          }
        })

        // 然后绘制线段 - 每段线段从一个点到下一个点
        for (let i = 0; i < items.length; i++) {
          // 确定线段的起点和终点
          const currentItem = items[i]
          const startX = this.datePositions[currentItem.startDate]
          if (!startX) continue

          // 获取起点评级颜色 - 这个颜色将用于整个线段
          // 使用新的评级颜色系统
          const color = this.getRatingColor(currentItem.rating)

          // 确定线段的结束点
          let endX = null

          if (currentItem.endDate) {
            // 如果当前点有结束日期，使用它
            endX = this.datePositions[currentItem.endDate]
          } else if (i < items.length - 1) {
            // 如果没有结束日期但有下一个点，则使用下一个点的开始日期
            endX = this.datePositions[items[i + 1].startDate]
          } else {
            // 如果是最后一个点且没有结束日期，则延伸到图表右边界
            endX = padding.left + chartWidth
          }

          if (!endX) continue

          // 应用偏移量
          const adjustedStartX = startX + this.offsetX
          const adjustedEndX = endX + this.offsetX

          // 检查线段是否完全在可见区域外
          if (adjustedEndX < padding.left && adjustedStartX < padding.left) continue
          if (adjustedStartX > padding.left + chartWidth && adjustedEndX > padding.left + chartWidth) continue

          // 图例状态检查 - 只有当该评级在图例中启用时才绘制线段
          if (this.legendStatus[currentItem.rating]) {
            // 处理线段与边界的交叉
            let visibleStartX = adjustedStartX
            let visibleEndX = adjustedEndX

            // 如果起点在左边界外
            if (adjustedStartX < padding.left) {
              visibleStartX = padding.left
            }

            // 如果终点在右边界外
            if (adjustedEndX > padding.left + chartWidth) {
              visibleEndX = padding.left + chartWidth
            }

            // 绘制可见部分的线段 - 使用起点评级的颜色
            ctx.beginPath()
            ctx.moveTo(visibleStartX, y)
            ctx.lineTo(visibleEndX, y)
            ctx.strokeStyle = color
            ctx.lineWidth = 2
            ctx.stroke()
          }
        }
      })

      // 然后绘制所有点，确保点显示在线段之上
      pointsToDraw.forEach((point) => {
        ctx.beginPath()
        ctx.arc(point.x, point.y, 4, 0, Math.PI * 2)
        ctx.fillStyle = point.color
        ctx.fill()
        ctx.strokeStyle = 'white'
        ctx.lineWidth = 2
        ctx.stroke()
      })

      // 恢复绘图环境，移除裁剪区域
      ctx.restore()
    },

    // 绘制按时间序列显示的图表（评级等级为Y轴，时间为X轴）
    drawTimelineChart(ctx, rect) {
      // 如果没有数据，则不绘制任何内容
      if (this.ratingData.length === 0) {
        // 清空图例区域
        this.legendAreas = []
        return
      }

      // 设置文字样式
      ctx.font = '12px Arial'
      ctx.textBaseline = 'alphabetic' // 设置默认基线

      // 获取所有唯一的评级等级并排序
      const allRatings = [...new Set(this.ratingData.map((item) => item.rating))]
      // 按评级从小到大排序
      allRatings.sort((a, b) => this.getRatingOrder(a) - this.getRatingOrder(b))

      // 计算Y轴标签的最大宽度
      let maxRatingWidth = 0
      allRatings.forEach((rating) => {
        const textWidth = ctx.measureText(rating).width
        maxRatingWidth = Math.max(maxRatingWidth, textWidth)
      })

      // 设置内边距，左侧根据评级标签宽度确定
      const paddingLeft = Math.max(50, maxRatingWidth + 20)
      const padding = { top: 30, right: 30, bottom: 40, left: paddingLeft }
      const chartWidth = rect.width - padding.left - padding.right
      const chartHeight = rect.height - padding.top - padding.bottom

      // 获取所有日期并排序
      const allDates = this.getValidDates()

      // 清空日期位置对象
      this.datePositions = {}
      this.validDates = allDates

      // 计算日期在X轴上的位置
      const dateToPositionMap = this.calculateDatePositions(allDates, padding.left, chartWidth)

      // 保存日期位置
      allDates.forEach((date) => {
        this.datePositions[date] = dateToPositionMap[date]
      })

      // 绘制X轴（时间轴）
      const timelineY = padding.top + chartHeight
      ctx.beginPath()
      ctx.moveTo(padding.left, timelineY)
      ctx.lineTo(padding.left + chartWidth, timelineY)
      ctx.strokeStyle = '#ADADAD'
      ctx.lineWidth = 1
      ctx.stroke()

      // 添加Y轴起点0标记
      ctx.textAlign = 'right'
      ctx.textBaseline = 'middle'
      ctx.fillStyle = '#666'
      ctx.fillText('0', padding.left - 5, timelineY)

      // 筛选要显示的日期标签
      const displayLabelDates = this.filterDatesToDisplay(allDates, chartWidth)

      // 绘制日期刻度和标签
      ctx.font = '12px Arial'
      ctx.fillStyle = '#666'

      allDates.forEach((date, index) => {
        const x = dateToPositionMap[date]
        // 应用偏移量获取实际绘制位置
        const adjustedX = x + this.offsetX

        // 如果调整后的位置在可见区域外，跳过绘制
        if (adjustedX < padding.left - 10 || adjustedX > padding.left + chartWidth + 10) {
          return
        }

        // 绘制刻度线
        ctx.beginPath()
        ctx.moveTo(adjustedX, timelineY)
        ctx.lineTo(adjustedX, timelineY + 5)
        ctx.strokeStyle = '#ADADAD'
        ctx.lineWidth = 1
        ctx.stroke()

        // 判断是否显示标签
        const shouldDisplayLabel = displayLabelDates.includes(date) || date === this.hoverDate

        if (shouldDisplayLabel) {
          ctx.save()

          // 所有日期标签居中对齐
          ctx.textAlign = 'center'

          if (date === this.hoverDate && !displayLabelDates.includes(date)) {
            ctx.fillStyle = '#FF8E2B'
            ctx.font = 'bold 12px Arial'
          }

          ctx.fillText(date, adjustedX, timelineY + 20)
          ctx.restore()
        }

        // 绘制垂直辅助线
        if (!this.simpleMode) {
          ctx.beginPath()
          ctx.moveTo(adjustedX, padding.top)
          ctx.lineTo(adjustedX, timelineY)
          ctx.setLineDash([4, 4])
          ctx.strokeStyle = '#E9E9E9'
          ctx.stroke()
          ctx.setLineDash([])
        }
      })

      // 绘制Y轴（评级等级轴）- 简化模式下不显示
      if (!this.simpleMode) {
        ctx.beginPath()
        ctx.moveTo(padding.left, padding.top)
        ctx.lineTo(padding.left, timelineY)
        ctx.strokeStyle = '#ADADAD'
        ctx.lineWidth = 1
        ctx.stroke()
      }

      // 计算评级等级在Y轴上的位置
      const ratingPositions = {}
      const ratingGap = chartHeight / (allRatings.length || 1)

      ctx.textAlign = 'right'
      ctx.fillStyle = '#333'

      // 从底部向上绘制评级标签，低评级在底部，高评级在顶部
      allRatings.forEach((rating, index) => {
        // 计算Y轴位置，index=0对应最低评级(接近底部)，最高评级在顶部
        // 注意这里不需要反转，因为allRatings已经是从小到大排序的
        const y = padding.top + chartHeight - ratingGap * (index + 0.5)
        ratingPositions[rating] = y

        // 绘制评级标签
        ctx.textBaseline = 'middle'
        ctx.fillText(rating, padding.left - 10, y)

        // 绘制水平辅助线 - 在简化模式下仍然显示横向网格线，但跳过与X轴重叠的网格线
        // 检查是否是最后一个评级且网格线与X轴重叠（容差范围内）
        const isLastRatingNearAxis = index === 0 && Math.abs(y - timelineY) < 5

        if (!isLastRatingNearAxis) {
          ctx.beginPath()
          ctx.moveTo(padding.left, y)
          ctx.lineTo(padding.left + chartWidth, y)
          ctx.setLineDash([4, 4])
          ctx.strokeStyle = '#E9E9E9'
          ctx.stroke()
          ctx.setLineDash([])
        }
      })

      // 简化模式下不显示图例
      if (!this.simpleMode) {
        // 绘制图例 - 按机构显示
        this.legendAreas = []

        // 获取唯一的机构列表
        const agencyList = this.getAgencyList()
        const legendWidth = 100
        const legendGap = 10
        const legendStartX = padding.left + chartWidth - 10
        const legendY = padding.top - 15

        // 定义固定的机构颜色
        const agencyColors = {}
        const colors = ['#5AD8A6', '#5B8FF9', '#F6BD16', '#6DC8EC', '#945FB9', '#FF9845', '#1E9493', '#FF99C3']

        agencyList.forEach((agency, index) => {
          // 为每个机构分配一个固定颜色
          agencyColors[agency] = colors[index % colors.length]

          const legendX = legendStartX - (index + 1) * legendWidth - index * legendGap
          const color = agencyColors[agency]

          // 绘制图例颜色框
          ctx.fillStyle = color
          ctx.beginPath()
          ctx.arc(legendX + 8, legendY + 8, 4, 0, Math.PI * 2)
          ctx.fill()

          // 绘制图例文字
          ctx.fillStyle = '#333'
          ctx.textAlign = 'left'
          ctx.fillText(this.simplifyAgencyName(agency), legendX + 20, legendY + 12)

          // 存储图例区域信息
          this.legendAreas.push({
            x: legendX,
            y: legendY,
            width: 90,
            height: 15,
            agency: agency
          })
        })
      } else {
        // 简化模式下清空图例区域
        this.legendAreas = []
      }

      // 清空点数据数组
      this.ratingPointsData = []

      // 存储需要绘制的点的信息
      const pointsToDraw = []

      // 创建裁剪区域，确保线段不会超出图表网格
      ctx.save()
      ctx.beginPath()
      ctx.rect(padding.left, padding.top, chartWidth, chartHeight)
      ctx.clip()

      // 按机构分组整理数据
      const agencyData = {}

      this.ratingData.forEach((item) => {
        if (!agencyData[item.agency]) {
          agencyData[item.agency] = []
        }
        agencyData[item.agency].push(item)
      })

      // 为每个机构按日期排序
      Object.keys(agencyData).forEach((agency) => {
        agencyData[agency].sort((a, b) => new Date(a.startDate) - new Date(b.startDate))
      })

      // 定义固定的机构颜色
      const agencyColors = {}
      const colors = ['#5AD8A6', '#5B8FF9', '#F6BD16', '#6DC8EC', '#945FB9', '#FF9845', '#1E9493', '#FF99C3']

      // 为每个机构分配一个固定颜色
      Object.keys(agencyData).forEach((agency, index) => {
        agencyColors[agency] = colors[index % colors.length]
      })

      // 为每个机构绘制评级线
      Object.entries(agencyData).forEach(([agency, items]) => {
        // 确保有评级数据
        if (items.length === 0) return

        // 使用机构的固定颜色，而不是按评级颜色
        const color = agencyColors[agency]

        ctx.strokeStyle = color
        ctx.lineWidth = 2
        ctx.beginPath()

        // 绘制连接线
        let firstPoint = true
        let lastValidX = null
        let lastValidY = null

        // 存储需要绘制的点
        const agencyPoints = []

        items.forEach((item, idx) => {
          const x = this.datePositions[item.startDate]
          const y = ratingPositions[item.rating]
          const adjustedX = x + this.offsetX

          // 确保坐标有效
          if (!x || !y) return

          // 检查点是否在可视区域内
          const isPointVisible = adjustedX >= padding.left && adjustedX <= padding.left + chartWidth

          // 如果是第一个点且在可视区域内，或者前一个点在可视区域内而当前点不在
          if ((firstPoint && isPointVisible) || (!firstPoint && isPointVisible)) {
            if (firstPoint) {
              ctx.moveTo(adjustedX, y)
              firstPoint = false
            } else {
              ctx.lineTo(adjustedX, y)
            }
            lastValidX = adjustedX
            lastValidY = y

            // 存储点信息，稍后绘制（使用机构固定颜色）
            if (!this.simpleMode) {
              agencyPoints.push({
                x: adjustedX,
                y: y,
                color: color // 使用机构颜色
              })
            }
          } else if (!firstPoint && !isPointVisible && lastValidX !== null) {
            // 计算与边界的交点
            let intersectX, intersectY

            // 如果点在左边界外
            if (adjustedX < padding.left) {
              intersectX = padding.left
              // 计算y坐标（线性插值）
              const ratio = (padding.left - lastValidX) / (adjustedX - lastValidX)
              intersectY = lastValidY + ratio * (y - lastValidY)
              ctx.lineTo(intersectX, intersectY)
            } else if (adjustedX > padding.left + chartWidth) {
              intersectX = padding.left + chartWidth
              // 计算y坐标（线性插值）
              const ratio = (padding.left + chartWidth - lastValidX) / (adjustedX - lastValidX)
              intersectY = lastValidY + ratio * (y - lastValidY)
              ctx.lineTo(intersectX, intersectY)
            }
          }

          // 如果有结束日期且不是最后一项，则水平延伸到结束日期
          if (item.endDate && idx < items.length - 1) {
            const endX = this.datePositions[item.endDate]
            const adjustedEndX = endX + this.offsetX

            if (endX && isPointVisible) {
              // 如果结束点在可视区域内
              if (adjustedEndX >= padding.left && adjustedEndX <= padding.left + chartWidth) {
                ctx.lineTo(adjustedEndX, y)
                lastValidX = adjustedEndX
                lastValidY = y
              } else if (adjustedEndX > padding.left + chartWidth) {
                ctx.lineTo(padding.left + chartWidth, y)
                lastValidX = padding.left + chartWidth
                lastValidY = y
              }
            }
          }

          // 简化模式下不存储点位置数据
          if (!this.simpleMode && isPointVisible) {
            // 存储点位置，用于悬停交互
            this.ratingPointsData.push({
              x: adjustedX,
              y: y,
              radius: 4,
              date: item.startDate,
              agency: agency,
              rating: item.rating,
              enabled: true,
              tooltip: `${item.startDate} ${agency} ${item.rating}`
            })
          }
        })

        // 如果最后一项有结束日期，延伸到结束位置
        const lastItem = items[items.length - 1]
        if (lastItem && lastItem.endDate && !firstPoint) {
          const lastX = this.datePositions[lastItem.endDate]
          const lastY = ratingPositions[lastItem.rating]
          const adjustedLastX = lastX + this.offsetX

          if (lastX && lastY) {
            // 如果结束点在可视区域内
            if (adjustedLastX >= padding.left && adjustedLastX <= padding.left + chartWidth) {
              ctx.lineTo(adjustedLastX, lastY)
            } else if (
              adjustedLastX > padding.left + chartWidth &&
              lastValidX !== null &&
              lastValidX <= padding.left + chartWidth
            ) {
              ctx.lineTo(padding.left + chartWidth, lastY)
            }
          }
        }

        // 完成线段绘制
        ctx.stroke()

        // 添加所有点到绘制队列
        pointsToDraw.push(...agencyPoints)
      })

      // 现在绘制所有点，确保它们在线段之上
      if (!this.simpleMode) {
        pointsToDraw.forEach((point) => {
          ctx.beginPath()
          ctx.arc(point.x, point.y, 4, 0, Math.PI * 2)
          ctx.fillStyle = point.color
          ctx.fill()
          ctx.strokeStyle = 'white'
          ctx.lineWidth = 2
          ctx.stroke()
        })
      }

      // 恢复绘图环境，移除裁剪区域
      ctx.restore()
    },

    // 初始化鼠标事件监听器
    initMouseEvents() {
      const canvas = this.$refs.ratingCanvas
      if (!canvas) return

      // 记录当前悬停的日期（用于临时显示日期标签）
      this.hoverDate = null

      // 不再为mousemove添加事件监听器，因为已经通过@mousemove="onDrag"绑定到模板

      // 添加点击事件处理
      canvas.addEventListener('click', (event) => {
        // 如果正在拖动，不处理点击
        if (this.isDragging) return

        const rect = canvas.getBoundingClientRect()
        const x = event.clientX - rect.left
        const y = event.clientY - rect.top

        // 检查是否点击了图例
        for (const area of this.legendAreas || []) {
          if (x >= area.x && x <= area.x + area.width && y >= area.y && y <= area.y + area.height) {
            // 向父组件发出事件，让父组件处理图例状态变更
            this.$emit('legend-click', area.rating)
            break
          }
        }
      })

      // 鼠标离开画布时隐藏提示和临时标签
      canvas.addEventListener('mouseleave', () => {
        if (this.activeTooltip || this.hoverDate) {
          this.activeTooltip = null
          this.hoverDate = null
          // 重绘图表以隐藏提示
          this.drawRatingChart()
        }
      })
    },

    // 处理鼠标移动（ECharts风格交互）
    handleMouseMove(event) {
      const canvas = this.getCanvas()
      if (!canvas) return

      const rect = canvas.getBoundingClientRect()
      const x = event.clientX - rect.left
      const y = event.clientY - rect.top

      // 计算图表区域参数
      const padding = { top: 30, right: 30, bottom: 40, left: 70 }
      const chartHeight = rect.height - padding.top - padding.bottom
      const timelineY = padding.top + chartHeight

      // 检查鼠标是否在图表区域内
      if (x < padding.left || x > rect.width - padding.right || y < padding.top || y > timelineY) {
        // 鼠标在图表区域外，隐藏tooltip
        if (this.activeTooltip) {
          this.activeTooltip = null
          this.hoverDate = null
          this.scheduleRedraw()
        }
        return
      }

      // 根据鼠标X坐标收集所有相关数据
      const allDataAtPosition = this.getAllDataAtPosition(x)

      if (allDataAtPosition && allDataAtPosition.ratings.length > 0) {
        // 检查是否需要更新tooltip（避免不必要的重绘）
        const needUpdate =
          !this.activeTooltip ||
          Math.abs(this.activeTooltip.x - x) > 2 ||
          Math.abs(this.activeTooltip.y - y) > 2 ||
          this.hoverDate !== allDataAtPosition.displayDate

        if (needUpdate) {
          this.activeTooltip = {
            x: x, // tooltip跟随鼠标X位置
            y: y, // tooltip跟随鼠标Y位置
            timePointData: allDataAtPosition,
            crosshairX: x // 十字线位置
          }
          this.hoverDate = allDataAtPosition.displayDate
          this.scheduleRedraw()
        }
      } else {
        // 没有找到数据，隐藏tooltip
        if (this.activeTooltip) {
          this.activeTooltip = null
          this.hoverDate = null
          this.scheduleRedraw()
        }
      }
    },

    // 绘制圆角矩形的兼容性方法
    drawRoundedRect(ctx, x, y, width, height, radius) {
      ctx.moveTo(x + radius, y)
      ctx.lineTo(x + width - radius, y)
      ctx.quadraticCurveTo(x + width, y, x + width, y + radius)
      ctx.lineTo(x + width, y + height - radius)
      ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height)
      ctx.lineTo(x + radius, y + height)
      ctx.quadraticCurveTo(x, y + height, x, y + height - radius)
      ctx.lineTo(x, y + radius)
      ctx.quadraticCurveTo(x, y, x + radius, y)
      ctx.closePath()
    },

    // 绘制十字线（ECharts风格）
    drawCrosshair(ctx, x, rect) {
      const padding = { top: 30, right: 30, bottom: 40, left: 70 }
      const chartHeight = rect.height - padding.top - padding.bottom
      const timelineY = padding.top + chartHeight

      ctx.save()
      ctx.strokeStyle = 'rgba(85, 85, 85, 0.5)'
      ctx.lineWidth = 1
      ctx.setLineDash([4, 4])

      // 绘制垂直线
      ctx.beginPath()
      ctx.moveTo(x, padding.top)
      ctx.lineTo(x, timelineY)
      ctx.stroke()

      ctx.restore()
    },

    // 获取图例区域信息
    getLegendArea() {
      // 图例区域的基本参数（与绘制图例时保持一致）
      const padding = { top: 30, right: 30, bottom: 40, left: 70 }
      const legendY = padding.top - 15
      const legendHeight = 30 // 图例区域高度（包括一些缓冲）

      // 根据图表模式返回不同的图例区域
      if (this.chartMode === 'agency') {
        // agency模式：图例在图表右上角
        const canvas = this.getCanvas()
        if (!canvas) return null
        const rect = canvas.getBoundingClientRect()
        const chartWidth = rect.width - padding.left - padding.right

        return {
          x: padding.left + chartWidth * 0.6, // 图例大概从右侧60%位置开始
          y: legendY - 5,
          width: chartWidth * 0.4 + padding.right,
          height: legendHeight
        }
      } else if (this.chartMode === 'timeline') {
        // timeline模式：图例也在右上角
        const canvas = this.getCanvas()
        if (!canvas) return null
        const rect = canvas.getBoundingClientRect()
        const chartWidth = rect.width - padding.left - padding.right

        return {
          x: padding.left + chartWidth * 0.5, // 图例大概从右侧50%位置开始
          y: legendY - 5,
          width: chartWidth * 0.5 + padding.right,
          height: legendHeight
        }
      }

      return null
    },

    // 计算tooltip位置，适应跟随鼠标模式
    calculateTooltipPosition(x, y, tooltipWidth, tooltipHeight, canvasWidth, canvasHeight, legendArea) {
      const margin = 15 // 边距
      const offsetX = 15 // 鼠标右侧偏移
      const offsetY = 10 // 鼠标下方偏移

      // ECharts风格：默认显示在鼠标右下方
      let tooltipX = x + offsetX
      let tooltipY = y + offsetY

      // 边界检查：如果右侧空间不足，显示在左侧
      if (tooltipX + tooltipWidth + margin > canvasWidth) {
        tooltipX = x - tooltipWidth - offsetX
      }

      // 边界检查：如果下方空间不足，显示在上方
      if (tooltipY + tooltipHeight + margin > canvasHeight) {
        tooltipY = y - tooltipHeight - offsetY
      }

      // 检查是否与图例区域重叠
      if (
        legendArea &&
        this.isRectOverlap(
          tooltipX,
          tooltipY,
          tooltipWidth,
          tooltipHeight,
          legendArea.x,
          legendArea.y,
          legendArea.width,
          legendArea.height
        )
      ) {
        // 如果与图例重叠，尝试其他位置

        // 尝试左上角
        let altX = x - tooltipWidth - offsetX
        let altY = y - tooltipHeight - offsetY

        if (
          altX >= margin &&
          altY >= margin &&
          !this.isRectOverlap(
            altX,
            altY,
            tooltipWidth,
            tooltipHeight,
            legendArea.x,
            legendArea.y,
            legendArea.width,
            legendArea.height
          )
        ) {
          tooltipX = altX
          tooltipY = altY
        } else {
          // 尝试左下角
          altX = x - tooltipWidth - offsetX
          altY = y + offsetY

          if (
            altX >= margin &&
            altY + tooltipHeight + margin <= canvasHeight &&
            !this.isRectOverlap(
              altX,
              altY,
              tooltipWidth,
              tooltipHeight,
              legendArea.x,
              legendArea.y,
              legendArea.width,
              legendArea.height
            )
          ) {
            tooltipX = altX
            tooltipY = altY
          } else {
            // 尝试右上角
            altX = x + offsetX
            altY = y - tooltipHeight - offsetY

            if (
              altX + tooltipWidth + margin <= canvasWidth &&
              altY >= margin &&
              !this.isRectOverlap(
                altX,
                altY,
                tooltipWidth,
                tooltipHeight,
                legendArea.x,
                legendArea.y,
                legendArea.width,
                legendArea.height
              )
            ) {
              tooltipX = altX
              tooltipY = altY
            }
            // 如果所有位置都重叠，保持原位置
          }
        }
      }

      // 最终边界检查
      tooltipX = Math.max(margin, Math.min(tooltipX, canvasWidth - tooltipWidth - margin))
      tooltipY = Math.max(margin, Math.min(tooltipY, canvasHeight - tooltipHeight - margin))

      return { x: tooltipX, y: tooltipY }
    },

    // 检查两个矩形是否重叠
    isRectOverlap(x1, y1, w1, h1, x2, y2, w2, h2) {
      return !(x1 + w1 <= x2 || x2 + w2 <= x1 || y1 + h1 <= y2 || y2 + h2 <= y1)
    },

    // 绘制tooltip箭头，根据tooltip位置自动调整方向
    drawTooltipArrow(ctx, pointX, pointY, tooltipX, tooltipY, tooltipWidth, tooltipHeight) {
      const arrowSize = 8

      // 判断tooltip相对于数据点的位置
      const centerX = tooltipX + tooltipWidth / 2
      const centerY = tooltipY + tooltipHeight / 2

      // 计算数据点相对于tooltip中心的位置
      const deltaX = pointX - centerX
      const deltaY = pointY - centerY

      // 根据位置关系决定箭头方向和位置
      if (Math.abs(deltaY) > Math.abs(deltaX)) {
        // 垂直方向的箭头
        if (deltaY > 0) {
          // 数据点在tooltip下方，箭头指向下方
          const triangleX = Math.min(Math.max(pointX, tooltipX + 10), tooltipX + tooltipWidth - 10)
          ctx.moveTo(triangleX, tooltipY + tooltipHeight)
          ctx.lineTo(triangleX - arrowSize, tooltipY + tooltipHeight - arrowSize)
          ctx.lineTo(triangleX + arrowSize, tooltipY + tooltipHeight - arrowSize)
        } else {
          // 数据点在tooltip上方，箭头指向上方
          const triangleX = Math.min(Math.max(pointX, tooltipX + 10), tooltipX + tooltipWidth - 10)
          ctx.moveTo(triangleX, tooltipY)
          ctx.lineTo(triangleX - arrowSize, tooltipY + arrowSize)
          ctx.lineTo(triangleX + arrowSize, tooltipY + arrowSize)
        }
      } else {
        // 水平方向的箭头
        if (deltaX > 0) {
          // 数据点在tooltip右侧，箭头指向右侧
          const triangleY = Math.min(Math.max(pointY, tooltipY + 10), tooltipY + tooltipHeight - 10)
          ctx.moveTo(tooltipX + tooltipWidth, triangleY)
          ctx.lineTo(tooltipX + tooltipWidth - arrowSize, triangleY - arrowSize)
          ctx.lineTo(tooltipX + tooltipWidth - arrowSize, triangleY + arrowSize)
        } else {
          // 数据点在tooltip左侧，箭头指向左侧
          const triangleY = Math.min(Math.max(pointY, tooltipY + 10), tooltipY + tooltipHeight - 10)
          ctx.moveTo(tooltipX, triangleY)
          ctx.lineTo(tooltipX + arrowSize, triangleY - arrowSize)
          ctx.lineTo(tooltipX + arrowSize, triangleY + arrowSize)
        }
      }
    },

    // 检测鼠标是否在线段上
    detectLineSegment(mouseX, mouseY) {
      const lineDetectionDistance = 8 // 线段检测距离

      // 根据图表模式进行不同的检测
      if (this.chartMode === 'agency') {
        return this.detectAgencyLineSegment(mouseX, mouseY, lineDetectionDistance)
      } else if (this.chartMode === 'timeline') {
        return this.detectTimelineLineSegment(mouseX, mouseY, lineDetectionDistance)
      }

      return null
    },

    // 检测agency模式下的线段
    detectAgencyLineSegment(mouseX, mouseY, detectionDistance) {
      // 计算图表区域参数
      const canvas = this.getCanvas()
      if (!canvas) return null
      const rect = canvas.getBoundingClientRect()

      // 动态获取评级机构列表
      const agencyList = this.getAgencyList()
      if (agencyList.length === 0) return null

      // 设置一些常量（与绘制时保持一致）
      let maxAgencyNameWidth = 0
      const ctx = this.getContext()
      ctx.font = '12px Arial'
      agencyList.forEach((agency) => {
        const simplifiedName = this.simplifyAgencyName(agency)
        const textWidth = ctx.measureText(simplifiedName).width
        maxAgencyNameWidth = Math.max(maxAgencyNameWidth, textWidth)
      })

      const paddingLeft = Math.max(50, maxAgencyNameWidth + 20)
      const padding = { top: 30, right: 30, bottom: 40, left: paddingLeft }
      const chartHeight = rect.height - padding.top - padding.bottom

      // 计算机构位置
      const agencyPositions = {}
      agencyList.forEach((agency, index) => {
        const y = padding.top + (chartHeight / (agencyList.length + 1)) * (index + 1)
        agencyPositions[agency] = y
      })

      // 按机构分组数据
      const agencyRatingData = {}
      this.ratingData.forEach((item) => {
        if (!agencyRatingData[item.agency]) {
          agencyRatingData[item.agency] = []
        }
        agencyRatingData[item.agency].push(item)
      })

      // 对每个机构的数据按日期排序
      Object.keys(agencyRatingData).forEach((agency) => {
        agencyRatingData[agency].sort((a, b) => {
          return new Date(a.startDate) - new Date(b.startDate)
        })
      })

      // 检测每个机构的线段
      for (const [agency, items] of Object.entries(agencyRatingData)) {
        const y = agencyPositions[agency]
        if (!y) continue

        // 检查该Y坐标附近是否有鼠标
        if (Math.abs(mouseY - y) > detectionDistance) continue

        // 检查线段
        for (let i = 0; i < items.length; i++) {
          const currentItem = items[i]
          const startX = this.datePositions[currentItem.startDate]
          if (!startX) continue

          // 确定线段的结束点
          let endX = null
          if (currentItem.endDate) {
            endX = this.datePositions[currentItem.endDate]
          } else if (i < items.length - 1) {
            endX = this.datePositions[items[i + 1].startDate]
          } else {
            endX = padding.left + (rect.width - padding.left - padding.right)
          }

          if (!endX) continue

          // 应用偏移量
          const adjustedStartX = startX + this.offsetX
          const adjustedEndX = endX + this.offsetX

          // 检查鼠标是否在线段范围内
          if (mouseX >= Math.min(adjustedStartX, adjustedEndX) && mouseX <= Math.max(adjustedStartX, adjustedEndX)) {
            // 检查图例状态
            if (this.legendStatus[currentItem.rating]) {
              return {
                agency: agency,
                rating: currentItem.rating,
                startDate: currentItem.startDate,
                endDate: currentItem.endDate,
                x: mouseX,
                y: y
              }
            }
          }
        }
      }

      return null
    },

    // 检测timeline模式下的线段
    detectTimelineLineSegment(mouseX, mouseY, detectionDistance) {
      // 计算图表区域参数
      const canvas = this.getCanvas()
      if (!canvas) return null
      const rect = canvas.getBoundingClientRect()

      // 获取所有唯一的评级等级并排序
      const allRatings = [...new Set(this.ratingData.map((item) => item.rating))]
      allRatings.sort((a, b) => this.getRatingOrder(a) - this.getRatingOrder(b))

      if (allRatings.length === 0) return null

      // 计算Y轴标签的最大宽度
      let maxRatingWidth = 0
      const ctx = this.getContext()
      ctx.font = '12px Arial'
      allRatings.forEach((rating) => {
        const textWidth = ctx.measureText(rating).width
        maxRatingWidth = Math.max(maxRatingWidth, textWidth)
      })

      const paddingLeft = Math.max(50, maxRatingWidth + 20)
      const padding = { top: 30, right: 30, bottom: 40, left: paddingLeft }
      const chartHeight = rect.height - padding.top - padding.bottom

      // 计算评级等级在Y轴上的位置
      const ratingPositions = {}
      const ratingGap = chartHeight / (allRatings.length || 1)

      allRatings.forEach((rating, index) => {
        const y = padding.top + chartHeight - ratingGap * (index + 0.5)
        ratingPositions[rating] = y
      })

      // 按机构分组数据
      const agencyData = {}
      this.ratingData.forEach((item) => {
        if (!agencyData[item.agency]) {
          agencyData[item.agency] = []
        }
        agencyData[item.agency].push(item)
      })

      // 为每个机构按日期排序
      Object.keys(agencyData).forEach((agency) => {
        agencyData[agency].sort((a, b) => new Date(a.startDate) - new Date(b.startDate))
      })

      // 检测每个机构的线段
      for (const [agency, items] of Object.entries(agencyData)) {
        if (items.length === 0) continue

        for (let i = 0; i < items.length; i++) {
          const item = items[i]
          const x = this.datePositions[item.startDate]
          const y = ratingPositions[item.rating]
          const adjustedX = x + this.offsetX

          if (!x || !y) continue

          // 检查水平线段（在同一个评级等级上的延续）
          if (item.endDate) {
            const endX = this.datePositions[item.endDate]
            const adjustedEndX = endX + this.offsetX

            if (endX) {
              // 检查水平线段
              if (this.isPointOnLineSegment(mouseX, mouseY, adjustedX, y, adjustedEndX, y, detectionDistance)) {
                return {
                  agency: agency,
                  rating: item.rating,
                  startDate: item.startDate,
                  endDate: item.endDate,
                  x: mouseX,
                  y: y
                }
              }
            }
          }

          // 检查到下一个点的连接线段
          if (i < items.length - 1) {
            const nextItem = items[i + 1]
            const nextX = this.datePositions[nextItem.startDate]
            const nextY = ratingPositions[nextItem.rating]
            const adjustedNextX = nextX + this.offsetX

            if (nextX && nextY) {
              // 确定连接起点
              let connectStartX = adjustedX
              const connectStartY = y

              // 如果当前项有结束日期，从结束点开始连接
              if (item.endDate) {
                const endX = this.datePositions[item.endDate]
                if (endX) {
                  connectStartX = endX + this.offsetX
                }
              }

              // 检查连接线段
              if (
                this.isPointOnLineSegment(
                  mouseX,
                  mouseY,
                  connectStartX,
                  connectStartY,
                  adjustedNextX,
                  nextY,
                  detectionDistance
                )
              ) {
                return {
                  agency: agency,
                  rating: item.rating,
                  startDate: item.startDate,
                  endDate: item.endDate,
                  x: mouseX,
                  y: mouseY
                }
              }
            }
          }
        }
      }

      return null
    },

    // 判断点是否在线段上
    isPointOnLineSegment(px, py, x1, y1, x2, y2, tolerance) {
      // 计算点到线段的距离
      const A = px - x1
      const B = py - y1
      const C = x2 - x1
      const D = y2 - y1

      const dot = A * C + B * D
      const lenSq = C * C + D * D

      if (lenSq === 0) {
        // 线段是一个点
        return Math.sqrt(A * A + B * B) <= tolerance
      }

      const param = dot / lenSq

      let xx, yy

      if (param < 0) {
        xx = x1
        yy = y1
      } else if (param > 1) {
        xx = x2
        yy = y2
      } else {
        xx = x1 + param * C
        yy = y1 + param * D
      }

      const dx = px - xx
      const dy = py - yy
      return Math.sqrt(dx * dx + dy * dy) <= tolerance
    },

    // 根据线段信息创建tooltip数据
    createLineSegmentTooltip(segmentInfo) {
      return {
        date: segmentInfo.startDate,
        ratings: [
          {
            agency: segmentInfo.agency,
            rating: segmentInfo.rating,
            enabled: this.legendStatus[segmentInfo.rating] || true
          }
        ]
      }
    },

    // 获取指定X坐标位置上的所有数据信息
    getAllDataAtPosition(mouseX) {
      // 计算当前鼠标位置对应的时间
      const currentTime = this.getTimeFromPosition(mouseX)
      if (!currentTime) return null

      const allRatings = []
      const processedItems = new Set() // 防止重复添加

      // 遍历所有评级数据
      this.ratingData.forEach((item) => {
        // 检查图例状态，只显示启用的评级
        if (!this.legendStatus[item.rating]) return

        const startTime = new Date(item.startDate).getTime()
        const endTime = item.endDate ? new Date(item.endDate).getTime() : Date.now()
        const currentTimeMs = new Date(currentTime).getTime()

        // 创建唯一标识，避免重复
        const itemKey = `${item.agency}-${item.rating}-${item.startDate}-${item.endDate || 'ongoing'}`
        if (processedItems.has(itemKey)) return

        let status = null

        // 判断当前时间点与该评级的关系
        if (Math.abs(currentTimeMs - startTime) < 24 * 60 * 60 * 1000) {
          // 1天容差
          status = '开始'
        } else if (item.endDate && Math.abs(currentTimeMs - endTime) < 24 * 60 * 60 * 1000) {
          status = '结束'
        } else if (currentTimeMs >= startTime && currentTimeMs <= endTime) {
          status = '持续中'
        }

        // 如果该评级与当前时间位置相关，则添加到结果中
        if (status) {
          allRatings.push({
            agency: item.agency,
            rating: item.rating,
            startDate: item.startDate,
            endDate: item.endDate,
            status: status,
            enabled: true
          })
          processedItems.add(itemKey)
        }
      })

      // 按机构名称排序
      allRatings.sort((a, b) => a.agency.localeCompare(b.agency))

      if (allRatings.length === 0) return null

      return {
        date: currentTime,
        displayDate: currentTime,
        ratings: allRatings
      }
    },

    // 根据X坐标计算对应的时间
    getTimeFromPosition(mouseX) {
      // 调整鼠标X坐标，考虑偏移量
      const adjustedMouseX = mouseX - this.offsetX

      // 找到最接近的时间点
      let closestDate = null
      let minDistance = Infinity

      for (const [date, posX] of Object.entries(this.datePositions)) {
        const distance = Math.abs(adjustedMouseX - posX)
        if (distance < minDistance) {
          minDistance = distance
          closestDate = date
        }
      }

      // 如果距离太远，则进行时间插值
      if (minDistance > 50 && this.validDates.length >= 2) {
        // 在两个时间点之间进行插值
        const sortedDates = [...this.validDates].sort((a, b) => new Date(a) - new Date(b))

        for (let i = 0; i < sortedDates.length - 1; i++) {
          const date1 = sortedDates[i]
          const date2 = sortedDates[i + 1]
          const pos1 = this.datePositions[date1]
          const pos2 = this.datePositions[date2]

          if (adjustedMouseX >= pos1 && adjustedMouseX <= pos2) {
            // 线性插值计算时间
            const ratio = (adjustedMouseX - pos1) / (pos2 - pos1)
            const time1 = new Date(date1).getTime()
            const time2 = new Date(date2).getTime()
            const interpolatedTime = time1 + ratio * (time2 - time1)
            // 格式化为YYYY/MM/DD格式，保持与其他时间显示一致
            const interpolatedDate = new Date(interpolatedTime).toISOString().split('T')[0]
            return this.formatDateToSlash(interpolatedDate)
          }
        }
      }

      return closestDate
    },

    // 绘制tooltip - 修复弹窗位置对应问题
    drawTooltip(ctx, x, y, timePointData) {
      if (!timePointData || !timePointData.ratings || timePointData.ratings.length === 0) return

      // 获取画布尺寸
      const canvas = this.$refs.ratingCanvas
      if (!canvas) return
      const canvasRect = canvas.getBoundingClientRect()
      const canvasWidth = canvasRect.width
      const canvasHeight = canvasRect.height

      // 设置内边距
      const padding = 12
      // 计算文本行高
      const lineHeight = 20

      // 计算标题的宽度
      ctx.font = 'bold 14px Arial'
      const titleText = `${timePointData.date} ${this.tooltipTitle}`
      const titleWidth = ctx.measureText(titleText).width

      // 计算评级信息的最大宽度
      let maxRatingWidth = 0
      ctx.font = '12px Arial'
      timePointData.ratings.forEach((rating) => {
        // 评级符号宽度 + 间距 + 机构名称宽度 + 状态信息宽度
        const ratingTextWidth = ctx.measureText(rating.rating).width
        const agencyTextWidth = ctx.measureText(rating.agency).width
        const statusTextWidth = rating.status ? ctx.measureText(`(${rating.status})`).width : 0
        // 15px是评级符号和圆点的宽度，50px是评级到机构名的间距，10px是机构名到状态的间距
        const totalWidth = 15 + ratingTextWidth + 50 + agencyTextWidth + (statusTextWidth ? 10 + statusTextWidth : 0)
        maxRatingWidth = Math.max(maxRatingWidth, totalWidth)
      })

      // 取标题宽度和最大评级信息宽度的较大值，加上左右内边距
      const contentWidth = Math.max(titleWidth, maxRatingWidth)
      // 设置最小宽度为250px，最大不超过500px
      const tooltipWidth = Math.min(500, Math.max(250, contentWidth + padding * 2))

      // 动态计算弹窗高度 (标题 + 每个评级一行)
      const tooltipHeight = padding * 2 + lineHeight * (1 + timePointData.ratings.length)

      // 获取图例区域信息，用于避让计算
      const legendArea = this.getLegendArea()

      // 计算弹窗位置，避开图例区域
      const tooltipPosition = this.calculateTooltipPosition(
        x,
        y,
        tooltipWidth,
        tooltipHeight,
        canvasWidth,
        canvasHeight,
        legendArea
      )

      const tooltipX = tooltipPosition.x
      const tooltipY = tooltipPosition.y

      // 绘制信息框背景 (圆角矩形)
      ctx.fillStyle = 'rgba(50, 50, 50, 0.85)' // 增加不透明度
      ctx.beginPath()
      // 兼容性处理：检查roundRect是否支持
      if (typeof ctx.roundRect === 'function') {
        ctx.roundRect(tooltipX, tooltipY, tooltipWidth, tooltipHeight, 4)
      } else {
        // 使用传统方法绘制圆角矩形
        this.drawRoundedRect(ctx, tooltipX, tooltipY, tooltipWidth, tooltipHeight, 4)
      }
      ctx.fill()

      // 绘制标题
      ctx.fillStyle = 'white'
      ctx.font = 'bold 14px Arial'
      ctx.textAlign = 'left'
      // 绘制时间点和标题在同一行，格式为"时间点 标题"
      ctx.fillText(`${timePointData.date} ${this.tooltipTitle}`, tooltipX + padding, tooltipY + padding + 14)

      // 当没有起始于该点的评级时显示提示信息
      if (timePointData.ratings.length === 0) {
        ctx.font = '12px Arial'
        ctx.fillStyle = '#CCC'
        ctx.fillText('无评级数据变更', tooltipX + padding, tooltipY + padding + lineHeight + 14)
      } else {
        // 绘制评级信息
        ctx.font = '12px Arial'
        timePointData.ratings.forEach((rating, index) => {
          const y = tooltipY + padding + lineHeight * (index + 1) + 14

          // 使用新的评级颜色系统
          const ratingColor = this.getRatingColor(rating.rating)

          // 绘制评级颜色圆点
          if (rating.enabled) {
            ctx.fillStyle = ratingColor
          } else {
            // 禁用状态使用半透明灰色
            ctx.fillStyle = '#999'
          }
          ctx.beginPath()
          ctx.arc(tooltipX + padding + 5, y - 4, 4, 0, Math.PI * 2)
          ctx.fill()

          // 绘制评级文本
          if (rating.enabled) {
            ctx.fillStyle = ratingColor
          } else {
            ctx.fillStyle = '#999'
          }
          ctx.fillText(rating.rating, tooltipX + padding + 15, y)

          // 绘制机构名称(完整)
          ctx.fillStyle = 'white'
          ctx.fillText(rating.agency, tooltipX + padding + 50, y)

          // 绘制状态信息（如果有）
          if (rating.status) {
            ctx.fillStyle = '#FFD700' // 金色状态文字
            const agencyTextWidth = ctx.measureText(rating.agency).width
            ctx.fillText(`(${rating.status})`, tooltipX + padding + 50 + agencyTextWidth + 10, y)
          }
        })
      }
    },

    // 获取特定时间点上所有公司的评级信息
    getTimePointRatings(date) {
      // 查找该日期时所有公司的评级
      const result = []

      // 只显示以该时间点为起始点的数据（不受图例状态影响，但在显示时会根据状态处理）
      this.ratingData.forEach((item) => {
        // 只选择以当前点为起始点的数据
        if (item.startDate === date) {
          result.push({
            agency: item.agency,
            rating: item.rating,
            // 添加是否启用的状态，用于控制显示
            enabled: this.legendStatus[item.rating] || false
          })
        }
      })

      return {
        date: date,
        ratings: result
      }
    },

    // 获取有数据点的有效日期（带缓存）
    getValidDates() {
      // 检查缓存是否有效
      if (this.cachedValidDates && this.dataHashCache === this.dataHash) {
        return this.cachedValidDates
      }

      // 重新计算
      const uniqueDates = new Set()
      this.ratingData.forEach((item) => {
        uniqueDates.add(item.startDate)
        if (item.endDate) {
          uniqueDates.add(item.endDate)
        }
      })

      // 转为数组并按日期排序
      this.cachedValidDates = [...uniqueDates].sort((a, b) => new Date(a) - new Date(b))
      this.dataHashCache = this.dataHash

      return this.cachedValidDates
    },

    // 16进制颜色转RGB
    hexToRgb(hex) {
      // 移除#号如果存在
      hex = hex.replace('#', '')

      // 解析RGB值
      const r = parseInt(hex.substring(0, 2), 16)
      const g = parseInt(hex.substring(2, 4), 16)
      const b = parseInt(hex.substring(4, 6), 16)

      return { r, g, b }
    },

    // 根据评级数据获取所有机构列表（带缓存）
    getAgencyList() {
      // 检查缓存是否有效
      if (this.cachedAgencyList && this.dataHashCache === this.dataHash) {
        return this.cachedAgencyList
      }

      // 重新计算
      const agencies = new Set()
      this.ratingData.forEach((item) => {
        agencies.add(item.agency)
      })

      // 更新缓存
      this.cachedAgencyList = [...agencies]
      this.dataHashCache = this.dataHash

      return this.cachedAgencyList
    },

    // 计算每个日期在X轴上的位置
    calculateDatePositions(dates, startX, width) {
      const positions = {}

      // 确保日期已排序
      const sortedDates = [...dates].sort((a, b) => new Date(a) - new Date(b))

      // 如果只有一个日期，将其放在时间轴中间
      if (sortedDates.length === 1) {
        positions[sortedDates[0]] = startX + width / 2
        this.canScroll = false
        this.maxOffsetX = 0
        return positions
      }

      // 计算需要的总宽度
      const dateCount = sortedDates.length

      // 每个日期点最少需要的宽度，确保不会过于拥挤
      const minWidthPerDate = 80

      // 计算实际需要的总宽度
      const requiredWidth = minWidthPerDate * (dateCount - 1)

      // 确定是否需要滚动
      this.canScroll = requiredWidth > width

      // 确定实际使用的宽度
      const effectiveWidth = Math.max(width, requiredWidth)
      this.totalWidth = effectiveWidth

      // 计算最大偏移量
      this.maxOffsetX = Math.max(0, effectiveWidth - width)

      // 设置内边距（起点和终点各缩进的宽度）
      // 针对主体评级图表减少左侧内边距，让起始日期更靠近左侧
      const paddingRatio = this.chartMode === 'agency' ? 0.025 : 0.05 // 主体评级模式下使用2.5%的内边距，时间轴模式仍保持5%
      const padding = effectiveWidth * paddingRatio
      const adjustedWidth = effectiveWidth - padding * 2
      const adjustedStartX = startX + padding

      // 计算最早和最晚日期的时间戳
      const firstDate = new Date(sortedDates[0]).getTime()
      const lastDate = new Date(sortedDates[sortedDates.length - 1]).getTime()
      const totalTimeSpan = lastDate - firstDate

      // 使用混合方案：70%均匀分布 + 30%时间比例
      // 这样既能反映时间跨度差异，又不会导致显示过于离散
      const uniformWeight = 0.7
      const timeProportionWeight = 0.3

      // 定义最小间距，确保标签不会重叠
      const MIN_LABEL_SPACING = 65 // 像素

      // 如果时间跨度为0，完全使用均匀分布
      if (totalTimeSpan === 0) {
        const interval = adjustedWidth / (sortedDates.length - 1)
        sortedDates.forEach((date, index) => {
          positions[date] = adjustedStartX + interval * index
        })
      } else {
        // 混合计算每个日期的位置
        sortedDates.forEach((date, index) => {
          // 均匀分布的位置
          const uniformPosition = index / (sortedDates.length - 1)

          // 基于时间的位置
          const dateTime = new Date(date).getTime()
          const timePosition = (dateTime - firstDate) / totalTimeSpan

          // 混合两种位置计算方式
          const mixedPosition = uniformPosition * uniformWeight + timePosition * timeProportionWeight

          // 计算最终位置
          positions[date] = adjustedStartX + adjustedWidth * mixedPosition
        })
      }

      // 检查并调整相邻日期间距，确保不小于最小间距
      let needAdjustment = false
      do {
        needAdjustment = false

        // 检查相邻日期之间的间距
        for (let i = 1; i < sortedDates.length; i++) {
          const prevDate = sortedDates[i - 1]
          const currDate = sortedDates[i]
          const spacing = positions[currDate] - positions[prevDate]

          // 如果间距小于最小要求
          if (spacing < MIN_LABEL_SPACING) {
            needAdjustment = true

            // 计算新的总宽度，以满足最小间距
            const currentTotalWidth = this.totalWidth
            const scaleFactor = MIN_LABEL_SPACING / spacing
            const newTotalWidth = currentTotalWidth * scaleFactor

            // 更新总宽度和偏移量
            this.totalWidth = newTotalWidth
            this.maxOffsetX = Math.max(0, newTotalWidth - width)

            // 重新计算内边距和调整后的宽度
            const newPadding = newTotalWidth * paddingRatio
            const newAdjustedWidth = newTotalWidth - newPadding * 2
            const newAdjustedStartX = startX + newPadding

            // 重新计算所有日期位置
            if (totalTimeSpan === 0) {
              const newInterval = newAdjustedWidth / (sortedDates.length - 1)
              sortedDates.forEach((date, index) => {
                positions[date] = newAdjustedStartX + newInterval * index
              })
            } else {
              sortedDates.forEach((date, index) => {
                const uniformPosition = index / (sortedDates.length - 1)
                const dateTime = new Date(date).getTime()
                const timePosition = (dateTime - firstDate) / totalTimeSpan
                const mixedPosition = uniformPosition * uniformWeight + timePosition * timeProportionWeight
                positions[date] = newAdjustedStartX + newAdjustedWidth * mixedPosition
              })
            }

            // 只需要调整一次，然后再检查
            break
          }
        }
      } while (needAdjustment)

      // 确保需要滚动
      this.canScroll = this.totalWidth > width

      // 重置偏移量，确保不超出范围
      this.offsetX = Math.min(0, Math.max(-this.maxOffsetX, this.offsetX))

      return positions
    },

    // 筛选要显示的日期标签，显示所有有效日期
    filterDatesToDisplay(allDates, chartWidth) {
      // 主体评级图表显示所有日期标签
      if (this.chartMode === 'agency') {
        return allDates
      }

      // 中债隐含评级图表(timeline模式)仍然筛选日期标签以避免重叠
      // 如果日期数量少，直接全部显示
      if (allDates.length <= 8) {
        return allDates
      }

      // 当日期过多时，避免标签重叠
      // 根据画布宽度计算可以显示的标签数量
      const maxLabels = Math.floor(chartWidth / 80) // 假设每个标签需要80px宽度

      if (maxLabels >= allDates.length) {
        return allDates // 如果空间足够，显示所有日期
      }

      // 空间不足时，均匀采样日期点
      const result = []
      const step = Math.ceil(allDates.length / maxLabels)

      // 始终包含第一个和最后一个日期
      result.push(allDates[0])

      // 均匀采样中间的日期点
      for (let i = step; i < allDates.length - 1; i += step) {
        result.push(allDates[i])
      }

      // 添加最后一个日期
      if (allDates.length > 1) {
        result.push(allDates[allDates.length - 1])
      }

      return result
    },

    // 开始拖动
    startDrag(event) {
      // 如果不可滚动或滚动距离为0，不处理拖动
      if (!this.canScroll || this.maxOffsetX === 0) return

      this.isDragging = true
      this.dragStartX = event.clientX
      // 设置光标样式为grabbing
      const canvas = this.$refs.ratingCanvas
      if (canvas) {
        canvas.style.cursor = 'grabbing'
      }
      // 清除任何活动的tooltip
      this.activeTooltip = null
      this.hoverDate = null
    },

    // 拖动中
    onDrag(event) {
      // 处理普通鼠标移动检测（原有功能）
      if (!this.isDragging) {
        this.handleMouseMove(event)
        return
      }

      // 处理拖动
      const deltaX = event.clientX - this.dragStartX
      let newOffsetX = this.offsetX + deltaX

      // 限制拖动范围
      newOffsetX = Math.min(0, Math.max(-this.maxOffsetX, newOffsetX))

      // 只有在偏移量变化时才重绘
      if (newOffsetX !== this.offsetX) {
        this.offsetX = newOffsetX
        this.dragStartX = event.clientX
        this.drawRatingChart()
      }
    },

    // 结束拖动
    endDrag() {
      this.isDragging = false
      // 恢复光标样式
      const canvas = this.$refs.ratingCanvas
      if (canvas) {
        canvas.style.cursor = 'grab'
      }
    },

    // 将日期格式从YYYY-MM-DD转换为YYYY/MM/DD
    formatDateToSlash(date) {
      if (date && date.length > 0) {
        return date.replace(/-/g, '/')
      }
      return date
    },

    // 根据评级等级获取对应颜色
    getRatingColor(rating) {
      // 简化的评级颜色映射，只使用图片中提供的颜色值
      if (rating === 'AAA' || rating === 'AAA+' || rating === 'Aaa') {
        return '#008000' // 深绿色
      } else if (rating === 'AA+' || rating === 'Aa1') {
        return '#00FF00' // 亮绿色
      } else if (rating === 'AA' || rating === 'Aa2' || rating === 'Aa3') {
        return '#90EE90' // 浅绿色
      } else if (rating === 'A+' || rating === 'A1') {
        return '#FFFF99' // 浅黄色
      } else if (rating === 'A' || rating === 'A2') {
        return '#FFD700' // 深黄色
      } else if (
        rating === 'A-' ||
        rating === 'A3' ||
        rating.startsWith('BBB') ||
        rating === 'Baa1' ||
        rating === 'Baa2' ||
        rating === 'Baa3'
      ) {
        return '#FFA500' // 橙黄色
      } else if (
        rating.startsWith('BB') ||
        rating.startsWith('B') ||
        rating.startsWith('C') ||
        rating === 'D' ||
        rating.startsWith('Ba') ||
        rating.startsWith('B') ||
        rating.startsWith('Caa') ||
        rating === 'Ca' ||
        rating === 'C'
      ) {
        return '#8B0000' // 深红色
      }

      // 默认颜色 - 如果没有匹配到任何规则
      return '#4169E1' // 深蓝色
    },

    // 获取评级的等级序号，用于排序
    getRatingOrder(rating) {
      // 定义评级顺序，从高到低
      const ratingOrder = {
        // 中文评级系统
        'AAA+': 21,
        'AAA': 20,
        'AA+': 19,
        'AA': 18,
        'AA-': 17,
        'A+': 16,
        'A': 15,
        'A-': 14,
        'BBB+': 13,
        'BBB': 12,
        'BBB-': 11,
        'BB+': 10,
        'BB': 9,
        'BB-': 8,
        'B+': 7,
        'B': 6,
        'B-': 5,
        'CCC+': 4,
        'CCC': 3,
        'CCC-': 2,
        'CC': 1,
        'C': 0,
        'D': -1,

        // 穆迪评级系统
        'Aaa': 20,
        'Aa1': 19,
        'Aa2': 18,
        'Aa3': 17,
        'A1': 16,
        'A2': 15,
        'A3': 14,
        'Baa1': 13,
        'Baa2': 12,
        'Baa3': 11,
        'Ba1': 10,
        'Ba2': 9,
        'Ba3': 8,
        'B1': 7,
        'B2': 6,
        'B3': 5,
        'Caa1': 4,
        'Caa2': 3,
        'Caa3': 2,
        'Ca': 1
      }

      // 返回评级对应的序号，如果未定义则返回-100
      return ratingOrder[rating] || -100
    }
  }
}
</script>

<style lang="scss" scoped>
.rating-chart {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;

  .scroll-hint {
    position: absolute;
    bottom: 5px;
    right: 10px;
    background-color: rgba(0, 0, 0, 0.6);
    color: #fff;
    padding: 3px 8px;
    border-radius: 4px;
    font-size: 12px;
    opacity: 0.7;
    pointer-events: none;
    transition: opacity 0.3s;

    &:hover {
      opacity: 0;
    }
  }
}
</style>
