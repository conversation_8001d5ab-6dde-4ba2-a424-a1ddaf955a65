/**
 * 获取目标对象中对应的方法名
 * @param {Array} names 方法名数组
 * @param {Object} target 目标对象
 * @returns 目标对象中对应的方法名
 */
function getPropertyName(names, target) {
  return names.find((name) => name in target)
}

// 获取当前浏览器进入全屏的方法名
const enterFullScreenName = getPropertyName(
  ['requestFullScreen', 'webkitRequestFullScreen', 'mozRequestFullScreen', 'msRequestFullScreen'],
  document.documentElement
)

// 进入全屏
export function enter(ele) {
  enterFullScreenName && ele[enterFullScreenName]()
}

// 获取退出全屏的方法名
const exitFullScreenName = getPropertyName(
  ['exitFullscreen', 'webkitExitFullscreen', 'mozCancelFullScreen', 'msExitFullscreen'],
  document
)

// 退出全屏
export function exit() {
  exitFullScreenName && document[exitFullScreenName]()
}

// 获取处于全屏的方法名
const fullScreenName = getPropertyName(
  ['fullscreenElement', 'webkitFullscreenElement', 'mozFullScreenElement', 'msFullscreenElement'],
  document
)

// 获取当前处于全屏的元素
export function fullEle() {
  return document[fullScreenName] || null
}

// 判断当前是否处于全屏
export function isFull() {
  return !!fullEle()
}

// 全屏元素状态切换
export function toggleFullScreen(ele) {
  isFull() ? exit() : enter(ele)
}
