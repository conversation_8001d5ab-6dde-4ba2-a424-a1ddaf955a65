<template>
  <!-- POC专用 -->
  <!-- 财务数据 -->
  <div class="poc-home">
    <h3>财务数据</h3>
    <div>
      <div class="poc-home--mblock">
        <div v-for="(item, index) in blockList" :key="index" class="poc-home--mblock-item" @click="activeId = item.id">
          <div class="poc-home--mblock-item-name">
            <span>{{ item.name }}</span>
            <jr-svg-icon :icon-class="item.color" />
          </div>
          <div class="poc-home--mblock-item-value-item">
            <span>{{ item.value1 }}</span>
            <span :class="item.color">{{ item.value2 }}</span>
          </div>
        </div>
      </div>
      <b-template-module style="width: 100%;height: 100%;" :height="height" :chart-seq="activeId" :custom-options="customOptions" />
    </div>
  </div>
</template>
<script>
export default {
  data() {
    const self = this
    return {
      activeId: '6bedef9d5d4c4d9f91c80d8d8dde7be9',
      height: 220,
      customOptions: () => ({
        toolbox: {
          show: false
        },
        legend: {
          // show: false
          top: 0,
          right: 0,
          textStyle: {
            color: '#000'
          }
        },
        grid: {
          top: 90,
          left: 0,
          right: 20,
          bottom: 0
        },
        series: [
          {
            name: self.blockList.find(a => a.id === self.activeId).name
          },
          {
            name: '同比(%)'
          }
        ]
      }),
      blockList: [
        {
          name: '总资产(亿元)',
          value1: '2273.90',
          value2: '+7.79',
          color: 'bp-red',
          id: '6bedef9d5d4c4d9f91c80d8d8dde7be9'
        },
        {
          name: '净资产(亿元)',
          value1: '831.90',
          value2: '+3.32%',
          color: 'bp-red',
          id: '77e8036bf2d2490a9a5874a4b94201de'
        },
        {
          name: '营业收入(亿元)',
          value1: '137.12',
          value2: '-8.58%',
          color: 'bp-green',
          id: '7a31d89e56a9420ea3e6c630c580302a'
        },
        {
          name: '净利润(亿元)',
          value1: '4.08',
          value2: '-31.52%',
          color: 'bp-green',
          id: '37038ef18103472ab585aa50af8f7260'
        }
      ]
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.height = this.$el.clientHeight
    })
  }
}
</script>
<style lang="scss" scoped>
.poc-home--mblock {
  display: flex;
  flex-wrap: nowrap;
  margin-top: 10px;
  margin-bottom: 10px;
  height: 60px;
  width: 90%;
  position: absolute;
  top: 30px;
  z-index: 1;
}
.poc-home--mblock-item {
  width: 20%;
  margin-right: 16px;
  padding: 6px 12px;
  border-radius: 4px;
  background-color: #fff;
  border: 1px solid #e5e5e5;
  position: relative;
  cursor: pointer;
}
.poc-home--mblock-item-name {
  font-size: 14px;
  font-weight: 700;
  color: #333;
  margin-bottom: 6px;
  .jr-svg-icon {
    position: absolute;
    right: 10px;
    font-size: 24px;
    top: 0;
  }
}
.poc-home--mblock-item-value-item {
  display: flex;
  justify-content: space-between;
}
.bp-primary {
  font-weight: bold;
  color: var(--theme--color);
  font-size: 14px;
  line-height: 28px;
}
</style>
<style>
@media screen and (max-width: 1600px) {
  .poc-home--mblock-item-name .jr-svg-icon  {
    font-size: 16px !important;
    top: 4px !important;
    right: 0 !important;
  }
}
</style>
