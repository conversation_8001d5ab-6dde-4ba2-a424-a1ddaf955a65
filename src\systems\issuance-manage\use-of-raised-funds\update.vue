<template>
  <div>
    <collapse-panel title="基本信息">
      <jr-form-item-create
        v-show="expand.top"
        ref="formCreate"
        :data="fields"
        :model="data"
        :prop-path="''"
        :disabled="false"
        :column="2"
        style="padding-right: 64px"
      />
    </collapse-panel>
    <collapse-panel title="已使用详情">
      <jr-table-editor
        v-show="expand.bottom"
        ref="editor"
        v-model="raisedFundsDetails"
        :columns="columns"
        :show-delete="(row) => row.text !== '1'"
        :height="200"
        style="position: relative; z-index: 2; min-height: 200px"
        :actionWidth="80"
      >
        <el-table-column prop="amt">
          <template slot="header">
            <p style="width: 100%; text-align: center; margin-bottom: 0px">规模</p>
          </template>
          <template slot-scope="scope">
            <jr-number-input v-model="scope.row.amt" :precision="4" append-text="亿" />
          </template>
        </el-table-column>
        <el-table-column prop="repayPrincipal">
          <template slot="header">
            <p style="width: 100%; text-align: center; margin-bottom: 0px">已使用本金</p>
          </template>
          <template slot-scope="scope">
            <jr-number-input v-model="scope.row.repayPrincipal" :precision="2" append-text="万元" />
          </template>
        </el-table-column>
        <el-table-column prop="repayInterest">
          <template slot="header">
            <p style="width: 100%; text-align: center; margin-bottom: 0px">已使用利息</p>
          </template>
          <template slot-scope="scope">
            <jr-number-input v-model="scope.row.repayInterest" :precision="2" append-text="万元" />
          </template>
        </el-table-column>
      </jr-table-editor>
    </collapse-panel>
    <!-- <div>
        <span class="title-icon" />
        <span class="item-title">基本信息</span>
        <span v-if="expand.top" @click.stop="unExpand('top')"><jr-svg-icon icon-class="down" /></span>
        <span v-if="!expand.top" @click.stop="unExpand('top')"><jr-svg-icon icon-class="up" /></span>
      </div> -->
    <!-- <div style="margin-top: 33px">
        <span class="title-icon" />
        <span class="item-title">已使用详情</span>
        <span v-if="expand.bottom" @click.stop="unExpand('bottom')"><jr-svg-icon icon-class="down" /></span>
        <span v-if="!expand.bottom" @click.stop="unExpand('bottom')"><jr-svg-icon icon-class="up" /></span>
      </div> -->
    <!-- 按钮占位元素 -->
    <div class="footerBac" />
    <div slot="modal-footer">
      <div class="footer">
        <el-button @click.stop="handleCancel">取消</el-button>
        <el-button type="primary" @click.stop="handleSave">确认</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import { GetComboboxList } from '@/api/home'
import { queryBondNameByCompony } from '@/api/public/public'
import { issuancePurposeDetail, issuanceQueryIssuerList, issuanceQueryBondShortNameList } from '@/api/issuance/issuance'
const CATEGORY = 'CATEGORY' // 融资类别字典项
export default {
  props: {
    save: {
      type: Function,
      default: () => {}
    },
    visible: {
      type: Boolean,
      default: false
    },
    modalType: {
      type: String,
      default: ''
    },
    tabActiveName: {
      type: String,
      default: ''
    },
    itemData: {
      type: Object,
      default: () => ({})
    },
    data: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      fields: [],
      columns: [
        {
          title: '债券简称',
          prop: 'bondShortName'
        }
      ],
      raisedFundsDetails: [],
      expand: {
        top: true,
        bottom: true
      }
    }
  },
  computed: {
    // 计算利息
    computedInterest() {
      if (typeof this.data.faceRate === 'number' && typeof this.data.prinAmt === 'number') {
        const temp = (this.data.prinAmt * 100 * (this.data.faceRate * 10000)) / 100000000
        return temp
      } else {
        return undefined
      }
    },
    // 计算已使用本金
    computedUseRepayamt() {
      return (
        this.raisedFundsDetails.reduce((pre, current) => {
          pre = pre + (current.repayPrincipal || 0) * 100
          return pre
        }, 0) / 100
      )
    },
    // 计算已使用利息
    computedUseInterest() {
      return (
        this.raisedFundsDetails.reduce((pre, current) => {
          pre = pre + (current.repayInterest || 0) * 100
          return pre
        }, 0) / 100
      )
    },
    // 计算已使用总计
    computedUseTotal() {
      if (typeof this.data.useRepayamt === 'number' && typeof this.data.useInterest === 'number') {
        return (this.data.useRepayamt * 100 + this.data.useInterest * 100) / 100
      } else {
        return undefined
      }
    },
    // 计算未使用本金
    computedUnusePayamt() {
      if (typeof this.data.prinAmt === 'number' && typeof this.data.useRepayamt === 'number') {
        return (this.data.prinAmt * 100 - this.data.useRepayamt * 100) / 100
      } else {
        return undefined
      }
    },
    // 计算未使用利息
    computedUnuseInterest() {
      if (typeof this.data.interest === 'number' && typeof this.data.useInterest === 'number') {
        return (this.data.interest * 100 - this.data.useInterest * 100) / 100
      } else {
        return undefined
      }
    }
  },
  watch: {
    // 给data赋值，触发展示
    computedInterest() {
      this.$emit('setFieldsValue', {
        interest: this.computedInterest
      })
    },
    computedUseRepayamt() {
      this.$emit('setFieldsValue', {
        useRepayamt: this.computedUseRepayamt
      })
    },
    computedUseInterest() {
      this.$emit('setFieldsValue', {
        useInterest: this.computedUseInterest
      })
    },
    computedUseTotal() {
      this.$emit('setFieldsValue', {
        useTotal: this.computedUseTotal
      })
    },
    computedUnusePayamt() {
      this.$emit('setFieldsValue', {
        unusePayamt: this.computedUnusePayamt
      })
    },
    computedUnuseInterest() {
      this.$emit('setFieldsValue', {
        unuseInterest: this.computedUnuseInterest
      })
    },
    modalType: {
      deep: true,
      immediate: true,
      handler() {
        if (this.modalType === 'edit') {
          this.getDetailApi()
        }
      }
    }
  },
  created() {
    this.init()
  },
  mounted() {
    this.$emit('setModalClass', 'useofRaisedFunds-modal')
  },
  methods: {
    /**
     * 初始化fields
     */
    init() {
      this.fields = [
        {
          title: '发行人名称',
          prop: 'entId',
          type: 'select',
          optionLabel: 'name',
          optionValue: 'id',
          options: [],
          showCode: false,
          required: true,
          disabled: this.modalType === 'edit',
          trigger: 'change',
          change: (e) => {
            // 债券简称跟随发行人名称变化
            if (this.tabActiveName === '1') {
              this.getBondShortNameByIssueCode(e)
            }
          }
        },
        {
          title: '融资类别',
          prop: 'finCategory',
          type: 'select',
          options: [],
          showCode: false,
          disabled: this.tabActiveName === '1' && this.modalType === 'edit',
          required: this.tabActiveName !== '1',
          trigger: 'change'
        },
        {
          title: '是否一类债务',
          prop: 'isDebt',
          type: 'radio',
          optionValue: 'value',
          options: [
            {
              label: '是',
              value: 1
            },
            {
              label: '否',
              value: 0
            }
          ]
        },
        {
          title: '利率(%)',
          prop: 'faceRate',
          type: 'number',
          precision: 4,
          required: true,
          disabled: this.modalType === 'edit',
          trigger: 'change'
        },
        {
          title: '本金',
          prop: 'prinAmt',
          type: 'number',
          precision: 2,
          disabled: this.modalType === 'edit',
          uiProps: {
            appendText: '万元'
          },
          required: true,
          trigger: 'change'
        },
        {
          title: '利息',
          prop: 'interest',
          type: 'number',
          precision: 2,
          uiProps: {
            appendText: '万元'
          }
        },
        {
          title: '已使用总计',
          prop: 'useTotal',
          type: 'number',
          precision: 2,
          disabled: true,
          uiProps: {
            appendText: '万元'
          }
        },
        {
          title: '已使用本金',
          prop: 'useRepayamt',
          type: 'number',
          precision: 2,
          disabled: true,
          uiProps: {
            appendText: '万元'
          }
        },
        {
          title: '已使用利息',
          prop: 'useInterest',
          type: 'number',
          precision: 2,
          disabled: true,
          uiProps: {
            appendText: '万元'
          }
        },
        {
          title: '未使用本金',
          prop: 'unusePayamt',
          type: 'number',
          precision: 2,
          disabled: true,
          uiProps: {
            appendText: '万元'
          }
        },
        {
          title: '未使用利息',
          prop: 'unuseInterest',
          type: 'number',
          precision: 2,
          disabled: true,
          uiProps: {
            appendText: '万元'
          }
        },
        {
          title: '起息日期',
          prop: 'vdate',
          type: 'date',
          disabled: this.modalType === 'edit',
          rules: [{ required: true, trigger: 'change' }]
        },
        {
          title: '到期日期',
          prop: 'mdate',
          type: 'date',
          disabled: this.modalType === 'edit',
          rules: [{ required: true, trigger: 'change' }]
        }
      ]
      Object.assign(this._data.fields, this.$options.data().fields)
      if (this.tabActiveName === '1') {
        this.fields.splice(1, 0, {
          title: '债券简称',
          prop: 'bondShortName',
          type: 'select',
          required: true,
          trigger: 'change',
          optionLabel: 'text',
          disabled: this.modalType === 'edit',
          showCode: false,
          optionValue: 'text',
          options: []
        })
        this.fields.splice(3, 0, {
          title: '存续期管理机构',
          prop: 'manageOrg',
          type: 'text',
          disabled: this.modalType === 'edit',
          required: true,
          trigger: 'change'
        })
      } else {
        this.fields.splice(2, 0, {
          title: '借款银行/机构',
          prop: 'leadUnderwriter',
          type: 'text',
          required: true,
          disabled: this.modalType === 'edit',
          trigger: 'change'
        })
      }
      this.fields = this.fields.map((item) => {
        item.class = 'useOfRaisedFundsFields'
        return item
      })
      this.getDictDataApi()
      this.getIssuerList()
      // if (this.tabActiveName === '1') {
      //   this.getBondShortNameList()
      // }
      this.$emit('setFieldsValue', {
        finCategory: this.tabActiveName === '1' ? '债券业务' : '',
        useTotal: 0,
        useRepayamt: 0,
        useInterest: 0,
        isDebt: 0
      })
    },
    /**
     * 关闭弹框
     */
    handleCancel() {
      this.$emit('handleCancel')
    },
    // 获取部分字典数据
    async getDictDataApi() {
      const data = await GetComboboxList([CATEGORY])
      this.fields = this.fields.map((item) => {
        if (item.title === '融资类别') {
          if (this.tabActiveName === '1') {
            item.options = [
              {
                text: '债券业务',
                id: '债券业务'
              }
            ]
          } else {
            item.options = data[CATEGORY]
          }
        }
        return item
      })
    },
    /**
     * 获取发行人下拉项
     */
    async getIssuerList() {
      const data = await issuanceQueryIssuerList()
      this.fields = this.fields.map((item) => {
        if (item.title === '发行人名称') {
          item.options = data
        }
        return item
      })
    },
    /**
     * 获取债券简称下拉项
     */

    async getBondShortNameList() {
      const data = await issuanceQueryBondShortNameList()
      this.fields = this.fields.map((item) => {
        if (item.title === '债券简称') {
          item.options = data
        }
        return item
      })
    },
    async getBondShortNameByIssueCode(code) {
      const res = await queryBondNameByCompony(code)
      console.log(res)
      if (Array.isArray(res)) {
        this.fields = this.fields.map((item) => {
          if (item.title === '债券简称') {
            item.options = res
          }
          return item
        })
      }
    },
    /**
     * 获取详情数据
     */
    async getDetailApi() {
      const data = await issuancePurposeDetail(this.itemData.id)
      this.$emit('setFieldsValue', {
        ...data
      })
      this.raisedFundsDetails = data.raisedFundsDetails
    },
    /**
     * 确认保存
     */
    handleSave() {
      // 伪数据endId
      this.$emit('setFieldsValue', {
        raisedFundsDetails: this.raisedFundsDetails,
        moduleType: this.tabActiveName === '1' ? '01' : '02'
      })
      if (this.data.prinAmt < this.computedUseRepayamt) {
        this.$message.error('已使用本金不得大于本金')
        return
      }

      if (this.data.interest < this.computedUseInterest) {
        this.$message.error('已使用利息不得大于利息')
        return
      }

      if (+new Date(this.data.mdate) < +new Date(this.data.vdate)) {
        this.$message.error('到期日期不能小于起息日期')
        return
      }

      const entOptions = this.fields.find((item) => item.title === '发行人名称').options
      const entName = entOptions.find((item) => item.id === this.data.entId).name
      this.$emit('setFieldsValue', {
        entName: entName
      })
      this.save(true)
    },
    /**
     * 收起部分内容
     */
    unExpand(key) {
      this.expand[key] = !this.expand[key]
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .useOfRaisedFundsFields {
  margin: 24px 0px 0px !important;
}
// .area {
//   padding-top: -20px;
//   // height: 757px;
//   overflow-y: scroll;
// }
.title {
  display: flex;
  align-items: center;
  gap: 11px;
  padding-left: 4px;
  &-icon {
    height: 17px;
    width: 3px;
    background-color: #ff8e2b;
  }
  span:nth-of-type(2) {
    height: 22px;
    font-size: var(--el-font-size-base);
    color: rgba(0, 0, 0, 0.85);
    line-height: 22px;
  }
  span:nth-of-type(3) {
    margin-left: auto;
    cursor: pointer;
  }
}
.footerBac {
  width: 100%;
  height: 207px;
  background-image: url('../../../assets/images/bondEntryBac.png');
  background-size: 100% 100%;
  position: fixed;
  bottom: 0px;
  left: 0px;
  z-index: 0;
}
.footer {
  width: 100%;
  height: 37px;
  display: flex;
  justify-content: center;
  align-items: flex-end;
  position: fixed;
  bottom: 12px;
  left: 0px;
  button {
    width: 88px;
  }
}
</style>
<style lang="scss">
.useofRaisedFunds-modal {
  .platform-modal-content {
    height: 845px !important;
  }
  // .el-form {
  //   padding: 18px 24px 0px 20px !important;
  //   overflow: hidden !important;
  // }
}
</style>
