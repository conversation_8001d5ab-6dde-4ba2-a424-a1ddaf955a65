<template>
  <flat-index :query="handleQuery" :reset="handleReset" :pagination="configTable.pagination">
    <template v-slot:form>
      <el-form ref="elForm" :model="configForm.model">
        <jr-form-item-create :column="0" :data="configForm.data" :model="configForm.model" />
      </el-form>
    </template>

    <template v-slot:table-list="{ height }">
      <jr-table
        :height="height"
        :columns="configTable.columns"
        :data-source="configTable.data"
        :loading="configTable.loading"
        :pagination="configTable.pagination"
        :on-change="handleQuery"
        border
      >
        <template v-slot:index>
          <el-table-column
            type="index"
            width="50px"
            align="center"
            :label="InitialMessage('common.columns.index')"
          >
            <template slot-scope="scope">
              <span>{{ (configTable.pagination.pageNo - 1) * configTable.pagination.pageSize + scope.$index +1 }}</span>
            </template>
          </el-table-column>
        </template>
      </jr-table>
    </template>
  </flat-index>
</template>

<script>

import * as API from '@/api/demo/flat-index'

export default {
  data() {
    return {
      configForm: {
        model: {},
        data: [
          {
            title: '日期区间',
            prop: 'field1',
            type: 'rangeDate'
          },
          {
            title: '债券类型',
            prop: 'type1',
            type: 'select',
            options: [
              { text: '1111', value: '1111' },
              { text: '2222', value: '2222' },
              { text: '3333', value: '3333' }
            ]
          },
          {
            title: '债项评级',
            prop: 'rate1',
            type: 'select',
            options: [
              { text: '1111', value: '1111' },
              { text: '2222', value: '2222' },
              { text: '3333', value: '3333' }
            ]
          },
          {
            title: '隐含评级',
            prop: 'rate2',
            type: 'select',
            options: [
              { text: '1111', value: '1111' },
              { text: '2222', value: '2222' },
              { text: '3333', value: '3333' }
            ]
          },
          {
            title: '剩余期限',
            prop: 'remainTerm',
            type: 'select',
            options: [
              { text: '1111', value: '1111' },
              { text: '2222', value: '2222' },
              { text: '3333', value: '3333' }
            ]
          },
          {
            title: '有无担保',
            prop: 'security',
            type: 'select',
            options: [
              { text: '1111', value: '1111' },
              { text: '2222', value: '2222' },
              { text: '3333', value: '3333' }
            ]
          },
          {
            title: '含权债',
            prop: 'obligation',
            type: 'select',
            options: [
              { text: '1111', value: '1111' },
              { text: '2222', value: '2222' },
              { text: '3333', value: '3333' }
            ]
          },
          {
            title: '主体层级',
            prop: 'subjectLevel',
            type: 'select',
            options: [
              { text: '1111', value: '1111' },
              { text: '2222', value: '2222' },
              { text: '3333', value: '3333' }
            ]
          },
          {
            title: '地区',
            prop: 'district',
            type: 'select',
            options: [
              { text: '1111', value: '1111' },
              { text: '2222', value: '2222' },
              { text: '3333', value: '3333' }
            ]
          }
        ]
      },
      // 表格数据
      configTable: {
        loading: false,
        columns: [
          {
            title: '债券简称',
            prop: 'name'
          },
          {
            title: '发行截止日',
            prop: 'deadline',
            type: 'date'
          },
          {
            title: '中债隐含评级',
            prop: 'impliedRating'
          },
          {
            title: '剩余期限',
            prop: 'remainTerm'
          },
          {
            title: '票面利率(%)',
            prop: 'rate1',
            type: 'rate'
          },
          {
            title: '中债估值(%)',
            prop: 'rate2',
            type: 'rate'
          },
          {
            title: '同期限同评级基准利率(%)',
            prop: 'rate3',
            type: 'rate'
          },
          {
            title: '超额利差(BP)',
            prop: 'rate4',
            type: 'rate'
          },
          {
            title: '发行人',
            prop: 'issuer'
          },
          {
            title: '是否城投',
            prop: 'investment'
          }
        ],
        data: [
          {
            name: '测试名称1',
            deadline: 1624931532000,
            impliedRating: 100,
            remainTerm: 200,
            rate1: 0.01,
            rate2: 0.01,
            rate3: 0.01,
            rate4: 0.01,
            issuer: '测试',
            investment: '是'
          },
          {
            name: '测试名称1',
            deadline: 1624931532000,
            impliedRating: 100,
            remainTerm: 200,
            rate1: 0.01,
            rate2: 0.01,
            rate3: 0.01,
            rate4: 0.01,
            issuer: '测试',
            investment: '是'
          },
          {
            name: '测试名称1',
            deadline: 1624931532000,
            impliedRating: 100,
            remainTerm: 200,
            rate1: 0.01,
            rate2: 0.01,
            rate3: 0.01,
            rate4: 0.01,
            issuer: '测试',
            investment: '是'
          },
          {
            name: '测试名称1',
            deadline: 1624931532000,
            impliedRating: 100,
            remainTerm: 200,
            rate1: 0.01,
            rate2: 0.01,
            rate3: 0.01,
            rate4: 0.01,
            issuer: '测试',
            investment: '是'
          },
          {
            name: '测试名称1',
            deadline: 1624931532000,
            impliedRating: 100,
            remainTerm: 200,
            rate1: 0.01,
            rate2: 0.01,
            rate3: 0.01,
            rate4: 0.01,
            issuer: '测试',
            investment: '是'
          }
        ],
        pagination: {
          pageNo: 1,
          pageSize: window.screen.height > 1000 ? 25 : 15,
          pageSizeOptions: [15, 25, 50, 100],
          total: 0,
          showSizeChanger: true
        }
      }
    }
  },
  methods: {
    // 查询
    handleQuery() {
      const self = this
      const { configForm, configTable } = self

      self.$refs.elForm.validate(async valid => {
        if (valid) {
          configTable.loading = true

          const params = {
            data: { ...configForm },
            page: { ...configTable.pagination }
          }

          const { list = [], total = 0 } = { ...await API.GetListData(params) }

          if (list.length) {
            configTable.loading = false
            configTable.data = list
            configTable.pagination.total = total
          }
        }
      })
    },
    // 重置
    handleReset() {

    }
  }
}
</script>

<style>

</style>
