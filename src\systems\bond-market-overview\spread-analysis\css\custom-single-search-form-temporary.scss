::v-deep .el-checkbox__input {
  margin-top: 5px;
}

::v-deep .el-tooltip {
  margin-top: 3px;
}

::v-deep .jr-svg-icon {
  font-size: 12px !important;
  margin-top: 1px !important;
}

.list-item {
  display: flex;
  align-items: center;
  width: 100%;
  height: 40px;
  background: #f8f8f8;
  padding: 8px 16px;
  gap: 16px;

  &-action {
    display: flex;
    align-items: center;
    height: 26px;
    width: 40px;
    gap: 8px;
  }

  &-label {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 5px;
    width: 200px;

    &-icon {
      display: flex;
      align-items: center;
      width: 20px;
      height: 10px;

      &-left,
      &-right {
        width: 5px;
        height: 3px;
        background: #5b8ff9;
      }

      &-center {
        width: 10px;
        height: 10px;
        background: #ffffff;
        border-radius: 5px;
        border: 2px solid #5b8ff9;
      }
    }

    &-text {
      flex: 1;
      height: 22px;
      font-size: var(--el-font-size-base);
      color: rgba(0, 0, 0, 0.9);
      line-height: 22px;
      text-align: left;
      font-style: normal;
    }
  }
}
