<template>
  <div class="pricingResults">
    <div class="pricingResultsTitle">参考定价结果</div>
    <!-- <el-empty description="请填写预发行信息进行计算" /> -->
    <div class="pricingResults--content">
      <div class="left">
        <div class="item-title">发行预定价</div>
        <el-form :model="form" label-width="60px" label-position="left">
          <jr-form-item label="企业全称" :rules="[{required: true}]">
            <el-input v-model="form.enterpriseName" placeholder="请输入企业全称" suffix-icon="el-icon-search" />
          </jr-form-item>
          <jr-form-item label="发行方式" :rules="[{required: true}]">
            <jr-radio-group v-model="form.issueType" class="width-auto" :data="[{text: '公开发行', value: '公开发行' }, {text: '非公开发行', value: '非公开发行' }]" />
          </jr-form-item>
          <jr-form-item label="债券类型" :rules="[{required: true}]">
            <jr-combobox v-model="form.bondType" placeholder="请选择债券类型" :data="[{ text: 'SCP&CP', value: 'SCP&CP'}]" />
          </jr-form-item>
          <jr-form-item label="发行期限" :rules="[{required: true}]">
            <div style="display: flex;flex-direction: row;">
              <el-input v-model="form.years" />
              <span style="margin-right: 6px;margin-left: 2px;font-size: 14px;">Y</span>
              <el-input v-model="form.months" />
              <span style="margin-left: 2px;font-size: 14px;">M</span>
            </div>
          </jr-form-item>
          <jr-form-item label="担保人">
            <el-input v-model="form.guarantor" placeholder="请输入企业全称" />
          </jr-form-item>
          <jr-form-item label="是否永续" :rules="[{required: true}]">
            <jr-radio-group v-model="form.yxz" class="width-auto" :data="[{text: '是', value: '是' }, {text: '否', value: '否' }]" />
          </jr-form-item>
          <jr-form-item label="选用评级" :rules="[{required: true}]">
            <jr-radio-group v-model="form.lhpj" :data="[{text: '量化评级', value: '量化评级' }]" />
          </jr-form-item>
          <jr-form-item label="选择样本">
            <div style="display: flex;flex-direction: row;justify-content: space-between;align-items: center;">
              <jr-combobox v-model="form.yb" placeholder="选择样本" :data="[]" />
              <div style="margin-left: 20px;width: 200px;text-align: right;">
                <el-button type="primary">定价</el-button>
                <el-button type="primary" plain>导出报告</el-button>
              </div>
            </div>
          </jr-form-item>
        </el-form>
        <div class="block-list">
          <div style="width: 68%;">
            <label>定价</label>
            <span style="color: var(--theme--color);font-size: 16px;">3.53%</span>
          </div>
          <div style="width: 30%;">
            <label>定价区间</label>
            <span style="font-size: 14px;color: #333;">3.13% - 3.93%</span>
          </div>
        </div>
        <div class="tootip" style="margin-top: 20px;">
          <jr-svg-icon icon-class="exclamation-circle" />
          <span>参考定价区间不做任何新券估值结果保证。实际发行定价时，请结合当日市场情况进行综合判断</span>
        </div>
        <div class="tootip">
          <jr-svg-icon icon-class="exclamation-circle" />
          <span>不提供私尊可续期债蝉、次级债券或有担保债券的发行报价</span>
        </div>
      </div>
      <div style="flex: 1;max-width: 50%;padding-right: 20px;height: 100%;">
        <div class="item-title" style="margin-left: 10px;">定价分布</div>
        <div class="ruler">
          <span class="start">2.43%</span>
          <span class="el-icon el-icon-location">57%</span>
          <span class="start">4.18%</span>
        </div>
        <template-module style="height: calc(100% - 80px);" chart-seq="3356b907d7164392a01225a9ecc25ba7" chart-type="BAR" />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      form: {
        enterpriseName: '中远海运租赁有限公司',
        issueType: '公开发行',
        bondType: 'SCP&CP',
        years: 0,
        months: 6,
        yxz: '否',
        lhpj: '量化评级',
        yb: ''
      }
    }
  },
  methods: {}
}
</script>
<style lang="scss">
.pricingResults {
  margin-top: 10px;
  padding: 6px 10px 6px 10px;
  background: #fff;
  .tootip {
    color: #909399;
  }
  .pricingResultsTitle {
    font-size: 16px;
    color: #303133;
    font-weight: 700;
    padding-bottom: 6px;
    border-bottom: 1px solid #ebeef5;
  }
}
.pricingResults--content {
  display: flex;
  justify-content: space-between;
  margin-top: 10px;
  height: 450px;
  .left {
    width: 50%;
    padding-right: 40px;
    border-right: 1px solId #e8e8e8;
    .required {
      position: absolute;
      left: -6px;
    }
    .el-form {
      padding-left: 6px;
    }
  }
  div.block-list {
    display: flex;
    width: 100%;
    flex-direction: row;
    justify-content: space-around;
    height: 60px;
    margin-top: 20px;
    &>div {
      display: inline-flex;
      background-color: #eee;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      label {
        color: #5f5f5f;
        line-height: 20px;
      }
      span {
        font-weight: bold;
      }
    }
  }
  .ruler {
    display: flex;
    justify-content: space-between;
    margin: 30px 20px;
    height: 3px;
    background-color: #435ecc;
    width: calc(100% - 40px);
    flex-direction: row;
    position:relative;
    font-size: 12px !important;
    &::before {
      content: " ";
      background-color: #435ecc;
      display: inline-block;
      position: absolute;
      height: 10px;
      width: 4px;
      top: -4px;
      left: 0;
    }
    &::after {
      content: " ";
      background-color: #435ecc;
      height: 10px;
      width: 4px;
      top: -4px;
      position: absolute;
      right: 0;
    }
    .el-icon {
      display: inline-flex;
      position: absolute;
      left: 57%;
      flex-direction: column-reverse;
      text-align: center;
      top: -34px;
      color: #435ecc;
      font-size: 12px;
      &::before {
        font-size: 16px;
        color: #64bff2;
      }
      &::after {
        content: " ";
        background-color: #435ecc;
        height: 10px;
        width: 4px;
        bottom: -12px;
        position: absolute;
        right: 9px;
      }
    }
  }
}
</style>
