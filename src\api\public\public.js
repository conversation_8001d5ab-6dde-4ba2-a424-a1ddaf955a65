import { GetListInfo, ExportFn } from '@jupiterweb/utils/api'
//公共模块-机构下拉框接口（发行人下拉项）
export const getIssuerList = (params) => GetListInfo('/common/baseinfo/orgInfoOption', params)
//自定义列配置导出合并单元格Excel
export const exportExcelByCustomColumn = (params) => ExportFn('/web/common/exportExcelByCustomColumn', params)
// 查询债券类型下拉
export const queryAllBondType = (params) => GetListInfo('/common/queryBondType/queryAllBondType', params)
// 查询省市区
export const regionLink = (params) => GetListInfo('/web/common/regionLink', params)
// 公共模块-权限企业信息
export const publicCompanyList = (params) => GetListInfo('/system/common/companyAuthInfo', params)
// 获取下载模板（募集资金用途）
export const downloadTemplate = (params) => ExportFn('/web/common/downloadTemplate', params)

// 查询债券公共模块一根据指定发行人查询债券简称
export const queryBondNameByCompony = (params) =>
  GetListInfo(`/common/queryBondType/queryBondNameByComp?param=${params}`)

// 查询登陆企业评级信息
export const queryLoginCompanyRating = (params) => GetListInfo(`/web/common/queryCompanyImplyRating`, params)

// 自定义请求路径
export const customRequest = (url, params) => GetListInfo(url, params)

