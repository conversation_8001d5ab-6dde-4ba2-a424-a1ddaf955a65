<template>
  <el-popover placement="bottom-start" style="width: 100%" trigger="click">
    <el-input
      slot="reference"
      :value="saveDataFormat"
      :style="inputStyle"
      :placeholder="placeholder"
    />
    <div style="padding-bottom: 12px;">
      <el-button @click.stop="selectAll">全选</el-button>
      <el-button @click.stop="selectInvert">反选</el-button>
      <el-button @click.stop="selectClear">清空</el-button>
    </div>
    <el-cascader-panel
      :value="saveData"
      :options="options"
      :props="{
        multiple: true,
        label: labelKey,
        value: valueKey,
        children: children
      }"
      :show-all-levels="false"
      @change="cascaderChange"
    />
  </el-popover>
</template>

<script>
export default {
  name: 'SelectAutoSet',
  props: {
    // 树形结构数据
    options: {
      type: Array,
      default: () => []
    },
    // 占位符
    placeholder: {
      type: String,
      default: '请选择'
    },
    inputStyle: {
      type: Object,
      default: () => {
        return {
          width: '100%'
        }
      }
    },
    // label存储数据名称
    labelKey: {
      type: String,
      default: 'label'
    },
    // value存储数据名称
    valueKey: {
      type: String,
      default: 'value'
    },
    /**
     * 返回上一级数据的格式，是只要最后一级还是全都要
     * last: 只返回最后一级数据
     * all: 返回所有数据
     */
    mode: {
      type: String,
      default: 'last'
    },
    /**
     * 子选项属性值
     */
    children: {
      type: String,
      default: 'children'
    }
  },
  data() {
    return {
      saveData: [],
      flattenTreeArr: []
    }
  },
  computed: {
    saveDataFormat() {
      return this.flattenTreeArr
        .reduce((pre, current) => {
          if (current.checked) {
            pre.push(current.values.map((_) => _.label))
          }
          return pre
        }, [])
        .join(',')
    }
  },
  watch: {
    options: {
      immediate: true,
      handler(newVal) {
        this.flattenTreeArr = this.flattenTree(newVal)
        this.flattenTreeArr = this.flattenTreeArr.map((item) => {
          const obj = {}
          obj.checked = false
          obj.values = item
          return obj
        })
      }
    }
  },
  methods: {
    /**
     * 全选功能处理
     */
    selectAll() {
      this.saveData = this.flattenTreeArr.reduce((pre, current) => {
        this.$set(current, 'checked', true)
        pre.push(current.values.map((_) => _.value))
        return pre
      }, [])
      this.emitConfirmData()
    },
    /**
     * 反选功能处理
     */
    selectInvert() {
      this.saveData = this.flattenTreeArr.reduce((pre, current) => {
        this.$set(current, 'checked', !current.checked)
        if (current.checked) {
          pre.push(current.values.map((_) => _.value))
        }
        return pre
      }, [])
      this.emitConfirmData()
    },
    /**
     * 清空功能处理
     */
    selectClear() {
      this.saveData = []
      this.resetFlattenArr()
      this.emitConfirmData()
    },
    /**
     * 辅助方法：平铺树形结构
     */
    flattenTree(options, arr = [], result = []) {
      options.forEach((node) => {
        const newArr = [...arr]
        newArr.push({
          label: node[this.labelKey],
          value: node[this.valueKey]
        })
        const children = node[this.children]
        if (children && children.length) {
          this.flattenTree(node[this.children], newArr, result)
        } else {
          result.push(newArr)
        }
      })
      return result
    },
    /**
     * 选中级联记录选中状态
     */
    cascaderChange(selectArray) {
      this.resetFlattenArr()
      selectArray.forEach((select) => {
        const str = select.toString()
        const index = this.flattenTreeArr.findIndex((item) => {
          return item.values.map((_) => _.value).toString() === str
        })
        if (index !== -1) {
          this.flattenTreeArr[index].checked = true
        }
      })
      this.saveData = selectArray
      this.emitConfirmData()
    },
    /**
     * 重置选中状态
     */
    resetFlattenArr() {
      this.flattenTreeArr = this.flattenTreeArr.map((item) => {
        item.checked = false
        return item
      })
    },
    /**
     * 返回选中数据给父组件列表
     */
    emitConfirmData() {
      const callBackMode = {
        'all': () => {
          return this.saveData.map((item) => item)
        },
        'last': () => {
          return this.saveData.map((item) => item[item.length - 1])
        }
      }
      this.$emit(
        'emitConfirmData',
        callBackMode[this.mode]()
      )
    }
  }
}
</script>

<style lang="scss" scoped></style>
