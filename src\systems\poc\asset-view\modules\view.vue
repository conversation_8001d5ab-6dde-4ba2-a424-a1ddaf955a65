<!--
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023-10-25 10:54:01
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2023-10-25 15:23:06
 * @Description: 概览
-->
<template>
  <article>
    <div class="header">
      <span class="title">概览</span>
      <el-radio-group v-model="combo" size="mini">
        <el-radio-button v-for="com in comboxList" :key="com" :label="com" />
      </el-radio-group>
    </div>
    <div class="body">
      <section v-for="data in dataList" :key="data.text">
        <span>{{ data.value }}</span>
        <p>{{ data.text }}</p>
      </section>
    </div>
  </article>
</template>

<script>
export default {
  data() {
    return {
      combo: '整体',
      comboxList: ['整体', '资产包池', '基金类池', '债券池', '股票池'],
      dataList: [
        {
          value: 5053,
          text: '资产总量(个)'
        },
        {
          value: 15,
          text: '最新入池(个)'
        },
        {
          value: 5,
          text: '最新出池(个)'
        },
        {
          value: 581,
          text: '配置总量(个)'
        },
        {
          value: '3.35%',
          text: '年化收益率'
        },
        {
          value: '5.67%',
          text: '累计收益率'
        }
      ]
    }
  }
}
</script>

<style lang="scss" scoped>
// .header {}
.body {
  display: grid;
  grid-template-columns: repeat(3, 33.3%);
  section {
    text-align: center;
    padding-top: 18px;
    span {
      font-size: 30px;
      line-height: 60px;
      color: var(--theme--color);
    }
    p {
      font-weight: bold;
    }
  }
}
</style>
