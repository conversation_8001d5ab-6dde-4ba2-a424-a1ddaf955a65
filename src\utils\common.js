import moment from 'moment'
// 自定义工具方法
export const downLoadImg = (url, name) => {

}

export const date = (d = moment()) => {
  /** *
     * date().now()  获取当前时间
     * date().now('YYYY/MM/DD')  格式化时间
     * date('2020-01-01').now('YYYY/MM/DD')  时间差
     *  */

  const now = (fs = 'YYYY-MM-DD') => moment().format(fs)
  /**
   * date().add(1, 'd')  加一天
   *
  */
  const add = (amount = 1, unit = 'd', fs = 'YYYY-MM-DD') => moment(d).add(amount, unit).format(fs)
  /**
   * date().subtract(1, 'd')  减一天
   *
   * **/
  const subtract = (amount = 1, unit = 'd', fs = 'YYYY-MM-DD') => moment(d).subtract(amount, unit).format(fs)
  /**
    *  date().diff('2020-01-01', 'd')  获取时间差
    *  date('2020-01-11').diff('2020-01-01', 'd')  获取时间差
    * **/
  const diff = (b = moment(), unitOfTime = 'd', precise = false) => moment(d).diff(moment(b), unitOfTime, precise)
  /**
   * date(xxx).format()
   *
   * **/
  const format = (fs = 'YYYY-MM-DD') => moment(d).format(fs)
  return {
    now,
    add,
    subtract,
    diff,
    format
  }
}
