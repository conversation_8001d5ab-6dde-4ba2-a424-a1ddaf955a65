<!--
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023-04-20 19:49:22
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2024-06-25 15:29:46
 * @Description: 合规中心
-->
<template>
  <div style="height: 100%;" class="home-poc-item has-fullscreen">
    <div class="home-poc-item--header float-left">合规中心(<font color="red">{{ totalCount }}</font>)<fullscreen v-on="{ ...$listeners }" /></div>
    <!-- 产品概览 -->
    <jr-decorated-table
      ref="table"
      custom-id="d48e5ed343064f9abfc9b6ccfb5fba74"
      :custom-render="config.columns"
      :handleclose="handleClose"
      v-bind="{ ...$attrs, menuinfo: { pageId: 'Rule', btnList: [{btnkey: 'tiaocang', btnnm: '调仓', expr: '{{indName}}!=null', btnPosition: 'LIST', icon: 'zhuancang'},{btnkey: 'close', btnnm: '关闭', btnPosition: 'LIST', icon: 'bell-off'}]} }"
      @refreshed="queryEnd"
    />
    <modal-non-process
      ref="modal"
      size="large"
      :modal-footer="false"
      :close-modal="closeModal"
      :visible="ConfigModal.visible"
      :modal-type="ConfigModal.modalType"
      :modal-type-name="ConfigModal.modalTypeName"
      :modal-title="ConfigModal.modalTitle"
      :module-id="ConfigModal.moduleId"
      :businessno="ConfigModal.businessno"
      :menuinfo="ConfigModal.menuinfo"
      :item-data="ConfigModal.itemData"
      join-type
    />
  </div>
</template>

<script>
import { JoyinMath } from '@/utils/common'
import { CloseRemind } from '@/api/home.js'
import * as API from '@/api/invest/riskredis/quota/rsksupervisionriskanalyze'
import fullscreen from './fullscreen'
export default {
  components: {
    fullscreen
  },
  data() {
    const self = this
    return {
      SUP_UNIT: [],
      totalCount: 0,
      // 监控值明细信息
      ConfigModal: {
        visible: false,
        itemData: {},
        modalTitle: '监控值明细信息',
        modalType: 'new',
        modalTypeName: '',
        moduleId: '',
        menuinfo: {
          componenturl: 'invest/riskredis/quota/rsksupervisionriskanalyze/RskSupervisionRiskAnalyze001/infoDetail' // 组件地址
        }
      },
      config: {
        columns: {
          supStatAbbr: (h, { row }) => {
            if (row.supStatAbbr === '正常' && row.mxCount === 0) {
              return <span style='color:#008018'>{row.supStatAbbr}</span>
            } else if (row.supStatAbbr === '正常') {
              return <a style='color:#008018;text-decoration:underline' onClick={this.handleVisible.bind(this, row)}>{row.supStatAbbr}</a>
            }
            if (row.supStatAbbr === '触发审批' && row.mxCount === 0) {
              return <span style='color:#ffa500'>{row.supStatAbbr}</span>
            } else if (row.supStatAbbr === '触发审批') {
              return <a style='color:#ffa500;text-decoration:underline' onClick={this.handleVisible.bind(this, row)} >{row.supStatAbbr}</a>
            }
            if (row.supStatAbbr === '异常') {
              return <span>{row.supStatAbbr}</span>
            }
            if (row.supStatAbbr === '触发超限' && row.mxCount === 0) {
              return <span style='color:red'>{row.supStatAbbr}</span>
            } else if (row.supStatAbbr === '触发超限') {
              return <a style='color:red;text-decoration:underline' onClick={this.handleVisible.bind(this, row)}>{row.supStatAbbr}</a>
            }
            if (row.supStatAbbr === '触发预警' && row.mxCount === 0) {
              return <span style='color:#ffa500'>{row.supStatAbbr}</span>
            } else if (row.supStatAbbr === '触发预警') {
              return <a style='color:#ffa500;text-decoration:underline' onClick={this.handleVisible.bind(this, row)}>{row.supStatAbbr}</a>
            }
          },
          supVal: (h, { row }) => {
            const joyinMath = new JoyinMath()
            var wg = row.supVal
            if (wg === '') {
              return wg
            }
            if (row.indType.indexOf('FORBID_') !== -1) {
              /* 禁止型 */
              var secCell = false
              if (wg.indexOf('||') !== -1) {
                secCell = true
              }
              var wgArr = wg.split(',')
              var wgStr = ''
              for (var m = 0; m < wgArr.length; m++) {
                if (wgArr[m] != null && wgArr[m] !== '' && wgArr[m] !== undefined) {
                  if (secCell) {
                    var wgArrSub = wgArr[m].split('||')
                    if (wgArrSub.length > 1) { wgStr += wgArrSub[1] + ',' } else { wgStr += wgArrSub[0] + ',' }
                  } else {
                    wgStr += wgArr[m] + ','
                  }
                }
              }
              wg = wgStr.substring(0, wgStr.length - 1)
            } else {
              if (row.supStat === 'ERROR') {
                return <span style='color: red'> 分母可能为0 </span>
              }
              wg = joyinMath.doMul(parseFloat(Number(wg)), 1, 10)
              wg = Number(wg)
              if (row.foulCond) {
                var detailJson = JSON.parse(row.foulCond)
                var curunit = this.valueConvertUnit(detailJson.threshold_1.unit)
                if (detailJson.threshold_1.unit === 'PERCENT') {
                  wg = this.toDecimal(wg * 100, 4)
                } else if (detailJson.threshold_1.unit === 'WRMB') {
                  wg = joyinMath.doMul(parseFloat(Number(wg / 10000)), 1, 4)
                } else if (detailJson.threshold_1.unit === 'BP') {
                  wg = joyinMath.doMul(parseFloat(Number(wg * 10000)), 1, 4)
                } else {
                  wg = joyinMath.doMul(parseFloat(Number(wg)), 1, 6)
                }
                wg = wg === Number(wg) ? Number(wg) + curunit : wg + curunit
              } else {
                wg = (wg * 100).toFixed(4) + '%'
              }
            }
            return h('span', wg)
          },
          warnCond: (h, { row }) => {
            var wg = ''
            if (!row.warnCond) {
              return <span>{wg}</span>
            }
            var detailJson = JSON.parse(row.warnCond)
            if (detailJson !== null && detailJson !== '' && detailJson !== undefined) {
              if (row.indType.indexOf('LIMIT_') !== -1 || row.indType.indexOf('INNER_') !== -1) {
                if (detailJson.threshold_1.value != null && detailJson.threshold_1.value !== '' && detailJson.threshold_1.value !== undefined) {
                  wg = self.decode(detailJson.threshold_1.op) + detailJson.threshold_1.value + self.valueConvertUnit(detailJson.threshold_1.unit)
                }
              } else {
                wg = detailJson.threshold_1.value_cn
                wg = wg.substring(0, wg.length - 1)
              }
            }
            return <span>{wg}</span>
          },
          approveCond: (h, { row }) => {
            var wg = ''
            if (!row.approveCond) {
              return <span>{wg}</span>
            }
            var detailJson = JSON.parse(row.approveCond)
            if (detailJson !== null && detailJson !== '' && detailJson !== undefined) {
              if (row.indType.indexOf('LIMIT_') !== -1 || row.indType.indexOf('INNER_') !== -1) {
                if (detailJson.threshold_1.value != null && detailJson.threshold_1.value !== '' && detailJson.threshold_1.value !== undefined) {
                  wg = self.decode(detailJson.threshold_1.op) + detailJson.threshold_1.value + self.valueConvertUnit(detailJson.threshold_1.unit)
                }
              } else {
                wg = detailJson.threshold_1.value_cn
                wg = wg.substring(0, wg.length - 1)
              }
            }
            return <span>{wg}</span>
          },
          foulCond: (h, { row }) => {
            var wg = ''
            if (!row.foulCond) {
              return <span>{wg}</span>
            }
            var detailJson = JSON.parse(row.foulCond)
            if (detailJson !== null && detailJson !== '' && detailJson !== undefined) {
              if (row.indType.indexOf('LIMIT_') !== -1 || row.indType.indexOf('INNER_') !== -1) {
                if (detailJson.threshold_1.value != null && detailJson.threshold_1.value !== '' && detailJson.threshold_1.value !== undefined) {
                  wg = self.decode(detailJson.threshold_1.op) + detailJson.threshold_1.value + self.valueConvertUnit(detailJson.threshold_1.unit)
                }
              } else {
                wg = detailJson.threshold_1.value_cn
                wg = wg.substring(0, wg.length - 1)
              }
            }
            return <span>{wg}</span>
          }
        }
      }
    }
  },
  created() {
    this.GetDict()
  },
  methods: {
    valueConvertUnit(value) {
      if (value === '') {
        return value
      }
      if (value === 'NONE') {
        return ''
      }
      var retStr
      var supUnitArr = this.SUP_UNIT
      supUnitArr.forEach(item => {
        if (item.id === value) {
          if (value === 'NONE') {
            retStr = ''
          } else {
            retStr = item.text
          }
          return false
        }
      })
      return retStr
    },
    toDecimal(x, num) {
      const joyinMath = new JoyinMath()
      var f = parseFloat(x)
      if (isNaN(f)) {
        return
      }
      f = joyinMath.doMul(f, 1, num)
      return f
    },
    GetDict() {
      API.GetDict([{ 'dictCode': 'SUP_UNIT' }]).then(res => {
        res[0].options.forEach(item => {
          this.SUP_UNIT.push(item)
        })
      })
    },
    // 转义字符
    decode(text) {
      const matchList = {
        '&lt;': '<',
        '&gt;': '>',
        '&amp;': '&',
        '&#34;': '"',
        '&quot;': '"',
        '&#39;': "'"
      }
      let regStr = '(' + Object.keys(matchList).toString() + ')'
      regStr = regStr.replace(/,/g, ')|(')
      const regExp = new RegExp(regStr, 'g')
      return text.replace(regExp, match => matchList[match])
    },
    handleVisible(row) {
      this.ConfigModal.visible = true
      this.ConfigModal.itemData = row
    },
    closeModal() {
      this.ConfigModal.visible = false
    },
    handleClose(row) {
      const self = this

      let mainCategory = '02'
      const getParams = (remType, id, name, feeId, mdate, remark) => {
        const param = {
          cdate: mdate, // TODO
          remType: remType,
          finprodId: id,
          finprodName: name,
          feeId: feeId,
          mdate: mdate,
          remark: remark,
          queryDate: mdate // TODO
        }
        if (row.remType === 'R1S3') {
          param.businessPrimaryKey1 = row.mdateStr
        } else if (row.remType === 'R5S2') {
          param.finprodId = row.portfolioId
          param.businessPrimaryKey1 = row.indId
        } else if (row.remType === 'R3S5') {
          param.finprodId = row.assetId
          param.businessPrimaryKey1 = row.oneAttr
          param.businessPrimaryKey2 = row.mdateStr
        } else if (row.remType === 'R3S8') {
          param.businessPrimaryKey1 = row.reminder
        } else if (row.remType === 'R3S16' || row.remType === 'R3S15' || row.remType === 'R3S13' || row.remType === 'R3S10' || row.remType === 'R3S3' ||
            row.remType === 'R3S12' || row.remType === 'R3S1' || row.remType === 'R3S2' || row.remType === 'R3S4') {
          param.finprodId = row.assetCode
          param.businessPrimaryKey1 = row.reminder
        }
        return param
      }
      const paramsMap = {
        R1: getParams(row.remType, row.prodId, row.finprodName, row.feeId, row.mdateStr, null),
        R2: getParams(row.remType, row.assetCode, row.assetName, row.feeId, row.mdateStr, null),
        R3: getParams(row.remType, row.portfolioId, row.indId, row.feeId, row.mdateStr, row.supFrequency),
        R5: getParams(row.remType, row.assetCode, row.assetName, row.feeId, row.mdateStr, row.remark),
        R12: {
          remType: row.remType,
          finprodId: row.finprodId,
          businessPrimaryKey1: row.tradePlace,
          finprodName: row.finprodName,
          remark: null,
          cdate: row.cdateStr,
          queryDate: row.cdateStr // TODO
        },
        R14: {
          remType: row.remType,
          finprodId: row.assetCode,
          finprodName: row.assetName,
          businessPrimaryKey1: row.assetId,
          remark: row.remark,
          cdate: row.cdateStr,
          queryDate: row.cdateStr // TODO
        }
      }

      if (row.remType === 'R3S8' || row.remType === 'R3S15' || row.remType === 'R3S14') {
        mainCategory = 'R2'
      }

      self.confirm('是否确认关闭提醒!', {
        async onOk() {
          const result = await CloseRemind(paramsMap[mainCategory] || paramsMap['R2'])
          if (result) {
            self.msgSuccess('关闭成功！')
            self.$refs.table.triggerTableRefresh()
          }
        }
      })
    },
    queryEnd(ins) {
      this.totalCount = ins.pagination.total
    }
  }
}
</script>

<style lang="scss" scoped>
@import "./poc.scss";
</style>
