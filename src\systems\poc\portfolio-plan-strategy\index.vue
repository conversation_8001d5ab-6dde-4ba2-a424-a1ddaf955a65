<!--
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023-11-06 15:48:25
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2023-11-10 20:37:53
 * @Description: 描述
-->

<template>
  <flat-index class="poc-portfolio-plan" :query="handleQuery" :reset="handleReset" :pagination="configTable.pagination">
    <template v-slot:form>
      <el-form ref="elForm" :model="configForm.model">
        <jr-form-item-create :column="0" :data="configForm.data" :model="configForm.model" />
      </el-form>
    </template>

    <template v-slot:right-button>
      <el-button>
        <jr-svg-icon icon-class="export" />
      </el-button>
    </template>

    <template v-slot:table-header-button>
      <el-button type="primary" @click="handleOpen('/portfolio.html?strategy', '策略组合')">{{ InitialMessage('common.system.button.add') }}组合</el-button>
      <el-button type="primary" @click="handleOpen('/backtest.html', '组合回测', 'full')">回测</el-button>
      <el-button type="primary" @click="handleOpen('/compare.html', '组合对比', 'full')">对比</el-button>
    </template>

    <template v-slot:table-list="{ height }">
      <jr-table
        :height="height"
        :columns="configTable.columns"
        :data-source="configTable.data"
        row-key="名称"
        :expand-row-keys="['股票权益类策略', '股票多头组合', '固收类策略', '混合类']"
        :loading="configTable.loading"
        :pagination="configTable.pagination"
        :cell-style="handleCellStyle"
        :row-style="handleRowStyle"
        :on-change="handleQuery"
        border
      />
    </template>

    <!-- 详情弹框(全局注册的组件)-->
    <modal-non-process
      ref="modal"
      :size="configModal.size"
      :close-modal="closeModal"
      :save="configModal.save"
      :visible="configModal.visible"
      :modal-type="configModal.modalType"
      :modal-type-name="configModal.modalTypeName"
      :modal-title="configModal.modalTitle"
      :module-id="configModal.moduleId"
      :businessno="configModal.businessno"
      :menuinfo="configModal.menuinfo"
      :item-data="configModal.itemData"
      join-type
    />
  </flat-index>
</template>

<script>
export default {
  props: {
    // 平台日期
    date: {
      type: [Number, String],
      default: null
    },
    // 权限相关
    permitdetail: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    const self = this
    return {
      // 查询区域
      configForm: {
        model: {
          field2: '全部',
          field3: '2022-08-16'
        },
        data: [
          {
            title: '组合代码',
            prop: 'field1',
            type: 'select',
            placeholder: '请选择组合',
            options: [
              { text: '1111', value: '1111' },
              { text: '2222', value: '2222' },
              { text: '3333', value: '3333' }
            ]
          },
          {
            title: '策略类型',
            prop: 'field2',
            type: 'select',
            options: [
              { text: '全部', value: '全部' },
              { text: '股票权益类策略', value: '股票权益类策略' },
              { text: '固收类策略', value: '固收类策略' },
              { text: '混合类', value: '混合类' }
            ]
          },
          {
            title: '查询日期',
            prop: 'field3',
            type: 'date'
          }
        ]
      },
      // 表格数据
      configTable: {
        loading: false,
        columns: [
          '名称',
          '策略标签',
          '持仓权重（%）',
          '市值（元）',
          '当日盈亏（元）',
          '累计盈亏（元）',
          '最新净值/价格',
          '净值/价格日期',
          '七日年化收益率（%）',
          '成立以来年化收益率（%）',
          '年化标准差（%）',
          '最大回撤（%）',
          '夏普比率',
          '索提诺比率',
          '创建人',
          '创建日期',
          '更新日期'
        ]
          .map((k, index) => {
            return {
              title: k,
              prop: k,
              minWidth: index === 0 ? 200 : index > 3 && index < 7 ? 200 : 120,
              className: index > 1 && index < 6 ? 'linear-cell' : '',
              align: /元|价格|%|比率/.test(k) ? 'right' : 'left'
            }
          })
          .concat({
            title: '操作',
            prop: '操作',
            width: 120,
            fixed: 'right',
            align: 'center',
            render: (h, { row }) => {
              if (row.策略标签) {
                return (
                  <span class='table-action-box'>
                    <el-tooltip content='调整组合'>
                      <jr-svg-icon icon-class='merge-cells' onClick={self.handleOpen.bind(self, '/portfolio.html?adjust', '调整组合方案')}/>
                    </el-tooltip>
                    <el-tooltip content='调成记录'>
                      <jr-svg-icon icon-class='log' onClick={self.handleOpen.bind(self, '/portfolio.html?log', '调成记录')}/>
                    </el-tooltip>
                    <el-tooltip content='组合再平衡'>
                      <jr-svg-icon icon-class='skill' onClick={self.handleOpen.bind(self, '/portfolio.html?log', '投资组合设置')}/>
                    </el-tooltip>
                  </span>
                )
              }
              return <span />
            }
          }),
        data: [
          {
            '名称': '股票权益类策略',
            '策略标签': '',
            '持仓权重（%）': '',
            '市值（元）': '3,224,138,350.00',
            '当日盈亏（元）': '65,663,312.18',
            '累计盈亏（元）': '127,640,091.24',
            '最新净值/价格': '',
            '净值/价格日期': '',
            '七日年化收益率（%）': '',
            '成立以来年化收益率（%）': '',
            '年化标准差（%）': '',
            '最大回撤（%）': '',
            '夏普比率': '',
            '索提诺比率': '',
            '创建人': '',
            '创建日期': '',
            '更新日期': '',
            'children': [
              {
                '名称': '股票多头组合',
                '策略标签': '量化选股',
                '持仓权重（%）': '100.00',
                '市值（元）': '1,000,186,760.00',
                '当日盈亏（元）': '25,036,312.91',
                '累计盈亏（元）': '83,098,411.05',
                '最新净值/价格': '1.12',
                '净值/价格日期': '2022/8/16',
                '七日年化收益率（%）': '6.35',
                '成立以来年化收益率（%）': '5.18',
                '年化标准差（%）': '10.09',
                '最大回撤（%）': '15.43',
                '夏普比率': '1.03',
                '索提诺比率': '1.29',
                '创建人': 'zlp',
                '创建日期': '2019/9/22',
                '更新日期': '2022/8/3',
                'children': [
                  {
                    '名称': '奥泰生物',
                    'rate': true,
                    '策略标签': '',
                    '持仓权重（%）': '20.53',
                    '市值（元）': '205,331,110.00',
                    '当日盈亏（元）': '8,143,873.11',
                    '累计盈亏（元）': '24,431,619.34',
                    '最新净值/价格': '5.51',
                    '净值/价格日期': '2022/8/16',
                    '七日年化收益率（%）': '5.29',
                    '成立以来年化收益率（%）': '4.12',
                    '年化标准差（%）': '11.30',
                    '最大回撤（%）': '8.61',
                    '夏普比率': '1.58',
                    '索提诺比率': '1.61',
                    '创建人': '',
                    '创建日期': '',
                    '更新日期': ''
                  },
                  {
                    '名称': '精致达',
                    '策略标签': '',
                    'rate': true,
                    '持仓权重（%）': '15.14',
                    '市值（元）': '151,411,550.00',
                    '当日盈亏（元）': '7,743,873.11',
                    '累计盈亏（元）': '23,231,619.34',
                    '最新净值/价格': '8.82',
                    '净值/价格日期': '2022/8/16',
                    '七日年化收益率（%）': '5.01',
                    '成立以来年化收益率（%）': '3.84',
                    '年化标准差（%）': '15.51',
                    '最大回撤（%）': '10.59',
                    '夏普比率': '0.91',
                    '索提诺比率': '0.93',
                    '创建人': '',
                    '创建日期': '',
                    '更新日期': ''
                  },
                  {
                    '名称': '云南城投',
                    '策略标签': '',
                    'rate': true,
                    '持仓权重（%）': '14.53',
                    '市值（元）': '145,333,800.00',
                    '当日盈亏（元）': '7,643,873.11',
                    '累计盈亏（元）': '22,931,619.34',
                    '最新净值/价格': '9.21',
                    '净值/价格日期': '2022/8/16',
                    '七日年化收益率（%）': '4.89',
                    '成立以来年化收益率（%）': '3.72',
                    '年化标准差（%）': '10.01',
                    '最大回撤（%）': '9.73',
                    '夏普比率': '0.85',
                    '索提诺比率': '0.69',
                    '创建人': '',
                    '创建日期': '',
                    '更新日期': ''
                  },
                  {
                    '名称': '康尼机电',
                    '策略标签': '',
                    'rate': true,
                    '持仓权重（%）': '13.43',
                    '市值（元）': '134,313,100.00',
                    '当日盈亏（元）': '-9,384,794.89',
                    '累计盈亏（元）': '-9,381,794.89',
                    '最新净值/价格': '15.55',
                    '净值/价格日期': '2022/8/16',
                    '七日年化收益率（%）': '-2.01',
                    '成立以来年化收益率（%）': '1.16',
                    '年化标准差（%）': '16.22',
                    '最大回撤（%）': '15.71',
                    '夏普比率': '0.13',
                    '索提诺比率': '0.52',
                    '创建人': '',
                    '创建日期': '',
                    '更新日期': ''
                  },
                  {
                    '名称': '津投诚开',
                    '策略标签': '',
                    'rate': true,
                    '持仓权重（%）': '12.01',
                    '市值（元）': '120,114,100.00',
                    '当日盈亏（元）': '-5,384,794.89',
                    '累计盈亏（元）': '-10,760,308.77',
                    '最新净值/价格': '9.28',
                    '净值/价格日期': '2022/8/16',
                    '七日年化收益率（%）': '-2.85',
                    '成立以来年化收益率（%）': '-0.68',
                    '年化标准差（%）': '17.43',
                    '最大回撤（%）': '21.29',
                    '夏普比率': '-0.25',
                    '索提诺比率': '-0.13',
                    '创建人': '',
                    '创建日期': '',
                    '更新日期': ''
                  },
                  {
                    '名称': '江苏吴中',
                    '策略标签': '',
                    'rate': true,
                    '持仓权重（%）': '10.35',
                    '市值（元）': '103,534,910.00',
                    '当日盈亏（元）': '-884,794.89',
                    '累计盈亏（元）': '-1,676,661.77',
                    '最新净值/价格': '10.85',
                    '净值/价格日期': '2022/8/16',
                    '七日年化收益率（%）': '-1.69',
                    '成立以来年化收益率（%）': '-3.86',
                    '年化标准差（%）': '18.64',
                    '最大回撤（%）': '25.43',
                    '夏普比率': '0.08',
                    '索提诺比率': '0.12',
                    '创建人': '',
                    '创建日期': '',
                    '更新日期': ''
                  },
                  {
                    '名称': '腾龙股份',
                    '策略标签': '',
                    'rate': true,
                    '持仓权重（%）': '8.24',
                    '市值（元）': '82,414,520.00',
                    '当日盈亏（元）': '-384,794.89',
                    '累计盈亏（元）': '-766,658.77',
                    '最新净值/价格': '20.55',
                    '净值/价格日期': '2022/8/16',
                    '七日年化收益率（%）': '1.09',
                    '成立以来年化收益率（%）': '-4.08',
                    '年化标准差（%）': '8.89',
                    '最大回撤（%）': '8.51',
                    '夏普比率': '0.05',
                    '索提诺比率': '0.21',
                    '创建人': '',
                    '创建日期': '',
                    '更新日期': ''
                  },
                  {
                    '名称': '泉阳泉',
                    '策略标签': '',
                    'rate': true,
                    '持仓权重（%）': '5.77',
                    '市值（元）': '57,733,670.00',
                    '当日盈亏（元）': '17,543,873.11',
                    '累计盈亏（元）': '35,088,977.23',
                    '最新净值/价格': '19.55',
                    '净值/价格日期': '2022/8/16',
                    '七日年化收益率（%）': '10.93',
                    '成立以来年化收益率（%）': '7.76',
                    '年化标准差（%）': '10.10',
                    '最大回撤（%）': '9.03',
                    '夏普比率': '1.79',
                    '索提诺比率': '1.81',
                    '创建人': '',
                    '创建日期': '',
                    '更新日期': ''
                  }
                ]
              },
              {
                '名称': '指数增强策略',
                '策略标签': '量化选股',
                'children': [{ '名称': '122211' }],
                '持仓权重（%）': '100.00',
                '市值（元）': '996,751,415.00',
                '当日盈亏（元）': '22,692,889.91',
                '累计盈亏（元）': '-1,764,579.95',
                '最新净值/价格': '1.00',
                '净值/价格日期': '2022/8/16',
                '七日年化收益率（%）': '3.21',
                '成立以来年化收益率（%）': '3.19',
                '年化标准差（%）': '3.29',
                '最大回撤（%）': '10.02',
                '夏普比率': '0.98',
                '索提诺比率': '',
                '创建人': '',
                '创建日期': '',
                '更新日期': ''
              },
              {
                '名称': '行业周期策略',
                '策略标签': '行业周期',
                'children': [{ '名称': '122212' }],
                '持仓权重（%）': '100.00',
                '市值（元）': '175,843,539.00',
                '当日盈亏（元）': '1,992,890.06',
                '累计盈亏（元）': '8,235,420.05',
                '最新净值/价格': '1.00',
                '净值/价格日期': '2022/8/16',
                '七日年化收益率（%）': '3.55',
                '成立以来年化收益率（%）': '2.05',
                '年化标准差（%）': '10.91',
                '最大回撤（%）': '13.91',
                '夏普比率': '0.67',
                '索提诺比率': '',
                '创建人': '',
                '创建日期': '',
                '更新日期': ''
              },
              {
                '名称': '大消费策略',
                '策略标签': '大消费',
                '持仓权重（%）': '100.00',
                'children': [{ '名称': '122213' }],
                '市值（元）': '675,843,318.00',
                '当日盈亏（元）': '9,992,422.14',
                '累计盈亏（元）': '-964,579.95',
                '最新净值/价格': '1.00',
                '净值/价格日期': '2022/8/16',
                '七日年化收益率（%）': '4.01',
                '成立以来年化收益率（%）': '3.13',
                '年化标准差（%）': '15.02',
                '最大回撤（%）': '7.91',
                '夏普比率': '0.61',
                '索提诺比率': '',
                '创建人': '',
                '创建日期': '',
                '更新日期': ''
              },
              {
                '名称': '宏观策略',
                '策略标签': '宏观',
                'children': [{ '名称': '122214' }],
                '持仓权重（%）': '100.00',
                '市值（元）': '375,513,318.00',
                '当日盈亏（元）': '5,948,797.16',
                '累计盈亏（元）': '39,035,420.05',
                '最新净值/价格': '1.00',
                '净值/价格日期': '2022/8/16',
                '七日年化收益率（%）': '2.91',
                '成立以来年化收益率（%）': '2.05',
                '年化标准差（%）': '8.34',
                '最大回撤（%）': '15.90',
                '夏普比率': '-0.47',
                '索提诺比率': '',
                '创建人': '',
                '创建日期': '',
                '更新日期': ''
              }
            ]
          },
          {
            '名称': '固收类策略',
            '策略标签': '',
            '持仓权重（%）': '100.00',
            '市值（元）': '1,522,473,118.88',
            '当日盈亏（元）': '40,645,680.89',
            '累计盈亏（元）': '81,370,642.78',
            '最新净值/价格': '',
            '净值/价格日期': '',
            '七日年化收益率（%）': '',
            '成立以来年化收益率（%）': '',
            '年化标准差（%）': '',
            '最大回撤（%）': '',
            '夏普比率': '',
            '索提诺比率': '',
            '创建人': '',
            '创建日期': '',
            '更新日期': '',
            'children': [
              {
                '名称': '信用债优选策略',
                '策略标签': '债券优选',
                'children': [{ '名称': '122215' }],
                '持仓权重（%）': '100.00',
                '市值（元）': '999,579,586.41',
                '当日盈亏（元）': '4,166,078.64',
                '累计盈亏（元）': '8,333,388.28',
                '最新净值/价格': '1.00',
                '净值/价格日期': '2022/8/16',
                '七日年化收益率（%）': '4.19',
                '成立以来年化收益率（%）': '3.02',
                '年化标准差（%）': '4.36',
                '最大回撤（%）': '6.28',
                '夏普比率': '0.69',
                '索提诺比率': '0.75',
                '创建人': 'zlp',
                '创建日期': '2019/9/8',
                '更新日期': '2022/8/3'
              },
              {
                '名称': '固收+策略优选',
                '策略标签': '债券优选',
                'children': [{ '名称': '122216' }],
                '持仓权重（%）': '100.00',
                '市值（元）': '465,256,412.82',
                '当日盈亏（元）': '30,733,761.28',
                '累计盈亏（元）': '61,468,753.56',
                '最新净值/价格': '1.00',
                '净值/价格日期': '2022/8/16',
                '七日年化收益率（%）': '4.05',
                '成立以来年化收益率（%）': '2.06',
                '年化标准差（%）': '2.91',
                '最大回撤（%）': '4.98',
                '夏普比率': '0.91',
                '索提诺比率': '0.86',
                '创建人': 'zlp',
                '创建日期': '2020/6/10',
                '更新日期': '2022/7/19'
              },
              {
                '名称': '纯债策略',
                '策略标签': '久期策略',
                '持仓权重（%）': '100.00',
                'children': [{ '名称': '122217' }],
                '市值（元）': '57,637,119.65',
                '当日盈亏（元）': '5,745,840.97',
                '累计盈亏（元）': '11,568,500.93',
                '最新净值/价格': '1.00',
                '净值/价格日期': '2022/8/16',
                '七日年化收益率（%）': '3.26',
                '成立以来年化收益率（%）': '2.89',
                '年化标准差（%）': '5.88',
                '最大回撤（%）': '10.81',
                '夏普比率': '0.77',
                '索提诺比率': '0.93',
                '创建人': 'zlp',
                '创建日期': '2022/4/18',
                '更新日期': '2022/6/15'
              }
            ]
          },

          {
            '名称': '混合类',
            '策略标签': '',
            '持仓权重（%）': '100.00',
            '市值（元）': '1,968,966,791.00',
            '当日盈亏（元）': '47,121,039.10',
            '累计盈亏（元）': '94,343,553.20',
            '最新净值/价格': '',
            '净值/价格日期': '',
            '七日年化收益率（%）': '',
            '成立以来年化收益率（%）': '',
            '年化标准差（%）': '',
            '最大回撤（%）': '',
            '夏普比率': '',
            '索提诺比率': '',
            '创建人': '',
            '创建日期': '',
            '更新日期': '',
            'children': [
              {
                '名称': 'FOF策略',
                '策略标签': 'FOF',
                'children': [{ '名称': '122218' }],
                '持仓权重（%）': '100.00',
                '市值（元）': '990,369,541.00',
                '当日盈亏（元）': '29,245,074.10',
                '累计盈亏（元）': '58,502,459.20',
                '最新净值/价格': '1.00',
                '净值/价格日期': '2022/8/16',
                '七日年化收益率（%）': '3.01',
                '成立以来年化收益率（%）': '3.06',
                '年化标准差（%）': '8.99',
                '最大回撤（%）': '9.81',
                '夏普比率': '0.83',
                '索提诺比率': '0.85',
                '创建人': 'zlp',
                '创建日期': '2020/4/13',
                '更新日期': '2022/8/10'
              },
              {
                '名称': 'MOM策略',
                '策略标签': 'MOM',
                'children': [{ '名称': '122219' }],
                '持仓权重（%）': '100.00',
                '市值（元）': '801,843,327.00',
                '当日盈亏（元）': '21,192,452.70',
                '累计盈亏（元）': '42,397,250.40',
                '最新净值/价格': '1.00',
                '净值/价格日期': '2022/8/16',
                '七日年化收益率（%）': '2.71',
                '成立以来年化收益率（%）': '2.01',
                '年化标准差（%）': '14.67',
                '最大回撤（%）': '8.29',
                '夏普比率': '0.98',
                '索提诺比率': '0.99',
                '创建人': 'zlp',
                '创建日期': '2020/4/16',
                '更新日期': '2022/8/3'
              },
              {
                '名称': '期货CTA策略',
                '策略标签': 'CTA',
                'children': [{ '名称': '122220' }],
                '持仓权重（%）': '100.00',
                '市值（元）': '176,753,923.00',
                '当日盈亏（元）': '-3,316,487.70',
                '累计盈亏（元）': '-6,556,156.40',
                '最新净值/价格': '1.00',
                '净值/价格日期': '2022/8/16',
                '七日年化收益率（%）': '2.85',
                '成立以来年化收益率（%）': '2.37',
                '年化标准差（%）': '19.89',
                '最大回撤（%）': '10.94',
                '夏普比率': '0.91',
                '索提诺比率': '1.07',
                '创建人': 'zlp',
                '创建日期': '2020/4/10',
                '更新日期': '2022/8/12'
              }
            ]
          }
        ],
        pagination: {
          pageNo: 1,
          pageSize: window.screen.height > 1000 ? 25 : 15,
          pageSizeOptions: [15, 25, 50, 100],
          total: 0,
          showSizeChanger: true
        }
      },
      // 弹框配置项
      configModal: {
        save: '/invest/basicdata/mstcommissionset/MstCommissionSet002', // 初始化 init 的接口地址，必传(这里随便写的，开发需填写真实接口地址)
        visible: false,
        itemData: {},
        // businessno: DICT.SYSCONSTANT.BUSINSSNO.SYSTEMADJUSTDEAL,
        modalTitle: '演示弹窗',
        modalType: 'new',
        modalTypeName: '新建',
        size: 'normal',
        moduleId: '',
        menuinfo: {
          componenturl: 'demo/update' // 组件地址
        }
      }
    }
  },
  methods: {
    handleOpen(page, title, size = 'normal') {
      Object.assign(this.configModal, {
        visible: true,
        modalTitle: title,
        modalType: 'iframe',
        size,
        menuinfo: {
          componenturl: location.origin + page
        }
      })
    },
    handleRowStyle({ row }) {
      if (row['名称'].includes('投资组合')) {
        return {
          fontWeight: 'bold'
        }
      }
    },
    handleCellStyle({ row, column }) {
      const prop = column.columnKey
      const val = (typeof row[prop] === 'string' ? row[prop] : '').replace(/,/g, '')
      if (!val) return {}
      if (prop === '持仓权重（%）' && row.rate) {
        return {
          '--wdh': Number(val) + '%',
          '--bg': 'linear-gradient(90deg, #70ade9, transparent)'
        }
      }
      if (['市值（元）', '当日盈亏（元）', '累计盈亏（元）'].includes(prop)) {
        const maxMap = {
          '市值（元）': ' 3224138350.00',
          '当日盈亏（元）': '65663312.18',
          '累计盈亏（元）': '127640091.24'
        }
        const ratio = parseInt((Number(val) / Number(maxMap[prop])) * 100)
        return {
          '--wdh': Math.abs(ratio) + '%',
          '--left': ratio > 0 ? '14%' : 'unset',
          '--right': ratio > 0 ? 'unset' : '86%',
          '--bg': val > 0 ? 'linear-gradient(90deg, #57bf57, transparent)' : 'linear-gradient(90deg, transparent, red)'
        }
      }
      if (
        [
          '七日年化收益率（%）',
          '成立以来年化收益率（%）',
          '年化标准差（%）',
          '最大回撤（%）',
          '夏普比率',
          '索提诺比率'
        ].includes(prop)
      ) {
        return Number(val) < 0
          ? {
            color: 'red'
          }
          : ''
      }
      return {}
    },
    closeModal() {
      this.configModal.visible = false
    },
    // 查询
    handleQuery() {},
    // 重置
    handleReset() {}
  }
}
</script>

<style lang="scss">
.poc-portfolio-plan {
  .linear-cell {
    .cell::before {
      content: '';
      height: 14px;
      margin-top: 3px;
      width: var(--wdh);
      background: var(--bg);
      position: absolute;
      left: var(--left);
      right: var(--right);
    }
  }
}
</style>
