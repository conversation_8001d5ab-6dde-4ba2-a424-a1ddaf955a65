<!--
  @文件: index.vue
  @描述: 集团新券行情页面组件
  @功能: 展示债券行情信息，包含债券列表、筛选和图表展示
  @模块: 左侧-债券卡片列表，右侧-筛选条件和图表展示
-->
<template>
  <div class="company-value-container">
    <!-- 页面头部标题栏 -->
    <div class="public-title-container">
      <div class="title">发行定价估值</div>
      <div class="subtitle">
        <el-tooltip placement="right" effect="dark">
          <jr-svg-icon color="rgba(0,0,0,0.6)" icon-class="info-circle" />
          <div slot="content" style="max-width: 300px">
            历史定价曲线由系统每隔一定周期采集的定价估值构成，展示定价估值变动趋势
          </div>
        </el-tooltip>
      </div>
    </div>
    <!-- 水平布局组件，左侧为债券列表，右侧为筛选和图表 -->
    <jr-layout-horizontal :width="352" style="background-color: #fff" :disabled="true">
      <template slot="left">
        <!-- 左侧区域：搜索框和债券卡片列表 -->
        <div class="left-container">
          <!-- 债券卡片列表区域 -->
          <div class="bond-card-list">
            <div
              v-for="(bond, index) in bondList"
              :key="index"
              class="bond-card"
              :class="{ active: searchForm.ids.includes(bond.id) }"
              @click.stop="cardSelectBond(bond)"
            >
              <p class="bond-name">
                <!-- <el-tooltip class="item" effect="dark" :content="bond.s_info_name" placement="top-start"> -->
                {{ bond.term }}
                <!-- </el-tooltip> -->
              </p>
              <div class="bond-info-list">
                <div class="bond-info-item">
                  <span class="label">发行方式</span>
                  <span class="value">{{ bond.b_info_issuetypename || bond.bInfoIssuetypename }}</span>
                </div>
                <div class="bond-info-item">
                  <span class="label">是否可续期</span>
                  <span class="value">{{ bond.isRenewal === 'Y' ? '可续期' : '不可续期' }}</span>
                </div>
                <div class="bond-info-item">
                  <span class="label">定价估值 (%)</span>
                  <span class="value">{{ ConvertAmount('rate', bond.valuation, 1, 3) }}</span>
                </div>
                <div class="bond-info-item">
                  <span class="label">估值日期</span>
                  <span class="value">{{ bond.valuationDt }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </template>
      <template slot="right">
        <!-- 右侧区域：筛选部分 -->
        <div class="filter-container">
          <el-form ref="form" label-position="left" :model="searchForm" label-width="90px">
            <jr-form-item label="日期区间" class="filter-container-form-item">
              <el-date-picker
                v-model="customDateRange"
                type="daterange"
                range-separator="至"
                unlink-panels
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="yyyy-MM-dd"
                class="date-picker"
                @change="changeDatePicker"
              />
            </jr-form-item>
            <jr-form-item label="期限">
              <!-- 这里需要拉字典项 -->
              <jr-checkbox-group
                v-model="searchForm.ids"
                cancelable
                option-label="cnname"
                option-value="code"
                :data="radioList"
                :max="5"
                class="width-auto"
              />
            </jr-form-item>
          </el-form>

          <!-- 操作按钮区域 -->
          <div class="filter-actions">
            <el-button class="reset-btn" @click="resetSearcRight">重置</el-button>
            <el-button type="primary" class="query-btn" @click="searchRight">查询</el-button>
          </div>
        </div>

        <!-- 右侧图表区域 -->
        <div class="chart-container">
          <div class="chart-header">
            <div class="chart-actions">
              <el-dropdown trigger="click" @command="handleCommand">
                <el-button style="color: rgba(0, 0, 0, 0.9); width: 92px; height: 28px">
                  <jr-svg-icon class="el-icon--left" icon-class="upload" />
                  导出
                  <i class="el-icon-caret-bottom el-icon--right" />
                </el-button>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item command="bg">导出为Excel</el-dropdown-item>
                  <el-dropdown-item command="tp">导出为图片</el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </div>
          </div>
          <!-- <template-module
            v-if="searchForm.ids.length > 0"
            chart-seq="c0a6d825e33d4a41a865f2c1b31ed77b"
            :params="rightParams"
            :height="450"
            :callback="chartCallback"
            :custom-options="customOptions"
          /> -->
          <publicChart :chartdata="chartOptions" ref="publicChart" />
        </div>
      </template>
    </jr-layout-horizontal>
  </div>
</template>

<script>
import moment from 'moment'
import { GetListData } from '@jupiterweb/api/common/active-module-svr'
import { exportBondMyEstimateInfo } from '@/api/bonds/bonds'
import { get } from 'lodash'
import templateModule from '@/components/template-module'
import { ConvertAmount } from '@jupiterweb/utils/common.js'
import publicChart from '../../../bond-market-overview/spread-analysis/components/public-charts'
import * as op from '../js/chartParams.js'
import { debounce } from 'lodash'
/**
 * @组件: 集团新券行情
 * @描述: 展示债券行情信息，提供筛选和查询功能
 * @依赖组件: RatingChart - 用于绘制债券收益率图表
 */
export default {
  components: {
    templateModule,
    publicChart
  },
  data() {
    return {
      chartOptions: op.options,
      // 债券列表样例数据
      bondList: [],
      radioList: [],
      customDateRange: [],
      searchForm: {
        ids: [],
        valDtStart: '',
        valDtEnd: ''
      },
      cardParams: {
        ccid: 'b0e2ec33a8c44b209f2af395147719b3',
        ownedModuleid: '708631605142536192',
        b_info_issuercode: ''
      },
      rightParams: {},
      tooltip: {
        // trigger: 'axis',
        backgroundColor: 'rgba(0,0,0,0.6)',
        borderWidth: '0',
        padding: [12, 16],
        textStyle: {
          color: '#fff',
          fontSize: 14
        },
        formatter: (item) => {
          // ${item.color}
          let content = ''
          content = `${item.name}<br/>
              <span style="margin-right:8px;width:8px;height:8px;border-radius:50%;display:inline-block;background:#E1B01E"></span>
              ${''},${item.value}`
          return content
        }
      },
      legend: {
        show: false
      },
      series: [
        {
          smooth: true,
          lineStyle: {
            color: '#E1B01E',
            type: 'solid',
            width: 2
          },
          itemStyle: {
            color: '#E1B01E'
          }
        }
      ],
      myChart: null, // 图表实例
      pngName: '发行定价估值',
      customOptions: {
        grid: {
          top: 16,
          left: 0,
          bottom: 72,
          right: 0
        },
        legend: {
          orient: 'horizontal',
          top: 400,
          left: 'center',
          type: 'scroll', // 设置为可滚动
          pageIconColor: '#000', // 分页箭头颜色
          pageIconInactiveColor: '#333', // 禁用时分页箭头颜色
          pageTextStyle: {
            color: '#000' // 页码文字颜色
          },
          textStyle: {
            color: '#000' // 设置文字颜色为深灰色
          },
          pageButtonItemGap: 5, // 分页按钮与图例项的间隔
          pageButtonGap: 10, // 分页按钮与图例组件外框的间隔
          pageButtonPosition: 'end', // 分页按钮位置
          pageFormatter: '{current}/{total}', // 页码显示格式
          padding: 5 // 图例内边距
        }
      }
    }
  },
  computed: {},
  watch: {
    // 监听 searchForm 变化，触发重新获取图表数据
    cardParams: {
      handler(newVal, oldVal) {
        this.cartData()
      },
      deep: true
    }
  },
  created() {
    this.echartsData = debounce(this.echartsData, 500)

    this.initCustomDateRange()
    this.cartData()
    this.echartsData()
  },
  mounted() {},
  methods: {
    ConvertAmount,
    async echartsData() {
      console.log('1')
      const chartOptions = await op.getchartData(this.rightParams)
      this.chartOptions = { ...chartOptions, ...{ grid: [{ top: 48, bottom: 60, left: 40, right: 60 }] } }
    },
    // 获取左侧卡片数据
    async cartData() {
      const data = await GetListData({
        // CCID是自定义列id ownedModuleid是菜单id
        page: {
          pageNo: 1,
          pageSize: 9999
        },
        params: this.cardParams
      })
      const list = get(data, 'pageInfo.list', [])
      this.bondList = list || []
      // 默认选中第一个card
      if (this.bondList.length > 0) {
        this.cardSelectBond(this.bondList[0])
        this.radioList = this.bondList.map((item) => {
          return {
            cname: item.id,
            value: item.processTerm,
            code: item.id
          }
        })
        this.searchRight()
      }
    },
    /**
     * 选择债券的方法
     * @param {Object} bond - 债券对象
     */
    selectBond(bond) {
      if (this.searchForm.ids.includes(bond.id)) {
        this.searchForm.ids = this.searchForm.ids.filter((item) => item !== bond.id)
      } else {
        if (this.searchForm.ids.length < 5) {
          this.searchForm.ids.push(bond.id)
        }
      }
    },
    cardSelectBond(bond) {
      this.searchForm.ids = [bond.id]
      this.searchRight()
    },
    // 选择日期控件
    changeDatePicker() {
      this.searchForm.valDtStart = this.customDateRange[0]
      this.searchForm.valDtEnd = this.customDateRange[1]
    },
    // 初始化日期
    initCustomDateRange() {
      const now = moment()
      const date51WeeksAgo = now.subtract(51, 'weeks')
      const valDtStart = date51WeeksAgo.format('YYYY-MM-DD')
      const valDtEnd = moment(+new Date()).format('YYYY-MM-DD')
      this.customDateRange = [valDtStart, valDtEnd]
      this.searchForm.valDtStart = valDtStart
      this.searchForm.valDtEnd = valDtEnd
    },
    // 右侧搜索方法
    searchRight() {
      const { valDtStart, valDtEnd, ids } = this.searchForm
      const params = {
        ids: ids,
        valDtStart: moment(new Date(valDtStart)).format('YYYYMMDD'),
        valDtEnd: moment(new Date(valDtEnd)).format('YYYYMMDD')
      }
      params.ids = params.ids.map((id) => {
        const obj = this.bondList.find((item) => item.id === id)
        const str = `${obj.term}-${obj.bInfoIssuetype}-${obj.isRenewal}`
        return str
      })
      this.rightParams = { ...params }
      this.echartsData()
    },
    // 重置右侧
    resetSearcRight() {
      // 重置左侧？
      this.initCustomDateRange()
      this.searchForm.ids = []
      this.cardSelectBond(this.bondList[0])
      this.searchRight()
    },
    // 导出下拉菜单点击事件
    handleCommand(type) {
      if (type === 'tp') {
        // 下载图片
        const exportOptions = {
          backgroundColor: '#ffffff',
          pixelRatio: 2 // 提高导出清晰度（可选）
        }
        // // 生成图片 URL 并触发下载
        const chart = this.$refs.publicChart.getMyChart()
        console.log(chart, 'chart')
        const imgUrl = chart.getDataURL(exportOptions)
        const link = document.createElement('a')
        link.href = imgUrl
        link.download = `${this.pngName}.png`
        link.click()
      } else {
        const params = {
          ...this.rightParams,
          chartSeq: 'c0a6d825e33d4a41a865f2c1b31ed77b'
        }
        exportBondMyEstimateInfo(params).then((res) => {
          console.log(res, 'res')
        })
      }
    },
    // templateModule 组件回调函数，获取图表实例
    chartCallback(refData) {
      this.myChart = refData
    }
  }
}
</script>

<style lang="scss" scoped>
/**
   * 样式结构:
   * 1. 页面容器 (.company-value-container)
   * 2. 页面头部 (.page-header)
   * 3. 左侧区域样式 (.left-container)
   *    - 搜索区域 (.search-container)
   *    - 债券卡片列表 (.bond-card-list)
   * 4. 右侧区域样式
   *    - 筛选区域 (.filter-container)
   *    - 图表区域 (.chart-container)
   */
::v-deep .jr-checkbox-group .el-checkbox {
  display: inline-flex;
  align-items: center;
  vertical-align: middle;
}
::v-deep .el-checkbox__label {
  font-size: var(--el-font-size-base);
}
::v-deep .el-checkbox-group {
  .is-disabled {
    .el-checkbox__label {
      font-size: var(--el-font-size-base) !important;
    }
  }
}

.company-value-container {
  height: calc(100% - 56px);
  overflow: hidden;
  background-color: #ffffff;
  padding: 0px 16px;
  // margin-top: 1px;

  /* 页面头部样式 */
  .page-header {
    display: flex;
    align-items: center;
    padding: 24px 0px;
    // border-bottom: 1px solid #ebeef5;

    .title {
      font-size: var(--el-font-size-base);
      font-weight: bold;
      color: rgba(0, 0, 0, 0.85);
      margin-right: 10px;
    }

    .subtitle {
      font-size: var(--el-font-size-base);
      color: rgba(0, 0, 0, 0.6);
    }
  }

  /* 左侧容器样式 */
  .left-container {
    display: flex;
    flex-direction: column;
    height: 100%;
    width: 100%;
    position: relative;
  }

  /* 搜索区域样式 */
  .search-container {
    padding: 15px;
    padding-left: 0;
    padding-top: 0;
    position: sticky; /* 搜索框固定在顶部 */
    top: 0;
    background-color: #fff;
    z-index: 10;
  }

  /* 债券卡片列表样式 */
  .bond-card-list {
    height: 100%;
    padding: 0 15px 15px 0;
    flex: 1;
    overflow-y: auto;
    scrollbar-width: none; /* Firefox隐藏滚动条 */
    -ms-overflow-style: none; /* IE和Edge隐藏滚动条 */

    &::-webkit-scrollbar {
      display: none; /* Chrome, Safari, Opera隐藏滚动条 */
    }

    /* 债券卡片样式 */
    .bond-card {
      // background: #f9f9f9;
      background: linear-gradient(315deg, #fef6eb 0%, #fffbf3 100%);
      border-radius: 8px;
      padding: 24px;
      margin-bottom: 16px;
      cursor: pointer;
      transition: all 0.3s;
      height: 192px;
      &:last-child {
        margin-bottom: 40px; /* 移除最后一个卡片的底部间距 */
      }

      .bond-name {
        font-weight: bold;
        color: #000;
        margin-bottom: 16px;
        // 单行文本溢出省略
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-size: var(--el-font-size-base);
        height: 20px;
        line-height: 20px;
      }

      .bond-rating {
        font-size: var(--el-font-size-base);
        color: rgba(0, 0, 0, 0.6);
        margin-bottom: 10px;
      }

      /* 债券信息列表样式 */
      .bond-info-list {
        .bond-info-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 8px;
          height: 21px;
          line-height: 21px;

          .label {
            color: rgba(0, 0, 0, 0.6);
            font-size: var(--el-font-size-base);
          }

          .value {
            font-weight: bold;
            color: rgba(0, 0, 0, 0.85);
            font-size: var(--el-font-size-base);

            /* 涨跌颜色样式 */
            &.up {
              color: #f56c6c; /* 上涨-红色 */
            }

            &.down {
              color: #67c23a; /* 下跌-绿色 */
            }
          }
        }
      }

      &:hover {
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
      }
    }

    /* 选中状态的债券卡片样式 */
    .bond-card.active {
      background: linear-gradient(315deg, #dddbff 0%, #bdbaff 40%, #8d89ff 100%) !important;
      box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.03) !important;
      border: none !important;

      .bond-name,
      .bond-info-item .value {
        color: #ffffff !important;
      }

      .bond-rating,
      .bond-info-item .label {
        color: rgba(255, 255, 255, 0.9) !important;
      }

      /* 选中状态下的涨跌颜色样式 */
      .bond-info-item .value.up {
        color: #ff9ff3 !important; /* 上涨-粉色 */
      }

      .bond-info-item .value.down {
        color: #81ecec !important; /* 下跌-蓝绿色 */
      }
      .down {
        color: #fff; /* 下跌-蓝绿色 */
      }
    }
  }

  /* 右侧筛选容器样式 */
  .filter-container {
    padding: 8px 16px;
    border-bottom: 1px solid #ebeef5;
    background-color: #f6f8fd;

    /* 筛选区域各部分样式 */
    .filter-section {
      margin-bottom: 20px;
      display: flex;
      align-items: center;
      .section-title {
        margin-bottom: 12px;
        color: #606266;
        font-size: var(--el-font-size-base);
        font-weight: normal;
      }

      /* 日期筛选区域特殊样式 */
      &.date-section {
        .date-filter {
          display: flex;
          justify-content: space-between;
          align-items: center;

          /* 单选按钮组样式 */
          .radio-group-wrapper {
            margin-bottom: 0;

            .el-radio {
              margin-right: 20px;
              line-height: 36px;

              &:last-child {
                margin-right: 0;
              }

              .el-radio__input {
                vertical-align: middle;
              }

              .el-radio__label {
                padding-left: 6px;
              }

              /* Element UI单选框自定义主题色 */
              .el-radio__input.is-checked .el-radio__inner {
                background-color: #f18f01;
                border-color: #f18f01;
              }

              .el-radio__input.is-checked + .el-radio__label {
                color: #606266;
              }
            }
          }

          /* 日期选择器样式 */
          .date-picker {
            width: 260px;

            .el-range-separator {
              padding: 0;
            }
          }
        }
      }

      /* 通用单选按钮组样式 */
      .radio-group-wrapper {
        .el-radio {
          margin-right: 30px;
          margin-bottom: 10px;

          /* 自定义单选框选中颜色 */
          .el-radio__input.is-checked .el-radio__inner {
            background-color: #f18f01;
            border-color: #f18f01;
          }

          .el-radio__input.is-checked + .el-radio__label {
            color: #606266;
          }
        }
      }

      /* 债券选择器样式 */
      .bond-select {
        width: 100%;
      }
    }

    &-form-item {
      .el-form-item__content {
        display: flex;
        align-items: center;

        .date-picker {
          flex: 1;
          max-width: 260px;
        }
      }

      // margin-bottom: 10px;
      .jr-radio-group .el-radio {
        min-width: 30px;
        margin-right: 16px;
      }
    }
    .el-form-item {
      padding-bottom: 8px;

      ::v-deep .el-form-item__label {
        padding-top: 10px;
      }

      ::v-deep .el-form-item__content {
        height: 32px;
        line-height: 32px;
      }
    }

    /* 筛选操作按钮样式 */
    .filter-actions {
      display: flex;
      justify-content: flex-end;

      /* 重置按钮样式 */
      .reset-btn {
        // margin-right: 10px;
        border-radius: 2px;
        padding: 10px 20px;
      }

      /* 查询按钮样式 */
      .query-btn {
        background-color: #f18f01; /* 主题色-橙色 */
        border-color: #f18f01;
        border-radius: 2px;
        padding: 10px 20px;

        &:hover {
          background-color: #e08601;
          border-color: #e08601;
        }
      }
    }
  }

  /* 右侧图表容器样式 */
  .chart-container {
    padding: 20px;

    /* 图表头部样式 */
    .chart-header {
      display: flex;
      justify-content: flex-end;
      align-items: center;
      margin-bottom: 15px;

      /* 图表操作按钮样式 */
      .chart-actions {
        .el-button {
          padding: 7px 15px;
          font-size: var(--el-font-size-extra-base);
        }
      }
    }
  }
}
</style>
