<template>
  <jr-combobox
    v-model="selectedValues"
    clearable
    multiple
    collapse-tags
    :value-key="valueKey"
    :placeholder="placeholder"
    popper-class="custom-split-select-popper"
    :popper-append-to-body="false"
  >
    <el-option
      v-for="item in options"
      :key="item[valueKey]"
      style="display: none;"
      :value="item[valueKey]"
      :label="item[labelKey]"
    />
    <div class="split-panel">
      <el-row>
        <el-col :span="12" class="unselected-panel">
          <div class="split-panel--subtitle">
            <span>待选择({{ unselectedOptions.length || 0 }})</span>
          </div>
          <div class="split-panel--body">
            <div
              v-for="item in unselectedOptions"
              :key="item[valueKey]"
              class="option-item"
            >
              <div class="option-item--label">{{ item[labelKey] }}</div>
              <div class="option-item--icon">
                <jr-svg-icon class="icon-svg" icon-class="add-item" @click="addItem(item)" />
              </div>
            </div>
          </div>
        </el-col>
        <el-col :span="12" class="selected-panel">
          <div class="split-panel--subtitle">
            <span>已选择({{ selectedValues.length || 0 }})</span>
          </div>
          <div class="split-panel--body">
            <div v-if="selectedValues.length === 0" class="empty-tip">
              暂无选项
            </div>
            <div
              v-for="val in selectedValues"
              v-else
              :key="val"
              class="option-item"
            >
              <div class="option-item--label"><span class="label-dot">•</span>{{ getDisplayValue(val) }}</div>
              <div class="option-item--icon">
                <jr-svg-icon class="icon-svg" icon-class="delete-item" @click="removeItem(val)" />
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
  </jr-combobox>
</template>

<script>
export default {
  name: 'SplitSelect',
  props: {
    /**
     * 组件值
     */
    value: {
      type: Array,
      default: () => []
    },
    /**
     * 数据源
     */
    options: {
      type: Array,
      default: () => []
    },
    valueKey: {
      type: String,
      default: 'value'
    },
    labelKey: {
      type: String,
      default: 'label'
    },
    /**
     * 选择框占位文本
     */
    placeholder: {
      type: String,
      default: '请选择内容'
    },
    /**
     * 是否支持清空选项
     */
    clearable: {
      type: Boolean,
      default: true
    },
    /**
     * 宽度
     */
    width: {
      type: String,
      default: '250px'
    }
  },
  data() {
    return {
      selectedValues: []
    }
  },
  computed: {
    unselectedOptions() {
      const selectedOpts = this.options.filter(opt => this.selectedValues.includes(opt[this.valueKey]))
      const unselectedOpts = this.options.filter(opt => !this.selectedValues.includes(opt[this.valueKey]))
      this.$emit('emitOptsData', { selectedOpts, unselectedOpts })
      return unselectedOpts
    },
    selectedOptions() {
      return this.options.filter(opt => this.selectedValues.includes(opt[this.valueKey]))
    }
  },
  watch: {
    value: {
      handler(val) {
        this.selectedValues = val || []
      },
      immediate: true
    }
  },
  methods: {
    getDisplayValue(val) {
      const foundOpt = this.options.find(opt => opt[this.valueKey] === val) || {}
      return foundOpt[this.labelKey] || val
    },
    addItem(item) {
      this.selectedValues.push(item[this.valueKey])
      this.$emit('addItem', item)
    },
    removeItem(val) {
      this.selectedValues = this.selectedValues.filter(v => v !== val)
      this.$emit('removeItem', val)
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~@jupiterweb/assets/styles/variables.scss';
.custom-split-select-popper {
  .split-panel {
    width: 400px;
    overflow: hidden;
    color: rgba(0,0,0,0.9);
    font-size: var(--el-font-size-base);

    &--subtitle {
      height: 32px;
      line-height: 32px;
      background: #F4F4F4;
      box-shadow: inset 0px -1px 0px 0px #EAE9E9;
      & span {
        margin: 6px 8px;
      }
    }

    .unselected-panel {
      border-right: 0.5px solid #EAE9E9;
    }

    .selected-panel {
      border-left: 0.5px solid #EAE9E9;
    }

    &--body {
      padding: 6px 0px 6px 8px;
      max-height: 300px;
      overflow-y: auto;
      overflow-x: hidden;

      .option-item {
        height: 32px;
        margin-top: 2px;
        display: flex;
        align-items: center;
        justify-content: space-between;

        &:hover {
          background: rgba(255,142,43,0.1);
          border-radius: 2px;
          color: var(--theme--color);
          .icon-svg {
            color: var(--theme--color);
          }
        }

        &--label {
          padding: 0 8px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          min-width: 0;
        }

        .label-dot {
          color: var(--theme--color);
          padding-right: 8px;
        }

        &--icon {
          padding-right: 8px;
          .icon-svg {
            color: rgba(0,0,0,0.9);
          }
        }
      }
    }
  }

  .el-col-12 {
    width: 48.8%;
  }

  .empty-tip {
    color: #909399;
    text-align: center;
    padding: 20px;
  }

  .el-select-dropdown__item {
    &.hover, &.hover:not(.is-disabled) {
      background: transparent !important;
    }
    &.selected {
      font-weight: normal !important;
    }
  }
}
</style>
<style lang="scss">
.custom-split-select-popper {
  .el-scrollbar__bar.is-vertical {
    display: none !important;
  }
  .el-select-dropdown__list {
    padding: 0 0 4px 0 !important;
  }
  .el-select-dropdown__item {
    height: auto !important;
    padding: 0 !important;
  }
  .el-scrollbar__wrap {
    overflow: hidden !important;
  }
  .el-scrollbar {
    display: block !important;
  }
}
</style>
