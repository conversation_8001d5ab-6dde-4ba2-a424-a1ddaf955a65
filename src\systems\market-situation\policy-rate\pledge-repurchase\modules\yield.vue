<template>
  <div class="page-market-rate--yield">
    <div class="page-market-rate--yield-title">
      <span class="title-text">质押回购</span>
    </div>
    <div class="page-market-rate--yield-content">
      <div v-for="item in list" :key="item.yield" class="list-item" :class="activeName === item.yield && 'active'" @click="activeName = item.yield">
        <h3>{{ item.yield }}</h3>
        <div>
          <label>最新值（%）</label>
          <span class="val">{{ item.val.toFixed(4) }}</span>
        </div>
        <div>
          <label>涨跌BP</label>
          <span
            class="bp"
            :class="[item.bp > 0 && 'bp-red', item.bp < 0 && 'bp-green', , item.bp === 0 && 'bp-normal']"
          >
            {{ `${item.bp > 0 ? '+' : ''}${item.bp.toFixed(2)}` }}
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>

export default {
  data() {
    return {
      activeName: 'DR007',
      list: [
        {
          yield: 'DR007',
          val: 1.8282,
          bp: 1.39
        },
        {
          yield: 'DR014',
          val: 1.9098,
          bp: -1.55
        },
        {
          yield: 'R007',
          val: 1.8293,
          bp: -2.87
        },
        {
          yield: 'R014',
          val: 1.9211,
          bp: -2.26
        }
      ]
    }
  },
  methods: {}
}
</script>

<style lang="scss" scoped>
.page-market-rate--yield {
  width: 100%;
  padding: 12px;
  &-title {
    display: flex;
    justify-content: space-between;
    font-size: 14px;
    font-weight: bold;
  }
  &-content {
    margin-top: 12px;
    display: flex;
    column-gap: 30px;
    .list-item {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      height: 100px;
      width: 18%;
      border: 1px solid #e8e8e8;
      padding: 12px 16px;
      cursor: pointer;
      transition: background-color 0.2s ease-in-out;
      h3 {
        font-size: 16px;
        font-weight: bold;
      }
      div {
        display: flex;
        justify-content: space-between;
        font-size: 12px;
        label {
          color: #999;
        }
        .bp,.val {
          font-weight: bold;
        }
        .bp-red {
          color: #f56c6c;
        }
        .bp-green {
          color: #67c23a;
        }
      }
      &.active {
        background-color: var(--theme--color);
        h3 {
          color: var(--el-theme-color-warning);
        }
        .val,
        div label {
          color: #fff;
        }
      }
    }
  }
}
</style>
