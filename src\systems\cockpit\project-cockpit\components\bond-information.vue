<template>
  <div class="bond-information">
    <cockpit-header 
      :style="{ width:px2vw(362),height:px2vh(40) }" 
      title="自选债券信息"
    />
    <div class="bond-information-header">
      <div class="bond-information-header-left">
        <p>自选债券信息列表</p>
        <el-tooltip effect="dark" placement="bottom-start" :teleported="false">
          <div class="toolTip">
            <img src="@/assets/cockpit/info_pro_circle.png" alt="" :style="{width:px2vh(13),height:px2vh(13),marginLeft:px2vw(4)}">
          </div>
          <div slot="content">
            <p>自选列表范围:添加的债券到期后，列表将会自动移除该债券</p>
            <p style="margin-bottom: 0;">对标债券设置:仅限于添加“我的对标”下企业存续债券信息”</p>
          </div>
        </el-tooltip>
      </div>
      <div class="bond-information-header-right">
        <div class="bond-information-header-right-checkbox">
          <el-checkbox v-model="tableParams.isbenchmarking" true-label="1" false-label="0">对标债券</el-checkbox>
        </div>
        <div class="bond-information-header-right-self" @click.stop="openSelf">
          <img src="@/assets/cockpit/cockpit_self.png" alt="" :style="{width:px2vw(16),height:px2vh(17)}">
          <span>自选</span>
        </div>
        <img 
          src="@/assets/cockpit/cockpit_set.png" 
          alt="" 
          :style="{width:px2vw(32),height:px2vh(33),cursor: 'pointer',marginLeft:px2vw(16)}"
          @click.stop="openSetting"
        >
        <img 
          src="@/assets/cockpit/cockpit_export.png" 
          alt="" 
          :style="{width:px2vw(32),height:px2vh(33),cursor: 'pointer'}"
          @click.stop="exportData"
        >
      </div>
    </div>
    <jr-decorated-table
        ref="table"
        stripe
        :style="{width: px2vw(828) , height: px2vh(374)}"
        :params="{
          ...tableParams
        }"
        :custom-id="ccid"
        :visible="true"
        @refreshed="callFn"
        v-bind="{
          ...$props
        }"
        :menuinfo="{ moduleid: '1369319537827659776' }"
      />
      <SelfDefinedList
        v-if="visible"
        :visible="visible"
        :permitdetail="{}"
        :menuinfo="{}"
        module="COCKPIT"
        @close="closemodal"
      />
  </div>
</template>

<script>
import cockpitHeader from '../../components/cockpit-header.vue';
import SelfDefinedList from '@/systems/bond-market-overview/spread-analysis/components/self-defined-tables.vue'
import { px2vw,px2vh } from '../../utils/portcss';
import { exportExcelByCustomColumn } from '@/api/public/public'
export default {
  name: 'BondInformation',
  components:{
    cockpitHeader,
    SelfDefinedList
  },
  data() {
    return {
      checked:false,
      visible:false,
      tableParams:{
        b_info_issuercode:this.$store.getters.personInfo.outCompCode,
        isbenchmarking:'0',
        ownedModuleid:'1369319537827659776'
      },
      ccid:'2e5091d274724253bbacf26367f79394',
      columns:[],
      sort:'',
      direction:''
    }
  },
  methods:{
    px2vw,
    px2vh,
    /**
     * 打开设置功能
     */
    openSetting(){
      this.$refs.table.$children[1].$children[1].customColumnPopShow = true
    },
    /**
     * 打开自选弹窗
     */
    openSelf(){
      this.visible = true
    },
    /**
     * 关闭自选弹窗
     */
    closemodal(){
      this.visible = false
      this.tableParams = this.handleSearchParams()
    },
    // 处理表格查询参数
    handleSearchParams() {
      const obj = {
        webTime: new Date().getTime()
      }

      return { ...this.tableParams, ...obj }
    },
    /**
     * 导出全量数据
     */
    async exportData() {
      const params = {
        params: {
          filename: '自选债券信息',
          column: this.columns,
          selectData: null,
          ccid: this.ccid,
          ownedModuleid: '1352315117735067648',
          ...this.tableParams
        },
        page: {
          sort: this.sort,
          direction: this.direction
        }
      }
      await exportExcelByCustomColumn(params)
    },
    // 表格组件渲染回调
    callFn(data) {
      this.columns = data.config.columns
      this.sort = data.sort
      this.direction = data.direction
    },
  },
}
</script>

<style lang="scss" scoped>
@import '~assets/styles/portcss.scss';
::v-deep .jr-decorated-table--top-search .jr-decorated-table--header{
  visibility: hidden;
}
::v-deep .jr-decorated-table--top-search .jr-decorated-table--header-left{
  background-color: transparent !important;
}
::v-deep .jr-decorated-table--top-search .jr-decorated-table--header-right{
  background-color: transparent !important;
}
::v-deep .jr-decorated-table--body .jr-table .el-table th.el-table__cell, 
.jr-decorated-table--body .jr-table .el-table .el-table__header-wrapper th .caret-wrapper, 
.jr-decorated-table--body .jr-table .el-table .el-table__header-wrapper th{
  background-color: transparent !important;
}
::v-deep .el-table tr{
  background-color: transparent !important;
}
::v-deep .jr-decorated-table > div{
  background-color: transparent !important;
}
::v-deep .el-table, .el-table__expanded-cell{
  background-color: transparent !important;
}
::v-deep .jr-decorated-table--body .jr-table .el-table--border th.el-table__cell.is-leaf, .jr-decorated-table--body .jr-table .el-table--border th.el-table__cell{
  border-bottom: 1px solid rgba(213,226,232,0.15) !important;
}
::v-deep .jr-table .el-table--border th{
  border-right: unset !important;
}
::v-deep .jr-decorated-table--body .jr-table .el-table .el-table__header-wrapper th .caret-wrapper{
  background-color: transparent !important;
}
::v-deep .jr-table .el-table table th.el-table__cell > .cell, .jr-table .el-table table th > .cell{
  color: #FFFFFF !important;
}
::v-deep .jr-table .el-table .sort-caret.ascending{
  border-bottom-color: #FFFFFF !important;
}
::v-deep .jr-table .el-table .sort-caret.descending{
  border-top-color: #FFFFFF !important;
}
::v-deep .el-checkbox__label{
  font-size: vh(14) !important;
  color: rgba(255,255,255,0.9) !important;
}
::v-deep .el-checkbox__inner{
  width: vw(16) !important;
  height: vh(16) !important;
  background-color: rgba(30,53,85,0.4) !important;
  border: 1px solid #315280;
}
::v-deep .jr-table .el-table--striped .el-table__body tr.el-table__row--striped:not(.sortable-ghost) td{
  background: transparent !important;
}
::v-deep .jr-decorated-table--body .jr-table .el-table--border td, .jr-decorated-table--body .jr-table .el-table--border td.el-table__cell{
  border-bottom-color: rgba(213,226,232,0.15) !important;
}
::v-deep  .jr-decorated-table--body .jr-table .el-table{
  color: #FFFFFF !important;
}
::v-deep  .jr-decorated-table--body .jr-table .el-table--enable-row-hover .el-table__body tr:hover{
  background-color: transparent !important;
}
::v-deep  .jr-pagination .pagination-slot{
  color: rgba(255,255,255,0.55) !important;
}
::v-deep .jr-pagination .el-pagination.is-background .btn-prev:disabled{
  width: vw(28) !important;
  height: vh(28) !important;
  background: rgba(30,53,85,0.4);
  border-radius: 2px;
  border: 1px solid #315280;
}
::v-deep .jr-pagination .el-pagination.is-background .el-pager li:not(.disabled).active{
  width: vw(30) !important;
  height: vh(28) !important;
  background: #2668DB;
  border-radius: 2px;
  color: #FFFFFF !important;
  border-color: #2668DB !important;
}
::v-deep .jr-pagination .el-pagination.is-background .el-pager li{
  width: vw(30) !important;
  height: vh(28) !important;
  background: rgba(30,53,85,0.4);
  border-radius: 2px;
  border: 1px solid #315280;
  color: #FFFFFF;
}
::v-deep .jr-pagination .el-pagination.is-background .btn-next, .jr-pagination .el-pagination.is-background .btn-prev{
  width: vw(28) !important;
  height: vh(28) !important;
  background: rgba(30,53,85,0.4);
  border-radius: 2px;
  border: 1px solid #315280;
  color: #FFFFFF;
}
::v-deep .jr-pagination .el-pagination .el-select .el-input .el-input__inner{
  background-color: rgba(30,53,85,0.4) !important;
  color: #FFFFFF !important;
  border: 1px solid #315280 !important;
}
::v-deep .el-pagination__editor.el-input .el-input__inner{
  background-color: rgba(30,53,85,0.4) !important;
  color: #FFFFFF !important;
  border: 1px solid #315280 !important;
}
::v-deep .el-pagination__jump{
  color: rgba(255,255,255,0.55);
}
.bond-information {
  width: 100%;
  height: vh(446);
  background-image: url("../../../../assets/cockpit/cockpit_pro_self.png");
  background-size: 100% 100%;
  padding: vh(8) vw(8) vh(8) vw(8);
  position: relative;
  &-header {
    width: 100%;
    height: vh(32);
    position: absolute;
    top: vh(56);
    display: flex;
    justify-content: space-between;
    padding-left: vw(28);
    padding-right: vw(32);
    &-left {
      display: flex;
      gap: vw(4);
      & > p {
        height: vh(22);
        font-family: MicrosoftYaHeiSemibold;
        font-size: vh(20);
        color: #FFFFFF;
        line-height: vh(22);
      }
    }
    &-right {
      display: flex;
      align-items: center;
      flex-shrink: 0;
      &-self {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: vw(4);
        width: vw(80);
        height: vh(32);
        background: #2668DB;
        border-radius: vh(2);
        margin-left: vw(16);
        cursor: pointer;
        & > span {
          height: vh(22);
          font-family: MicrosoftYaHei;
          font-size: vh(14);
          color: rgba(255,255,255,0.9);
          line-height: vh(22);
        }
      }
    }
  }
}
</style>
