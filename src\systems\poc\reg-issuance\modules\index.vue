<template>
  <el-dialog class="poc-zzj-dialog" :visible.sync="dialogVisible" title="山东钢铁集团有限公司" width="80%">
    <el-container>
      <el-header>
        <div class="flex">
          <div class="flex_left">
            <span class="pr10"><i class="el-icon-collection-tag" />未收藏</span>
            <span class="pr10"><el-tag size="mini">战略客户</el-tag></span>
            <span class="pr10"><el-tag size="mini">投资管理不大</el-tag></span>
            <span class="pr10"><el-tag size="mini">活跃发行人</el-tag></span>
          </div>
          <div class="flex_right">
            <el-select v-model="value" placeholder="请选择">
              <el-option label="产业类" value="item1" />
              <el-option label="产业类" value="item2" />
            </el-select>
            <el-button plain size="mini">保存</el-button>
            <el-button plain size="mini">生成报告</el-button>
            <el-button plain size="mini">上传至底稿</el-button>
          </div>
        </div>

      </el-header>
      <el-main>
        <el-tabs v-model="activeName" @tab-click="handleClick">
          <el-tab-pane label="整体市场环境分析" name="first">
            <div class="modules_main">
              <div class="modules_main_title">最近央行重大货币政策</div>
              <div class="modules_main_body">
                <div style="border: 1px #eee solid;padding:6px;margin:10px 0;">
                  <p>2023-29市场动态</p>
                  <p>1、信用债一级市场:近5个工作日内，信用摄新发行1322亿元，支现净教资49亿元;其中，银行间市场债务融资工具发行165亿元，安现净融资618亿元，发行盘及净融资均较上周明显提升。</p>
                  <p>2、资金面变动情况:近5个工作日内，央行公开市场操作逆回购127302元，逆回购到期19350亿元。</p>
                  <p>3、市场热点:2月6日，沪深交易所正式启动债芬营市业务，推出慢劳监市业务，一方面有利于蜂在车品在温价和服务发行成本，完善交恩所慢苏市场功酶，进一步发保债苏市场测安体经济的支持作用;另一方面有利于提高定价效率，形成能更加性确反使市场出求关系的债券收益率曲线，为市场定价提供基准参考，</p>
                </div>
              </div>
            </div>
            <div class="modules_main">
              <div class="modules_main_title">市场发行情况分析</div>
              <div class="modules_main_body">
                <p>近六个月市场发行量为69,567.40亿元，其中协会债发行量为43,10106亿元，近六个月净融资额为47474亿元</p>
                <echarts :key="chartSeq" :options="chartOption" :styles="{height: height + 'px'}" />
              </div>
            </div>
          </el-tab-pane>
          <el-tab-pane label="对标债券发行情况" name="second">
            <div class="modules_main">
              <el-alert
                title="同类型企业在近六个月无同品种债务融资工具发行。"
                type="warning"
                show-icon
              />
              <div class="modules_main_title">对标债券发行情况</div>
              <div class="modules_main_body">
                <jr-table
                  :height="height"
                  :columns="configTable.columns"
                  :data-source="configTable.data"
                  :loading="configTable.loading"
                  :pagination="configTable.pagination"
                  border
                />
              </div>
            </div>
            <div class="modules_main">
              <div class="modules_main_title">其他债券发行情况</div>
              <div class="modules_main_body">
                <jr-table
                  :height="height"
                  :columns="configTable.columns"
                  :data-source="configTable.data"
                  :loading="configTable.loading"
                  :pagination="configTable.pagination"
                  border
                />
              </div>
            </div>
          </el-tab-pane>
          <el-tab-pane label="本公司估值情况" name="third">
            <div class="modules_main">
              <div class="modules_main_title">本公司估值情况</div>
              <div class="modules_main_body">
                <jr-table
                  :height="height"
                  :columns="configTable1.columns"
                  :data-source="configTable1.data"
                  :loading="configTable1.loading"
                  :pagination="configTable1.pagination"
                  border
                />
              </div>
            </div>
            <div class="modules_main">
              <div class="modules_main_title">本公司二级成交明细</div>
              <div class="modules_main_body">
                <jr-table
                  :height="height"
                  :columns="configTable2.columns"
                  :data-source="configTable2.data"
                  :loading="configTable2.loading"
                  :pagination="configTable2.pagination"
                  border
                />
              </div>
            </div>
          </el-tab-pane>
          <el-tab-pane label="DQ报价" name="fourth">
            <div class="modules_main_body">
              <el-alert
                title="中债一级市场定价工具DQ报价截图如下:"
                type="warning"
                show-icon
              />
              <el-upload
                class="upload-btn"
              >
                <el-button size="small" type="primary">点击上传</el-button>
              </el-upload>
              <jr-table
                :height="height"
                :columns="configTable3.columns"
                :data-source="configTable3.data"
                :loading="configTable3.loading"
                :pagination="configTable3.pagination"
                border
              />
            </div>
          </el-tab-pane>
          <el-tab-pane label="发行建议和发行时间表" name="five">
            <div class="modules_main_body">
              <el-alert
                title="根据我行对于市场行情的制断，同时基于对标企业酒券发行及要司情等传值分析，我行关于贵司的2023年度朝四十五期中网票据的发行建议如下:"
                type="warning"
                show-icon
              />

              <el-form ref="elForm" :model="configForm.model" style="grid-template-columns:repeat(3, 32%)">
                <jr-form-item-create :column="1" :data="configForm.data" :model="configForm.model" />
              </el-form>
            </div>
          </el-tab-pane>
          <el-tab-pane label="服务方案" name="six">服务方案</el-tab-pane>
        </el-tabs>
      </el-main>
    </el-container>
  </el-dialog>
</template>
<script>
import echarts from '@jupiterweb/components/echarts'
import { moduleConfig } from './config'
export default {
  components: {
    echarts
  },
  props: {},
  data() {
    return {
      dialogVisible: false,
      formData: null,
      activeName: 'first',
      chartSeq: '',
      chartOption: moduleConfig.chartOption,
      height: 300,
      configTable: moduleConfig.configTable,
      configTable1: moduleConfig.configTable1,
      configTable2: moduleConfig.configTable2,
      configTable3: moduleConfig.configTable3,
      configForm: moduleConfig.configForm
    }
  },
  methods: {
    initData(row) {
      this.dialogVisible = true
      this.formData = row
    },
    handleClick() {}
  }
}
</script>

<style lang="scss" scoped>
.pr10{
    padding-right: 10px;
}
 .flex{
    display: flex;
    justify-content: space-between;
    .flex_left,.flex_right{
        flex:1
    }
    .flex_right{
        text-align: right;
    }
 }
 .modules_main{
    // #00c1de
    .modules_main_title{
        height: 21px;
        line-height: 21px;
        position: relative;
        padding-left: 6px;
        font-weight: 600;
    }
    .modules_main_title::before{
        content: '';
        display:block;
        width:3px;
        height: 21px;
        background-color: #00c1de;
        position:absolute;
        left:0;
        top:0;
    }
 }
 .upload-btn{
    margin:10px 0;
 }
</style>
