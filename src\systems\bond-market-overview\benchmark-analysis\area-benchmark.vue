<template>
  <div class="area-benchmark">
    <benchmark-company-config @saveConfingCallbackMethod="reloadBondBenchmarkCompanySelectList" :params="params" />
    <bond-benchmark ref="bondBenchmark" :params="params" />
    <financial-benchmark ref="financialBenchmark" :params="params" />
  </div>
</template>

<script>
import BenchmarkCompanyConfig from './components/benchmark-company-config.vue'
import BondBenchmark from './components/bond-benchmark.vue'
import FinancialBenchmark from './components/financial-benchmark.vue'

export default {
  components: { BenchmarkCompanyConfig, BondBenchmark, FinancialBenchmark },
  props: {
    params: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
    }
  },
  methods: {
    /**
     * 重新加载债券对标中对标企业下拉框信息
     */
    reloadBondBenchmarkCompanySelectList() {
      this.$refs.bondBenchmark.queryBenchmarkCompanySelectList()
      this.$refs.financialBenchmark.queryBenchmarkCompanySelectList()
    }
  }
}
</script>

<style lang="scss" scoped>
.area-benchmark {
  background: #ffffff;
}
</style>
