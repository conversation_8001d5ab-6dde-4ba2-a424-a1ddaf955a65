<template>
  <div class="spread-analysis">
    <div class="public-tabs-container">
      <el-tabs v-model="tabActiveName" @tab-click="changeTabs">
        <el-tab-pane label="信用利差" name="CreditSpread" />
        <el-tab-pane label="发行利差" name="IssueSpread" />
        <el-tab-pane label="估值利差" name="ValuationSpread" />
      </el-tabs>
    </div>
    <component :is="tabActiveName" v-bind="{ ...$attrs, ...$props, params: { ...tableParams } }" />
  </div>
</template>

<script>
import CreditSpread from './components/credit-spread.vue'
import IssueSpread from './components/issue-spread.vue'
import ValuationSpread from './components/valuation-spread'
export default {
  name: 'SpreadAnalysis',
  components: { CreditSpread, IssueSpread, ValuationSpread },
  props: {
    // 权限相关
    permitdetail: {
      type: Object,
      default: () => ({})
    },
    menuinfo: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      tabActiveName: 'CreditSpread',
      tableParams: {
        ccid: '92fd33cdbb364789967a93eb5b649e4d'
      },
      btnArr: {}
    }
  },
  methods: {
    changeTabs() {}
  }
}
</script>

<style lang="scss" scoped>
@import '~@jupiterweb/assets/styles/variables.scss';
::v-deep .el-tabs__nav-wrap::after {
  display: none;
}

::v-deep .is-center {
  .cell {
    .el-checkbox {
      width: 100% !important;
      height: 100% !important;
      align-items: center;
      display: flex;
      justify-content: center;
    }
  }
}
.spread-analysis {
  height: 100%;
  &-tabs {
    width: 100%;
    height: 48px;
    padding: 8px 0px 0px 16px;
    box-sizing: border-box;
    background-color: #ffffff;

    ::v-deep .el-tabs__item {
      font-size: var(--el-font-size-base) !important;
    }
  }
}
</style>
