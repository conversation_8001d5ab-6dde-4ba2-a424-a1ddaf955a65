<template>
  <div class="groupBonds">
    <div class="groupBonds-topSelect public-table-search-container">
      <span style="font-size: var(--el-font-size-base)">
        追溯时间点
        <el-tooltip effect="dark" content="当前页面数据图表和列表均以追湖时间点计算债券余额、剩余期限和估值等信息" placement="right">
          <jr-svg-icon icon-class="info-circle" />
        </el-tooltip>   
      </span>
      <div class="groupBonds-topSelect-dateAndBtn">
        <el-date-picker v-model="selectParam.showDate" type="date" style="height: 32px" :clearable="false" />
        <span class="groupBonds-topSelect-dateAndBtn-today_btn" @click.stop="initDateSelect">今</span>
      </div>

      <div class="groupBonds-topSelect-canlendar">
        <el-popover placement="bottom-start" trigger="click">
          <div slot="reference" class="groupBonds-topSelect-canlendar-icon">
            <i class="el-icon-date" style="color: var(--theme--color)" />
          </div>
          <el-cascader-panel :options="cascaderOptions" @change="confirmAnalyseDate" />
        </el-popover>
      </div>
      <el-button type="primary" @click.stop="search">查询</el-button>
    </div>
    <div class="groupBonds-bondBalanceAnalysis">
      <div class="public-title-container">债券余额分析</div>
      <div class="groupBonds-bondBalanceAnalysis-balanceDetail">
        存续期人民币债券：
        <span>{{ bondBalanceAnalysisData.num }}</span>
        只，规模：
        <span>{{ bondBalanceAnalysisData.totalAmount | amountFormat }}</span>
        亿元，余额：
        <span>{{ bondBalanceAnalysisData.amount | amountFormat }}</span>
        亿元。其中永续债占余额：
        <span>{{ ConvertAmount('rate', bondBalanceAnalysisData.perpetualPercent / 100, 1, 2) }}</span>
        %(
        <span>{{ ConvertAmount('rate', bondBalanceAnalysisData.perpetualAmount / 100, 1, 2) }}</span>
        亿元)
      </div>
      <div class="groupBonds-bondBalanceAnalysis-chartArea">
        <div
          v-for="(chartData, cIndex) in chartAreaBaseData"
          :key="cIndex"
          class="groupBonds-bondBalanceAnalysis-chartArea-chartBox"
        >
          <div class="groupBonds-bondBalanceAnalysis-chartArea-chartBox-title">
            <span />
            <span>{{ chartData.title }}</span>
            <template v-if="chartData.description">
              <el-tooltip placement="right" effect="dark">
                <jr-svg-icon icon-class="info-circle" />
                <div slot="content" v-html="chartData.description" style="max-width: 300px" />
              </el-tooltip>
            </template>
          </div>
          <div class="groupBonds-bondBalanceAnalysis-chartArea-chartBox-content">
            <div class="groupBonds-bondBalanceAnalysis-chartArea-chartBox-content-chart">
              <div v-if="chartData.showEchart">
                <template-module
                  :chart-seq="chartData.chartSql"
                  chart-type="PIE"
                  :params="chartData.params"
                  :custom-options="chartData.chartOptions"
                />
              </div>
            </div>
            <div class="groupBonds-bondBalanceAnalysis-chartArea-chartBox-content-description">
              <div
                v-for="(tableData, tIndex) in chartData.echartTableArray"
                :key="tIndex"
                class="groupBonds-bondBalanceAnalysis-chartArea-chartBox-content-description-item"
                :style="
                  tIndex === 0 || tIndex === chartData.echartTableArray.length - 1
                    ? { backgroundColor: tableData.color }
                    : {}
                "
              >
                <span :style="{ backgroundColor: tableData.color }" />
                <span>{{ tableData.dateRange }}</span>
                <span style="min-width: 120px">{{ (tableData.amount || 0) | amountFormat }}</span>
                <span>{{ tableData.num || 0 }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="groupBonds-tableSelect">
      <div class="public-title-container">债券明细列表</div>
      <table-select
        ref="tableSelect"
        :bond-type-options="bondTypeOptions"
        @setTableSelectParams="setTableSelectParams"
      />
    </div>
    <div class="groupBonds-tableArea">
      <div v-if="hasAdd" class="groupBonds-tableArea-buttons">
        <el-button type="primary" @click.stop="openBondEntry">
          <jr-svg-icon icon-class="filled-file" />
          债券录入
        </el-button>
      </div>
      <jr-decorated-table
        ref="table"
        stripe
        style="width: 100%; height: 516px"
        :params="tableSelectParams"
        custom-id="2642e060df324cddbcc58b88f22b472c"
        :custom-render="tableColumns"
        :handleissuance_materials="handleissuanceMaterials"
        :handleexport="handleexport"
        :visible="true"
        v-bind="{
          ...$props
        }"
        :default-page-size="10"
        :initPagination="{
          pageSizeOptions: [10, 20, 50, 100]
        }"
        :menuinfo="menuinfo"
        @refreshed="callFn"
      />
      <div class="groupBonds-tableArea-infoDetail">
        <p>
          <span>平均发行期限</span>
          <span title="为列表中债券发行期限的平均值，含权债发行期限取行权期限">
            <jr-svg-icon icon-class="info-circle" />
            ：
          </span>
          <span>{{ averageData.avgIssueTerm || '' }}</span>
        </p>
        <p>
          <span>平均利率(%)</span>
          <span title="为列表中债券发行利率的平均值">
            <jr-svg-icon icon-class="info-circle" />
            ：
          </span>
          <span>{{ averageData.avgCouponrate || '' }}</span>
        </p>
        <p>
          <span>票面加权(%)</span>
          <span title="为列表中债券发行利率与债券规模的加权平均值">
            <jr-svg-icon icon-class="info-circle" />
            ：
          </span>
          <span>{{ averageData.weightedCouponrate }}</span>
        </p>
      </div>
      <p class="groupBonds-tableArea-tips">
        <span><jr-svg-icon icon-class="info-circle" /></span>
        <span style="margin-left: 8px">集合票据、集合企业债不纳入统计范围</span>
      </p>
    </div>
    <BondEntry ref="bondEntry" :bond-type-options="bondTypeOptions" />
    <issuanceMaterials ref="issuanceMaterials" />
    <detail ref="detail" />
  </div>
</template>

<script>
import TemplateModule from '@/components/template-module'
import TableSelect from './components/TableSelect.vue'
import { GetListData } from '@jupiterweb/api/common/active-module-svr'
import BondEntry from './dialogs/bondEntry.vue'
import issuanceMaterials from './dialogs/issuanceMaterials.vue'
import detail from '@/systems/bond-market-overview/publish-query/dialog/detail.vue'
import { queryBondTypeByIssuerCode, mineBondsAverageInfo } from '@/api/bonds/bonds'
import { issuanceQueryBondTypeList } from '@/api/issuance/issuance'
import { exportExcelByCustomColumn } from '@/api/public/public'
import { ConvertAmount } from '@jupiterweb/utils/common.js'
import moment from 'moment'

export default {
  name: 'GroupBonds',
  components: {
    TemplateModule,
    TableSelect,
    BondEntry,
    issuanceMaterials,
    detail
  },
  props: {
    // 权限相关
    permitdetail: {
      type: Object,
      default: () => ({})
    },
    menuinfo: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      selectParam: {
        showDate: '',
        analyseDate: ''
      },
      // 饼图区域数据渲染基本数据
      chartAreaBaseData: [
        {
          title: '以到期计',
          echartColorArray: [
            {
              color: '#5B8FF9',
              title: '1年以内'
            },
            {
              color: '#F6BD16',
              title: '1-3年'
            },
            {
              color: '#5AD8A6',
              title: '3-5年'
            },
            {
              color: '#E8684A',
              title: '5年以上'
            }
          ],
          chartSql: '20a5e6d6321a478e8e03eb453e301287',
          showEchart: false,
          chartOptions: {},
          params: {},
          // 下方为图表右侧表格数据
          echartTableArray: [],
          echartTableParams: {
            ccid: '355336920f10454cb1f9b73bba65b5ed',
            ownedModuleid: '708631605142536192',
            flag: '1'
          }
        },
        {
          title: '以行权计',
          description: '债券不涉及行权，则根据债券的到期日进行统计',
          echartColorArray: [
            {
              color: '#66BDFF',
              title: '1年以内'
            },
            {
              color: '#9C75DD',
              title: '1-3年'
            },
            {
              color: '#FF128E',
              title: '3-5年'
            },
            {
              color: '#E7855E',
              title: '5年以上'
            }
          ],
          chartSql: 'bf50f317ae3a4dcf96b41eba47db580b',
          showEchart: false,
          chartOptions: {},
          params: {},
          // 下方为图表右侧表格数据
          echartTableArray: [],
          echartTableParams: {
            ccid: '355336920f10454cb1f9b73bba65b5ed',
            ownedModuleid: '708631605142536192',
            flag: '2'
          }
        }
      ],
      cascaderOptions: [],
      bondTypeOptions: [],
      tableColumns: {
        sInfoWindcode: (h, { row }) => {
          return (
            <span
              style='color:var(--theme--color);cursor:pointer'
              onClick={() => this.showCodeDetail(row.sInfoWindcode)}
            >
              {row.sInfoWindcode}
            </span>
          )
        },
        exerciseValuation: (h, { row }) => {
          return (
            <span style='color:var(--theme--color);cursor:pointer' onClick={() => this.goToValuationDetails('1', row)}>
              {row.exerciseValuation}
            </span>
          )
        },
        maturityValuation: (h, { row }) => {
          return (
            <span style='color:var(--theme--color);cursor:pointer' onClick={() => this.goToValuationDetails('2', row)}>
              {row.maturityValuation}
            </span>
          )
        }
      },
      tableSelectParams: {
        ownedModuleid: '708631605142536192'
      },
      issuerCodes: [], // 发行人id集合 -先写死后续改动动态
      bondBalanceAnalysisData: {},
      averageData: {},
      column: []
    }
  },
  computed: {
    hasAdd() {
      return Object.prototype.hasOwnProperty.call(this.permitdetail, 'customAdd')
    }
  },
  created() {
    this.initDateSelect()
    this.initCascaderOptions()
    this.search()
    this.getBondTypeOptionsApi()
    this.getAverageDataApi()
  },
  mounted() {
    this.$nextTick(() => {
      this.$refs.tableSelect.tableSelect()
    })
  },
  methods: {
    ConvertAmount,
    /**
     * 初始化当前日期携带筛选
     */
    initDateSelect() {
      this.selectParam.showDate = new Date()
    },
    /**
     * 初始化季度级联选择器
     */
    initCascaderOptions() {
      const nowYear = moment(+new Date()).format('YYYY')
      for (let i = 5; i > 0; i--) {
        const year = nowYear * 1 - i
        this.cascaderOptions.push({
          value: year,
          label: year + '年',
          children: [
            {
              value: year + '-03-31',
              label: year + '-03-31'
            },
            {
              value: year + '-06-30',
              label: year + '-06-30'
            },
            {
              value: year + '-09-30',
              label: year + '-09-30'
            },
            {
              value: year + '-12-31',
              label: year + '-12-31'
            }
          ]
        })
      }
    },
    /**
     * 确认选择追溯时间点
     */
    confirmAnalyseDate(e) {
      this.selectParam.showDate = e[1]
      this.search()
    },
    /**
     * 搜索/查询
     */
    search() {
      this.chartAreaBaseData = this.chartAreaBaseData.map((item) => {
        item.showEchart = false
        return item
      })
      this.$nextTick(() => {
        if (this.selectParam.showDate) {
          this.selectParam.analyseDate = moment(this.selectParam.showDate).format('YYYY-MM-DD')
        } else {
          this.selectParam.analyseDate = ''
        }
        for (let i = 0; i < this.chartAreaBaseData.length; i++) {
          this.getChartTableDataApi(i)
        }
        this.$refs.tableSelect.tableSelect()
        this.getAverageDataApi()
        this.getBondBalanceAnalysisDataApi()
      })
    },
    /**
     * 打开债券录入弹框
     */
    openBondEntry() {
      this.$refs.bondEntry.open()
    },
    /**
     * 获取图表右侧表格数据API
     */
    async getChartTableDataApi(index) {
      const params = {
        ...this.chartAreaBaseData[index].echartTableParams
      }
      if (this.selectParam.analyseDate) {
        params.analyseDate = this.selectParam.analyseDate
      }
      const data = await GetListData({
        // CCID是自定义列id ownedModuleid是菜单id
        params,
        page: {
          pageNo: 1,
          pageSize: 50
        }
      })
      this.chartAreaBaseData = this.chartAreaBaseData.map((item, bIndex) => {
        if (bIndex === index) {
          item.echartTableArray = data?.pageInfo?.list || []
          // 为每一项前置添加不同的颜色
          item.echartTableArray = item.echartTableArray.map((item, iIndex) => {
            item.color = this.chartAreaBaseData[bIndex].echartColorArray[iIndex].color
            return item
          })
          // 合计计算展示
          item.echartTableArray.push({
            color: '#FFF3E9',
            num: item.echartTableArray.reduce((pre, current) => pre + current.num, 0),
            dateRange: '合计',
            amount: item.echartTableArray.reduce((pre, current) => pre + current.amount * 10000, 0) / 10000
          })
          // 添加头部标题
          item.echartTableArray.unshift({
            color: '#FFFFFF',
            num: '只数',
            dateRange: '时间',
            amount: '余额(亿)'
          })
          item.chartOptions = this.setEchartOptions(index)
          item.params = {
            analyseDate: this.selectParam.analyseDate
          }
          // // 渲染图表
          item.showEchart = true
        }
        return item
      })

      console.log(this.chartAreaBaseData, 'this.chartAreaBaseData')
    },
    /**
     * 合并echarts数组合并项
     */
    setEchartOptions(index) {
      const chartAreaBaseData = this.chartAreaBaseData[index]
      return {
        legend: {
          show: false
        },
        toolbox: {
          show: false
        },
        color: this.chartAreaBaseData[index].echartColorArray.map((color) => color.color),
        tooltip: {
          // trigger: 'axis',
          backgroundColor: 'rgba(0,0,0,0.6)',
          borderWidth: '0',
          padding: [12, 16],
          textStyle: {
            color: '#fff',
            fontSize: 14
          },
          formatter: (item) => {
            const content = `
              <div style="display:flex;flex-direction:column;align-items:flex-start;justify-content:center;">
                <span>
                  <span style="margin-right:8px;width:8px;height:8px;border-radius:50%;display:inline-block;background:${
                    item.color
                  }"></span>
                  <span>剩余期限：${item.name}</span>
                </span>
                <span style="margin-left:20px">余额(亿)：${item.value}</span>
                <span style="margin-left:20px">余额占比：${item.percent}%</span>
                <span style="margin-left:20px">债券只数：${
                  chartAreaBaseData.echartTableArray[++item.dataIndex]?.num || 0
                }</span>
              </div>
              `
            return content
          }
        },
        series: [
          {
            type: 'pie',
            radius: ['50%', '70%'],
            center: ['50%', '50%'],
            label: {
              show: true, // 是否显示
              formatter: function (params) {
                return `{percent|${params.percent}%}\n{name|${params.name},${
                  chartAreaBaseData.echartTableArray[++params.dataIndex]?.num || 0
                }只}`
              },
              rich: {
                percent: {
                  color: 'auto',
                  lineHeight: 17, // 行高
                  align: 'center', // 文本对齐方式
                  fontSize: 12, // 字体大小
                  marginBottom: 3
                },
                name: {
                  color: '#000000', // 文本颜色
                  lineHeight: 14, // 行高
                  align: 'center', // 文本对齐方式
                  fontSize: 12, // 字体大小
                  marginTop: 5
                }
              }
            },
            labelLine: {
              show: false, // 是否显示视觉引导线
              normal: {
                length: 5, // 视觉引导线第一段的长度
                length2: 10, // 视觉引导线第一段的长度
                align: 'right',
                lineStyle: {
                  width: 2 // 线宽
                }
              }
            }
          }
        ]
      }
    },
    /**
     * 设置表格区域查询数据
     */
    setTableSelectParams(data) {
      if (data.lastissue?.length) {
        data.lastissueStart = moment(data.lastissue[0]).format('YYYY-MM-DD')
        data.lastissueEnd = moment(data.lastissue[1]).format('YYYY-MM-DD')
      } else {
        data.lastissueStart = ''
        data.lastissueEnd = ''
      }
      if (data.maturitydate?.length) {
        data.maturitydateStart = moment(data.maturitydate[0]).format('YYYY-MM-DD')
        data.maturitydateEnd = moment(data.maturitydate[1]).format('YYYY-MM-DD')
      } else {
        data.maturitydateStart = ''
        data.maturitydateEnd = ''
      }
      this.tableSelectParams = {
        ...this.tableSelectParams,
        ...data
      }
      this.$set(this.tableSelectParams, 'analyseDate', this.selectParam.analyseDate)
      this.getAverageDataApi()
    },
    /**
     * 获取债券类型下拉项参数
     */
    async getBondTypeOptionsApi() {
      const data = await issuanceQueryBondTypeList()
      this.bondTypeOptions = data.reduce((pre, current) => {
        if (current) {
          const index = pre.findIndex((item) => item.value === current.bondTypeCode)
          if (index === -1) {
            pre.push({
              label: current.bondTypeName,
              value: current.bondTypeCode,
              children: []
            })
          } else {
            pre[index].children.push({
              label: current.bondTypeName2,
              value: current.bondTypeCode2
            })
          }
        }
        return pre
      }, [])
    },
    /**
     * 获取债券余额分析文本数据
     */
    async getBondBalanceAnalysisDataApi() {
      const params = {
        params: {
          ownedModuleid: '708631605142536192',
          ccid: '5cfb521000d6426cbd556c4aa31ee28b',
          analyseDate: this.selectParam.analyseDate
        },
        page: {
          pageNo: 1,
          pageSize: 25
        }
      }
      const data = await GetListData(params)
      if (data?.pageInfo) {
        this.bondBalanceAnalysisData = data.pageInfo
        console.log(this.bondBalanceAnalysisData)
      }
    },
    /**
     * 跳转估值详情页面
     * @param {String} isRight '1'为行权估值 '2'为到期估值
     * @param {Object} row 行数据
     */
    goToValuationDetails(isRight, row) {
      this.$store.dispatch('tagsView/updateCurrentTab', {
        path: '/1361645930918469632',
        // 跳转需要刷新的话 refresh传true
        meta: {
          refresh: true,
          params: {
            comp_name: row.bInfoIssuer,
            s_info_windcode: row.sInfoWindcode,
            s_info_name: row.sInfoName,
            b_is_right: isRight
          }
        }
      })
    },
    /**
     * 打开发行材料弹框
     */
    handleissuanceMaterials(row) {
      const params = {
        ownedModuleid: '708631605142536192',
        ccid: '27716d3fb1cb469c970724740aa6291a'
      }
      this.$refs.issuanceMaterials.open(params, row, '发行材料')
    },
    /**
     * 获取左下角平均数据展示
     */
    async getAverageDataApi() {
      const data = await mineBondsAverageInfo({
        ...this.tableSelectParams,
        analyseDate: this.selectParam.analyseDate
      })
      this.averageData = data
      console.log(data)
    },
    /**
     * 获取表头相关方法
     */
    callFn(data) {
      this.column = data.config.column
      this.sort = data.sort
      this.direction = data.direction
    },
    /**
     * 自定义导出按钮
     */
    async handleexport() {
      const sysVersion = localStorage.getItem('sysVersion')
      const params = {
        params: {
          pageInfo: {},
          filename: sysVersion === 'group' ? '集团发债' : '发债明细',
          column: this.column,
          ccid: '2642e060df324cddbcc58b88f22b472c',
          ownedModuleid: '1362361842164305920',
          ...this.tableSelectParams
        },
        page: {
          sort: this.sort,
          direction: this.direction
        }
      }
      await exportExcelByCustomColumn(params)
    },
    showCodeDetail(sInfoWindcode) {
      this.$refs.detail.open({ sinfoWindcode: sInfoWindcode })
    }
  },
  filters: {
    amountFormat(val) {
      if (typeof val === 'string') {
        return val
      } else {
        return ConvertAmount('HMU', val * 1, 1, 4)
      }
    }
  }
}
</script>
<style scoped lang="scss">
::v-deep .jr-decorated-table > div {
  padding-left: 0px;
  padding-right: 0px;
}
::v-deep .jr-decorated-table--header-left {
  .custom-button-component {
    display: none;
  }
}
.groupBonds {
  background-color: rgba(245, 245, 245, 1);
  &-topSelect {
    background-color: #ffffff;
    width: 100%;
    display: flex;
    align-items: center;
    gap: 8px;
    &-dateAndBtn {
      position: relative;
      width: 330px;
      &-today_btn {
        width: 28px;
        height: 28px;
        color: #ffffff;
        line-height: 28px;
        text-align: center;
        background-color: var(--theme--color);
        border-radius: 2px;
        position: absolute;
        right: 2px;
        top: 2px;
        cursor: pointer;
      }
      ::v-deep .el-input__inner {
        height: 32px;
        line-height: 32px;
      }
    }
    &-canlendar {
      width: 32px;
      height: 32px;
      border-radius: 2px;
      position: relative;
      &-icon {
        width: 100%;
        height: 100%;
        position: absolute;
        top: 0px;
        left: 0px;
        z-index: 3;
        background-color: rgba(255, 142, 43, 0.1);
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
      }
    }
    button {
      background-color: var(--theme--color);
      border: none;
      outline: none;
      color: #ffffff;
      margin-left: 16px;
    }
  }
  &-bondBalanceAnalysis {
    background-color: #ffffff;
    padding: 0 16px;
    margin-top: 1px;
    min-width: 460px;
    &-chartArea {
      width: 100%;
      display: flex;
      gap: 16px;
      &-chartBox {
        width: calc(50% - 8px);
        height: 360px;
        background: #ffffff;
        border-radius: 4px;
        border: 1px solid #eae9e9;
        min-width: 460px;
        &-title {
          display: flex;
          gap: 8px;
          height: 64px;
          padding: 21px 0px 21px 16px;
          box-sizing: border-box;
          align-items: center;
          & > span:nth-of-type(1) {
            width: 3px;
            height: 16px;
            background-color: var(--theme--color);
            border-radius: 1px;
          }
          & > span:nth-of-type(2) {
            font-size: var(--el-font-size-base);
            height: 22px;
            line-height: 22px;
          }
          & > span:nth-of-type(3) {
            width: 13px;
            height: 13px;
            font-size: var(--el-font-size-base);
            line-height: 13px;
          }
          & > span:nth-of-type(4) {
            height: 19px;
            font-size: var(--el-font-size-base);
            color: rgba(0, 0, 0, 0.6);
            line-height: 19px;
          }
        }
        &-content {
          display: flex;
          height: calc(100% - 64px);
          padding-left: 16px;
          padding-right: 31px;
          box-sizing: border-box;
          &-chart {
            width: 50%;
            border: 1px solod #000;
            height: 100%;
            padding-top: 50px;
            box-sizing: border-box;
          }
          &-description {
            width: 50%;
            border: 1px solod #000;
            height: 100%;
            display: flex;
            flex-direction: column;
            gap: 8px;
            &-item {
              width: 100%;
              height: 40px;
              display: flex;
              align-items: center;
              background-color: rgba(0, 0, 0, 0.02);
              padding: 0px 24px;
              box-sizing: border-box;
              justify-content: space-between;
              text-align: center;
              & > span:nth-of-type(1) {
                width: 8px;
                height: 8px;
                background: #5b8ff9;
              }
              & > span:nth-of-type(2) {
                min-width: 52px;
                text-align: center;
                height: 20px;
                font-weight: 400;
                font-size: var(--el-font-size-base);
                color: #8c8c8c;
                line-height: 20px;
              }
              & > span:nth-of-type(3),
              & > span:nth-of-type(4) {
                min-width: 60px;
                height: 20px;
                font-weight: 400;
                font-size: var(--el-font-size-base);
                color: rgba(0, 0, 0, 0.9);
                line-height: 20px;
              }
            }
          }
        }
      }
    }
    &-balanceDetail {
      width: 100%;
      height: 46px;
      margin-bottom: 8px;
      line-height: 46px;
      font-size: var(--el-font-size-base);
      background-color: #fef4ee;
      border-radius: 2px;
      padding-left: 16px;
      box-sizing: border-box;
      min-width: 460px;
      span {
        color: var(--theme--color);
      }
    }
  }
  &-tableSelect {
    padding: 0px 16px 8px 16px;
    box-sizing: border-box;
    background-color: #ffffff;
  }
  &-tableArea {
    margin-top: 1px;
    padding: 16px 16px 27px;
    box-sizing: border-box;
    background-color: #ffffff;
    position: relative;
    &-buttons {
      height: 32px;
      border-radius: 2px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      position: absolute;
      top: 16px;
      left: 16px;
      z-index: 3;
      & > button {
        background-color: var(--theme--color);
        color: #ffffff;
      }
    }
    &-infoDetail {
      width: 40%;
      position: relative;
      top: 0px;
      display: flex;
      gap: 24px;
      overflow: hidden;
      & > p {
        margin-bottom: 0px;
        margin-top: 10px;
        display: flex;
        align-items: center;
        flex-shrink: 0;
        & > span:nth-of-type(1) {
          height: 22px;
          font-size: var(--el-font-size-base);
          color: rgba(0, 0, 0, 0.9);
          line-height: 22px;
        }
        & > span:nth-last-of-type(1) {
          height: 22px;
          font-size: var(--el-font-size-base);
          color: var(--theme--color);
          line-height: 22px;
        }
      }
    }
    &-tips {
      height: 19px;
      font-size: var(--el-font-size-base);
      color: rgba(0, 0, 0, 0.6);
      line-height: 19px;
    }
  }
}
.title {
  height: 22px;
  font-size: var(--el-font-size-base);
  color: rgba(0, 0, 0, 0.85);
  line-height: 22px;
  font-weight: 600;
  position: relative;
  top: -6px;
}
::v-deep .jr-pagination--right {
  margin-top: 16px;
}
</style>
