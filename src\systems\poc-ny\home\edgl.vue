<template>
  <!-- POC专用 -->
  <!-- 额度管理 -->
  <div class="poc-home">
    <h3>额度管理</h3>
    <b-template-module chart-seq="7b9fdd55056840cc97fded35563ce468" :custom-options="ringPieOptions" :height="240" />
  </div>
</template>
<script>
export default {
  data() {
    return {
      ringPieOptions: (data) => ({
        title: [
          {
            text: '{header1|债券类型} {header2| 有效剩余额度} {header3|有效注册通知书}',
            textAlign: 'left',
            left: '4%',
            bottom: 20,
            textStyle: {
              color: 'red',
              rich: {
                header1: {
                  width: 95,
                  fontSize: 15,
                  color: '#fb9c34'
                },
                header2: {
                  width: 135,
                  fontSize: 15,
                  color: '#fb9c34'
                },
                header3: {
                  width: 85,
                  fontSize: 15,
                  color: '#fb9c34'
                }
              }
            }
          },
          {
            text: '{name|总剩余额度}\n{val|' + data.reduce((c, n) => c + Number(n.value), 0) + '亿元}',
            top: '25%',
            left: '29%',
            textStyle: {
              rich: {
                name: {
                  fontSize: 14,
                  width: 85,
                  color: '#383838',
                  align: 'center',
                  padding: [10, 0]
                },
                val: {
                  fontSize: 16,
                  width: 85,
                  color: 'rgb(250,140,22)',
                  align: 'center'
                }
              }
            }
          }
        ],
        toolbox: {
          top: 0,
          feature: {
            saveAsImage: {
              show: false
            },
            restore: {
              show: false
            },
            magicType: {
              show: false
            },
            dataView: {
              show: false
            }
          }
        },
        legend: {
          type: 'plain',
          icon: 'rect',
          orient: 'vertical',
          left: '4%',
          top: '90%',
          align: 'left',
          itemGap: 15,
          itemWidth: 10, // 设置宽度
          itemHeight: 10, // 设置高度
          symbolKeepAspect: false,
          textStyle: {
            color: '#000',
            rich: {
              name: {
                align: 'left',
                width: 65,
                fontSize: 12
              },
              value: {
                align: 'center',
                width: 125,
                fontSize: 12
              },
              scale: {
                align: 'right',
                width: 95,
                fontSize: 12
              }
            }
          },
          formatter: function(name) {
            if (data && data.length) {
              for (var i = 0; i < data.length; i++) {
                if (name === data[i].name) {
                  return (
                    '{name| ' +
                    name +
                    '}' +
                    '{value| ' +
                    data[i].value.toFixed(4) + '亿元' +
                    '}' +
                    '{scale|' +
                    Number(data[i].param.tzs || 0) +
                    '张} '
                  )
                }
              }
            }
          }
        },
        series: [
          {
            radius: ['50%', '60%'],
            center: ['40%', '40%'],
            label: {
              show: false,
              position: 'center'
            },
            labelLine: {
              show: false
            }
          }
        ]
      })
    }
  }
}
</script>
<style lang="scss">
.poc-home {
  width: 100%;
  height: 100%;
  background-color: #fff;
  padding: 16px 16px 0 16px;
  overflow: hidden;
  h3 {
    font-weight: bold;
    font-size: 14px;
    display: flex;
    justify-content: space-between;
    & + div {
      height: calc(100% - 28px);
    }
  }
  .jr-decorated-table--body,
  &.jr-decorated-table--body .jr-table .el-table {
    .el-table__fixed-header-wrapper th,
    .el-table__fixed-body-wrapper td,
    .el-table__body-wrapper td,
    .el-table__fixed-right th,
    .el-table__header-wrapper th {
      height: 38px !important;
    }
  }
  .bp-red {
    color: #f56c6c;
  }
  .bp-green {
    color: #67c23a;
  }
}
</style>
