<?xml version="1.0" encoding="UTF-8"?>
<svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>Finance/funds-box-fill</title>
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-1">
            <stop stop-color="#B7AAFF" offset="0%"></stop>
            <stop stop-color="#856FFE" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-2">
            <stop stop-color="#FFFFFF" stop-opacity="0.4" offset="0%"></stop>
            <stop stop-color="#FFFFFF" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
        <path d="M5.14285714,0 L15.4285714,0 C15.7441627,0 16,0.255837286 16,0.571428571 L16,9.71428571 C16,10.029877 15.7441627,10.2857143 15.4285714,10.2857143 L5.14285714,10.2857143 C4.82726586,10.2857143 4.57142857,10.029877 4.57142857,9.71428571 L4.57142857,0.571428571 C4.57142857,0.255837286 4.82726586,0 5.14285714,0 Z" id="path-3"></path>
        <filter x="-17.5%" y="-19.4%" width="135.0%" height="138.9%" filterUnits="objectBoundingBox" id="filter-4">
            <feGaussianBlur stdDeviation="2" in="SourceAlpha" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="0" dy="0" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.4 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>
        </filter>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-5">
            <stop stop-color="#FFFFFF" stop-opacity="0.3" offset="0%"></stop>
            <stop stop-color="#FFFFFF" stop-opacity="0.13" offset="100%"></stop>
        </linearGradient>
        <path d="M0.714285714,1.71428571 L13.5714286,1.71428571 C13.9659177,1.71428571 14.2857143,2.03408232 14.2857143,2.42857143 L14.2857143,13.8571429 C14.2857143,14.251632 13.9659177,14.5714286 13.5714286,14.5714286 L0.714285714,14.5714286 C0.319796607,14.5714286 0,14.251632 0,13.8571429 L0,2.42857143 C0,2.03408232 0.319796607,1.71428571 0.714285714,1.71428571 Z" id="path-6"></path>
        <filter x="-14.0%" y="-15.6%" width="128.0%" height="131.1%" filterUnits="objectBoundingBox" id="filter-7">
            <feGaussianBlur stdDeviation="2" in="SourceAlpha" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="0" dy="0" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 1   0 0 0 0 1   0 0 0 0 1  0 0 0 0.3 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>
        </filter>
    </defs>
    <g id="成都银行智管家UI" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="债市一览/利差分析-发行利差-自定义利率曲线利差" transform="translate(-284, -324)">
            <g id="编组-7" transform="translate(236, 112)">
                <g id="编组-34" transform="translate(16, 72)">
                    <g id="编组-28" transform="translate(16, 16)">
                        <g id="编组-14" transform="translate(0, 104)">
                            <g id="编组-9" transform="translate(16, 17)">
                                <g id="Finance/funds-box-fill" transform="translate(0, 3)">
                                    <polygon id="路径" points="0 0 16 0 16 16 0 16"></polygon>
                                    <g id="编组-24" transform="translate(0, 0.7143)">
                                        <g id="编组-25">
                                            <g id="形状">
                                                <use fill="url(#linearGradient-1)" fill-rule="evenodd" xlink:href="#path-3"></use>
                                                <use fill="black" fill-opacity="1" filter="url(#filter-4)" xlink:href="#path-3"></use>
                                                <path stroke="url(#linearGradient-2)" stroke-width="0.5" d="M15.193593,0.262713307 L15.73566,9.47785232 L5.37783557,10.023001 L4.83576857,0.807861961 L15.193593,0.262713307 Z" stroke-linejoin="square"></path>
                                            </g>
                                            <g id="形状备份-2">
                                                <use fill-opacity="0.2" fill="#856FFE" fill-rule="evenodd" xlink:href="#path-6"></use>
                                                <use fill="black" fill-opacity="1" filter="url(#filter-7)" xlink:href="#path-6"></use>
                                                <path stroke="url(#linearGradient-5)" stroke-width="0.5" d="M13.5714286,1.96428571 L14.0357143,13.8571429 L0.714285714,14.3214286 L0.25,2.42857143 L13.5714286,1.96428571 Z" stroke-linejoin="square"></path>
                                            </g>
                                        </g>
                                        <polygon id="路径" fill="#FFFFFF" fill-rule="nonzero" points="9.13785714 6.56642857 7.38785714 8.31642857 5.87285714 6.80071429 2.84214286 9.83142857 3.85214286 10.8414286 5.87285714 8.82142857 7.38785714 10.3364286 10.1478571 7.57642857 11.4285714 8.85714286 11.4285714 5.28571429 7.85714286 5.28571429"></polygon>
                                    </g>
                                </g>
                            </g>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>