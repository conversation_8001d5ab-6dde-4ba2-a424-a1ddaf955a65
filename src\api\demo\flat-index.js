import { GetListInfo, GetInfoFn, UpdateFn } from '@jupiterweb/utils/api'

// 查看详情
export const GetViewData = (id, businesskey, businessno) => GetInfoFn(`/dcCurrent/contractDeal/view?id=${id}&businesskey=${businesskey}&businessno=${businessno}`, {}, 'get')

// 查询列表数据
export const GetListData = params => GetListInfo('bond/repay/bondRepayCalculator', params)

// 查询账户余额
export const GetAccrualList = () => GetInfoFn('/platform/ka/accountDaily/findRemainAmt')

// 保存
export const SaveAccrual = (params, cb) => UpdateFn('/repo/accrualDetail/save', params, cb)
