<template>
  <div class="personal-container">
    <el-tabs v-model="activeTab" class="personal-tabs">
      <el-tab-pane label="个人信息" name="personalInfo">
        <person v-bind="{ ...$attrs, ...$props }" />
      </el-tab-pane>
      <el-tab-pane label="消息管理" name="messageManage" lazy>
        <message v-bind="{ ...$attrs, ...$props }" />
      </el-tab-pane>
      <el-tab-pane
        v-if="$store.getters.sysVersion === $dict.COMPANY_VER_group"
        label="集团成员"
        name="groupManage"
        lazy
      >
        <group v-bind="{ ...$attrs, ...$props }" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import Person from './modules/person.vue'
import Message from './modules/message.vue'
import Group from './modules/group.vue'
export default {
  components: {
    Person,
    Message,
    Group
  },
  data() {
    return {
      activeTab: 'personalInfo'
    }
  }
}
</script>

<style lang="scss">
.personal-modal.jr-modal.el-dialog {
  --el-component-size: 32px;
  .el-dialog__title {
    // font-size: var(--el-font-size-large);
    color: rgba(0, 0, 0, 0.85);
  }
  .platform-modal-content {
    padding: 24px 24px 0;
    .el-form-item {
      margin-top: 0 !important;
      margin-bottom: 24px !important;
      line-height: var(--el-component-size);
      .el-form-item__label {
        white-space: nowrap;
        line-height: var(--el-component-size);
        padding-top: 0 !important;
      }
      .el-form-item__content {
        line-height: var(--el-component-size);
      }
    };
  }
  .jr-modal__headerbtn {
    display: none;
  }
  .el-dialog__footer {
    height: 120px;
    padding-top: 64px;
    background: url('~@/assets/images/personal/modal-footer-bg.png') no-repeat center center;
    background-size: 100% 100%;
    border-top: none;
    padding-top: 76px;
    padding-bottom: 12px;
    .el-button {
      width: 88px;
      height: 32px;
    }
  }
}
.personal-container {
  height: 100%;
  .personal-tabs {
    height: calc(100% - 24px);
    background: #fff;
    & > .el-tabs__header {
      margin-bottom: 8px;
      .el-tabs__active-bar {
        width: 60px !important;
        left: -2px !important;
        // height: 4px !important;
      }
      .el-tabs__item {
        // font-size: var(--el-font-size-extra-large);
        font-weight: 600;
        height: 36px;
        line-height: 36px;
        padding: 0 16px !important;
        color: rgba(0, 0, 0, 0.6) !important;
        &.is-active {
          color: var(--theme--color) !important;
        }
      }
    }
    & > .el-tabs__content {
      height: calc(100% - 86px);
      .el-tab-pane {
        height: 100%;
      }
    }
  }
}
</style>
