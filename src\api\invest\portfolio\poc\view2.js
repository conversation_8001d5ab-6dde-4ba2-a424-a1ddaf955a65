import { GetListInfo } from '@jupiterweb/utils/api'

// 获取列表
export const getTableList = params => GetListInfo('/invest/portfolio/ptlthematic/PtlThematicAnalysis001/findPage', params)

// 可选指标-树结构
export const getAllTreeNode = params => GetListInfo('/invest/portfolio/ptlpositionanalysis/PtlPositionAnalysis003/getAllTreeNode', params)

// 保存列配置
export const savaTableCols = params => GetListInfo('/invest/portfolio/ptlpositionanalysis/PtlPositionAnalysis003/savaTableCols', params)

// 生成新的列
export const getTableCols = params => GetListInfo('/invest/portfolio/ptlthematic/PtlThematicAnalysis001/getTableCols', params)

// 组合收益风险概览
export const getPtlIncomeRiskData = params => GetListInfo('/invest/portfolio/ptlincomeanalysis/PtlIncomeAnalysis001/getPtlIncomeRiskData', params)

// 组合胜率分析
export const getWinRateData = params => GetListInfo('/invest/portfolio/ptlincomeanalysis/PtlIncomeAnalysis001/getWinRateData', params)
