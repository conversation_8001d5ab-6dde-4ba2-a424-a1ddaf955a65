<template>
  <!-- POC专用 发行查询-发行分析-->
  <div class="issue-statistics">
    <div class="issue-overview">
      <div class="item-title">发行总览</div>
      {{ searchForm.fxcqsr[0] }}至{{ searchForm.fxcqsr[1] }}，根据筛选条件，市场共发行债券
      <el-link type="primary">268.0.400</el-link>
      亿元，其中人民币债券
      <el-link type="primary">238.1000</el-link>
      亿元，境外债券/境内区内债券
      <el-link type="primary">29.9400</el-link>
      亿元，从发行情况来看，平均发行期限
      <el-link type="primary">1.8390Y</el-link>
      ，平均利率
      <el-link type="primary">3.6659%</el-link>
      ，票面加权
      <el-link type="primary">3.8B37%</el-link>
      。
    </div>
    <el-row class="issue-bond">
      <el-col :span="12">
        <div class="item-title">人民币债券概览</div>
        <TemplateModule chart-type="TABLE" chart-seq="f83f549381244671a6637da2671f9a64" />
      </el-col>
      <el-col :span="11" :offset="1">
        <div class="item-title">境外债券/境内区内债券概览</div>
        <TemplateModule chart-type="TABLE" chart-seq="df2c46843b7044138063ee4c6cf1013d" />
      </el-col>
    </el-row>
    <div class="issue-fb">
      <div class="issue-origin">
        <div class="item-title">区域总览</div>
        <template-module style="height: 300px;" chart-seq="016940b57e364d59a56980e9af6169e9" chart-type="LINEBAR" />
      </div>
      <div class="flex-box">
        <div>
          <div class="item-title float">
            利率分布
            <div>
              <el-checkbox>含权债</el-checkbox>
              <jr-combobox
                v-model="llType"
                :data="[
                  { text: '按发行期限统计', value: 'fxqx' },
                  { text: '按发行时间统计', value: 'fxsj' }
                ]"
                style="width: 140px; margin-left: 10px"
              />
            </div>
          </div>
          <b-template-module
            :height="300"
            :chart-seq="llType === 'fxqx' ? '2894e6903674492b919b5fea9302bcfa' : '3d95bb33792d4967935d510c3b454a5d'"
            chart-type="scatter"
            :custom-options="hideDataView"
          />
        </div>
        <div>
          <div class="item-title float">
            规模分布
            <jr-combobox
              v-model="gmType"
              :data="[
                { text: '按债券类型统计', value: 'zqlx' },
                { text: '按评级统计', value: 'pj' }
              ]"
              style="width: 140px"
            />
          </div>
          <b-template-module
            :height="300"
            :chart-seq="gmType === 'zqlx' ? '6e8b30b947cf4488b7ce3471bd01f6b4' : '25e3daa05e4f4a53bca73f21f9b72990'"
            :custom-options="ringPieOptions"
          />
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import TemplateModule from '@jupiterweb/components/template-module'
export default {
  components: {
    TemplateModule
  },
  props: {
    searchForm: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    const hideDataView = (name) => ({
      toolbox: {
        top: 0,
        feature: {
          saveAsImage: {
            show: true,
            title: '导出',
            name: name
          },
          restore: {
            show: false
          },
          magicType: {
            show: false
          },
          dataView: {
            show: false
          }
        }
      }
    })
    return {
      llType: 'fxsj',
      gmType: 'zqlx',
      hideDataView: { ...hideDataView('利率分布'), series: [{ name: '' }] },
      ringPieOptions: (data) => ({
        title: [
          {
            text: '{header1|} {header2| 债券数} {header3| 规模}',
            textAlign: 'left',
            left: '49%',
            top: '20%',
            textStyle: {
              color: '#000',
              rich: {
                header1: {
                  width: 85,
                  fontSize: 15
                },
                header2: {
                  width: 85,
                  fontSize: 15
                },
                header3: {
                  fontSize: 15
                }
              }
            }
          },
          {
            text: '{name|总计}\n{val|' + data.reduce((c, n) => c + Number(n.param.gm), 0).toFixed(4) + '}',
            top: '40%',
            left: '15%',
            textStyle: {
              rich: {
                name: {
                  fontSize: 16,
                  width: 85,
                  color: '#383838',
                  align: 'center',
                  padding: [10, 0]
                },
                val: {
                  fontSize: 20,
                  width: 85,
                  color: 'rgb(250,140,22)',
                  align: 'center'
                }
              }
            }
          }
        ],
        legend: {
          // selectedMode: false, // 取消图例上的点击事件
          type: 'plain',
          icon: 'circle',
          orient: 'vertical',
          left: '50%',
          top: '35%',
          align: 'left',
          itemGap: 15,
          itemWidth: 10, // 设置宽度
          itemHeight: 10, // 设置高度
          symbolKeepAspect: false,
          textStyle: {
            color: '#000',
            rich: {
              name: {
                verticalAlign: 'right',
                align: 'left',
                width: 85,
                fontSize: 12
              },
              value: {
                align: 'left',
                width: 80,
                fontSize: 12
              },
              scale: {
                align: 'left',
                width: 80,
                fontSize: 12
              }
            }
          },
          formatter: function(name) {
            if (data && data.length) {
              for (var i = 0; i < data.length; i++) {
                if (name === data[i].name) {
                  return (
                    '{name| ' +
                    name +
                    '}' +
                    '{value| ' +
                    data[i].value +
                    '}' +
                    '{scale|' +
                    Number(data[i].param.gm || 0).toFixed(4) +
                    '} '
                  )
                }
              }
            }
          }
        },
        ...hideDataView('规模分布'),
        series: [
          {
            radius: ['60%', '75%'],
            center: ['20%', '55%'],
            label: {
              show: false,
              position: 'center'
            },
            labelLine: {
              show: false
            }
          }
        ]
      })
    }
  },
  created() {}
}
</script>
<style lang="scss">
.issue-statistics {
  .item-title {
    border-left: none;
    padding-left: 0;
    &.float {
      display: flex;
      justify-content: space-between;
      margin-right: 60px;
      position: relative;
      z-index: 2;
      width: calc(100% - 60px);
      & + div {
        position: relative;
        z-index: 1;
        margin-top: -40px;
      }
    }
  }
  .issue-overview {
    .el-link {
      vertical-align: baseline;
    }
  }
  .issue-bond {
    margin-top: 20px;
  }
  .issue-fb {
    margin-top: 20px;
    border: 1px solid #e8e8e8;
    .issue-origin {
      padding: 12px;
    }
  }
  .flex-box {
    display: flex;
    border-top: 1px solid #e8e8e8;
    & > div {
      padding: 12px;
      border-left: 1px solid #e8e8e8;
      width: 50%;
      &:first-child {
        border-left: none;
      }
    }
  }
}
</style>
