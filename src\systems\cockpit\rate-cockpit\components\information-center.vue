<template>
  <div class="informationCenter">
    <cockpitHeader title="资讯中心" :style="{ width: px2vw(382), height: px2vh(40) }" type="light" />
    <cockpitTabs :tabs="tabs" :style="{ marginTop: px2vh(16) }" @change="tabChange" @handleMore="redirectToMenu"/>
    <div class="informationCenter-content">
      <div v-for="(card,index) in cardData" :key="index" class="informationCenter-content-single">
        <div class="informationCenter-content-single-line">
          <span :class="index === 0 ? 'informationCenter-content-single-line-first' : ''"/>
          <img src="@/assets/cockpit/infoBall.png" alt="" />
          <span :class="index === (cardData.length - 1) ? 'informationCenter-content-single-line-last' : ''"/>
        </div>
        <div class="informationCenter-content-single-card">
          <img src="@/assets/cockpit/infoCard.png" alt="" />
          <div class="informationCenter-content-single-card-text">
            <span>{{ card.category }}</span>
            <span>{{ card.publishDate }}</span>
          </div>
          <div class="informationCenter-content-single-card-info">
            <img v-show="card.publishDate === today" src="@/assets/cockpit/cockpit_info_new.png">
            <span>{{ card.title }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import cockpitHeader from '../../components/cockpit-header.vue'
import cockpitTabs from '../../components/cockpit-tabs.vue'
import { cockpitGetInfoList } from '@/api/cockpit/cockpit.js'
import { px2vw, px2vh } from '../../utils/portcss'
import moment from 'moment'
export default {
  name: 'InformationCenter',
  components: {
    cockpitHeader,
    cockpitTabs
  },
  data() {
    return {
      tabs: [],
      cardData: [],
      menuList: [
        {
          name:"专题研究",
          menuId:"1368643546893844480"
        },
        {
          name:"债市资讯",
          menuId:"1368643657350840320"
        }
      ],
      activeMenuId: '',
    }
  },
  created() {
    this.getInfomationDataApi()
  },
  computed:{
    today() {
      return moment(+new Date()).format('YYYY-MM-DD')
    }
  },
  methods: {
    px2vw,
    px2vh,
    /**
     * 获取资讯中心数据
     */
    async getInfomationDataApi() {
      const data = await cockpitGetInfoList()
      console.log(data,'data')
      for (const key in data) {
        if(key === '专题研究'){
          this.tabs.unshift({
            tabName: key,
            value: data[key],
            menuId:this.menuList.find(item => item.name === key)?.menuId || ''
          })
        }else{
          this.tabs.push({
            tabName: key,
            value: data[key],
            menuId:this.menuList.find(item => item.name === key)?.menuId || ''
          })
        }
      }
    },
    /**
     * tab切换
     */
    tabChange(data){
      this.cardData = data?.value || []
      this.activeMenuId = data?.menuId
    },
    /**
     * 路径跳转
     */
    redirectToMenu(){
      this.$store.dispatch('tagsView/updateCurrentTab', {
        path: '/' + this.activeMenuId,
        // 跳转需要刷新的话 refresh传true
        meta: {
          refresh: true,
          params: {}
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~assets/styles/portcss.scss';
.informationCenter {
  width: 100%;
  height: vh(436);
  background: rgba(255, 255, 255, 0.5);
  box-shadow: 0px 4px 20px -2px rgba(0, 0, 0, 0.06);
  border: vh(2) solid transparent;
  border-radius: vh(12);
  background-clip: padding-box, border-box;
  background-origin: padding-box, border-box;
  background-image: linear-gradient(to right, rgba(255, 255, 255, 1), rgba(255, 255, 255, 0.5)),
    radial-gradient(rgba(255, 255, 255, 1), rgba(255, 255, 255, 0.5));
  padding: 0px vw(16);
  display: flex;
  flex-direction: column;
  &-content:hover {
    overflow-y: overlay;
  }
  &-content {
    margin-top: vh(11);
    width: 100%;
    height: vh(345);
    overflow: hidden;
    &-single {
      width: 100%;
      height: vh(104);
      display: flex;
      align-items: center;
      gap: vw(6);
      &-line {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: space-between;
        width: vw(13);
        height: 100%;
        gap: vw(6);
        img {
          width: 13px;
          height: 14px;
        }
        span {
          width: vw(1);
          height: vh(40);
          background: #FF8E2B;
        }
        &-first {
          background: linear-gradient( to bottom, rgba(227,156,58,0) 0%, #F3B86C 100%) !important;
        }
        &-last {
          background: linear-gradient( to top, rgba(227,156,58,0) 0%, #F3B86C 100%) !important;
        }
      }
      &-card {
        width: vw(413);
        height: vh(80);
        position: relative;
        img {
          width: 100%;
          height: 100%;
        }
        &-text {
          width: vw(340);
          height: vh(17);
          position: absolute;
          top: vh(19);
          left: vw(26);
          display: flex;
          align-items: center;
          justify-content: space-between;
          span {
            height: 100%;
            font-weight: 400;
            font-size: vh(12);
            color: rgba(0,0,0,0.9);
            line-height: vh(17);
          }
          & > span:nth-of-type(1) {
            color: rgba(0,0,0,0.9);
            font-weight: 600;
          }
          & > span:nth-of-type(2) {
            color: rgba(0,0,0,0.6);
          }
        }
        &-info {
          width: vw(390);
          height: vh(20);
          position: absolute;
          top: vh(40);
          left: vw(26);
          display: flex;
          align-items: center;
          img{
            width: vw(18);
            height: vh(19);
            margin-right: vw(10);
          }
          & > span:nth-of-type(1) {
            height: vh(20);
            font-weight: 400;
            font-size: vh(14);
            color: #FF8E2B;
            text-overflow: ellipsis;
          }
        }
      }
    }
  }
}
</style>
