<template>
  <div class="cockpit-lighter">
    <div class="cockpit-lighter-left">
      <quota-switching />
      <cash-flow />
      <company-announce />
    </div>
    <div class="cockpit-lighter-middle">
      <bond-overview />
      <bond-information />
    </div>
    <div class="cockpit-lighter-right">
      <calculator />
      <thing-todo />
      <payment-reminder />
    </div>
  </div>
</template>

<script>
import quotaSwitching from './quota-switching.vue'
import cashFlow from './cash-flow.vue'
import companyAnnounce from './company-announce.vue'
import bondOverview from './bond-overview.vue'
import bondInformation from './bond-information.vue'
import calculator from './calculator.vue'
import thingTodo from './thing-todo.vue'
import paymentReminder from './payment-reminder.vue'
export default {
  name: '<PERSON><PERSON><PERSON><PERSON>ighter',
  components: {
    quotaSwitching,
    cashFlow,
    companyAnnounce,
    bondOverview,
    bondInformation,
    calculator,
    thingTodo,
    paymentReminder
  }
}
</script>

<style lang="scss" scoped>
@import '~assets/styles/portcss.scss';
.cockpit-lighter {
  width: 100%;
  height: 100%;
  height: vh(908);
  overflow-y: overlay;
  overflow-x: hidden;
  margin-top: vh(16);
  margin-bottom: vh(16);
  padding: 0 vw(18) 0 vw(19);
  display: flex;
  justify-content: space-between;
  &-left {
    width: vw(490);
    height: vh(1356);
  }
  &-middle {
    width: vw(868);
    height: vh(1356);
  }
  &-right {
    width: vw(490);
    height: vh(1356);
  }
  & > div {
    & > div:nth-of-type(n + 2) {
      margin-top: vh(16);
    }
  }
}
</style>
