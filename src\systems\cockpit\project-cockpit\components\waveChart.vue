<template>
  <div id="wave-chart" style="width: 100%; height: 100%"></div>
</template>

<script>
import * as echarts from 'echarts'
import { px2vw, px2vh } from '../../utils/portcss'
import moment from 'moment'

export default {
  props: {
    chartData: {
      type: Array,
      default: () => []
    },
    totalAmount: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      chart: null
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
    })
  },
  beforeDestroy() {
    if (this.chart) {
      this.chart.dispose()
    }
  },
  methods: {
    px2vw,
    px2vh,
    initChart() {
      this.chart = echarts.init(this.$el)

      const center = ['16%', '50%', '83%']

      const seriesData = this.chartData.map((item, index) => {
        const percent = item.amount / this.totalAmount
        return {
          type: 'liquidFill',
          radius: '40%',
          center: [center[index], '40%'],
          color: [
            {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: '#255BAB'
                },
                {
                  offset: 1,
                  color: '#69A2FF'
                }
              ],
              globalCoord: false
            }
          ],
          data: [percent, percent],
          backgroundStyle: {
            borderWidth: 2,
            color: {
              type: 'radial',
              x: 0.5,
              y: 0.5,
              r: 1,
              colorStops: [
                {
                  offset: 0,
                  color: '#023A7E' // 0% 处的颜色
                },
                {
                  offset: 1,
                  color: '#83A2FF' // 100% 处的颜色
                }
              ],
              global: false // 缺省为 false
            }
          },
          label: {
            fontSize: 16,
            color: '#fff',
            fontWeight: 'bold',
            formatter: function(params) {
              // 方式1: 使用rich样式的多行文字
              return `{date|${moment(item.enddate).format('YYYYMMDD')}}\n{label|到期日期}`
            },
            rich: {
              date: {
                fontSize: 18,
                fontWeight: 'bold',
                color: '#fff',
                lineHeight: 18
              },
              label: {
                fontSize: 14,
                color: `rgba(255,255,255,0.9)`,
                lineHeight: 16,
                padding: [8, 0, 0, 0]  // [上, 右, 下, 左] 上边距8像素
              },
            }
          },
          outline: {
            borderDistance: 0,
            itemStyle: {
              borderWidth: 2,
              borderColor: '#81BAFF80'
            }
          },
          waveAnimation: true,
          animationDuration: 2000
        }
      })

      const xArr = ['16%', '50%', '83%']  // 对齐球心位置
      
      const titleData = this.chartData.map((item, index) => {
        // 处理文本换行，如果超过指定长度则换行
        const text = item.filenum || ''
        const maxCharsPerLine = 9 // 增加每行字符数，让文字换两行
        let formattedText = ''
        
        if (text.length > maxCharsPerLine) {
          for (let i = 0; i < text.length; i += maxCharsPerLine) {
            if (i > 0) formattedText += '\n'
            formattedText += text.substr(i, maxCharsPerLine)
          }
        } else {
          formattedText = text
        }
        
        return {
          text: `{titleText|${formattedText}}`,
          left: xArr[index],
          top: '62%',
          textAlign: 'center',
          textStyle: {
            rich: {
              titleText: {
                fontSize: 16,
                fontWeight: 'normal',
                color: 'rgba(255,255,255,0.9)',
                lineHeight: 21,
                width: 95,
                align: 'center',
                verticalAlign: 'middle'
              }
            }
          }
        }
      })

      var option = {
        title: titleData,
        // title: [
        //   {
        //     text: '本年收缴率',
        //     x: '8%',
        //     y: '65%',
        //     textStyle: {
        //       fontSize: 12,
        //       fontWeight: 'normal',
        //       color: '#5dc3ea',
        //       textAlign: 'center'
        //     }
        //   },
        //   {
        //     text: '本月收缴率',
        //     x: '42%',
        //     y: '65%',
        //     textStyle: {
        //       fontSize: 12,
        //       fontWeight: 'normal',
        //       color: '#5dc3ea',
        //       textAlign: 'center'
        //     }
        //   },
        //   {
        //     text: '本季收缴率',
        //     x: '76%',
        //     y: '65%',
        //     textStyle: {
        //       fontSize: 12,
        //       fontWeight: 'normal',
        //       color: '#5dc3ea',
        //       textAlign: 'center'
        //     }
        //   }
        // ],
        series: seriesData
        // series: [
        //   {
        //     type: 'liquidFill',
        //     radius: '40%',
        //     center: ['16%', '50%'],
        //     color: [
        //       {
        //         type: 'linear',
        //         x: 0,
        //         y: 0,
        //         x2: 0,
        //         y2: 1,
        //         colorStops: [
        //           {
        //             offset: 0,
        //             color: '#255BAB'
        //           },
        //           {
        //             offset: 1,
        //             color: '#69A2FF'
        //           }
        //         ],
        //         globalCoord: false
        //       }
        //     ],
        //     data: [value, value],
        //     backgroundStyle: {
        //       borderWidth: 2,
        //       color: {
        //         type: 'radial',
        //         x: 0.5,
        //         y: 0.5,
        //         r: 1,
        //         colorStops: [
        //           {
        //             offset: 0,
        //             color: '#00216F' // 0% 处的颜色
        //           },
        //           {
        //             offset: 1,
        //             color: '#83A2FF' // 100% 处的颜色
        //           }
        //         ],
        //         global: false // 缺省为 false
        //       }
        //     },
        //     label: {
        //       fontSize: 16,
        //       color: '#fff',
        //       fontWeight: 'bold'
        //     },
        //     outline: {
        //       borderDistance: 0,
        //       itemStyle: {
        //         borderWidth: 2,
        //         borderColor: '#81BAFF80'
        //       }
        //     },
        //     waveAnimation: true,
        //     animationDuration: 2000
        //   },
        //   {
        //     type: 'liquidFill',
        //     radius: '40%',
        //     center: ['50%', '50%'],
        //     color: [
        //       {
        //         type: 'linear',
        //         x: 0,
        //         y: 0,
        //         x2: 0,
        //         y2: 1,
        //         colorStops: [
        //           {
        //             offset: 0,
        //             color: '#255BAB'
        //           },
        //           {
        //             offset: 1,
        //             color: '#69A2FF'
        //           }
        //         ],
        //         globalCoord: false
        //       }
        //     ],
        //     data: [value1, value1],
        //     backgroundStyle: {
        //       borderWidth: 2,
        //       color: {
        //         type: 'radial',
        //         x: 0.5,
        //         y: 0.5,
        //         r: 1,
        //         colorStops: [
        //           {
        //             offset: 0,
        //             color: '#00216F' // 0% 处的颜色
        //           },
        //           {
        //             offset: 1,
        //             color: '#83A2FF' // 100% 处的颜色
        //           }
        //         ],
        //         global: false // 缺省为 false
        //       }
        //     },
        //     label: {
        //       fontSize: 16,
        //       color: '#fff',
        //       fontWeight: 'bold'
        //     },
        //     outline: {
        //       borderDistance: 0,
        //       itemStyle: {
        //         borderWidth: 2,
        //         borderColor: '#81BAFF80'
        //       }
        //     },
        //     waveAnimation: true,
        //     animationDuration: 2000,
        //     animationDelay: 500
        //   },
        //   {
        //     type: 'liquidFill',
        //     radius: '40%',
        //     center: ['83%', '50%'],
        //     color: [
        //       {
        //         type: 'linear',
        //         x: 0,
        //         y: 0,
        //         x2: 0,
        //         y2: 1,
        //         colorStops: [
        //           {
        //             offset: 0,
        //             color: '#255BAB'
        //           },
        //           {
        //             offset: 1,
        //             color: '#69A2FF'
        //           }
        //         ],
        //         globalCoord: false
        //       }
        //     ],
        //     data: [value2, value2],
        //     backgroundStyle: {
        //       borderWidth: 2,
        //       color: {
        //         type: 'radial',
        //         x: 0.5,
        //         y: 0.5,
        //         r: 1,
        //         colorStops: [
        //           {
        //             offset: 0,
        //             color: '#00216F' // 0% 处的颜色
        //           },
        //           {
        //             offset: 1,
        //             color: '#83A2FF' // 100% 处的颜色
        //           }
        //         ],
        //         global: false // 缺省为 false
        //       }
        //     },
        //     label: {
        //       fontSize: 16,
        //       color: '#fff',
        //       fontWeight: 'bold'
        //     },
        //     outline: {
        //       borderDistance: 0,
        //       itemStyle: {
        //         borderWidth: 2,
        //         borderColor: '#81BAFF80'
        //       }
        //     },
        //     waveAnimation: true,
        //     animationDuration: 2000,
        //     animationDelay: 1000
        //   }
        // ]
      }

      this.chart.setOption(option)
    }
  }
}
</script>

<style lang="scss" scoped></style>
