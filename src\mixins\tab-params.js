/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023-03-29 15:56:22
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2023-03-29 18:30:38
 * @Description: 全局页签参数
 */

import { mapGetters } from 'vuex'

import { OPERATE_TYPES } from '@jupiterweb/constants'
// 跟据 btnPosition 筛选对应的 buttons
// const getButtonsByPosition = (btnPosition, buttons) => {
//   if (!Array.isArray(buttons)) {
//     return []
//   }

//   if (!btnPosition) {
//     return buttons
//   }

//   return buttons.filter(button => button.btnPosition === btnPosition)
// }
export default {
  computed: {
    ...mapGetters(['systemTime', 'userInfo', 'pageAuth']),
    // 是否是管理员
    isAdmin() {
      return (this.userInfo || {}).admin
    }
  },
  methods: {
    getTabParams(item = {}) {
      const self = this
      const { pageAuth, systemTime, isAdmin, userInfo } = self
      const { YN_Y, BTN_POSITION_OTHER
        // BTN_POSITION_TREE, BTN_POSITION_QUERY, BTN_POSITION_HEAD, BTN_POSITION_LIST
      } = this.$dict
      const { btnList } = item
      // let treeButtons = []
      // let queryButtons = []
      // let headButtons = []
      // let listButtons = []
      const permitdetail = {}
      if (Array.isArray(btnList)) {
        const currPageAuth = pageAuth.filter(p => p.startsWith(item.pageId + '_btn_') || btnList.some(b => b.btnkey === p))

        btnList.forEach((b) => {
          if (!isAdmin && // 非管理员
            b.permittag === '01' && // 受权限控制
            currPageAuth.indexOf(`${b.btnPosition === BTN_POSITION_OTHER ? '' : (item.pageId + '_btn_')}${b.btnkey}`) === -1) { // 没有分配权限
            return
          }
          let parameter = {}
          try {
            parameter = JSON.parse(b.parameter || '{}')
          } catch (e) {
            console.log(e)
          }
          permitdetail[b.btnkey] = {
            ...b,
            ...parameter,
            name: b.btnnm || b.btnkey,
            icon: b.icon
          }
        })

        // treeButtons = getButtonsByPosition(BTN_POSITION_TREE, btnList)
        // queryButtons = getButtonsByPosition(BTN_POSITION_QUERY, btnList)
        // headButtons = getButtonsByPosition(BTN_POSITION_HEAD, btnList)
        // listButtons = getButtonsByPosition(BTN_POSITION_LIST, btnList)
      }

      return {
        // treeButtons,
        // queryButtons,
        // headButtons,
        // listButtons,
        userInfo: userInfo,
        permitdetail,
        menuinfo: { meta: { params: {}}, ...item },
        hasFlow: item.workflowflag === YN_Y || !item.workflowflag,
        params: item.meta && item.meta.params || {}, // 可能会被覆盖
        date: systemTime,
        operateTypes: OPERATE_TYPES,
        moduleName: item.title || item.modulename,
        current: item.moduleid
      }
    }
  }
}
