<!-- 组合收益分析 -> 头部公共查询 -->
<template>
  <div class="benefit-search header-search-panel">
    <el-form ref="form" :model="form">
      <jr-form-item-create :data="cols" :model="form" :column="3" style="line-height: 0;" />
    </el-form>

    <el-button type="primary" class="search-panel" @click="query">
      查询
    </el-button>
  </div>
</template>

<script>
export default {
  data() {
    return {
      form: {},
      cols: [{
        title: '组合名称',
        prop: 'portfolioId',
        type: 'remoteSelect',
        required: true,
        optionValue: 'id',
        api: '/invest/portfolio/ptlincomeanalysis/PtlIncomeAnalysis001/getPortfolioId'
      }, {
        title: '起始日期',
        prop: 'startDate',
        type: 'date'
      }, {
        title: '截止日期',
        prop: 'endDate',
        type: 'date'
      }]
    }
  },
  methods: {
    query() {
      this.$refs.form.validate(valid => {
        if (valid) {
          const { portfolioId, startDate, endDate } = this.form
          const p = {
            portfolioId,
            startDate,
            endDate
          }

          this.$emit('setTargetParams', {
            'dcf3add805c542b493a21a1e5938cad9': p, // 组合收益风险概览
            '29e83352f04b4dc0995444e6f55d4cc0': p, // 组合胜率分析
            '2928b2a7848648758345d0a682297178': p // 组合风险调整后收益率
          })
        }
      })
    }
  }
}
</script>

<style lang="scss">
.benefit-search {
  background: #fff;
  display: flex !important;
  padding: 0 10px;

  .el-form {
    width: 100%;
    flex: 1;
  }

  .search-panel {
    width: 56px;
    margin-top: 4px;
    margin-left: 15px;
  }
}
</style>
