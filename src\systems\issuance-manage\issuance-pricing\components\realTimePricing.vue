<template>
  <div class="realTimePricing">
    <div class="realTimePricing-topSelect">
      <div class="realTimePricing-topSelect-inner">
        <div class="realTimePricing-topSelect-inner-select">
          <label>发行人</label>
          <jr-combobox
            v-model="computedParams.bInfoIssuerCode"
            style="width: 320px"
            :data="issuerList"
            option-label="entname"
            option-value="endid"
            :popper-append-to-body="false"
            placeholder="查询范围包括本企业和对标企业"
          />
        </div>
        <div class="realTimePricing-topSelect-inner-select">
          <label>发行期限</label>
          <jr-radio-group v-model="computedParams.term" :data="termOptions" />
        </div>
        <div class="realTimePricing-topSelect-inner-select">
          <label>发行方式</label>
          <jr-radio-group
            v-model="computedParams.bInfoIssueType"
            :data="bInfoIssuetypeOptions"
            @change="bInfoIssuetypeChange"
          />
        </div>
        <div class="realTimePricing-topSelect-inner-select">
          <label>是否可续期</label>
          <jr-radio-group v-model="computedParams.isRenewal" :data="isRenewalOptions" />
        </div>
        <div class="realTimePricing-topSelect-inner-actions">
          <span @click="openDialog('thresholdSetting')">
            <jr-svg-icon icon-class="dashboard" style="margin-right: 6px; color: var(--theme--color)" />
            <span>阈值设置</span>
          </span>
          <el-button type="primary" @click.stop="getBondListApi">计算</el-button>
        </div>
      </div>
    </div>
    <div class="public-title-container" style="padding-left: 16px">
      <span>参考定价结果</span>
      <span
        style="
          color: var(--theme--color) !important;
          cursor: pointer;
          font-family: MicrosoftYaHei !important;
          cursor: pointer;
        "
        @click.stop="openDialog('realTimePricingHistory')"
      >
        <jr-svg-icon icon-class="check-square" />
        <span style="margin-left: 6px">历史记录</span>
      </span>
    </div>
    <div class="realTimePricing-result">
      <div class="realTimePricing-result-inner">
        <span class="realTimePricing-result-inner-title">定价结果</span>
        <template v-if="priceResult.length > 0">
          <div class="realTimePricing-result-inner-boxes">
            <div
              v-for="(item, index) in priceResult"
              :key="index"
              class="realTimePricing-result-inner-boxes-box"
              :style="{
                backgroundColor: item.color,
                boxShadow: `inset 0px -2px 0px 0px ${item.shadowColor}`
              }"
            >
              <span>{{ item.title }}</span>
              <span>{{ item.value }}</span>
            </div>
          </div>
          <p class="realTimePricing-result-inner-tips" style="margin-top: 32px">
            <jr-svg-icon icon-class="info-circle" />
            <span>参考定价区间不做新券估值结果保证。实际发行定价时，请结合当日市场情况进行综合判断</span>
          </p>
          <p class="realTimePricing-result-inner-tips" style="margin-top: 12px">
            <jr-svg-icon icon-class="info-circle" />
            <span>不提供私募可续期债券，次级债券或有担保债券的发行报价</span>
          </p>
          <p v-if="warningTips" class="realTimePricing-result-inner-tips" style="margin-top: 12px">
            <jr-svg-icon icon-class="info-circle" />
            <span style="color: #f56c6c">{{ warningTips }}</span>
          </p>
        </template>
        <template v-else>
          <jr-empty text="请填写预发行信息进行计算" />
        </template>
      </div>
    </div>
    <div class="public-title-container" style="padding-left: 16px">
      <span>参考债券列表</span>
    </div>
    <div style="width: 100%; height: 516px; background-color: #ffffff; padding: 0px 16px" class="custom-empty-text">
      <jr-table
        :data-source="tableData"
        style="width: 100%"
        :height="416"
        empty-text="暂无参考债券"
        :pagination="pagination"
      >
        <el-table-column prop="bondCode" label="债券代码" />
        <el-table-column prop="bondShortName" label="债券简称" />
        <el-table-column prop="issueDt" label="发行日期" />
        <el-table-column prop="issueTerm" label="发行期限" />
        <el-table-column prop="issueAmount" label="发行规模(亿)" />
        <el-table-column prop="issueCouponrate" label="票面利率(%)" />
        <el-table-column prop="valSpread" label="票面与首次估值利差(BP)" />
        <el-table-column prop="remainTerm" label="剩余期限" />
        <el-table-column prop="exerciseValuation" label="行权估值(%)" />
        <el-table-column prop="maturityValuation" label="到期估值(%)" />
        <el-table-column prop="bondTypeName" label="债券类型" />
        <el-table-column prop="cnbdCreditrating" label="中债隐含评级" />
      </jr-table>
    </div>
    <realTimePricingHistory ref="realTimePricingHistory" />
    <thresholdSetting ref="thresholdSetting" />
  </div>
</template>

<script>
import { issuanceQueryBondList, issuanceQueryIssurList } from '@/api/issuance/issuance.js'
import realTimePricingHistory from '../dialogs/realTimePricingHistory.vue'
import thresholdSetting from '../dialogs/thresholdSetting.vue'
import moment from 'moment'
import { ConvertAmount } from '@jupiterweb/utils/common.js'

export default {
  name: 'RealTimePricing',
  components: {
    realTimePricingHistory,
    thresholdSetting
  },
  data() {
    return {
      // 计算传递参数
      computedParams: {
        bInfoIssuerCode: '',
        bInfoIssuerName: '',
        bInfoIssueType: '',
        term: '',
        isRenewal: ''
      },
      // 发行期限checkBoxList
      termOptions: [
        { value: '3M' },
        { value: '6M' },
        { value: '9M' },
        { value: '1Y' },
        { value: '2Y' },
        { value: '3Y' },
        { value: '5Y' },
        { value: '7Y' },
        { value: '10Y' },
        { value: '15Y' }
      ],
      // 发行方式checkBoxList
      bInfoIssuetypeOptions: [
        { label: '公募', value: 439012000 },
        { label: '私募', value: 439030000 }
      ],
      // 是否可续期checkBoxList
      isRenewalOptions: [
        { label: '不可续期', value: 'N' },
        { label: '可续期', value: 'Y' }
      ],
      tableData: [],
      priceResult: [],
      priceResultAnalysis: [
        {
          title: '发行人',
          key: 'bInfoIssuer',
          color: '#FFF7EB',
          shadowColor: '#F68E06'
        },
        {
          title: '发行期限',
          key: 'term',
          color: '#ECF8FE',
          shadowColor: '#66BDFF'
        },
        {
          title: '是否可续期',
          key: 'isRenewal',
          color: '#ECF0FF',
          shadowColor: '#9C75DD'
        },
        {
          title: '发行方式',
          key: 'bInfoIssuetypename',
          color: '#F5FBF1',
          shadowColor: '#AAD85A'
        },
        {
          title: '估值日期',
          key: 'valuationDt',
          format: 'YYYY-MM-DD',
          color: '#FFEEF0',
          shadowColor: '#E7855E'
        },
        {
          title: '发行参考定价区间',
          key: 'priceRange',
          color: '#ECF8FE',
          shadowColor: '#66BDFF'
        },
        {
          title: '发行参考定价均值',
          key: 'priceAverage',
          color: '#FFF7EB',
          shadowColor: '#F68E06'
        }
      ],
      pagination: {
        pageNo: 1,
        pageSize: 10,
        pageSizeOptions: [10, 15, 25, 50, 100],
        total: 0,
        showSizeChanger: true
      },
      issuerList: [],
      warningTips: ''
    }
  },
  watch: {
    pagination: {
      deep: true,
      handler() {
        this.getBondListApi()
      }
    }
  },
  created() {
    this.getIssuerList()
  },
  mounted() {
    this.$nextTick(() => {
      document.querySelector('.custom-empty-text .el-table__empty-text').querySelector('span').innerText =
        '暂无参考债券'
    })
  },
  methods: {
    formatterDateString(str) {
      return str.replace(/^(\d{4})(\d{2})(\d{2})$/, '$1-$2-$3')
    },
    formatterTable(list) {
      list.forEach((item) => {
        if (item.issueDt) {
          item.issueDt = this.formatterDateString(item.issueDt)
        }
        if (item.issueAmount) {
          item.issueAmount = ConvertAmount('HMU', item.issueAmount, 1, 4)
        }
        if (item.valSpread) {
          item.valSpread = ConvertAmount('rate', item.valSpread / 100, 1, 2)
        }
      })
      return list
    },
    /**
     * 获取发行人下拉项
     */
    async getIssuerList() {
      const data = await issuanceQueryIssurList({})
      this.issuerList = data
    },
    /**
     * 获取参考债券列表
     */
    async getBondListApi() {
      if (!this.computedParams.bInfoIssuerCode) {
        this.$message.warning('请选择发行人')
        return
      }
      if (!this.computedParams.bInfoIssueType) {
        this.$message.warning('请选择发行方式')
        return
      }
      if (!this.computedParams.term) {
        this.$message.warning('请选择发行期限')
        return
      }
      if (!this.computedParams.isRenewal) {
        this.$message.warning('请选择是否可续期')
        return
      }
      this.issuerList.forEach((item) => {
        if (item.endid === this.computedParams.bInfoIssuerCode) {
          this.computedParams.bInfoIssuerName = item.entname
        }
      })

      const data = await issuanceQueryBondList({
        data: {
          ...this.computedParams
        },
        page: {
          pageNo: this.pagination.pageNo,
          pageSize: this.pagination.pageSize
        }
      })

      if (Object.hasOwnProperty.call(data, 'bondList')) {
        this.tableData = Object.hasOwnProperty.call(data.bondList, 'list')
          ? this.formatterTable(data.bondList.list)
          : []
        this.pagination.total = Object.hasOwnProperty.call(data.bondList, 'total') ? data.bondList.total : 0
      }

      if (Object.hasOwnProperty.call(data, 'priceResult')) {
        this.priceResult = this.priceResultAnalysis.map((item) => {
          return {
            ...item,
            value:
              item.key === 'valuationDt'
                ? this.formatterDateString(data.priceResult[item.key])
                : item.key === 'priceAverage'
                ? ConvertAmount('rate', data.priceResult[item.key], 1, 3) + '%'
                : data.priceResult[item.key]
          }
        })
      } else {
        this.priceResult = []
      }

      if (Object.hasOwnProperty.call(data, 'tips')) {
        this.warningTips = data.tips
      } else {
        this.warningTips = ''
      }
    },
    /**
     * 发行方式切换
     */
    bInfoIssuetypeChange() {
      if (this.computedParams.bInfoIssueType === 439030000 && this.computedParams.isRenewal === 'Y') {
        this.computedParams.isRenewal = 'N'
      }
      this.isRenewalOptions = this.isRenewalOptions.map((item) => {
        if (item.label === '可续期') {
          this.$set(item, 'disabled', this.computedParams.bInfoIssueType === 439030000)
        }
        return item
      })
    },
    /**
     * 打开弹框
     */
    openDialog(ref) {
      this.$refs[ref].open()
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .realTimePricing-topSelect-inner-select .jr-radio-group .el-radio {
  min-width: 50px;
}
::v-deep .el-radio__label {
  font-size: var(--el-font-size-base);
}
::v-deep .realTimePricing-topSelect-inner-select .jr-radio-group .el-radio {
  display: inline-flex;
  align-items: center;
}
::v-deep .jr-radio-group .el-radio {
  line-height: 24px;
}
.realTimePricing {
  width: 100%;
  &-topSelect {
    width: 100%;
    background-color: #ffffff;
    padding: 16px 16px 0px;
    // margin-top: 1px;
    &-inner {
      width: 100%;
      height: 100%;
      background-color: #f6f8fd;
      padding: 8px 16px;
      position: relative;
      &-select {
        width: 100%;
        display: flex;
        margin-top: 8px;
        height: 32px;
        align-items: center;
        & > label {
          width: 132px;
          height: 32px;
          font-size: var(--el-font-size-base);
          color: rgba(0, 0, 0, 0.85);
          line-height: 32px;
          flex-shrink: 0;
        }
      }
      &-select:nth-of-type(1) {
        margin-top: 0px;
      }
      &-actions {
        position: absolute;
        right: 16px;
        bottom: 8px;
        cursor: pointer;
        & > span {
          margin-right: 32px;
          & > span {
            height: 22px;
            font-size: var(--el-font-size-base);
            color: var(--theme--color);
            line-height: 22px;
          }
        }
      }
    }
  }
  &-title {
    width: 100%;
    display: flex;
    align-items: center;
    background-color: #ffffff;
    & > span:nth-of-type(1) {
      height: 22px;
      font-size: var(--el-font-size-base);
      color: rgba(0, 0, 0, 0.85);
      line-height: 22px;
      font-weight: 600;
    }
    & > span:nth-of-type(2) {
      height: 22px;
      font-size: var(--el-font-size-base);
      color: var(--theme--color);
      line-height: 22px;
    }
  }
  &-result {
    width: 100%;
    min-width: 100%;
    min-height: 258px;
    background-color: #ffffff;
    padding: 0px 16px;
    &-inner {
      width: 100%;
      min-height: 258px;
      height: 100%;
      overflow: hidden;
      background: linear-gradient(180deg, #fff6ee 0%, #ffffff 26%, #ffffff 100%);
      box-shadow: 2px 2px 8px 0px rgba(55, 90, 170, 0.04), -2px -2px 8px 0px rgba(170, 145, 55, 0.1);
      border-radius: 4px;
      border: 1px solid #f4f4f4;
      padding: 16px;
      &-title {
        height: 22px;
        font-size: var(--el-font-size-base);
        color: rgba(0, 0, 0, 0.9);
        line-height: 22px;
        font-weight: 600;
      }
      &-boxes {
        margin-top: 16px;
        display: flex;
        gap: 8px;
        &-box {
          width: 222px;
          min-width: 222px;
          height: 98px;
          border-radius: 8px;
          padding-top: 24px;
          padding-left: 16px;
          display: flex;
          flex-direction: column;
          gap: 8px;
          & > span:nth-of-type(1) {
            height: 21px;
            font-size: var(--el-font-size-base);
            color: rgba(0, 0, 0, 0.6);
            line-height: 21px;
          }
          & > span:nth-of-type(2) {
            height: 21px;
            font-size: var(--el-font-size-base);
            color: rgba(0, 0, 0, 0.9);
            line-height: 21px;
          }
        }
      }
      &-tips {
        margin-bottom: 0px;
        & > span {
          height: 19px;
          font-size: var(--el-font-size-base);
          color: rgba(0, 0, 0, 0.6);
          line-height: 19px;
        }
      }
    }
  }
}
</style>
