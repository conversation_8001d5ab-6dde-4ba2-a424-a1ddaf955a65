<!--
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2024-01-11 17:46:55
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2024-06-06 14:52:52
 * @Description: 业绩评价组合详情
-->
<template>
  <div class="performance-evaluation-detail">
    <BasicInfo class="layout-header" :data="rowData" :dict-list="dictList" />
    <jr-layout-horizontal class="layout-body">
      <el-timeline slot="left">
        <el-timeline-item>
          <jr-svg-icon slot="dot" color="rgba(0,0,0,.15)" icon-class="list" />业绩表现
        </el-timeline-item>
        <el-timeline-item
          v-for="(activity) in stepData"
          :key="activity.prop"
          :type="activeStep === activity.prop ? 'primary' : ''"
          @click.native="goAnchor(activity.prop)"
        >
          {{ activity.title }}
        </el-timeline-item>
        <!-- <el-timeline-item>
          <jr-svg-icon slot="dot" color="rgba(0,0,0,.15)" icon-class="list" />风格归因
        </el-timeline-item>
        <el-timeline-item
          v-for="(activity) in stepData2"
          :key="activity.prop"
          :type="activeStep === activity.prop ? 'primary' : ''"
          @click.native="goAnchor(activity.prop)"
        >
          {{ activity.title }}
        </el-timeline-item> -->
        <!-- <el-timeline-item>
          <jr-svg-icon slot="dot" color="rgba(0,0,0,.15)" icon-class="list" />评价记录
        </el-timeline-item>
        <el-timeline-item
          v-for="(activity) in stepData3"
          :key="activity.prop"
          :type="activeStep === activity.prop ? 'primary' : ''"
          @click.native="goAnchor(activity.prop)"
        >
          {{ activity.title }}
        </el-timeline-item> -->
      </el-timeline>
      <div slot="right" ref="anchorRef" class="layout-body--content" @scroll="handleScroll">
        <div style="display: flex;justify-content: space-between">
          <jr-form-item-create :model="searchForm" :data="searchConfig" />
          <el-button type="primary" @click="handleQuery">查询</el-button>
        </div>

        <component
          :is="com.component"
          v-for="(com) in stepData"
          :id="com.prop"
          :key="'comp' + com.prop + (com.nosearch ? '' : queryParams.vdate)"
          class="anchor-item"
          :data="{ ...itemData, ...rowData }"
          :params="{...queryParams}"
        />
      </div>
    </jr-layout-horizontal>
  </div>
</template>

<script>
import BasicInfo from './components/basic-info'
import ProfitHistory from './components/profit-history'
import ProfitRollback from './components/profit-rollback'
import ProfitSpread from './components/profit-spread'
import ProfitTrend from './components/profit-trend'
// import ProfitSequence from './components/profit-sequence'
// import ProfitRange from './components/profit-range'
// import EvaluateLog from './components/evaluate-log'
import ProfitRiskIndicator from './components/profit-risk-indicator'
// import ProfitSelectStock from './components/profit-select-stock'

import { GetComboboxList } from '@/api/home'
import * as API from '@/api/poc/performance-evaluation'
import { getModalProps } from '@/systems/mixins'
import moment from 'moment'
import { FormatDate } from 'jupiterweb/src/utils/common'
export default {
  components: {
    BasicInfo
  },
  mixins: [getModalProps],
  data() {
    const platDate = this.platDate = JSON.parse(sessionStorage.platDate || '"2023-08-12"')
    return {
      queryParams: {
        portfolioId: '',
        curveCode: 'POCJZ',
        period: 'Q',
        vdate: moment(new Date(platDate)).subtract(3, 'months').format('YYYY-MM-DD'),
        mdate: platDate
      },
      searchForm: {
        curveCode: 'POCJZ',
        dateRange: 'Q'
      },
      searchConfig: [
        {
          prop: 'curveCode',
          title: '对比指数',
          showCode: false,
          type: 'select',
          options: [
            {
              value: 'POCJZ',
              text: '中债新综合指数(5-7年)财富指数'
            }
          ]
        },
        {
          prop: 'dateRange',
          title: '时间区间',
          type: 'select',
          options: [
            // {
            //   value: 'M',
            //   text: '近1月'
            // },
            {
              value: 'Q',
              text: '近3月'
            },
            {
              value: 'Y',
              text: '近1年'
            // },
            // {
            //   value: 'A',
            //   text: '成立以来'
            }
          ]
        }
      ],
      rowData: {},
      activeStep: 'anchor-1', // 锚点按钮
      dictList: [],
      stepData: [{
        prop: 'anchor-1',
        title: '收益走势',
        component: ProfitTrend
      }, {
        prop: 'anchor-2',
        title: '动态回撤',
        component: ProfitRollback
      }, {
        prop: 'anchor-3',
        title: '历史收益',
        component: ProfitHistory
      }, {
        prop: 'anchor-4',
        title: '历史收益分布',
        component: ProfitSpread
      }, {
      //   prop: 'anchor-5',
      //   title: '相关历史序列',
      //   component: ProfitSequence
      // }, {
      //   prop: 'anchor-6',
      //   title: '阶段收益',
      //   component: ProfitRange
      // }, {
        prop: 'anchor-7',
        title: '收益风险指标',
        nosearch: true,
        component: ProfitRiskIndicator
      }]// ,
      // stepData2: [{
      //   prop: 'anchor-8',
      //   title: '选股择时能力',
      //   nosearch: true,
      //   component: ProfitSelectStock
      // }],
      // stepData3: [{
      //   prop: 'anchor-9',
      //   title: '评价记录',
      //   nosearch: true,
      //   component: EvaluateLog
      // }]
    }
  },
  created() {
    this.$emit('setModalClass', 'performance-evaluation-detail-modal')
    this.init()
    this.getComboboxList()
  },
  methods: {
    async init() {
      const item = this.itemData || { 'investNature': '01', 'mdate': 2556028800000, 'portfolioId': 'HYAXX06-18M04', 'portfolioName': '演示组合8', 'rating': '1', 'ratingDate': 1690732800000, 'tdyUnitValue': 1.04620055, 'vdate': 1651766400000 }
      Object.assign(this.queryParams, {
        portfolioId: item.portfolioId
      })
      const params = { ...item, ratingDate: item.ratingDate ? FormatDate(item.ratingDate, 'yyyy-MM-dd') : undefined }
      this.rowData = { ...await API.GetViewData(params) }
    },
    async getComboboxList() {
      this.dictList = await GetComboboxList(['INVEST_TYPE', 'PTL_ISSUE_MODE', 'PROFIT_TYPE'])
    },
    handleQuery() {
      const { dateRange } = this.searchForm
      const { platDate } = this
      const subdate = (n) => moment(new Date(platDate)).subtract(n, 'months').format('YYYY-MM-DD')
      const map = {
        'M': [subdate(1), platDate],
        'Q': [subdate(3), platDate],
        'Y': [subdate(12), platDate],
        'A': [FormatDate(this.itemData.vdate, 'yyyy-MM-dd'), platDate]
      }
      dateRange && Object.assign(this.queryParams, {
        period: dateRange,
        vdate: map[dateRange][0],
        mdate: map[dateRange][1]
      })
    },
    // 锚点跳转
    goAnchor(selector) {
      // this.activeStep = selector
      requestAnimationFrame(() => {
        document.getElementById(selector).scrollIntoView({ behavior: 'smooth' })
      })
    },
    // 滚动监听器
    handleScroll() {
      const navContents = document.querySelectorAll('.anchor-item')
      const scrollTop = this.$refs.anchorRef.scrollTop
      navContents.forEach((item) => {
        if (scrollTop >= item.offsetTop) {
          this.activeStep = item.id
        }
      })
    }
  }
}
</script>

<style lang="scss">
.performance-evaluation-detail-modal {
  .platform-modal-content {
    padding: 0;
    background: #f5f5f5;
    .el-form {
      padding: 0 !important;
    }
  }
}
.performance-evaluation-detail {
  height: 100%;
  padding: 12px 16px;
  .c-r {
    color: #D9001B;
  }
  .a-r {
    text-align: right;
  }
  .b {
    font-weight: bold;
    font-size: 16px;
  }
  .c-r.b {
    font-size: 28px;
    color: #D9001B;
    font-weight: 650;
    line-height: 42px;
  }
  .el-row>.el-col-24.subtitle {
    line-height: 42px;
  }
  .el-col {
    margin-bottom: 10px;
  }
  .layout-header {
    height: 170px;
    padding: 10px;
    color: #aaa;
    background: #fff;
    line-height: 20px;
    .title {
      font-size: 16px;
      font-weight: bold;
      color: #111;
    }
    .basic-info {
      width: 100%;
      display: flex;
      &>div {
        flex: 1;
        border-right: 1px dashed #ccc;
        padding: 0 15px 0 15px;
        &:first-child {
          padding: 0;
          min-width: 400px;
        }
        &:last-child {
          border-right: none;
        }
      }
    }
    .range .el-col {
      margin-bottom: 0;
      line-height: 28px;
    }
    .detail-text {
      color: rgba(0,0,0,.65);
    }
    .detail {
      background: rgba(242,242,242,.50196078);
      margin: 0 0 0 15px;
      padding: 12px 10px 0 !important;
      color: rgba(0,0,0,.65);
      min-width: 280px;
      span {
        white-space: nowrap;
      }
    }
  }
  .layout-body {
    margin-top: 10px;
    height: calc(100% - 170px - 10px);
    overflow: hidden;
  }
  .jr-layout-horizontal--left {
    padding: 16px 12px;
    overflow: auto;
    .jr-svg-icon {
      font-size: 12px;
      color: rgba(1,0,0,.1);
      background: #fff;
    }
    .el-timeline-item__node {
      border: 1px solid rgba(1,0,0,.1);
      background: #fff;
      &.el-timeline-item__node--primary {
        border-color: var(--theme--color);
        background: var(--theme--color);
      }
    }
    .el-timeline-item__content {
      font-size: 12px;
      cursor: pointer;
    }
  }
  .jr-layout-horizontal--right {
    overflow: hidden;
    .layout-body--content {
      overflow: auto;
      height: 100%;
      position: relative;
      &>div {
        h2 {
          color: #111;
        }
        position: relative;
        padding: 16px;
        margin-bottom: 10px;
        background: #fff;
      }
    }
  }
}
</style>
