<!--
  @文件: index.vue
  @描述: 集团新券行情页面组件
  @功能: 展示债券行情信息，包含债券列表、筛选和图表展示
  @模块: 左侧-债券卡片列表，右侧-筛选条件和图表展示
-->
<template>
  <div ref="chartHeightRef" class="company-value-container">
    <div style="padding: 0 16px">
      <!-- 页面头部标题栏 -->
      <div class="public-title-container">
        {{ $store.getters.sysVersion === $dict.COMPANY_VER_group ? '集团新券行情' : '我司新券行情' }}
      </div>
      <!-- 水平布局组件，左侧为债券列表，右侧为筛选和图表 -->
      <jr-layout-horizontal :width="384" style="background-color: #fff; height: calc(100% - 46px)" :disabled="true">
        <template slot="left">
          <!-- 左侧区域：搜索框和债券卡片列表 -->
          <div class="left-container">
            <!-- 搜索区域 -->
            <div class="search-container">
              <el-input
                v-model="searchKeyword"
                placeholder="请输入债券简称或代码查询"
                suffix-icon="el-icon-search"
                style="height: 32px"
                clearable
                @input="handleSearch"
              />
            </div>

            <!-- 债券卡片列表区域 -->
            <div class="bond-card-list">
              <div
                v-for="(bond, index) in bondList"
                :key="index"
                class="bond-card"
                :class="{ active: searchForm.selectedBondCode && searchForm.selectedBondCode === bond.sInfoWindcode }"
                @click="selectBond(bond)"
              >
                <div class="bond-name">
                  <el-tooltip class="item" effect="dark" :content="bond.sInfoName" placement="top-start">
                    <span>{{ bond.sInfoName }}</span>
                  </el-tooltip>
                </div>
                <div class="bond-rating">{{ bond.compCreditrating }} | {{ bond.bondTypeName2 }}</div>
                <div class="bond-info-list">
                  <div v-sysversion="'group'" class="bond-info-item">
                    <span class="label">发行人</span>
                    <span class="value">{{ bond.bInfoIssuername }}</span>
                  </div>
                  <div class="bond-info-item">
                    <span class="label">发行期限</span>
                    <span class="value">{{ bond.issueTerm }}</span>
                  </div>
                  <div class="bond-info-item">
                    <span class="label">剩余期限</span>
                    <span class="value">{{ bond.remainTerm }}</span>
                  </div>
                  <div class="bond-info-item">
                    <span class="label">最新估值(%)</span>
                    <span class="value">{{ bond.latestValuation ? toFixedDecimal(bond.latestValuation, 4) : '' }}</span>
                  </div>
                  <div class="bond-info-item">
                    <span class="label">涨跌BP</span>
                    <span class="value" :class="bond.riseFallBp > 0 ? 'up' : 'down'">
                      {{ bond.riseFallBp ? toFixedDecimal(bond.riseFallBp, 2) : '' }}
                    </span>
                  </div>
                  <div class="bond-info-item">
                    <span class="label">最新YTM(%)</span>
                    <span class="value">{{ bond.latestYtm ? toFixedDecimal(bond.latestYtm, 4) : '' }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </template>
        <template slot="right">
          <!-- 右侧区域：筛选部分 -->
          <div class="filter-container">
            <el-form ref="form" label-position="left" :model="searchForm" label-width="90px">
              <jr-form-item label="日期区间" class="filter-container-form-item">
                <jr-radio-group v-model="searchForm.dateRange" cancelable :data="dateList" @change="changeDateRadio" />
                <el-date-picker
                  v-model="searchForm.customDateRange"
                  type="daterange"
                  range-separator="至"
                  unlink-panels
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  format="yyyy-MM-dd"
                  class="date-picker"
                  style="width: 280px !important; height: 32px"
                  @change="changeDatePicker"
                />
              </jr-form-item>
              <jr-form-item label="估值类型">
                <!-- 这里需要拉字典项 -->
                <jr-radio-group
                  v-model="searchForm.valtype"
                  cancelable
                  option-label="cnname"
                  :data="radioList"
                  class="width-auto"
                  @change="changeInterval"
                />
              </jr-form-item>
            </el-form>

            <!-- 操作按钮区域 -->
            <div class="filter-actions">
              <el-button type="primary" class="query-btn" @click="searchRight">查询</el-button>
              <el-button class="reset-btn" @click="resetSearcRight">重置</el-button>
            </div>
          </div>

          <!-- 右侧图表区域 -->
          <div class="chart-container">
            <div class="chart-header">
              <div class="chart-actions">
                <el-dropdown @command="handleCommand" @visible-change="visibleChange">
                  <ws-button class="el-dropdown-link" :loading="false">
                    <jr-svg-icon icon-class="upload" />
                    导出
                    <i :class="visible ? 'el-icon-caret-top' : 'el-icon-caret-bottom'" />
                  </ws-button>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item command="tp">
                      <jr-svg-icon icon-class="picture" />
                      导出图片
                    </el-dropdown-item>
                    <el-dropdown-item command="bg">
                      <jr-svg-icon icon-class="table" />
                      导出表格
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
              </div>
            </div>
            <template-module
              v-if="searchForm.selectedBondCode"
              chart-seq="605520115ed14c87afcb66eb3ed8a11e"
              :params="rightParams"
              :height="getChartHeight()"
              :callback="chartCallback"
              :custom-options="customOptions"
            />
          </div>
        </template>
      </jr-layout-horizontal>
    </div>
  </div>
</template>

<script>
import moment from 'moment'
import { GetListData } from '@jupiterweb/api/common/active-module-svr'
import { GetComboboxList } from '@/api/home'
import wsButton from '@/components/ws-button'
import { exportBondMyEstimateInfo } from '@/api/bonds/bonds'
import { debounce, get } from 'lodash'
import templateModule from '@/components/template-module'

/**
 * @组件: 集团新券行情
 * @描述: 展示债券行情信息，提供筛选和查询功能
 * @依赖组件: RatingChart - 用于绘制债券收益率图表
 */
export default {
  components: {
    templateModule,
    wsButton
  },
  data() {
    return {
      // 搜索相关数据
      searchKeyword: '',

      // 当前选中的债券对象
      selectedBond: null,

      // 债券列表样例数据
      bondList: [],
      radioList: [],
      dateList: [
        {
          text: '近1年',
          value: '1'
        },
        {
          text: '近6月',
          value: '2'
        },
        {
          text: '近3月',
          value: '3'
        },
        {
          text: '近1月',
          value: '4'
        }
      ],
      searchForm: {
        dateRange: '1',
        customDateRange: [],
        selectedBondCode: '',
        valtype: ''
      },
      cardParams: {
        ccid: '6d61d587625d429faabb52c851a1cbec',
        ownedModuleid: '708631605142536192'
      },
      rightParams: {},
      customOptions: {
        yAxis: {
          name: '单位(%)',
          nameGap: 32,
          nameTextStyle: {
            padding: [0, 0, 0, 45]
          },
          splitLine: {
            lineStyle: {
              type: 'dashed' // 设置y轴网格线为虚线
            }
          }
        },
        toolbox: {
          show: false
        },
        tooltip: {
          // trigger: 'axis',
          backgroundColor: 'rgba(0,0,0,0.6)',
          borderWidth: '0',
          padding: [12, 16],
          textStyle: {
            color: '#fff',
            fontSize: 14
          },
          formatter: (item) => {
            // ${item.color}
            let content = ''
            content = `${item.name}<br/>
            <span style="margin-right:8px;width:8px;height:8px;border-radius:50%;display:inline-block;background:#E1B01E"></span>
            ${this.selectedBond.sInfoName},${item.value}`
            return content
          }
        },
        legend: {
          show: false
        },
        series: [
          {
            smooth: true,
            lineStyle: {
              color: '#E1B01E',
              type: 'solid',
              width: 2
            },
            itemStyle: {
              color: '#E1B01E'
            }
          }
        ]
      },
      visible: false,
      myChart: null, // 图表实例
      pngName: '集团新券行情' // 图片名称
    }
  },
  computed: {},
  watch: {
    // 监听 searchForm 变化，触发重新获取图表数据
    cardParams: {
      handler(newVal, oldVal) {
        this.cartData()
      },
      deep: true
    }
  },
  created() {
    this.getComboboxList()
    this.searchForm.customDateRange = this.calculateDateRange('1')
    this.cartData()
    this.handleSearch = debounce(this.handleSearch, 500)
  },
  mounted() {},
  methods: {
    visibleChange(v) {
      this.visible = v
    },
    // 数字位数格式化
    toFixedDecimal(num, decimalPlaces) {
      return new Intl.NumberFormat('en-US', {
        minimumFractionDigits: decimalPlaces,
        maximumFractionDigits: decimalPlaces
      }).format(num)
    },
    getChartHeight() {
      const el = this.$refs.chartHeightRef
      if (el) {
        return el.clientHeight - 238 > 450 ? el.clientHeight - 238 : 450 // 可视高度
      }
      return 450
    },
    /**
     * 选择债券的方法
     * @param {Object} bond - 债券对象
     */
    selectBond(bond) {
      this.selectedBond = bond
      this.searchForm.selectedBondCode = bond.sInfoWindcode
      console.log(this.searchForm, 'this.searchForm')
      this.searchRight()

      // 此处可添加获取该债券图表数据的代码
    },
    // 估值单选组改变事件
    changeInterval() {
      console.log(this.valtype)
    },
    // 时间单选组改变事件
    changeDateRadio() {
      this.searchForm.customDateRange = this.calculateDateRange(this.searchForm.dateRange)
    },
    calculateDateRange(rangeType) {
      const today = moment(this.systemTime).format('YYYY-MM-DD')
      let startDate = today

      switch (rangeType) {
        case '1': // 近1年
          startDate = moment(new Date(today)).subtract(1, 'years').format('YYYY-MM-DD')
          break
        case '2': // 近6月
          startDate = moment(new Date(today)).subtract(6, 'months').format('YYYY-MM-DD')
          break
        case '3': // 近3月
          startDate = moment(new Date(today)).subtract(3, 'months').format('YYYY-MM-DD')
          break
        case '4': // 近1月
          startDate = moment(new Date(today)).subtract(1, 'months').format('YYYY-MM-DD')
          break
        default:
          startDate = today
      }

      return [startDate, today]
    },
    // 获取左侧卡片数据
    async cartData() {
      const data = await GetListData({
        // CCID是自定义列id ownedModuleid是菜单id
        params: this.cardParams,
        page: {
          pageNo: 1,
          pageSize: 50 // 卡片不会超过50个 以后有可以根据需求扩展
        }
      })
      const list = get(data, 'pageInfo.list', [])
      console.log(list, 'list')
      // this.list = list || []
      this.bondList = list || []
      console.log(this.bondList, 'this.bondList')

      // 默认选中第一个card
      if (this.bondList.length > 0) {
        this.selectBond(this.bondList[0])
        this.searchRight()
      }
      // 默认选择时间是近1年
      // this.searchForm.dateRange = '1'
      // this.calculateDateRange(this.searchForm.dateRange)
    },
    // 选择日期控件
    changeDatePicker() {
      if (
        (Array.isArray(this.searchForm.customDateRange) && this.searchForm.customDateRange.length === 2) ||
        !this.searchForm.customDateRange
      ) {
        // 主动选择日期控件的时候把时间单选框重置
        this.searchForm.dateRange = ''
      }
    },
    // 查询字典
    async getComboboxList() {
      const data = await GetComboboxList(['VALTYPE'])
      if (data && data.VALTYPE) {
        this.radioList = data.VALTYPE
        // 默认选择第一个
        this.searchForm.valtype = this.radioList[0].value
      }
      console.log(this.radioList, 'this.radioList')
    },
    // 左侧卡片搜索方法
    handleSearch() {
      console.log('防抖500')

      this.cardParams = {
        b_info_issuercode: this.searchKeyword,
        ccid: '6d61d587625d429faabb52c851a1cbec',
        ownedModuleid: '708631605142536192'
      }
      // 这边搜索完 左边卡片选中要去除右边数据也要修改吗
    },
    // 右侧搜索方法
    searchRight() {
      const { customDateRange, selectedBondCode, valtype } = this.searchForm
      if (selectedBondCode === '') return {}
      this.customOptions.yAxis.name = this.radioList.find((item) => item.value === valtype).cnname
      this.rightParams = {
        trade_dtStart: moment(customDateRange[0]).format('YYYY-MM-DD'),
        trade_dtEnd: moment(customDateRange[1]).format('YYYY-MM-DD'),
        valtype,
        s_info_windcode: selectedBondCode,
        b_is_right: this.selectedBond.b_is_right
      }
    },
    // 重置右侧
    resetSearcRight() {
      // 重置左侧？
      this.searchForm.customDateRange = this.calculateDateRange('1')
      this.searchForm.dateRange = '1'
      this.searchForm.valtype = this.radioList[0].value
      this.searchRight()
    },
    // 导出下拉菜单点击事件
    handleCommand(type) {
      if (type === 'tp') {
        // 下载图片
        const exportOptions = {
          backgroundColor: '#ffffff',
          pixelRatio: 2 // 提高导出清晰度（可选）
        }
        // // 生成图片 URL 并触发下载
        const chart = this.myChart
        console.log(chart, 'chart')
        const imgUrl = chart.getDataURL(exportOptions)
        const link = document.createElement('a')
        link.href = imgUrl
        link.download = `${this.getFileName()}_${moment(new Date()).format('YYYYMMDDHHmmss')}.png`
        link.click()
      } else {
        const params = {
          ...this.rightParams,
          chartSeq: '605520115ed14c87afcb66eb3ed8a11e',
          yAxisName: this.customOptions.yAxis.name,
          filename: this.getFileName()
        }
        exportBondMyEstimateInfo(params).then((res) => {
          console.log(res, 'res')
        })
      }
    },
    getFileName() {
      // 集团版
      if (this.$store.getters.sysVersion === this.$dict.COMPANY_VER_group) {
        return '集团债券估值'
      } else {
        return '我司债券估值'
      }
    },
    // templateModule 组件回调函数，获取图表实例
    chartCallback(refData) {
      console.log(refData, 'refData')
      this.myChart = refData
    }
  }
}
</script>

<style lang="scss" scoped>
/**
 * 样式结构:
 * 1. 页面容器 (.company-value-container)
 * 2. 页面头部 (.page-header)
 * 3. 左侧区域样式 (.left-container)
 *    - 搜索区域 (.search-container)
 *    - 债券卡片列表 (.bond-card-list)
 * 4. 右侧区域样式
 *    - 筛选区域 (.filter-container)
 *    - 图表区域 (.chart-container)
 */
.company-value-container {
  height: 100%;
  overflow: hidden;
  background-color: #ffffff;

  /* 页面头部样式 */
  .page-header {
    display: flex;
    align-items: center;
    height: 46px;
    // border-bottom: 1px solid #ebeef5;

    .title {
      height: 22px;
      font-family: MicrosoftYaHeiSemibold;
      font-size: var(--el-font-size-base);
      color: rgba(0, 0, 0, 0.85);
      line-height: 22px;
      text-align: left;
      font-style: normal;
      font-weight: 700;
    }
  }

  /* 左侧容器样式 */
  .left-container {
    height: 100%;
    width: 100%;
    position: relative;
  }

  /* 搜索区域样式 */
  .search-container {
    padding: 0 16px 0 0;
    position: sticky; /* 搜索框固定在顶部 */
    top: 0;
    background-color: #fff;
    z-index: 10;
  }

  /* 债券卡片列表样式 */
  .bond-card-list {
    height: calc(100% - 48px);
    padding-right: 16px;
    overflow-y: auto;
    scrollbar-width: none; /* Firefox隐藏滚动条 */
    -ms-overflow-style: none; /* IE和Edge隐藏滚动条 */

    &::-webkit-scrollbar {
      display: none; /* Chrome, Safari, Opera隐藏滚动条 */
    }

    /* 债券卡片样式 */
    .bond-card {
      // background: #f9f9f9;
      background: linear-gradient(315deg, #fef6eb 0%, #fffbf3 100%);
      border-radius: 8px;
      padding: 21px 24px;
      margin-top: 16px;
      cursor: pointer;
      transition: all 0.3s;
      &:last-child {
        margin-bottom: 16px; /* 移除最后一个卡片的底部间距 */
      }

      .bond-name {
        height: 20px;
        font-family: SourceHanSansCN, SourceHanSansCN;
        font-weight: bold;
        font-size: var(--el-font-size-base);
        color: #000000;
        line-height: 20px;
        text-align: left;
        font-style: normal;
        margin-bottom: 8px;
        // 单行文本溢出省略
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .bond-rating {
        height: 16px;
        font-family: SourceHanSansCN, SourceHanSansCN;
        font-weight: 400;
        font-size: var(--el-font-size-base);
        color: #000000;
        line-height: 16px;
        text-align: left;
        font-style: normal;
        margin-bottom: 16px;
        // 单行文本溢出省略
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      /* 债券信息列表样式 */
      .bond-info-list {
        display: flex;
        flex-direction: column;
        gap: 8px;
        .bond-info-item {
          display: flex;
          justify-content: space-between;

          .label {
            height: 21px;
            font-size: var(--el-font-size-base);
            color: rgba(0, 0, 0, 0.6);
            line-height: 21px;
            text-align: left;
            font-style: normal;
          }

          .value {
            height: 21px;
            max-width: 60%;
            font-size: var(--el-font-size-base);
            color: #000000;
            line-height: 21px;
            text-align: left;
            font-style: normal;
            // 单行文本溢出省略
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;

            /* 涨跌颜色样式 */
            &.up {
              color: #f56c6c; /* 上涨-红色 */
            }

            &.down {
              color: #67c23a; /* 下跌-绿色 */
            }
          }
        }
      }

      &:hover {
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
      }
    }

    /* 选中状态的债券卡片样式 */
    .bond-card.active {
      background: linear-gradient(315deg, #8d89ff 0%, #c7c5ff 100%) !important;
      // background: #a29bfe !important;
      border: none !important;
      box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.03) !important;

      .bond-name,
      .bond-info-item .value {
        color: #ffffff !important;
      }

      .bond-rating,
      .bond-info-item .label {
        color: rgba(255, 255, 255, 0.9) !important;
      }

      /* 选中状态下的涨跌颜色样式 */
      .bond-info-item .value.up {
        color: #ff9ff3 !important; /* 上涨-粉色 */
      }

      .bond-info-item .value.down {
        color: #81ecec !important; /* 下跌-蓝绿色 */
      }
      .down {
        color: #fff; /* 下跌-蓝绿色 */
      }
    }
  }

  /* 右侧筛选容器样式 */
  .filter-container {
    padding: 8px 16px;
    // border-bottom: 1px solid #ebeef5;
    background: #f6f8fd;

    ::v-deep .el-form-item {
      margin-bottom: 8px;

      .el-form-item__label {
        padding-top: 10px;
        font-size: var(--el-font-size-base);
        color: rgba(0, 0, 0, 0.85);
        text-align: left;
        font-style: normal;
      }

      .el-form-item__content {
        height: 32px;
        line-height: 32px;
      }
    }

    ::v-deep .el-range-input {
      font-size: var(--el-font-size-base) !important;
      color: rgba(0, 0, 0, 0.9) !important;
      line-height: 22px !important;
    }

    ::v-deep .el-radio {
      margin-right: 16px !important;
      height: 22px;
      width: auto !important;
      min-width: 0 !important;
      line-height: 22px !important;

      .el-radio__label {
        font-size: var(--el-font-size-base);
        color: rgba(0, 0, 0, 0.85);
        text-align: left;
        font-style: normal;
        height: 22px;
        line-height: 22px;
      }
    }

    /* 筛选区域各部分样式 */
    .filter-section {
      margin-bottom: 20px;
      display: flex;
      align-items: center;
      .section-title {
        margin-bottom: 12px;
        color: #606266;
        font-size: var(--el-font-size-base);
        font-weight: normal;
      }

      /* 日期筛选区域特殊样式 */
      &.date-section {
        .date-filter {
          display: flex;
          justify-content: space-between;
          align-items: center;

          /* 单选按钮组样式 */
          .radio-group-wrapper {
            margin-bottom: 0;

            .el-radio {
              margin-right: 20px;
              line-height: 36px;

              &:last-child {
                margin-right: 0;
              }

              .el-radio__input {
                vertical-align: middle;
              }

              .el-radio__label {
                padding-left: 6px;
              }

              /* Element UI单选框自定义主题色 */
              .el-radio__input.is-checked .el-radio__inner {
                background-color: #f18f01;
                border-color: #f18f01;
              }

              .el-radio__input.is-checked + .el-radio__label {
                color: #606266;
              }
            }
          }
        }
      }

      /* 通用单选按钮组样式 */
      .radio-group-wrapper {
        .el-radio {
          margin-right: 16px;
          // margin-bottom: 10px;

          /* 自定义单选框选中颜色 */
          .el-radio__input.is-checked .el-radio__inner {
            background-color: #f18f01;
            border-color: #f18f01;
          }

          .el-radio__input.is-checked + .el-radio__label {
            color: #606266;
          }
        }
      }

      /* 债券选择器样式 */
      .bond-select {
        width: 100%;
      }
    }

    &-form-item {
      .el-form-item__content {
        display: flex;
        align-items: center;
      }

      // margin-bottom: 10px;
      .jr-radio-group .el-radio {
        min-width: 30px;
        margin-right: 16px;
      }
    }

    /* 筛选操作按钮样式 */
    .filter-actions {
      height: 32px;
      display: flex;
      justify-content: flex-end;
      gap: 8px;

      /* 重置按钮样式 */
      .reset-btn {
        // margin-right: 10px;
        border-radius: 2px;
        padding: 10px 20px;
      }

      /* 查询按钮样式 */
      .query-btn {
        background-color: #f18f01; /* 主题色-橙色 */
        border-color: #f18f01;
        border-radius: 2px;
        padding: 10px 20px;

        &:hover {
          background-color: #e08601;
          border-color: #e08601;
        }
      }
    }
  }

  /* 右侧图表容器样式 */
  .chart-container {
    margin-top: 16px;
    position: relative;
    height: calc(100% - 148px);

    /* 图表头部样式 */
    .chart-header {
      position: absolute;
      top: 0;
      right: 16px;
      z-index: 999;
    }
  }
}
</style>
