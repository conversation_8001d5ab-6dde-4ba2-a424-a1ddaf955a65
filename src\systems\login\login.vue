<template>
  <div class="b-login">
    <div class="b-login--logo" />
    <img v-show="isShowPassword || activeTab === 'code'" src="@/assets/images/login/panda.png" alt="mascot" class="mascot">
    <img v-show="!isShowPassword && activeTab === 'password'" src="@/assets/images/login/panda-close.png" alt="mascot" class="mascot">
    <div class="b-login--form">
      <div class="b-login--form-register" @click="register">
        注册
        <img src="~@/assets/images/login/arrow.png" alt="register" height="21px" width="21px">
      </div>
      <el-tabs v-model="activeTab">
        <el-tab-pane label="普通登录" name="password" />
        <el-tab-pane label="手机验证" name="code" />
      </el-tabs>
      <el-form ref="loginForm" :model="loginForm" :rules="loginRules">
        <jr-form-item prop="userid">
          <el-input
            v-model="loginForm.userid"
            placeholder="请输入注册手机号码"
            @keyup.enter.native="login"
          >
            <template #prefix>
              <img src="~@/assets/images/login/user.png" alt="user">
            </template>
          </el-input>
        </jr-form-item>
        <jr-form-item
          v-if="activeTab === 'password'"
          prop="password"
          @keyup.enter.native="login"
        >
          <el-input v-model="loginForm.password" autocomplete="new-password" :type="isShowPassword ? 'text' : 'password'" placeholder="请输入密码">
            <template #prefix>
              <img src="~@/assets/images/login/pwd.png" alt="password">
            </template>
            <template #suffix>
              <span class="b-login--form-suffix" @click="toggleShowPassword">
                <img v-show="!isShowPassword" src="~@/assets/images/login/pwd-h.png" alt="eye">
                <img v-show="isShowPassword" src="~@/assets/images/login/pwd-o.png" alt="eye">
              </span>
            </template>
          </el-input>
        </jr-form-item>
        <jr-form-item
          v-else-if="activeTab === 'code'"
          prop="code"
          @keyup.enter.native="login"
        >
          <el-input v-model="loginForm.code" :maxlength="10" autocomplete="new-password" class="b-verify--form-code" placeholder="手机验证码">
            <template #prefix>
              <img src="@/assets/images/login/psw.png" alt="code">
            </template>
            <template #append>
              <el-link :type="isVerifyCode ? 'info' : 'primary'" :underline="false" :disabled="isVerifyCode" @click="sendCode">
                <span v-if="!isVerifyCode">获取手机验证码</span>
                <span v-else>{{ timer }}秒后重发</span>
              </el-link>
            </template>
          </el-input>
        </jr-form-item>
        <div :class="{'v-hidden': activeTab !== 'password'}" class="b-login--form-function">
          <el-checkbox v-model="rememberMe">记住密码</el-checkbox>
          <span class="b-login--form-forgot" @click="forgot">忘记密码</span>
        </div>
      </el-form>
      <div class="b-login--form-btn">
        <el-button
          type="primary"
          class="b-block-button"
          :loading="loading"
          @click="login"
        >
          <span v-if="!loading">登 录</span>
          <span v-else>登 录 中...</span>
        </el-button>
      </div>
    </div>
  </div>
</template>

<script>
import Login from '@jupiterweb/components/login.vue'
import { verifyCode } from '@/api/login'
export default {
  name: 'Login',
  extends: Login,
  data() {
    return {
      activeTab: 'password',
      rememberMe: false,
      timer: 60,
      isVerifyCode: false,
      isShowPassword: false,
      loginRules: {
        userid: [
          { required: true, trigger: 'blur', message: '手机号码不能为空' },
          { validator: this.validatePhone, trigger: 'blur' }
        ],
        password: [
          { required: true, trigger: 'blur', message: '密码不能为空' }
        ],
        code: [
          { required: true, trigger: 'blur', message: '短信验证码不能为空' }
        ]
      }
    }
  },
  watch: {
    errorMsg(val) {
      val && this.msgError(val)
    }
  },
  created() {
    const user = localStorage.getItem('__user__')
    if (user) {
      this.loginForm = JSON.parse(atob(user))
      this.rememberMe = true
    }
  },
  methods: {
    toggleShowPassword() {
      this.isShowPassword = !this.isShowPassword
    },
    validatePhone(rule, value, callback) {
      if (value) {
        if (!/^1[3-9]\d{9}$/.test(value)) {
          callback(new Error('手机号格式错误'))
        } else {
          callback()
        }
      } else {
        callback()
      }
    },
    login(e) {
      const isCode = this.activeTab === 'code'
      this.$set(this.loginForm, isCode ? 'password' : 'code', '')
      this.handleLogin(e)
    },
    loginSuccess() {
      const isCode = this.activeTab === 'code'
      if (!isCode) {
        const { userid, password } = this.loginForm
        this.rememberMe ? localStorage.setItem('__user__', btoa(JSON.stringify({ userid, password }))) : localStorage.removeItem('__user__')
      }
    },
    sendCode() {
      const { userid } = this.loginForm
      if (!userid || !/^1[3-9]\d{9}$/.test(userid)) {
        return this.msgError('手机号格式错误')
      }
      this.$emit('openCaptcha', () => {
        this.timer = 60
        this.timerFn()
        this.isVerifyCode = true
        verifyCode({
          type: 'L', // L登录 P注册, F忘记密码
          mobile: userid
        }, (isSuccess, data, message) => {
          isSuccess && this.msgSuccess(message || '验证码已发送')
        })
      })
    },
    timerFn() {
      if (this.timer > 1) {
        this.timer--
        setTimeout(this.timerFn, 1000)
      } else {
        this.isVerifyCode = false
      }
    },
    register() {
      this.$emit('open', 'register')
    },
    forgot() {
      this.$emit('open', 'forgot')
    }
  }
}
</script>

<style lang="scss" scoped>
.b-login {
  width: 100%;
  --el-component-size: 52px;
  ::v-deep .el-tabs__item {
    padding: 11px 16px 13px !important;
    font-size: var(--el-font-size-large);
    color: rgba(0, 0, 0, 0.9);
    line-height: 24px;
    height: auto;
    font-weight: normal;
    &.is-active {
      border-bottom: 2px solid var(--theme--color);
      color: var(--theme--color);
    }
  }
  .el-tabs ::v-deep .el-tabs__header {
    margin-bottom: 24px;
  }
  .mascot {
    width: 164px;
    height: 132px;
    z-index: 1;
    display: block;
    position: relative;
    margin: 42px auto -8px;
  }
  ::v-deep .el-tabs__item {
    padding: 11px 16px 13px !important;
    font-size: var(--el-font-size-large);
    color: rgba(0, 0, 0, 0.9);
    line-height: 24px;
    height: auto;
    font-weight: normal;
    &.is-active {
      border-bottom: 2px solid var(--theme--color);
      color: var(--theme--color);
    }
  }
  &--form {
    display: block;
    position: relative;
    padding: 40px 40px 24px;
    background: #ffffff;
    box-shadow: 0px 6px 16px 0px rgba(0, 0, 0, 0.03), 0px 3px 6px -4px rgba(0, 0, 0, 0.03),
      0px -6px 16px 0px rgba(0, 0, 0, 0.03);
    border-radius: 8px;
    &-function {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: var(--el-font-size-base);
      color: rgba(0, 0, 0, 0.9);
      line-height: 22px;
      margin-top: 16px;
      &.v-hidden {
        visibility: hidden;
      }
      span {
        cursor: pointer;
      }
    }
    &-register {
      position: absolute;
      background: url('~@/assets/images/login/reg.png') no-repeat center center;
      background-size: 100% 100%;
      top: 0;
      right: 0;
      width: 97px;
      height: 39px;
      padding: 6px 0 0 37px;
      font-size: var(--el-font-size-medium);
      color: #fff;
      line-height: 21px;
      cursor: pointer;
      img {
        vertical-align: middle;
      }
    }
    &-forgot {
      cursor: pointer;
    }
    &-btn {
      margin-top: 24px;
    }
  }
}
</style>
