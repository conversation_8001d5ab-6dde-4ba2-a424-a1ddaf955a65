<!--
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2024-01-12 15:36:56
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2024-01-13 16:34:12
 * @Description: 相关历史序列 - 【废除】
-->
<template>
  <div>
    <h2>相关历史序列</h2>
    <el-form class="float-form">
      <jr-form-item-create :model="searchForm" :data="searchConfig" :column="1" />
      <el-button type="primary" @click="handleQuery">查询</el-button>
    </el-form>
    <TemplateModule style="height: 300px;" chart-seq="76d73be6e5944141a4e2cac95a1dd684" :params="{ ...params, ...queryParams }" chart-type="MULTILINE" v-bind="$attrs" />
  </div>
</template>

<script>
import TemplateModule from '@jupiterweb/components/template-module'
export default {
  components: {
    TemplateModule
  },
  props: {
    params: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      queryParams: {
        splitRange: '0.5%'
      },
      searchForm: {
        splitRange: '0.5%'
      },
      searchConfig: [
        {
          prop: 'splitRange',
          title: '窗口',
          type: 'select',
          options: ['当前区间', '3个月', '6个月', '1年', '2年', '3年', '4年', '5年', '10年'].map((a, i) => ({ text: a, value: '0' + (i + 1) }))
        }
      ]
    }
  },
  methods: {
    handleQuery() {
      Object.assign(this.queryParams, {
        ...this.searchForm
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.float-form {
  display: flex;
  justify-content: space-between;
  position: absolute;
  right: 16px;
  top: 20px;
  width: 200px;
  height: 28px;
  .el-form-item__label {
    white-space: nowrap;
  }
  .el-button {
    margin-left: 10px;
  }
}
</style>
