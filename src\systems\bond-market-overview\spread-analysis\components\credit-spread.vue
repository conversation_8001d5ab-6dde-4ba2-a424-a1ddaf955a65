<template>
  <div class="credit-spread" style="height: calc(100% - 56px)">
    <div class="public-radio-group" style="padding: 0 16px">
      <el-radio-group v-model="headerBtns" @input="changeType">
        <el-radio-button label="mtn">中短期票据</el-radio-button>
        <el-radio-button label="lgfv">城投债</el-radio-button>
      </el-radio-group>
    </div>

    <div class="credit-spread-table national-rate">
      <customCharts
        :height="headerBtns === 'mtn' ? '326px' : '280px'"
        :max="spreadMax"
        :rows="tableData"
        :updateDate="updateDate"
        :title="headerBtns === 'mtn' ? '中短期票据信用利差' : '城投债信用利差'"
      />
    </div>

    <div class="credit-spread-chart">
      <chart
        :permitdetail="permitdetail"
        :params="{ headerBtns: headerBtns, flag: headerBtns === 'mtn' ? '0' : '1' }"
        :title="headerBtns === 'mtn' ? '中短期票据信用利差' : '城投债信用利差'"
      />
    </div>
  </div>
</template>

<script>
import wsButton from '@/components/ws-button'
import customCharts from './custom-table-charts'
import chart from './credit-spread-curve-chart'
import { date } from '@/utils/common'
import { queryCreditSpread } from '@/api/bonds/bonds'
import moment from 'moment'
export default {
  name: 'CreditSpread',
  components: { chart, wsButton, customCharts },
  provide() {
    return { parant: this }
  },
  props: {
    // 权限相关
    permitdetail: {
      type: Object,
      default: () => ({})
    },
    menuinfo: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      headerBtns: 'mtn',

      tableData: [],
      form: {
        dimension: '1',
        mainRating: 'AAA-',
        bAnalCurveterm: ['0.5'],
        date: [date().subtract(1, 'y'), date().now()],
        radioDate: 12
      },
      spreadMax: 100,
      allMTNRatings: ['AAA+', 'AAA', 'AAA-', 'AA+', 'AA', 'AA-'],
      allLGFVRatings: ['AAA', 'AA+', 'AA(2)', 'AA', 'AA-'],
      updateDate: ''
    }
  },
  created() {
    this.getTableData()
  },
  methods: {
    changeType() {
      this.getTableData()
    },
    getTableData() {
      queryCreditSpread({
        tab: this.headerBtns
      }).then((data) => {
        this.tableData = this.transformBondData(data)
      })
    },
    getMaxSpread(bondData) {
      // 提取所有spread值
      const spreads = bondData.map((item) => item.spread)

      // 返回最大值，如果没有数据则返回0
      return spreads.length > 0 ? Math.max(...spreads) : 0
    },
    // 返回值改变
    transformBondData(data) {
      // 定义所有可能的 term 值
      const allTerms = ['1Y', '2Y', '3Y', '5Y', '7Y']

      // 定义所有可能的 ATitle 值
      const allRatings = this.headerBtns === 'mtn' ? this.allMTNRatings : this.allLGFVRatings

      // 获取最大值
      this.spreadMax = this.getMaxSpread(data)

      // 获取更新日期
      if (Array.isArray(data) && data.length > 0) {
        this.updateDate = data[0].trade_dt ? moment(data[0].trade_dt).format('YYYY-MM-DD') : ''
      }

      // 首先按rating分组
      const groupedByRating = data.reduce((acc, item) => {
        const rating = item.rating
        if (!acc[rating]) {
          acc[rating] = {}
        }

        // 将term作为key，spread作为value
        acc[rating][item.term] = item.spread

        return acc
      }, {})

      // 转换为目标格式
      return allRatings.map((rating) => {
        const result = { ATitle: rating }

        // 为每个term设置值，如果没有则为0
        allTerms.forEach((term) => {
          result[term] = groupedByRating[rating]?.[term] || 0
        })

        return {
          name: result.ATitle,
          values: [result['1Y'], result['2Y'], result['3Y'], result['5Y'], result['7Y']]
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/assets/styles/national-rate.scss';
@import '../css/credit-spread-temporary.scss';
</style>
