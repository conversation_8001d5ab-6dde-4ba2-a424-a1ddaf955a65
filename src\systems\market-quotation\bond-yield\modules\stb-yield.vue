<template>
  <div class="bazaar" style="padding: 0 16px !important;">
    <h3 class="card-title">中债中短期票据到期收益率</h3>
    <jr-layout-horizontal>
      <template slot="left">
        <card-list ref="cardList" v-bind="{ isType, params, mainTitle, subtitle, ratingInd, ratingCartType }" :on-active="onActive" :rating-fn="ratingFn" />
      </template>
      <template slot="right">
        <form-list
          dict-type=""
          :tips-flag="false"
          v-bind="{ submit, reset, form }"
          :term-flag="false"
        >
          <with-form-input
            v-model="form.dimension"
            label="统计维度"
            :data="dimensionList"
            @change="dimensionChange"
          />
          <with-form-input
            v-model="form.mainRating"
            label="债项评级"
            :data="ratingList"
            option-label="cnname"
            option-value="itemcode"
            :mode="form.dimension === '2' ? 'checkbox' : 'radio'"
          />
          <with-form-input
            v-model="form.bAnalCurveterm"
            :data="termList"
            label="发行期限"
            :min="0"
            :max="10"
            option-label="cnname"
            option-value="itemcode"
            :mode="form.dimension === '1' ? 'checkbox' : 'radio'"
            @change="checkChange"
          />
        </form-list>
        <chart
          :options="chartOptions"
          :name="nameFn()"
        />
      </template>
    </jr-layout-horizontal>
  </div>
</template>

<script>
import formList from '../../components/form-list.vue'
import minxisBus from '../../components/minxisBus'
import { date } from '@/utils/common'
import * as op from '../../components/chartParams'
import chart from '../../components/chart.vue'
import withFormInput from '../../components/ws-form-input.vue'
import cardList from '../../components/card-list.vue'
export default {
  name: 'NationalDebt',
  provide() {
    return { parant: this }
  },
  components: {
    formList,
    withFormInput,
    chart,
    cardList
  },
  mixins: [minxisBus],
  data() {
    return {
      paramsDatas: {},
      mainTitle: '最新利率(%)',
      subtitle: '最新变动BP',
      ratingInd: 'AAA-',
      ratingList: [],
      ratingCartList: [],
      list: [],
      termList: [],
      dimensionList: [
        { value: '1', label: '同评级对比' },
        { value: '2', label: '同期限对比' }
      ],
      chartOptions: op.options,
      isType: '6M',
      form: {
        dimension: '1',
        mainRating: 'AAA-',
        bAnalCurveterm: ['0.5'],
        date: [date().subtract(1, 'y'), date().now()],
        radioDate: 12
      },
      dictList: [],
      params: {
        b_anal_curvename: '中债中短期票据收益率曲线',
        ownedModuleid: '708631605142536192',
        ccid: 'ee39f68a9cb541839e6e419e79629a65',
        main_rating: this.ratingInd,
        bond_term: 'BOND_TERM_MIDSHORT_RATIO',
        b_anal_curveterm: ['0.5', '0.75', '1', '3', '5']
      },
      ratingCartType: 'MIDSHORT_BOND_MAIN_RATING_KAPIAN',
      ratingDictType: 'MIDSHORT_BOND_MAIN_RATING',
      termDictType: 'BOND_TERM_MIDSHORT_RATIO'
    }
  },
  created() {
    this.dict()
    this.echartsData()
  },
  mounted() {
  },
  methods: {
    // echarts  接口
    async echartsData() {
      const { bAnalCurveterm, mainRating } = this.form
      this.paramsDatas = {
        bAnalCurveName: '中债中短期票据收益率曲线',
        ...this.form,
        bAnalCurveterm: Array.isArray(bAnalCurveterm) ? bAnalCurveterm : bAnalCurveterm.split(','),
        mainRating: Array.isArray(mainRating) ? mainRating : mainRating.split(',')
      }
      const chartOptions = await op.getchartData(this.paramsDatas, '/marketdata/market/bondYieldCurve', { smooth: true })
      this.chartOptions = chartOptions
    },
    nameFn() {
      const d = this.form.date.join().replaceAll('-', '').replace(',', '-')
      return `中短期票据收益率_${d}`
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/assets/styles/market.scss'
</style>
