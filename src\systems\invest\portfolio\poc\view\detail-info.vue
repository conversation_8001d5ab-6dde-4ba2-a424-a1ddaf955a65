<!--
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023-04-20 19:49:22
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2023-06-17 14:52:15
 * @Description: 组合明细
-->
<template>
  <div class="home-poc-item has-fullscreen">
    <div class="home-poc-item--header float-left">组合明细 (<font color="red">{{ totalCount }}<fullscreen v-on="{ ...$listeners }" /></font>)
      <!-- <jr-combobox v-model="form.range" style="top: 8px;position: absolute;width: 200px;right: 116px;" type="button" size="mini" :data="RANGE_LIST" @change="queryList" /> -->
    </div>
    <jr-decorated-table
      custom-id="40795d6960ad4606be119bf638726d00"
      :row-click="handleRowClick"
      :custom-render="{
        NetValueChartMap: netValueChartListRender
      }"
      v-bind="{ ...$attrs, params: queryParams, noPagination: true, menuinfo: { pageId: 'PtlDetail', btnList: [{btnkey: 'tiaocang', btnnm: '调仓', expr: '{{indName}}!=null', btnPosition: 'LIST', icon: 'zhuancang'}]} }"
      @refreshed="queryEnd"
    />
  </div>
</template>

<script>
import fullscreen from '../common/fullscreen'
import { FormatDate } from '@jupiterweb/utils/common'
export default {
  components: {
    fullscreen
  },
  data() {
    return {
      totalCount: 0,
      queryParams: {},
      platDate: JSON.parse(sessionStorage.getItem('platDate'))
    }
  },
  created() {
  },
  methods: {
    handleRowClick(item) {
      const p = {
        cdate: FormatDate(item.cdate, 'yyyy-MM-dd'),
        portfolioId: item.portfolioId,
        portfolioName: item.portfolioName
      }
      // 收益贡献：e02cb31f6a5f4ba7a28b0cddd34dc419
      // 持仓：dca8968a87b446cda499ff32583e8358
      this.$emit('setTargetParams', {
        'f4ab86230e764141acb2dadbb8097bb6': p, // 组合净值走势
        '36ecf6030f764238a761c482a0adc4a2': p, // 组合久期
        '99ee0eb3eaf6477b92d7fb434074917e': p, // 组合头寸
        'dca8968a87b446cda499ff32583e8358': p // 组合持仓
      })
    },
    // 净值走势列
    netValueChartListRender(h, { row }) {
      return (<echarts options={this.getChartOptions(row.netValueChartList || [])} />)
    },
    getChartOptions(data) {
      return {
        xAxis: {
          show: false, // 取消显示坐标轴,坐标轴刻度,坐标值(如果是y轴,默认的网格线也会取消显示)
          type: 'category',
          boundaryGap: false,
          splitLine: {
            show: false
          },
          data: data.map(d => FormatDate(d.X, 'yyyy-MM-dd'))
        },
        grid: {
          left: '0',
          top: '5px',
          right: '0',
          bottom: '0',
          containLabel: true
        },
        yAxis: {
          axisLabel: { // 取消显示坐标值
            show: false
          },
          min: Math.min(...data.map(d => d.Y), 1),
          splitLine: { // 取消网格线
            show: false
          },
          type: 'value'
        },
        series: [
          {
            symbol: 'none',
            type: 'line',
            data: data.map(d => d.Y)
          }
        ]
      }
    },
    queryEnd(ins) {
      this.totalCount = ins.pagination.total
    }
  }
}
</script>

<style lang="scss">
</style>

<style lang="scss" scoped>
@import "../common/poc.scss";
</style>
