<template>
  <div class="message-manage-right">
    <div class="message-manage-right-header">
      <div class="message-manage-right-title">
        <span>{{ msgItem.messageName }}提醒设置</span>
      </div>
      <!-- 下月债券到期/行权清单 || 债券到期不显示 -->
      <div
        v-if="!['XYZQDQXQQD', 'ZQDQXQQD'].includes(msgItem.businesstype)"
        class="message-manage-right-header-right"
        @click="handleOpenMessageModal"
      >
        <img src="~@/assets/images/personal/time.png" alt="自定义提醒时间">
        <span>自定义提醒时间</span>
      </div>
    </div>
    <div class="message-manage-right-content">
      <template v-if="isZXFXDF">
        <div v-for="row in bondTypeOption" :key="row.value" class="message-manage-right-content-item">
          <div class="message-manage-right-content-item-title">
            <span>{{ row.text }}</span>
          </div>
          <div class="message-manage-right-content-item-content">
            <span
              v-for="(subitem, idx) in config.ZXFXDF[row.value]"
              :key="subitem.configDay"
              class="message-manage-right-content-subitem"
            >
              <span class="message-manage-right-content-subitem-label">
                {{ getLabel(subitem.dateType, subitem.configDay) }}
              </span>
              <el-switch
                v-model="config.ZXFXDF[row.value][idx].status"
                active-value="Y"
                inactive-value="N"
                @change="handleSaveMessageConfig"
              />
            </span>
          </div>
        </div>
      </template>
      <template v-else>
        <div class="message-manage-right-content-item flex-row">
          <div
            v-for="(subitem, idx) in config[msgItem.businesstype]"
            :key="subitem.configDay"
            class="message-manage-right-content-subitem"
          >
            <span class="message-manage-right-content-subitem-label">
              {{ getLabel(subitem.dateType, subitem.configDay) }}
            </span>
            <el-switch
              v-model="config[msgItem.businesstype][idx].status"
              active-value="Y"
              inactive-value="N"
              @change="handleSaveMessageConfig"
            />
          </div>
        </div>
      </template>
    </div>
    <jr-modal
      ref="messageModal"
      width="640px"
      ok-text="确定"
      :height="isZXFXDF ? 120 : 80"
      modal-class="personal-modal message-frequency-modal"
      :handle-cancel="handleCloseMessageModal"
      :handle-ok="handleSaveMessageConfigModal"
      :visible="messageModalVisible"
    >
      设置自定义提醒时间
      <template #body>
        <el-form ref="messageModalForm" :model="customConfig" :label-width="customConfig.dateType === 'M' ? '100px' : '70px'">
          <jr-form-item v-if="isZXFXDF" label="债券类型" prop="bondType" :rules="{ required: true, message: '请选择内容' }">
            <jr-combobox v-model="customConfig.bondType" :data="bondTypeOption" placeholder="请选择内容" clearable />
          </jr-form-item>
          <jr-form-item :label="customConfigLabel.prefix" style="margin-top: 10px" prop="configDay" :rules="{ required: true, message: '请输入天数' }">
            <jr-number-input v-model="customConfig.configDay" :max="999" :append-text="customConfigLabel.suffix" placeholder="请输入天数" />
          </jr-form-item>
        </el-form>
      </template>
    </jr-modal>
  </div>
</template>

<script>
import { getMessageConfigViewConfig, updateMessageConfig } from '@/api/personal'
export default {
  props: {
    msgItem: {
      type: Object,
      default: () => ({})
    },
    bondTypeOption: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      messageModalVisible: false,
      customConfig: {
        dateType: 'W',
        bondType: '',
        configDay: ''
      },
      config: {
        // 债项付息兑付
        ZXFXDF: {}
      }
    }
  },
  computed: {
    // 债项付息兑付
    isZXFXDF() {
      return this.msgItem.businesstype === 'ZXFXDF'
    },
    customConfigLabel() {
      return this.getLabel(this.customConfig.dateType, null, true)
    }
  },
  watch: {
    'msgItem.id': {
      handler() {
        this.getMessageConfigViewConfig()
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    async getMessageConfigViewConfig() {
      const { id, businesstype } = this.msgItem
      const res = await getMessageConfigViewConfig({ configId: id })
      this.customConfig.dateType = Object.values(res).flat()?.[0]?.dateType
      this.$set(this.config, businesstype, this.isZXFXDF ? res : res[businesstype])
    },
    getLabel(dateType, configDay, isCustom = false) {
      const suffix = dateType === 'W' ? '个工作日' : dateType === 'M' ? '号' : '天'
      const prefix = dateType === 'M' ? '到期前一个月' : '提前'
      if (isCustom) {
        return {
          suffix,
          prefix
        }
      }
      return `${prefix}${configDay}${suffix}`
    },
    handleOpenMessageModal() {
      this.messageModalVisible = true
    },
    handleCloseMessageModal() {
      this.messageModalVisible = false
      Object.assign(this.customConfig, {
        bondType: '',
        configDay: ''
      })
    },
    handleSaveMessageConfigModal() {
      const { id: configId, businesstype } = this.msgItem
      const { bondType, ...otherConfig } = this.customConfig
      this.$refs.messageModalForm.validate(valid => {
        if (valid) {
          const item = {
            businesstype,
            configId,
            ...otherConfig,
            configItem: bondType || businesstype,
            dateType: 'W',
            status: 'Y'
          }
          if (this.isZXFXDF) {
            this.config.ZXFXDF[bondType].push(item)
          } else {
            this.config[businesstype].push(item)
          }
          this.handleSaveMessageConfig()
          this.handleCloseMessageModal()
        }
      })
    },
    handleSaveMessageConfig() {
      const businesstype = this.msgItem.businesstype
      let list = this.config[businesstype]
      if (this.isZXFXDF) {
        list = Object.values(list).flat()
      }
      updateMessageConfig(list)
    }
  }
}
</script>

<style lang="scss" scoped>
.message-frequency-modal {
  .el-form {
    width: 396px;
    margin: auto;
  }
}
.message-manage-right-header-right {
  cursor: pointer;
  img {
    margin-right: 10px;
  }
}
.message-manage-right-content {
  padding-top: 40px;
}
.message-manage-right-content-item {
  display: flex;
  align-items: flex-start !important;
  flex-direction: column;
  min-height: 141px;
  width: 100% !important;
  background: linear-gradient(180deg, #eff4ff 0%, #fdfdfd 100%);
  box-shadow: 0px 2px 10px 0px rgba(229, 229, 229, 0.5);
  border-radius: 10px;
  margin-bottom: 28px !important;
  margin-top: 0px !important;
  border: 2px solid #ffffff;
  padding: 20px;
  &.flex-row {
    flex-direction: row;
    align-items: center;
    flex-wrap: wrap;
    min-height: 88px;
    column-gap: 115px;
    row-gap: 24px;
    align-items: center !important;
  }
  .message-manage-right-content-item-title {
    // font-size: var(--el-font-size-large);
    font-weight: 600;
    color: #333333;
    margin-bottom: 32px;
    &::before {
      content: '';
      display: inline-block;
      width: 3px;
      height: calc(var(--el-font-size-large) - 4px);
      margin-right: var(--el-font-size-large);
      background: var(--theme--color);
    }
  }
  .message-manage-right-content-item-content {
    flex: 1;
    align-items: center;
    display: flex;
    flex-wrap: wrap;
    width: 100%;
    column-gap: 115px;
    row-gap: 24px;
  }
  .message-manage-right-content-subitem {
    display: inline-flex;
    align-items: center;
    min-width: 226px;
    justify-content: space-between;
    .message-manage-right-content-subitem-label {
      // font-size: var(--el-font-size-large);
      line-height: 24px;
    }
  }
}
</style>
