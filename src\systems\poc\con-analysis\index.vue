<template>
  <div class="var-poc has-fullscreen">
    <div class="search-form">
      <el-form class="form-items-container four-columns" :model="searchForm">
        <!-- 组合名称 -->
        <jr-form-item label="组合名称">
          <jr-combobox
            v-model="searchForm.portfolioId"
            :data="portfolioList"
            option-value="id"
            show-code
            multiple
            collapse-tags
            remote
            :remote-method="getPortfolioList"
          />
        </jr-form-item>

        <!-- 产品经理 -->
        <jr-form-item label="产品经理">
          <el-input v-model="searchForm.manager" />
        </jr-form-item>

        <!-- 查询日期 -->
        <jr-form-item label="查询日期">
          <el-date-picker v-model="searchForm.cdate" value-format="yyyy-MM-dd" />
        </jr-form-item>

        <!-- 是否穿透 -->
        <jr-form-item label="是否穿透">
          <jr-radio-group v-model="searchForm.isPene" :data="YNList" />
        </jr-form-item>
      </el-form>
      <div class="btn-div">
        <el-button type="primary" @click="handleQuery">查询</el-button>
        <el-button>重置</el-button>
      </div>
    </div>

    <el-tabs v-model="activeName" @tab-click="handleTabClick">
      <el-tab-pane label="个券集中度" name="first">
        <div class="echarts-box">
          <div class="echarts-item">
            <h4>持仓权重</h4>
            <echarts :options="options1" :styles="{ height: '100%' }" />
          </div>
        </div>
        <div>
          <div class="home-poc-item--header float-left">集中度明细</div>
          <jr-decorated-table
            v-if="searchForm.portfolioId.length"
            ref="first"
            style="min-height: 300px"
            custom-id="5ea6e2b6674043859ef0fb63e416a30f"
            v-bind="{
              ...$attrs,
              params: queryParams,
              noPagination: true,
              menuinfo: {
                pageId: 'Monitor',
              }
            }"
            @refreshed="queryEnd"
          />
        </div>
      </el-tab-pane>
      <el-tab-pane label="发行人集中度" name="second">
        <div class="echarts-box">
          <div class="echarts-item">
            <h4>持仓权重</h4>
            <echarts v-if="activeName === 'second'" :options="options2" :styles="{ height: '100%' }" />
          </div>
        </div>
        <div>
          <div class="home-poc-item--header float-left">集中度明细</div>
          <jr-decorated-table
            v-if="searchForm.portfolioId.length"
            ref="second"
            style="min-height: 300px"
            custom-id="515bb03152d6483aab610a464e90c78a"
            v-bind="{
              ...$attrs,
              params: queryParams,
              noPagination: true,
              menuinfo: {
                pageId: 'Monitor2',
              }
            }"
            @refreshed="queryEnd2"
          />
        </div>
      </el-tab-pane>
      <el-tab-pane label="行业集中度" name="third">
        <div class="echarts-box">
          <div class="echarts-item">
            <h4>持仓权重</h4>
            <echarts v-if="activeName === 'third'" :options="options3" :styles="{ height: '100%' }" />
          </div>
        </div>
        <div>
          <div class="home-poc-item--header float-left">集中度明细</div>
          <jr-decorated-table
            v-if="searchForm.portfolioId.length"
            ref="third"
            style="min-height: 300px"
            custom-id="c33dacdbda984fd08e025b54c9364825"
            v-bind="{
              ...$attrs,
              params: queryParams,
              noPagination: true,
              menuinfo: {
                pageId: 'Monitor3'
              }
            }"
            @refreshed="queryEnd3"
          />
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
// import fullscreen from './fullscreen'
import { GetInfoFn } from 'jupiterweb/src/utils/api'
import echarts from '@jupiterweb/components/echarts'
export default {
  components: {
    // fullscreen
    echarts
  },
  data() {
    const baseForm = {
      portfolioId: [],
      cdate: JSON.parse(sessionStorage.getItem('platDate')),
      isPene: 'N'
    }
    return {
      searchForm: {
        ...baseForm
      },
      portfolioList: [],
      YNList: [
        {
          text: '不穿透',
          value: 'N'
        },
        {
          text: '穿透',
          value: 'Y'
        }
      ],
      queryParams: {
        ...baseForm
      },
      options1: {},
      options2: {},
      options3: {},
      showAllSearch: false,
      activeName: 'first'
    }
  },
  created() {
    this.getPortfolioList('')
  },
  methods: {
    handleQuery() {
      Object.assign(this.queryParams, {
        ...this.searchForm
      })
      this.$refs[this.activeName]?.triggerTableRefresh(true, false)
    },
    handleTabClick({ name }) {
      requestAnimationFrame(() => {
        this.$refs[name]?.triggerTableRefresh(true, false)
      })
    },
    setOptions(xAxisData, series) {
      const options = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        legend: {},
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: [
          {
            type: 'category',
            data: xAxisData
            // axisLine: {
            //   show: true,
            //   interval: 0,
            //   lineStyle: {
            //     width: 1,
            //     type: 'solid'
            //   }
            // },
            // axisTick: {
            //   show: false
            // },
            // axisLabel: {
            //   show: true
            // }
          }
        ],
        yAxis: [
          {
            type: 'value'
            // axisTick: {
            //   show: false
            // },
            // axisLine: {
            //   show: false,
            //   lineStyle: {
            //     width: 1,
            //     type: 'solid'
            //   }
            // },
            // splitLine: {
            //   lineStyle: {
            //   }
            // }
          }
        ],
        series: [
          {
            name: series[0].name,
            type: 'bar',
            barWidth: 20,
            data: series[0].data
          },
          {
            name: series[1].name,
            type: 'bar',
            barWidth: 20,
            data: series[1].data
          }
        ]
      }
      return options
    },
    filterSeries(data, flag) {
      const arr = []
      if (flag === 1) {
        let xdata = []
        const nameList = [
          {
            text: '持仓集中度%',
            value: 'concentration'
          },
          {
            text: '持仓权重%',
            value: 'weight'
          }
        ]
        nameList.map((item) => {
          xdata = data.map((v) => {
            return v[item.value] * 100
          })
          arr.push({ name: item.text, data: xdata })
        })
      }
      return arr
    },
    filterXaxis(data, key) {
      const list = []
      data.map((item) => list.push(item[key]))
      return list
    },
    queryEnd(ins) {
      const tableData = ins.listData.slice(0, 10)
      this.options1 = this.setOptions(this.filterXaxis(tableData, 'finprodName'), this.filterSeries(tableData, 1))
    },
    queryEnd2(ins) {
      const tableData = ins.listData.slice(0, 10)
      this.options2 = this.setOptions(this.filterXaxis(tableData, 'customerName'), this.filterSeries(tableData, 1))
    },
    queryEnd3(ins) {
      const dict = ins.columns.find((v) => v.prop === 'industry')?.filters || []
      const tableData = ins.listData.slice(0, 10).map(l => ({ ...l, industryName: dict.find(d => d.value === l.industry)?.text || l.industry }))
      this.options3 = this.setOptions(this.filterXaxis(tableData, 'industryName'), this.filterSeries(tableData, 1))
    },
    toggleSearchField() {
      this.showAllSearch = !this.showAllSearch
    },
    getPortfolioList(search) {
      const params = {
        search,
        page: 1,
        length: 100
      }
      GetInfoFn('/invest/portfolio/ptloverview/PtlOverview001/getPortfolioId', params).then((res) => {
        this.portfolioList = res.root
        if (res.root?.length > 0 && !this.queryParams.portfolioId.length) {
          this.queryParams.portfolioId = [res.root[0].id]
          this.searchForm.portfolioId = [res.root[0].id]
        }
      })
    }
  }
}
</script>

<style lang="scss"></style>

<style lang="scss" scoped>
.var-poc {
  .search-form {
    display: flex;
    background: #fff;
    margin-bottom: 10px;
    padding: 8px 10px;
    .el-form {
      flex: 1;
      padding-right: 20px;
    }
    .btn-div {
      width: 122px;
      padding-top: 3px;
      position: relative;
      .el-link {
        position: absolute;
        bottom: 0;
        right: 0;
      }
    }
  }
  ::v-deep .el-tabs__header {
    background: #fff;
    padding: 0 10px;
    margin: 0;
  }
  .echarts-box {
    background: #fff;
    height: 400px;
    display: flex;
    justify-content: space-between;
    padding: 10px;
    & > .echarts-item {
      width: 100%;
      border: 1px solid RGB(241, 241, 241);
      h4 {
        margin-bottom: 0;
        padding: 0 10px;
        line-height: 36px;
        height: 36px;
        font-size: 14px;
        background: RGB(250, 250, 250);
        border-bottom: 1px solid RGB(241, 241, 241);
      }
      & > div {
        padding: 10px 10px 30px 10px;
      }
    }
  }
  ::v-deep .jr-radio-group .el-radio {
    min-width: auto !important;
  }
}
// @import "./poc.scss";
</style>
