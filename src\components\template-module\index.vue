<template>
  <echarts :key="chartSeq" :options="options" :styles="{height: height + 'px'}" @callback="callback" />
</template>

<script>
import { GetModuleData } from '@jupiterweb/api/common/template-module'
import echarts from '@jupiterweb/components/echarts'
export default {
  components: {
    echarts
  },
  props: {
    chartSeq: {
      required: true,
      type: String,
      default: ''
    },
    // 默认选中
    selected: {
      type: Array,
      default: () => []
    },
    params: {
      type: Object,
      default: () => ({})
    },
    // 图表类型
    chartType: {
      type: String,
      default: ''
    },
    // 自定义配置
    customOptions: {
      type: [Function, Object],
      default: null
    },
    height: {
      type: Number,
      default: 200
    },
    callback: {
      type: Function,
      default: () => () => {}
    }
  },
  data() {
    return {
      options: {},
      originalOptions: {},
      legendList: []
    }
  },
  watch: {
    selected(val) {
      this.options = JSON.parse(JSON.stringify(this.originalOptions))
      this.options.series = val.length === 0 ? [] : this.options.series.filter(a => val.includes(a.name))
    },
    params: {
      handler() {
        this.init()
      },
      deep: true
    },
    chartSeq() {
      this.init()
    }
  },
  created() {
    this.init()
  },
  methods: {
    async init() {
      const { data, type } = await GetModuleData({ ...this.params, chartSeq: this.chartSeq })
      this.originalOptions = data
      this.options = JSON.parse(JSON.stringify(data))
      if (type === 'MULTILINE') {
        this.legendList = data.series.map(a => a.name)
      }
      this.chartType && this.options.series.forEach(a => {
        a.type = this.chartType
      })
      this.customOptions && this.deepMerge(this.options, typeof this.customOptions === 'function' ? this.customOptions(data.series[0]?.data ?? []) : this.customOptions)
      console.log(this.options, 'smooth')
    },
    // 对象深度合并
    deepMerge(obj1, obj2) {
      for (const key in obj2) {
        if (obj1[key] && obj2[key] && typeof obj1[key] === 'object' && typeof obj2[key] === 'object' && Object.prototype.toString.call(obj1[key]) === Object.prototype.toString.call(obj2[key])) {
          this.deepMerge(obj1[key], obj2[key])
        } else {
          obj1[key] = obj2[key]
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped></style>
