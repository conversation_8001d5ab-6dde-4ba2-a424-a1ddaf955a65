<!--
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023-10-25 10:54:32
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2023-10-26 10:51:02
 * @Description: 待转组合表现
-->
<template>
  <article>
    <div class="header">
      <span class="title">待转组合表现</span>
      <div class="search-list">
        <label>组合：</label><jr-combobox v-model="indexType" :data="indexTypeList" />
        <label>指标：</label><jr-combobox v-model="status" :data="statusList" />
        <label>日期：</label><el-date-picker v-model="rangeDate" type="daterange" />
        <el-radio-group v-model="combo" size="mini">
          <el-radio-button v-for="com in comboxList" :key="com" :label="com" />
        </el-radio-group>
      </div>
    </div>
    <section class="body">
      <echarts :options="chartOptions" />
    </section>
  </article>
</template>

<script>
import echarts from '@jupiterweb/components/echarts'
export default {
  components: { echarts },
  data() {
    const platDate = JSON.parse(sessionStorage.getItem('platDate'))
    return {
      height: null,
      rangeDate: [platDate.substring(0, 8) + '01', platDate],
      indexType: '组合002',
      indexTypeList: ['组合002', '组合003'].map(t => ({ text: t, value: t })),
      status: '单位净值',
      statusList: ['单位净值', '其他'].map(t => ({ text: t, value: t })),
      combo: '图',
      comboxList: ['图', '表'],
      chartOptions: {

        tooltip: {
          trigger: 'axis',
          axisPointer: { type: 'cross' }
        },
        grid: {
          right: 80,
          left: 80,
          top: 30
        },
        legend: {
          bottom: 10,
          data: ['单位净值', '净值涨跌幅']
        },
        xAxis: [
          {
            type: 'category',
            axisTick: {
              alignWithLabel: true
            },
            data: ['2022-08-01', '2022-08-02', '2022-08-03', '2022-08-04', '2022-08-05', '2022-08-06', '2022-08-07', '2022-08-08', '2022-08-09', '2022-08-10', '2022-08-11', '2022-08-12', '2022-08-13', '2022-08-14', '2022-08-15', '2022-08-16']
          }
        ],
        yAxis: [
          {
            type: 'value',
            name: '',
            min: 1.0805,
            max: 1.0855,
            position: 'left',
            axisLabel: {
              formatter: '{value} '
            }
          },
          {
            type: 'value',
            name: '',
            min: -0.3000,
            max: 0.1500,
            position: 'right',
            axisLabel: {
              formatter: '{value} %'
            }
          }
        ],
        series: [
          {
            name: '单位净值',
            type: 'bar',
            barWidth: 20,
            data: [
              1.0849,
              1.0822,
              1.0825,
              1.0821,
              1.0824,
              1.0824,
              1.0824,
              1.0834,
              1.0835,
              1.0831,
              1.0843,
              1.0848,
              1.0848,
              1.0848,
              1.0833,
              1.0839
            ]
          },
          {
            name: '净值涨跌幅',
            type: 'line',
            yAxisIndex: 1,
            data: [
              0.1107,
              -0.2489,
              0.0277,
              -0.0370,
              0.0277,
              0.0000,
              0.0000,
              0.0924,
              0.0092,
              -0.0369,
              0.1108,
              0.0461,
              0.0000,
              0.0000,
              -0.1383,
              0.0554
            ]
          }
        ]
      }
    }
  }
}
</script>

<style lang="scss" scoped></style>
