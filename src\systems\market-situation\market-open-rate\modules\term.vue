<template>
  <div class="page-market-rate--term">
    <div class="page-market-rate--term-title">
      <span>发行期限选择<span class="color-warning">(上限10条)</span></span>
      <el-button type="text" @click="form.termSelected = []"><jr-svg-icon icon-class="sync" /> 重置</el-button>
    </div>
    <div class="page-market-rate--term-content">
      <jr-checkbox-group v-model="form.termSelected" :data="termList" :max="10" />
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      termList: [{
        text: '1M',
        value: '1M'
      }, {
        text: '2M',
        value: '2M'
      }, {
        text: '3M',
        value: '3M'
      }, {
        text: '6M',
        value: '6M'
      }, {
        text: '9M',
        value: '9M'
      }, {
        text: '1Y',
        value: '1Y'
      }, {
        text: '2Y',
        value: '2Y'
      }, {
        text: '3Y',
        value: '3Y'
      }, {
        text: '4Y',
        value: '4Y'
      }, {
        text: '5Y',
        value: '5Y'
      }, {
        text: '6Y',
        value: '6Y'
      }, {
        text: '7Y',
        value: '7Y'
      }, {
        text: '8Y',
        value: '8Y'
      }, {
        text: '9Y',
        value: '9Y'
      }, {
        text: '10Y',
        value: '10Y'
      }, {
        text: '15Y',
        value: '15Y'
      }, {
        text: '20Y',
        value: '20Y'
      }, {
        text: '25Y',
        value: '25Y'
      }, {
        text: '30Y',
        value: '30Y'
      }],
      form: {
        termSelected: ['10Y']
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.page-market-rate--term {
  width: 300px;
  height: 100%;
  padding: 0 12px;
  background-color: #f5f5f5;
  overflow: auto;
  .page-market-rate--term-title {
    display: flex;
    line-height: 40px;
    justify-content: space-between;
    align-items: center;
    span {
      font-size: 14px;
    }
  }
  .color-warning {
    color: var(--el-theme-color-warning);
  }
  .page-market-rate--term-content {
    height: calc(100% - 50px);
    width: 100%;
    min-width: 268px;
    overflow-x: hidden;
    ::v-deep.el-checkbox {
      display: inline-flex;
      flex-direction: row-reverse;
      justify-content: space-between;
      background: #fff;
      margin: 0 10px 10px 0px;
      padding: 6px 10px;
      width: 124px;
      align-items: center;
    }
    ::v-deep .jr-checkbox-group > label:nth-child(2n) {
      margin-right: 0;
    }
  }
}
</style>
