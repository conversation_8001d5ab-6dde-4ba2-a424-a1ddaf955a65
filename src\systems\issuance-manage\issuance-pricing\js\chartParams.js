import { customRequest } from '@/api/public/public'
import { deepClone } from '@jupiterweb/utils/common'
/** *
 * tooltio
 * 公共样式
 * **/
function getCircleStyle(item) {
  return `<div syle="margin-bottom: 5px;">
    <span style="display: inline-block;width:12px;height:12px;border-radius:50%;background:${item.color};"></span>${item.name},${item.value}
  `
}
const formatter = (data) => {
  const fmtData = data || []
  let result = `<div style="margin-bottom: 5px;">时间： ${fmtData[0].axisValue}</div>`
  fmtData.forEach((item) => {
    result += `${getCircleStyle(item)}`
  })
  return result
}

const color = [
  '#165DFF',
  '#13C2C2',
  '#2FC258',
  '#FACC14',
  '#FFB1B1',
  '#FA5151',
  '#BF94F6',
  '#8A38F5',
  '#7786B6',
  '#3D4876'
]

export const options = {
  tooltip: {
    trigger: 'axis',
    formatter
  },
  toolbox: {
    show: false, // 关闭所有工具栏功能
    feature: {
      saveAsImage: { show: true } // 若仅隐藏导出按钮，设为 false
    }
  },
  legend: {
    x: 'center',
    y: 'bottom',
    selectedMode: false,
    padding: [10, 10, 10, 35],
    itemGap: 10,
    itemHeight: 10,
    orient: 'horizontal',
    data: []
  },
  grid: [{ top: 48, bottom: 40, left: 40, right: 60 }],
  xAxis: [
    {
      type: 'category',
      boundaryGap: true,
      axisLabel: { margin: 10, textStyle: { color: '#333', fontSize: 12 }, showMaxLabel: true, show: true },
      splitLine: { show: false, interval: 'auto', lineStyle: { color: '#eee' } },
      axisLine: { lineStyle: { color: ['#50697a'] } },
      axisTick: { show: false }
    }
  ],
  yAxis: [
    {
      type: 'value',
      name: '单位：%',
      axisLabel: { textStyle: { color: '#50697a', fontSize: 12 } },
      splitLine: { lineStyle: { color: '#eee' } },
      axisLine: { lineStyle: { color: '#50697a' } },
      axisTick: { show: false },
      scale: true
    }
  ],
  series: [],
  color
}

export const series = {
  name: '',
  type: 'line',
  symbol: 'circle',
  symbolSize: 1,
  data: [],
  markPoint: {
    symbol: 'emptyCircle',
    symbolSize: 5,
    label: {
      show: true,
      position: 'outside', // 关键配置：标签显示在外侧
      distance: 5, // 标签与点的距离
      color: '#333',
      padding: [4, 8],
      borderRadius: 4,
      fontSize: 14
    },
    data: [
      /* 最高点专属样式 */
      {
        type: 'max',
        itemStyle: {
          color: '#DF4545', // 内部填充色
          borderColor: '#FF3030' // 红色边框
        },
        label: {
          color: '#DF4545' // 文字颜色
        }
      },

      /* 最低点专属样式 */
      {
        type: 'min',
        itemStyle: {
          color: '#66BE74',
          borderColor: '#37A2FF' // 蓝色边框
        },
        label: {
          color: '#66BE74'
        }
      }
    ]
  }
}
/** *
 * 多折线图请求公共方法与处理 echarts样式配置
 * **/
export const getchartData = async (
  params,
  url = '/issueManage/issuePricing/queryHisCurve',
  objParams = { smooth: true }
) => {
  console.log(2)

  const info = await customRequest(url, {
    ...params
  })
  const data = []
  const legend = []
  const seriesNew = deepClone(series)
  const optionsNew = deepClone(options)
  // const colorNew = deepClone(color)
  if (!info || !Object.keys(info)) {
    return optionsNew
  }
  if (Object.keys(info).length > 2) delete seriesNew.markPoint

  Object.keys(info).forEach((k, i) => {
    console.log(k)

    const seriesData = []
    info[k].forEach((g) => {
      seriesData.push([g.X, g.Y, g.LEGEND])
    })
    data.push({ ...seriesNew, data: seriesData, smooth: objParams.smooth, name: k })
    legend.push({ name: k, textStyle: { fontSize: 12, padding: [1, 0, 0, 6], color: '#333' } })
  })
  optionsNew.legend.data = legend
  optionsNew.series = data
  console.log(optionsNew)

  return optionsNew
}
