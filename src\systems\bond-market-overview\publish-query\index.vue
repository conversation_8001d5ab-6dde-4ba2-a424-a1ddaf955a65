<template>
  <!-- 发行查询 -->
  <div class="publish-query">
    <jr-layout-vertical disabled>
      <template v-slot:top>
        <div class="publish-query-form">
          <el-form inline :model="form">
            <jr-form-item class="form-item-large" label="发行状态">
              <el-radio-group v-model="form.listDateType">
                <el-radio v-for="(item, index) in listDateTypeList" :key="index" :label="item" @click.prevent.native="changeRadio(item)">{{ item }}</el-radio>
              </el-radio-group>
            </jr-form-item>
            <jr-form-item v-if="['近期已发行', '推迟发行', '取消发行'].includes(form.listDateType)" label="发行起始日">
              <el-date-picker
                v-model="firstDt"
                type="daterange"
                unlink-panels
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                format="yyyy-MM-dd"
                class="date-picker"
                :clearable="false"
                :picker-options="pickerOptions"
              />
            </jr-form-item>
            <jr-form-item label="主体性质">
              <SelectAutoSetCollapseTages
                ref="compProperty"
                style="max-width: 285px"
                :options="compPropertyData"
                mode="all"
                placeholder="请选择"
                @emitConfirmData="setCompPropertyList"
              />
            </jr-form-item>
            <jr-form-item label="主体评级">
              <SelectAutoSetCollapseTages
                ref="creditRating"
                style="max-width: 285px"
                :options="creditRatingData"
                mode="all"
                placeholder="请选择"
                @emitConfirmData="setCreditRatingList"
              />
            </jr-form-item>
            <jr-form-item label="地区">
              <SelectAutoSetCollapseTages
                ref="districtCode"
                style="max-width: 285px"
                mode="one"
                :options="districtCodeData"
                label-key="cname"
                value-key="id"
                placeholder="请选择"
                @emitConfirmData="setDistrictCodeList"
              />
            </jr-form-item>
            <jr-form-item label="行业">
              <SelectAutoSetCollapseTages
                ref="compindCode"
                style="max-width: 285px"
                mode="one"
                :options="compindCodeData"
                placeholder="请选择"
                @emitConfirmData="setCompindCodeList"
              />
            </jr-form-item>
            <jr-form-item label="债项评级">
              <SelectAutoSetCollapseTages
                ref="bondRating"
                style="max-width: 285px"
                :options="bondRatingData"
                mode="all"
                placeholder="请选择"
                @emitConfirmData="setBondRatingList"
              />
            </jr-form-item>
            <jr-form-item label="债券类型">
              <SelectAutoSetCollapseTages
                ref="bondType"
                style="max-width: 285px"
                mode="one"
                :options="bondTypeData"
                label-key="bondTypeName"
                value-key="bondTypeCode"
                children="list"
                placeholder="请选择"
                @emitConfirmData="setBondTypes"
              />
            </jr-form-item>
            <jr-form-item label="发行期限">
              <SelectAutoSetCollapseTages
                ref="term"
                style="max-width: 285px"
                :options="termData"
                mode="all"
                placeholder="请选择"
                @emitConfirmData="setTermList"
              />
            </jr-form-item>
            <jr-form-item label="关键字">
              <div class="form-item-tip">
                <el-tooltip effect="dark" content="查找范围为债券简称/企业名称/牵头主承销商/联席主承销商" placement="right">
                  <jr-svg-icon icon-class="info-circle" />
                </el-tooltip>
              </div>
              <el-input
                v-model="form.keyWords"
                clearable
                placeholder="请输入关键字"
              />
            </jr-form-item>
            <template v-if="showAllForm">
              <jr-form-item v-if="form.listDateType === '近期已发行'" label="隐含评级">
                <SelectAutoSetCollapseTages
                  ref="cnbdCreditRating"
                  style="max-width: 285px"
                  :options="cnbdCreditRatingData"
                  mode="all"
                  placeholder="请选择"
                  @emitConfirmData="setCnbdCreditRatingList"
                />
              </jr-form-item>
              <jr-form-item v-if="form.listDateType === '近期已发行'" label="创新专项品种">
                <SelectAutoSetCollapseTages
                  ref="productCode"
                  style="max-width: 285px"
                  mode="one"
                  :options="productCodeData"
                  placeholder="请选择"
                  @emitConfirmData="setProductCodeList"
                />
              </jr-form-item>
              <jr-form-item v-if="form.listDateType === '近期已发行'" label="是否有担保">
                <jr-combobox
                  v-model="form.isGuarantorList"
                  placeholder="请选择"
                  clearable
                  multiple
                  filterable
                  collapse-tags
                  :data="isGuarantorData"
                  option-value="value"
                  option-label="label"
                />
              </jr-form-item>
              <jr-form-item label="交易市场">
                <jr-combobox
                  v-model="form.exchmarketList"
                  placeholder="请选择"
                  clearable
                  multiple
                  filterable
                  collapse-tags
                  :data="exchmarketData"
                  option-value="value"
                  option-label="label"
                />
              </jr-form-item>
              <jr-form-item v-if="form.listDateType === '近期已发行'" label="发行截止日">
                <el-date-picker
                  v-model="lastDt"
                  type="daterange"
                  unlink-panels
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  format="yyyy-MM-dd"
                  class="date-picker"
                />
              </jr-form-item>
              <jr-form-item v-if="form.listDateType === '近期已发行'" label="起息日期">
                <el-date-picker
                  v-model="carryDt"
                  type="daterange"
                  unlink-panels
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  format="yyyy-MM-dd"
                  class="date-picker"
                />
              </jr-form-item>
              <jr-form-item v-if="form.listDateType === '近期已发行'" label="兑付日期">
                <el-date-picker
                  v-model="paymentDt"
                  type="daterange"
                  unlink-panels
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  format="yyyy-MM-dd"
                  class="date-picker"
                />
              </jr-form-item>
            </template>
          </el-form>
          <div class="btn-list">
            <el-button type="primary" @click="submit">查询</el-button>
            <el-button @click="reset('近期已发行')">重置</el-button>
          </div>
          <div class="form-ctrl">
            <el-button v-if="!showAllForm" type="text" @click="changeShowAllForm(true)">
              <jr-svg-icon icon-class="down" />
              展开
            </el-button>
            <el-button v-else type="text" @click="changeShowAllForm(false)">
              <jr-svg-icon icon-class="up" />
              收起
            </el-button>
          </div>
        </div>
      </template>

      <template v-slot:bottom>
        <div class="publish-query-content">
          <div class="publish-query-content-btn">
            <el-button type="primary" @click="publishAnalyse">
              <img src="../../../assets/images/icon-query-bar.png">
              发行分析
            </el-button>
          </div>
          <div class="publish-query-content-table">
            <jr-decorated-table
              ref="table"
              stripe
              :key="listDateKey"
              :params="tableParams"
              :menuinfo="menuinfo"
              :permitdetail="{...permitdetail, export: { icon: 'upload', name: '导出', componenturl: 'export', btnkey: 'export' }}"
              :custom-id="tableParams.ccid"
              :listDateType="listDateKey"
              :custom-render="customRender"
              :handleexport="handleexport"
              v-bind="{...$props}"
              @refreshed="callFn"
            />
            <div class="publish-query-content-table-tip">
              <span class="tip-label">
                平均发行期限
                <el-tooltip effect="dark" content="为列表中债券发行期限的平均值，含权债发行期限取行权期限" placement="right">
                  <jr-svg-icon icon-class="info-circle" />
                </el-tooltip>
                :
              </span>
              <span class="tip-val">{{ sumData.sumTerm }}Y</span>
              <span class="tip-label">
                平均利率(%)
                <el-tooltip effect="dark" content="为列表中债券发行利率的平均值" placement="right">
                  <jr-svg-icon icon-class="info-circle" />
                </el-tooltip>
                :
              </span>
              <span class="tip-val">{{ sumData.sumCouponRate }}</span>
              <span class="tip-label">
                票面加权(%)
                <el-tooltip effect="dark" content="为列表中债券发行利率与债券规模的加权平均值" placement="right">
                  <jr-svg-icon icon-class="info-circle" />
                </el-tooltip>
                :
              </span>
              <span class="tip-val">{{ sumData.sumAmountRate }}</span>
            </div>
          </div>
        </div>
      </template>
    </jr-layout-vertical>
    <detail ref="detail" />
  </div>
</template>
<script>
import { regionLink } from '@/api/public/public'
import {
  queryDictBondType,
  queryBondCompindDict,
  queryBondSpecialProductDict,
  getBondSumTotal
} from '@/api/bonds/bonds'
import moment from 'moment'
import SelectAutoSetCollapseTages from '@/components/selectAutoSetCollapseTages'
import { exportExcelByCustomColumn } from '@/api/public/public'
import detail from './dialog/detail.vue'
export default {
  components: {
    SelectAutoSetCollapseTages,
    detail
  },
  props: {
    permitdetail: {
      type: Object,
      default: () => ({})
    },
    menuinfo: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      showAllForm: false,
      tableParams: {
        ccid: 'b53fbde96c40413e90aa0347fc4890e4',
        ownedModuleid: '1352314542536605696',
        listDateType: '近期已发行',
        districtCodeList: []
      },
      column: null,
      sort: null,
      direction: null,
      customRender: {
        sinfoWindcode: (h, { row }) => {
          return (
            <span
              style='color:var(--theme--color);cursor:pointer'
              onClick={() => {
                this.showDetail(row.objectId)
              }}
            >
              {row.sinfoWindcode}
            </span>
          )
        },
        exerciseValuation: (h, { row }) => {
          return (
            <span
              style='color:var(--theme--color);cursor:pointer'
              onClick={() => {
                this.goToValuationDetails('1', row)
              }}
            >
              {row.exerciseValuation}
            </span>
          )
        },
        maturityValuation: (h, { row }) => {
          return (
            <span
              style='color:var(--theme--color);cursor:pointer'
              onClick={() => {
                this.goToValuationDetails('2', row)
              }}
            >
              {row.maturityValuation}
            </span>
          )
        }
      },
      listDateTypeList: ['近期已发行', '今日发行', '即将发行', '推迟发行', '取消发行'],
      form: {
        listDateType: '近期已发行',
        firstDtStart: '',
        firstDtEnd: '',
        compPropertyList: [],
        creditRatingList: [],
        districtCodeList: [],
        compindCodeList: [],
        bondRatingList: [],
        bondTypeList: [],
        termList: [],
        keyWords: '',
        cnbdCreditRatingList: [],
        productCodeList: [],
        isGuarantorList: [],
        exchmarketList: [],
        lastDtStart: '',
        lastDtEnd: '',
        carryDtStart: '',
        carryDtEnd: '',
        paymentDtStart: '',
        paymentDtEnd: ''
      },
      firstDt: [],
      lastDt: [],
      carryDt: [],
      paymentDt: [],
      // 主体性质
      compPropertyData: [
        {
          label: '中央国有企业',
          value: '中央国有企业'
        },
        {
          label: '国有企业',
          value: '国有企业'
        },
        {
          label: '地方国有企业',
          value: '地方国有企业'
        },
        {
          label: '民营企业',
          value: '民营企业'
        },
        {
          label: '集体企业',
          value: '集体企业'
        },
        {
          label: '公众企业',
          value: '公众企业'
        },
        {
          label: '中外合资企业',
          value: '中外合资企业'
        },
        {
          label: '外资企业',
          value: '外资企业'
        },
        {
          label: '外商独资企业',
          value: '外商独资企业'
        },
        {
          label: '其他企业',
          value: '其他企业'
        }
      ],
      // 主体评级
      creditRatingData: [
        {
          label: 'AAA+',
          value: 'AAA+'
        },
        {
          label: 'AAA',
          value: 'AAA'
        },
        {
          label: 'AAA-',
          value: 'AAA-'
        },
        {
          label: 'AA+',
          value: 'AA+'
        },
        {
          label: 'AA',
          value: 'AA'
        },
        {
          label: 'AA-',
          value: 'AA-'
        },
        {
          label: 'A+',
          value: 'A+'
        },
        {
          label: 'A',
          value: 'A'
        },
        {
          label: 'A-',
          value: 'A-'
        },
        {
          label: '其他',
          value: '其他'
        },
        {
          label: '无评级',
          value: '无评级'
        }
      ],
      districtCodeData: [],
      // 行业
      compindCodeData: [],
      // 债项评级
      bondRatingData: [
        {
          label: 'AAA+',
          value: 'AAA+'
        },
        {
          label: 'AAA',
          value: 'AAA'
        },
        {
          label: 'AAA-',
          value: 'AAA-'
        },
        {
          label: 'AA+',
          value: 'AA+'
        },
        {
          label: 'AA',
          value: 'AA'
        },
        {
          label: 'AA-',
          value: 'AA-'
        },
        {
          label: 'A+',
          value: 'A+'
        },
        {
          label: 'A',
          value: 'A'
        },
        {
          label: 'A-',
          value: 'A-'
        },
        {
          label: '其他',
          value: '其他'
        },
        {
          label: '无评级',
          value: '无评级'
        }
      ],
      bondTypeData: [],
      // 发行期限
      termData: [
        {
          label: '7D',
          value: '7D'
        },
        {
          label: '14D',
          value: '14D'
        },
        {
          label: '30D',
          value: '30D'
        },
        {
          label: '60D',
          value: '60D'
        },
        {
          label: '90D',
          value: '90D'
        },
        {
          label: '180D',
          value: '180D'
        },
        {
          label: '270D',
          value: '270D'
        },
        {
          label: '1Y',
          value: '1Y'
        },
        {
          label: '2Y',
          value: '2Y'
        },
        {
          label: '3Y',
          value: '3Y'
        },
        {
          label: '4Y',
          value: '4Y'
        },
        {
          label: '5Y',
          value: '5Y'
        },
        {
          label: '6Y',
          value: '6Y'
        },
        {
          label: '7Y',
          value: '7Y'
        },
        {
          label: '15Y',
          value: '15Y'
        },
        {
          label: '20Y',
          value: '20Y'
        },
        {
          label: '30Y',
          value: '30Y'
        },
        {
          label: '50Y',
          value: '50Y'
        },
        {
          label: '(1+1)Y',
          value: '(1+1)Y'
        },
        {
          label: '(2+1)Y',
          value: '(2+1)Y'
        },
        {
          label: '(2+2)Y',
          value: '(2+2)Y'
        },
        {
          label: '(2+2+2+2)Y',
          value: '(2+2+2+2)Y'
        },
        {
          label: '(2+3)Y',
          value: '(2+3)Y'
        },
        {
          label: '(3+2)Y',
          value: '(3+2)Y'
        },
        {
          label: '(3+3)Y',
          value: '(3+3)Y'
        },
        {
          label: '(2+N)Y',
          value: '(2+N)Y'
        },
        {
          label: '(3+3+3)Y',
          value: '(3+3+3)Y'
        },
        {
          label: '(3+N)Y',
          value: '(3+N)Y'
        },
        {
          label: '(4+N)Y',
          value: '(4+N)Y'
        },
        {
          label: '(5+5)Y',
          value: '(5+5)Y'
        },
        {
          label: '(5+N)Y',
          value: '(5+N)Y'
        },
        {
          label: '(15+5)Y',
          value: '(15+5)Y'
        },
        {
          label: '其他',
          value: '其他'
        }
      ],
      // 隐含评级
      cnbdCreditRatingData: [
        {
          label: 'AAA+',
          value: 'AAA+'
        },
        {
          label: 'AAA',
          value: 'AAA'
        },
        {
          label: 'AAA-',
          value: 'AAA-'
        },
        {
          label: 'AA+',
          value: 'AA+'
        },
        {
          label: 'AA',
          value: 'AA'
        },
        {
          label: 'AA-',
          value: 'AA-'
        },
        {
          label: 'A+',
          value: 'A+'
        },
        {
          label: 'A',
          value: 'A'
        },
        {
          label: 'A-',
          value: 'A-'
        },
        {
          label: '其他',
          value: '其他'
        },
        {
          label: '无评级',
          value: '无评级'
        }
      ],
      // 创新专项品种
      productCodeData: [],
      // 是否有担保
      isGuarantorData: [
        {
          label: '是',
          value: 'Y'
        },
        {
          label: '否',
          value: 'N'
        }
      ],
      // 交易市场
      exchmarketData: [
        {
          label: '银行间',
          value: 'NIB'
        },
        {
          label: '上交所',
          value: 'SSE'
        },
        {
          label: '深交所',
          value: 'SZSE'
        },
        {
          label: '其他',
          value: 'QT'
        }
      ],
      sumData: {
        sumTerm: '',
        sumAmount: '',
        sumCouponRate: '',
        sumAmountRate: ''
      },
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > new Date().getTime()
        }
      },
      listDateKey: '近期已发行',
      rating: null,
      compDefaultVal: null,
      area: null
    }
  },
  computed: {},
  mounted() {
    this.getBondTypes()
    this.getAreaList()
    this.getBondCompindDict()
    this.getBondSpecialProductDict()
    // 首次进入日期处理
    this.resetDefaultDate()
    this.form.firstDtStart = this.firstDt[0]
    this.form.firstDtEnd = this.firstDt[1]
    this.resetDefaultItems()
    this.tableParams = { ...this.tableParams, ...this.form }
    this.setSumData()
  },
  methods: {
    resetDefaultDate() {
      const day = new Date()
      const threeMonthsAgo = new Date()
      threeMonthsAgo.setMonth(day.getMonth() - 3)
      if (day.getDate() !== threeMonthsAgo.getDate()) {
        threeMonthsAgo.setDate(0) // 自动调整到上个月最后一天
      }
      const start = threeMonthsAgo.toISOString().split('T')[0]
      const end = day.toISOString().split('T')[0]
      this.firstDt = [start, end]
    },
    resetDefaultItems() {
      const personInfo = this.$store.getters.personInfo
      // 地区默认值处理
      this.area = personInfo.citycode
      // 主体评级默认值处理
      this.rating = personInfo.rating
      // 主体性质默认值处理
      this.compDefaultVal = personInfo.compProperty
      // 处理查询form
      this.area && (this.form.districtCodeList = [this.area])
      this.rating && (this.form.creditRatingList = [this.rating])
      this.compDefaultVal && (this.form.compPropertyList = [this.compDefaultVal])
      // dom勾选处理
      this.$nextTick(() => {
        this.$refs.creditRating?.selectOne(this.rating)
        this.$refs.compProperty?.selectOne(this.compDefaultVal)
      })
    },
    changeRadio(val) {
      this.reset(val)
    },
    callFn(data) {
      this.column = data.config.column
      this.sort = data.sort
      this.direction = data.direction
    },
    submit() {
      if (this.firstDt?.length > 0) {
        this.form.firstDtStart = moment(this.firstDt[0]).format('YYYY-MM-DD')
        this.form.firstDtEnd = moment(this.firstDt[1]).format('YYYY-MM-DD')
      } else {
        this.form.firstDtStart = ''
        this.form.firstDtEnd = ''
      }
      if (this.lastDt?.length > 0) {
        this.form.lastDtStart = moment(this.lastDt[0]).format('YYYY-MM-DD')
        this.form.lastDtEnd = moment(this.lastDt[1]).format('YYYY-MM-DD')
      } else {
        this.form.lastDtStart = ''
        this.form.lastDtEnd = ''
      }
      if (this.carryDt?.length > 0) {
        this.form.carryDtStart = moment(this.carryDt[0]).format('YYYY-MM-DD')
        this.form.carryDtEnd = moment(this.carryDt[1]).format('YYYY-MM-DD')
      } else {
        this.form.carryDtStart = ''
        this.form.carryDtEnd = ''
      }
      if (this.paymentDt?.length > 0) {
        this.form.paymentDtStart = moment(this.paymentDt[0]).format('YYYY-MM-DD')
        this.form.paymentDtEnd = moment(this.paymentDt[1]).format('YYYY-MM-DD')
      } else {
        this.form.paymentDtStart = ''
        this.form.paymentDtEnd = ''
      }
      this.listDateKey = this.form.listDateType
      this.tableParams = { ...this.tableParams, ...this.form }
      this.setSumData()
    },
    reset(val) {
      this.form = {
        listDateType: val || '',
        firstDt: '',
        compPropertyList: [],
        bondRatingList: [],
        bondTypeList: [],
        termList: [],
        keyWords: '',
        cnbdCreditRatingList: [],
        productCodeList: [],
        isGuarantorList: [],
        exchmarketList: [],
        lastDtStart: '',
        lastDtEnd: '',
        carryDtStart: '',
        carryDtEnd: '',
        paymentDtStart: '',
        paymentDtEnd: ''
      }
      this.firstDt = []
      this.lastDt = []
      this.carryDt = []
      this.paymentDt = []
      this.$refs.compProperty?.selectClear()
      this.$refs.creditRating?.selectClear()
      this.$refs.districtCode?.selectClear()
      this.$refs.compindCode?.selectClear()
      this.$refs.bondRating?.selectClear()
      this.$refs.bondType?.selectClear()
      this.$refs.term?.selectClear()
      this.$refs.cnbdCreditRating?.selectClear()
      this.$refs.productCode?.selectClear()
      if (['近期已发行', '推迟发行', '取消发行'].includes(val)) {
        this.resetDefaultDate()
        this.form.firstDtStart = this.firstDt[0]
        this.form.firstDtEnd = this.firstDt[1]
      }
      this.resetDefaultItems()
      this.$nextTick(() => {
        this.$refs.districtCode.selectSecond(this.area)
      })
    },
    // 查询债券类型
    async getBondTypes() {
      const params = {}
      const res = await queryDictBondType(params) || []
      this.formatBondTypes(res)
      this.bondTypeData = res
    },
    formatBondTypes(data) {
      for (const i of data) {
        if (i.list && i.list.length === 0) {
          i.list = null
        } else if (i.list) {
          this.formatBondTypes(i.list)
        }
      }
    },
    // 查询行业类型
    async getBondCompindDict() {
      const params = {}
      const res = await queryBondCompindDict(params)
      const list = []
      this.deepFormatData(res, list, 'sInfoCompindName', 'sInfoCompindCode', 'list')
      this.compindCodeData = list
    },
    async getBondSpecialProductDict() {
      const params = {}
      const res = await queryBondSpecialProductDict(params)
      const list = []
      this.deepFormatData(res, list, 'dictName', 'dictCode', 'dictList')
      this.productCodeData = list
    },
    // 查询区域
    getAreaList() {
      regionLink().then((res) => {
        this.districtCodeData = this.formatAreaList(res)
        this.$nextTick(() => {
          this.$refs.districtCode.selectSecond(this.area)
        })
      })
    },
    formatAreaList(areaList) {
      let arr = []
      arr = JSON.parse(JSON.stringify(areaList))

      const idMapping = arr.reduce((acc, el, i) => {
        acc[el.id] = i
        return acc
      }, {})

      const root = []
      arr.forEach((el) => {
        // 判断根节点
        if (!el.pid) {
          root.push(el)
          return
        }
        // 用映射表找到父元素
        const parentEl = arr[idMapping[el.pid]]
        if (parentEl) {
          // 把当前元素添加到父元素的`children`数组中
          parentEl.children = [...(parentEl.children || []), el]
        }
      })
      return root
    },
    deepFormatData(data, list, label, value, children) {
      for (const i in data) {
        list.push({
          label: data[i][label],
          value: data[i][value]
        })
        if (data[i][children] && data[i][children].length > 0) {
          list[i].children = []
          this.deepFormatData(data[i][children], list[i].children, label, value, children)
        }
      }
    },
    setCompPropertyList(data) {
      const list = []
      for (const i of data) {
        if (typeof i === 'string') {
          list.push(i)
        } else if (i && typeof i === 'object') {
          list.push(i[0])
        }
      }
      this.form.compPropertyList = list
    },
    setBondTypes(data) {
      this.form.bondTypeList = data
    },
    setDistrictCodeList(data) {
      console.log('???!!!')
      this.form.districtCodeList = data
    },
    setCompindCodeList(data) {
      this.form.compindCodeList = data
    },
    setProductCodeList(data) {
      this.form.productCodeList = data
    },
    setTermList(data) {
      const list = []
      for (const i of data) {
        if (typeof i === 'string') {
          list.push(i)
        } else if (i && typeof i === 'object') {
          list.push(i[0])
        }
      }
      this.form.termList = list
    },
    setCreditRatingList(data) {
      const list = []
      for (const i of data) {
        if (typeof i === 'string') {
          list.push(i)
        } else if (i && typeof i === 'object') {
          list.push(i[0])
        }
      }
      this.form.creditRatingList = list
    },
    setBondRatingList(data) {
      const list = []
      for (const i of data) {
        if (typeof i === 'string') {
          list.push(i)
        } else if (i && typeof i === 'object') {
          list.push(i[0])
        }
      }
      this.form.bondRatingList = list
    },
    setCnbdCreditRatingList(data) {
      const list = []
      for (const i of data) {
        if (typeof i === 'string') {
          list.push(i)
        } else if (i && typeof i === 'object') {
          list.push(i[0])
        }
      }
      this.form.cnbdCreditRatingList = list
    },
    changeShowAllForm(val) {
      this.showAllForm = val
    },
    publishAnalyse() {
      this.$store.dispatch('tagsView/updateCurrentTab', {
        path: '/1375068527660113920',
        meta: { refresh: true, params: {
          sumData: this.sumData,
          formData: this.form
        }}
      })
    },
    setSumData() {
      getBondSumTotal(this.form).then((res) => {
        this.sumData = res
      })
    },
    handleexport() {
      const params = {
        params: {
          pageInfo: {},
          filename: '发行查询',
          column: this.column,
          ccid: 'b53fbde96c40413e90aa0347fc4890e4',
          ownedModuleid: '1352314542536605696',
          ...this.tableParams
        },
        page: {
          sort: this.sort,
          direction: this.direction
        }
      }
      exportExcelByCustomColumn(params)
      return false
    },
    showDetail(objectId) {
      this.$refs.detail.open({ objectId })
    },
    goToValuationDetails(isRight, row) {
      this.$store.dispatch('tagsView/updateCurrentTab', {
        path: '/1361645930918469632',
        // 跳转需要刷新的话 refresh传true
        meta: {
          refresh: true,
          params: {
            comp_name: row.bInfoIssuer,
            s_info_windcode: row.sInfoWindcode,
            s_info_name: row.sInfoName,
            b_is_right: isRight
          }
        }
      })
    }
  }
}
</script>
<style lang="scss">
.publish-query {
  position: relative;
  height: 100%;

  .vertical-layout {
    background: #fff;
    padding: 0;
    height: 100%;

    &--top {
      height: auto !important;
    }

    &--resize {
      height: 1px;
      color: #eae9e9;
    }

    &--bottom {
      margin-top: 16px;
      padding: 0 16px;
    }
  }

  &-form {
    position: relative;

    .el-form {
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      width: calc(100% - 140px);

      .el-form-item {
        width: calc(25% - 8px) !important;

        &.form-item-large {
          width: 100% !important;
        }

        .el-form-item__label {
          padding: 8px 24px 0 0;
          width: 112px;
        }

        .el-form-item__content {
          width: calc(100% - 112px);

          .el-radio {
            height: 32px;
            line-height: 32px;
          }

          .form-item-tip {
            position: absolute;
            top: 50%;
            left: -20px;
            color: rgba(0, 0, 0, 0.6);
            transform: translateY(-50%);
          }
        }

        &.no-label {
          .el-form-item__content {
            width: 100%;
          }
        }
      }
    }

    .btn-list {
      position: absolute;
      top: 8px;
      right: 0;

      .el-button {
        margin-left: 8px;
      }
    }

    .form-ctrl {
      position: absolute;
      top: 90px;
      right: 0;
    }
  }

  &-content {
    position: relative;
    height: 100%;

    &-btn {
      height: 48px;

      .el-button {
        img {
          width: 13px;
          height: 12px;
          transform: translate(-2px, 1px);
        }
      }
    }

    &-table {
      position: relative;
      height: calc(100% - 56px);

      .jr-decorated-table--header-left {
        display: none;
      }

      .jr-decorated-table--header-right {
        position: absolute;
        top: -56px;
        right: -8px;
      }

      .jr-decorated-table--body {
        padding: 0;
        height: 100%;

        .jr-table {
          height: 100%;

          .el-table--border {
            height: calc(100% - 54px) !important;
          }

          .el-table table td.el-table__cell > .cell .el-button {
            padding: 0 !important;
          }
        }
      }

      &-tip {
        position: absolute;
        left: 0;
        bottom: 19px;

        .tip-label {
          color: rgba(0, 0, 0, 0.9);

          span {
            color: rgba(0, 0, 0, 0.6);
          }
        }

        .tip-val {
          margin: 0 24px 0 4px;
          color: var(--theme--color);
        }
      }
    }
  }
}

@media screen and (max-width: 1920px) {
  .publish-query {

    &-form {
      position: relative;

      .el-form {

        .el-form-item {
          width: calc(33.33% - 8px) !important;

          &.form-item-large {
            width: 100% !important;
          }
        }
      }
    }

  }
}
</style>
