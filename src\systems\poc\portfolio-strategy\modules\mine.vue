<!--
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023-10-25 10:54:32
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2023-11-08 10:15:42
 * @Description: 我的策略
-->
<template>
  <article>
    <div class="header">
      <span class="title">我的策略</span>
      <el-button>进入策略列表 > </el-button>
    </div>
    <div class="body">
      <jr-table :columns="columns" :expand-row-keys="['1', '2']" :data-source="dataList" row-key="id" :height="height" :cell-style="cellStyle" />
    </div>
  </article>
</template>

<script>
export default {
  data() {
    return {
      height: null,
      dataList: [
        { id: 1, children: [
          { id: 11, '名称': '股票多头策略', '年化收益率': '15%', '年化波动率': '15%', '最大撤回': '15%', '近一月回撤': '15%', '状态': '正常' },
          { id: 12, children: [{ id: 121 }], '名称': '指数增强策略', '年化收益率': '8%', '年化波动率': '8%', '最大撤回': '8%', '近一月回撤': '8%', '状态': '正常' },
          { id: 13, '名称': '行业周期策略', '年化收益率': '5%', '年化波动率': '5%', '最大撤回': '5%', '近一月回撤': '5%', '状态': '正常' },
          { id: 14, '名称': '大消费策略', '年化收益率': '10%', '年化波动率': '10%', '最大撤回': '10%', '近一月回撤': '10%', '状态': '正常' },
          { id: 15, '名称': '宏观策略', '年化收益率': '15%', '年化波动率': '15%', '最大撤回': '15%', '近一月回撤': '15%', '状态': '正常' }
        ], '名称': '股票权益类策略', '年化收益率': '10%', '年化波动率': '70%', '最大撤回': '10%', '近一月回撤': '10%', '状态': '' },
        { id: 2, children: [
          { id: 24, '名称': '信用债优选策略', '年化收益率': '15%', '年化波动率': '15%', '最大撤回': '15%', '近一月回撤': '15%', '状态': '正常' },
          { id: 25, '名称': '固收+策略优选', '年化收益率': '10%', '年化波动率': '10%', '最大撤回': '10%', '近一月回撤': '10%', '状态': '正常' }
        ], '名称': '固收类策略', '年化收益率': '10%', '年化波动率': '70%', '最大撤回': '10%', '近一月回撤': '10%', '状态': '' }
      ]
    }
  },
  computed: {
    columns() {
      return Object.keys(this.dataList[0]).filter(k => !['id', 'children'].includes(k)).map(item => ({
        prop: item,
        title: item,
        align: ['年化波动率', '年化收益率', '最大撤回', '近一月回撤'].includes(item) ? 'right' : 'left',
        className: ['年化波动率', '年化收益率', '最大撤回', '近一月回撤'].includes(item) ? 'linear-cell' : ''
      }))
    }
  },
  mounted() {
    this.height = this.$el.querySelector('.body').clientHeight - 20
  },
  methods: {
    cellStyle({ row, column }) {
      if (column.columnKey === '转指令状态') {
        return { color: 'red' }
      }
      const prop = column.columnKey
      if (['年化波动率', '年化收益率', '最大撤回', '近一月回撤'].includes(prop)) {
        const val = row[prop]
        return {
          '--wdh': val,
          '--left': '0px',
          '--right': 'unset',
          '--bg': 'linear-gradient(90deg, #57bf57, transparent)'
        }
      }
      return {}
    }
  }
}
</script>

<style lang="scss" scoped>
.body {
  padding: 10px 0;
}
</style>
