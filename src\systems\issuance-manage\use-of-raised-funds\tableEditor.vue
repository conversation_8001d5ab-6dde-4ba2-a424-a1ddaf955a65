<template>
  <div>
    <el-button v-if="couldEdit" icon="el-icon-delete" @click.stop="deleteRaised">删除</el-button>
    <el-form :model="data">
      <el-form-item>
        <jr-table-editor
          ref="editor"
          v-model="data.raisedFundsDetails"
          :columns="columns"
          style="width: 942px"
          :muti-select="couldEdit"
          :show-delete="couldEdit"
          :hiden-add-row="!couldEdit"
          :disabled="!couldEdit"
          @handleSelectionChange="handleSelectionChange"
        >
          <el-table-column prop="amt">
            <template slot="header">
              <p style="width: 100%; text-align: center; margin-bottom: 0px">规模</p>
            </template>
            <template slot-scope="scope">
              <jr-number-input
                v-model="scope.row.amt"
                :precision="4"
                append-text="亿"
                :disabled="!couldEdit"
              />
            </template>
          </el-table-column>
          <el-table-column prop="repayPrincipal">
            <template slot="header">
              <p style="width: 100%; text-align: center; margin-bottom: 0px">已使用本金</p>
            </template>
            <template slot-scope="scope">
              <jr-number-input
                v-model="scope.row.repayPrincipal"
                :precision="2"
                append-text="万元"
                :disabled="!couldEdit"
              />
            </template>
          </el-table-column>
          <el-table-column prop="repayInterest">
            <template slot="header">
              <p style="width: 100%; text-align: center; margin-bottom: 0px">已使用利息</p>
            </template>
            <template slot-scope="scope">
              <jr-number-input
                v-model="scope.row.repayInterest"
                :precision="2"
                append-text="万元"
                :disabled="!couldEdit"
              />
            </template>
          </el-table-column>
        </jr-table-editor>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { issuancePurposeDetail, issuancePurposeModify } from '@/api/issuance/issuance'
import { debounce } from '@jupiterweb/utils/common'
export default {
  props: {
    id: {
      type: String,
      default: ''
    },
    couldEdit: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      data: {
        raisedFundsDetails: []
      },
      selectionIndex: [],
      columns: [
        {
          title: '债券简称',
          prop: 'bondShortName'
        }
      ]
    }
  },
  watch: {
    id: {
      deep: true,
      immediate: true,
      handler() {
        this.getRaisedFundsDetails()
      }
    },
    data: {
      deep: true,
      handler() {
        this.debounceUpdate()
      }
    }
  },
  methods: {
    /**
     * 获取行内数据
     */
    async getRaisedFundsDetails() {
      const data = await issuancePurposeDetail(this.id)
      this.data = data
    },
    /**
     * 防抖update
     */
    debounceUpdate: debounce(function() {
      this.updateDetails()
    }, 500),
    /**
     * 更新部分数据
     */
    async updateDetails() {
      const keyArr = ['bondShortName', 'amt', 'repayPrincipal', 'repayInterest']
      let flag = true
      for (let i = 0; i < this.data.raisedFundsDetails.length; i++) {
        for (let j = 0; j < keyArr.length; j++) {
          if (keyArr[j] === 'bondShortName' && !this.data.raisedFundsDetails[i][keyArr[j]]) {
            flag = false
          }
          if (!Object.prototype.hasOwnProperty.call(this.data.raisedFundsDetails[i], keyArr[j])) {
            flag = false
          }
        }
      }
      if (flag) {
        this.data.useRepayamt =
          this.data.raisedFundsDetails.reduce((pre, current) => {
            pre = pre + (current.repayPrincipal || 0) * 100
            return pre
          }, 0) / 100
        this.data.useInterest =
          this.data.raisedFundsDetails.reduce((pre, current) => {
            pre = pre + (current.repayInterest || 0) * 100
            return pre
          }, 0) / 100
        this.data.useTotal = (this.data.useRepayamt * 100 + this.data.useInterest * 100) / 100
        this.data.unusePayamt = (this.data.prinAmt * 100 - this.data.useRepayamt * 100) / 100
        this.data.unuseInterest = (this.data.interest * 100 - this.data.useInterest * 100) / 100
        await issuancePurposeModify(this.data)
      }
    },
    /**
     * 选择行
     */
    handleSelectionChange(rows) {
      this.selectionIndex = rows.map((item) => item.index)
    },
    /**
     * 删除并且更新
     */
    deleteRaised() {
      this.data.raisedFundsDetails = this.data.raisedFundsDetails.filter((item) => {
        return !this.selectionIndex.includes(item.index)
      })
      this.debounceUpdate()
    }
  }
}
</script>

<style></style>
