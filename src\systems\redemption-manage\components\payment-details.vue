<template>
  <div class="payment-detail">
    <jr-decorated-table
      ref="jrTable"
      :params="tableParams"
      custom-id="8763e8bf5baa4d09bf6d23c56f9d5de6"
      :menuinfo="menuinfo"
      v-bind="{ ...$attrs, ...$props }"
      style="height: calc(100% - 56px);"
    />

    <div class="payment-detail-summary">
      <span>22淄博01</span>
      <span>发行日期:2022-04-27</span>
      <span>应付总计:883,520,000.00(元)</span>
      <span>已付:55,680,000.00(元)</span>
      <span>未付:827,840,000.00(元)</span>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    menuinfo: {
      type: Object,
      default: () => ({})
    },
    permitdetail: {
      type: Object,
      default: () => ({})
    },
    sInfoWindcode: {
      type: String,
      default() {
        return ''
      }
    }
  },
  data() {
    return {
      tableParams: {
        sInfoWindcode: '',
        ownedModuleid: '1352317240954667008'
      }
    }
  },
  components: {},
  methods: {},
  created() {
    this.tableParams.sInfoWindcode = this.sInfoWindcode
  },
  mounted() {}
}
</script>

<style lang="scss" scoped>
.payment-detail {
  width: 100%;
  height: 300px;
  ::v-deep .jr-decorated-table--header {
    display: none !important;
  }

  &-summary {
    display: flex;
    height: 54px;
    padding: 16px 0;
    gap: 5px;
    align-items: center;
    span {
      line-height: 22px;
      font-size: var(--el-font-size-base);
    }
  }
}
</style>
