<template>
  <el-popover v-model="customColumnPopShow" :manual="true" placement="bottom-end" popper-class="jr-custom-table-column">
    <el-button v-if="!admin" slot="reference" title="列配置"><jr-svg-icon icon-class="system-config" /></el-button>
    <div v-if="admin" slot="reference">列配置</div>

    <div ref="columnSort" v-clickoutside.native="handleCancel" class="jr-custom-table-column-body">
      <el-input v-model="query" class="query-panel" placeholder="请输入字段名称" @keyup.native.enter="handleQuery">
        <jr-svg-icon slot="suffix" icon-class="search" @click="handleQuery" />
      </el-input>
      <jr-table
        row-key="id"
        :columns="tableConfig.columns"
        stripe
        border
        :height="300"
        :tree-props="{ children: 'child', hasChildren: 'checked' }"
        :data-source="nameList"
      />

      <div class="action-panel" style="text-align: center;">
        <el-button type="primary" @click="handleSave">{{ InitialMessage('common.system.btn.save') }}</el-button>
        <el-button @click="handleReset">{{ InitialMessage('common.btn.empty') }}</el-button>
        <el-button @click="handleCancel">{{ InitialMessage('common.system.btn.cancel') }}</el-button>
      </div>
    </div>
  </el-popover>
</template>
<script>
import * as API from '@/api/invest/portfolio/portfolioanalysis/PtlPositionAnalysis002.js'
import Sortable from 'sortablejs'
// import { isNullOrUndefined } from '@jupiterweb/utils/common'
export default {
  name: 'ColumnSort',
  props: {
    showSearch: { type: Boolean, default: false },
    admin: { type: Boolean, default: false },
    data: { type: Array, default: () => [] },
    // 改成对象了
    // data: { type: Object, default: () => ({}) },
    nameList: {
      type: Array, default: () => []
    },
    tableList: {
      type: Array, default: () => []
    },

    displayType: {
      type: String, default: '02'
    }
  },
  data() {
    const self = this

    return {
      // 被显示的内容
      showList: [],
      query: '',
      loading: false,
      columns: [],
      oriColumns: [],
      customColumnPopShow: false,
      isSearch: false,
      tableConfig: {
        columns: [
          {
            title: '序号',
            width: 45,
            render: (h, { row, column, rowIndex }) => (
              <span>{ `${rowIndex < 9 ? 0 : ''}${rowIndex + 1}` }</span>
            )
          },
          {
            title: '字段名称',
            render: (h, { row, column, rowIndex }) => (
              <span>
                { row.title || row.columnTitle || row.colName || row.name}
                <span class='controls'>
                  <span v-show={!self.isSearch} title='拖拽排序'><jr-svg-icon icon-class='drag' class='drag' /></span>
                  <span title='置顶'><jr-svg-icon icon-class='vertical-align-top' onClick={ self.handleTopOrBottom.bind(self, rowIndex, 'top') } /></span>
                  <span title='置底'><jr-svg-icon icon-class='vertical-align-bottom' onClick={ self.handleTopOrBottom.bind(self, rowIndex, 'bottom') } /></span>
                </span>
              </span>
            )
          },
          {
            title: '显示',
            width: 65,
            editable: true,
            align: 'center',
            render: (h, { row, column }) => (
              <span>
                <el-switch
                  v-model={ row.disPlayed }
                  active-value={ 1 }
                  inactive-value={ 0 }
                  v-show={row.parent === false}
                  disabled={ !!{ ...JSON.parse(row.extendCfg || '{}') }.need }
                  onChange={ self.handleChange.bind(self, row) }
                />
              </span>
            )
          },
          {
            title: '冻结',
            width: 65,
            editable: true,
            align: 'center',
            render: (h, { row, column, rowIndex }) => (
              <span>
                <el-switch
                  v-show={row.parent === false}
                  v-model={ row.frozen }
                  active-value={ 1 }
                  inactive-value={ 0 }
                  onChange={ self.handleChange.bind(self, row) }
                />
              </span>
            )
          },
          {
            prop: 'supportSearch',
            title: '检索方式',
            width: 80,
            align: 'center',
            show: self.showSearch,
            render: (h, { row }) => {
              let { extendCfg } = row
              if (typeof extendCfg === 'string') {
                extendCfg = JSON.parse(extendCfg)
                return (<span>{extendCfg.supportSearch || '不支持'}</span>)
              } else {
                return ''
              }
            }
          }
        ]
      }
    }
  },
  watch: {
    'data': {
      handler() {
        if (this.customColumnPopShow) {
          this.init()
          this.$nextTick(this.rowDrop)
        }
      },
      deep: true
    },
    customColumnPopShow(visible) {
      if (visible) {
        this.init()
        this.$nextTick(this.rowDrop)
      }
    }
  },
  methods: {
    // 初始化
    init() {
      const self = this
      var nameArr = self.tableList.map((it) => {
        return it.title
      })
      console.log('!1111111', nameArr)
      self.nameList.map((item) => {
        item.child.map((it) => {
          if (nameArr.indexOf(it.name) > -1) {
            console.log('it', it)
            it.disPlayed = 1
          }
        })
      })

      console.log('xxx', self.nameList)
      // const data = self.initData = self.data.filter(f => !f.hidden).map((v, i) => {
      //   v.$index = i

      //   if (isNullOrUndefined(v.disPlayed)) {
      //     v.disPlayed = v.defaultShow ? 1 : 0
      //     v.frozen = 0
      //   }

      //   return v
      // })

      self.query = ''
      self.isSearch = false
      self.columns = JSON.parse(JSON.stringify(self.nameList))
      self.oriColumns = JSON.parse(JSON.stringify(self.nameList))
    },
    // 行拖拽
    rowDrop() {
      const self = this
      const el = self.$refs.columnSort.querySelector('.el-table__body tbody')

      if (!el) {
        setTimeout(() => self.rowDrop, 500)
        return
      }

      Sortable.create(el, {
        handle: '.drag',
        onEnd({ newIndex, oldIndex }) {
          const currRow = self.columns.splice(oldIndex, 1)[0]
          self.columns.splice(newIndex, 0, currRow)
          self.oriColumns = JSON.parse(JSON.stringify(self.columns))
          self.rowDrop()
        }
      })
    },
    // 置顶/置底
    handleTopOrBottom(index, flag) {
      const self = this
      const newIndex = flag === 'top' ? 0 : self.oriColumns.length - 1
      const currRow = self.columns.splice(index, 1)[0]

      self.columns.splice(newIndex, 0, currRow)
      // 找出在原始数据的位置
      const oldIndex = self.oriColumns.findIndex(item => item.$index === currRow.$index)
      self.oriColumns.splice(oldIndex, 1)
      self.oriColumns.splice(newIndex, 0, currRow)
      self.rowDrop()
    },
    refreshColumn() {
      this.$emit('setSessionColumns', this.oriColumns, true)
    },
    // 查询
    handleQuery() {
      const self = this

      self.columns = self.oriColumns.filter(v => (v.title || v.columnTitle || v.colName).includes(self.query))
      self.isSearch = self.columns.length !== self.oriColumns.length //   是不是进行过滤了  进行过滤了  不允许排序
    },
    // 保存
    async handleSave() {
      // 取出选中的数据
      const selected = this.showList.map((it) => { return it.id })
      const resultlist = selected.join(',')
      const params = {
        type: '2',
        logElementId: '',
        elementIdForLog: 'save',
        pageId: 'PtlThematicAnalysis001',
        isTodoList: false,
        resultlist: resultlist,
        msg: resultlist,
        pageIdForLog: 'PtlThematicAnalysis001'
      }
      // const res =
      await API.savaTableCols(params)
      // console.log('xxx', res)
      const param = {
        displayType: this.displayType,
        type: '1',
        logElementId: 'displayType',
        elementIdForLog: 'displayType',
        pageId: 'PtlThematicAnalysis001',
        isTodoList: false
      }
      const res1 = await API.getTableCols(param)
      const arr = res1.map((item) => {
        return {
          // 补充属性吗？
          prop: item.fieldMsg,
          title: item.title
        }
      })

      // console.log('zzzz', res1)
      console.log('QQQQ', arr)
      this.$emit('columnsChange', arr)

      this.customColumnPopShow = false
    },
    // 显示/冻结 改变事件
    handleChange(row, value, b) {
      console.log('*********', row)
      if (value === 1) {
        this.showList.push(row)
      } else {
        this.showList.filter((item, index) => {
          if (item.id === row.id) {
            this.showList.splice(index, 1)
          }
        })
      }
      // console.log('@@@@@', this.showList)
      this.oriColumns = this.oriColumns.map(item => {
        if (item.$index === row.$index) {
          item = { ...row }
        }

        return item
      })
    },
    // 重置
    handleReset() {
      const self = this

      self.query = ''
      self.isSearch = false
      self.columns = JSON.parse(JSON.stringify(self.initData))
      self.oriColumns = JSON.parse(JSON.stringify(self.initData))
    },
    // 取消
    handleCancel() {
      this.customColumnPopShow = false
    }
  }
}
</script>
<style lang="scss">
@import '~@jupiterweb/assets/styles/variables.scss';
.jr-custom-table-column.el-popover {
  padding: 0;
  .tip {
    padding-top: 10px;
    font-size: $font_size_14;
  }
}
.jr-custom-table-column-body {
  width: 560px;
  max-height: calc(100vh - 220px);
  min-height: 10vh;
  overflow: auto;
  padding: 12px;
  .jr-checkbox-group {
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    .controls {
      background-color: #fff;
      position: absolute;
      color: #646363;
      right: 0;
      padding-right: 10px;
      font-size: $font_size_14;
      display: none;
    }
    .el-checkbox {
      padding: 5px 10px;
      border: 1px dashed $--button-border-color;
      width: calc(50% - 8px);
      margin-right: 0;
      margin-top: 10px;
      overflow: hidden;
      &:nth-child(2n) {
        margin-left: 9px;
      }
      .el-checkbox__label {
        width: 100%;
      }
      &:hover .controls {
        display: inline-block;
      }
    }
  }

  .query-panel {
    width: 50% !important;

    .el-input__suffix {
      cursor: pointer;
      display: flex;
      align-items: center;
      margin-right: 2px;

      svg {
        color: #909399;
      }
    }
  }

  .el-table {
    margin: 10px 0 20px 0;

    .controls {
      background-color: #fff;
      position: absolute;
      color: #646363;
      right: 0;
      padding-right: 10px;
      font-size: $font_size_14;
      display: none;

      span {
        cursor: pointer;
        margin-left: 3px;
      }
    }

    .el-table__row {
      height: 30px !important;
      line-height: 30px !important;

      .el-switch__core {
        min-width: 40px !important;
      }

      &:hover .controls {
        display: inline-block;
      }
    }
  }

  .action-panel {
    text-align: center;
  }
}
</style>
