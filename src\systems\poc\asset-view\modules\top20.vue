<!--
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023-10-25 10:54:32
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2023-10-25 17:46:15
 * @Description: 配置贡献TOP20
-->
<template>
  <article>
    <div class="header">
      <span class="title">配置贡献TOP20</span>
      <div class="search-list">
        <label>资产类型：</label><jr-combobox v-model="assetType" :data="assetTypeList" />
        <label>时间区间：</label><jr-combobox v-model="indexType" :data="indexTypeList" />
        <jr-svg-icon icon-class="setting" />
        <jr-svg-icon icon-class="fullscreen" />
        <jr-svg-icon icon-class="export" />
      </div>
    </div>
    <div class="body">
      <jr-table :columns="columns" :data-source="dataList" :height="height" :cell-style="cellStyle" />
    </div>
  </article>
</template>

<script>
export default {
  data() {
    return {
      height: null,
      assetType: '债券',
      assetTypeList: ['全部', '资产包', '基金', '计划类净值产品', '债券', '股票', '其他'].map(t => ({ text: t, value: t })),
      indexType: '近一周',
      indexTypeList: ['近一周', '其他'].map(t => ({ text: t, value: t })),
      dataList: [
        { '资产代码': '112217046.IB', '资产名称': '22光大银行CD046', '所属池类型': '债券池', '所属池': '可投池', '配置数量': 150000, '收益贡献(%)': 36.0957, '池内贡献排名': 1 },
        { '资产代码': '2020068.IB', '资产名称': '20海峡银行二级01', '所属池类型': '债券池', '所属池': '可投池', '配置数量': 100000, '收益贡献(%)': 28.9551, '池内贡献排名': 2 },
        { '资产代码': '1821031.IB', '资产名称': '18天津农商二级01', '所属池类型': '债券池', '所属池': '可投池', '配置数量': 100000, '收益贡献(%)': 28.922, '池内贡献排名': 3 },
        { '资产代码': '194134.SH', '资产名称': '22德源02', '所属池类型': '债券池', '所属池': '可投池', '配置数量': 100000, '收益贡献(%)': 28.4853, '池内贡献排名': 4 },
        { '资产代码': '2020030.IB', '资产名称': '20北湾银行永续债', '所属池类型': '债券池', '所属池': '可投池', '配置数量': 90000, '收益贡献(%)': 28.4572, '池内贡献排名': 5 },
        { '资产代码': '2020090.IB', '资产名称': '20烟台银行永续债', '所属池类型': '债券池', '所属池': '可投池', '配置数量': 90000, '收益贡献(%)': 28.092, '池内贡献排名': 6 },
        { '资产代码': '112298214.IB', '资产名称': '22郑州银行CD123', '所属池类型': '债券池', '所属池': '可投池', '配置数量': 80000, '收益贡献(%)': 27.6803, '池内贡献排名': 7 },
        { '资产代码': '112283227.IB', '资产名称': '22邯郸银行CD197', '所属池类型': '债券池', '所属池': '可投池', '配置数量': 80000, '收益贡献(%)': 27.4461, '池内贡献排名': 8 },
        { '资产代码': '1920079.IB', '资产名称': '19龙江银行二级02', '所属池类型': '债券池', '所属池': '可投池', '配置数量': 80000, '收益贡献(%)': 22.5021, '池内贡献排名': 9 },
        { '资产代码': '1920085.IB', '资产名称': '19营口银行二级', '所属池类型': '债券池', '所属池': '可投池', '配置数量': 80000, '收益贡献(%)': 21.9857, '池内贡献排名': 10 },
        { '资产代码': '032100774.IB', '资产名称': '21昌阳投资PPN002', '所属池类型': '债券池', '所属池': '可投池', '配置数量': 50000, '收益贡献(%)': 20.3501, '池内贡献排名': 11 },
        { '资产代码': '032280531.IB', '资产名称': '22港兴港投PPN002', '所属池类型': '债券池', '所属池': '可投池', '配置数量': 50000, '收益贡献(%)': 20.1035, '池内贡献排名': 12 },
        { '资产代码': '092280031.IB', '资产名称': '22贵州银行永续债01', '所属池类型': '债券池', '所属池': '可投池', '配置数量': 50000, '收益贡献(%)': 19.8212, '池内贡献排名': 13 },
        { '资产代码': '102280016.IB', '资产名称': '22江津城建MTN001', '所属池类型': '债券池', '所属池': '可投池', '配置数量': 30000, '收益贡献(%)': 18.3021, '池内贡献排名': 14 },
        { '资产代码': '112209054.IB', '资产名称': '22浦发银行CD054', '所属池类型': '债券池', '所属池': '可投池', '配置数量': 50000, '收益贡献(%)': 17.3451, '池内贡献排名': 15 },
        { '资产代码': '112208031.IB', '资产名称': '22中信银行CD031', '所属池类型': '债券池', '所属池': '可投池', '配置数量': 40000, '收益贡献(%)': 17.2341, '池内贡献排名': 16 },
        { '资产代码': '1720058.IB', '资产名称': '17通商银行二级01', '所属池类型': '债券池', '所属池': '可投池', '配置数量': 30000, '收益贡献(%)': 16.9935, '池内贡献排名': 17 },
        { '资产代码': '177792.SH', '资产名称': '21修竹01', '所属池类型': '债券池', '所属池': '可投池', '配置数量': 30000, '收益贡献(%)': 16.3209, '池内贡献排名': 18 },
        { '资产代码': '1820032.IB', '资产名称': '18稠州商行二级01', '所属池类型': '债券池', '所属池': '可投池', '配置数量': 30000, '收益贡献(%)': 16.0121, '池内贡献排名': 19 },
        { '资产代码': '1820052.IB', '资产名称': '18宁夏银行二级01', '所属池类型': '债券池', '所属池': '可投池', '配置数量': 30000, '收益贡献(%)': 15.5209, '池内贡献排名': 20 }
      ]
    }
  },
  computed: {
    columns() {
      return Object.keys(this.dataList[0]).map(item => ({
        prop: item,
        title: item,
        align: item === '池内贡献排名' ? 'center' : typeof this.dataList[0][item] === 'number' ? 'right' : 'left',
        type: item !== '池内贡献排名' && typeof this.dataList[0][item] === 'number' ? 'amount' : 'text'
      }))
    }
  },
  mounted() {
    this.height = this.$el.querySelector('.body').clientHeight - 20
  },
  methods: {
    cellStyle({ column }) {
      if (column.columnKey === '收益贡献(%)') {
        return { color: 'red' }
      }
      return {}
    }
  }
}
</script>

<style lang="scss" scoped>
.body {
  padding: 10px 0;
}
</style>
