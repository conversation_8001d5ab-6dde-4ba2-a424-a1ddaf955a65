<!-- 组合收益分析 -> 组合收益风险概览 -->
<template>
  <div class="benefit-overview-page header-search-panel">
    <div class="overview-search home-poc-item--header">
      组合收益与回撤
      <!-- <el-form :model="form">
        <jr-form-item-create :data="cols" :model="form" :column="2" style="line-height: 0;" />
      </el-form>

      <el-button type="primary" class="search-panel" @click="() => init()">
        查询
      </el-button> -->
    </div>

    <echarts :options="options" :styles="{ width: '100%', height: 'calc(100% - 60px)', marginTop: '20px' }" />
  </div>
</template>

<script>
import * as API from '@/api/invest/portfolio/poc/view2'
import echarts from '@jupiterweb/components/echarts'

export default {
  components: {
    echarts
  },
  props: {
    params: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      form: {
        portfolioId: '',
        startDate: '',
        endDate: ''
      },
      cols: [{
        title: '起始日期',
        prop: 'startDate',
        type: 'date'
      }, {
        title: '截止日期',
        prop: 'endDate',
        type: 'date'
      }],
      options: {}
    }
  },
  watch: {
    params(v) {
      if (v && Object.keys(v).length) {
        Object.assign(this.form, v)
        this.init()
      }
    }
  },
  methods: {
    async init() {
      const data = await API.getPtlIncomeRiskData(this.form)
      this.options = this.getChartOptions(data)
      // console.log('---原始数据---：', data)
    },
    getChartOptions(data) {
      let x = []
      const legend = []
      const series = []

      if (data && Object.keys(data).length) {
        Object.keys(data).forEach((k, i) => {
          const d = data[k]

          !i && (x = d.map(v => v.X))
          legend.push(k)

          const type = /回撤/.test(k) ? 'bar' : 'line'
          const yAxisIndex = type === 'bar' ? 1 : 0

          series.push({
            name: k,
            type,
            yAxisIndex,
            tooltip: {
              valueFormatter(value) {
                return value + ' %'
              }
            },
            barWidth: '30px',
            data: d.map(v => v.Y)
          })
        })
      }

      // console.log('--x--：', x)
      // console.log('--legend--：', legend)
      // console.log('--series--：', series)

      return {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            crossStyle: {
              color: '#999'
            }
          }
        },
        toolbox: {
          feature: {
            dataView: { show: true, readOnly: false },
            magicType: { show: true, type: ['line', 'bar'] },
            restore: { show: true },
            saveAsImage: { show: true }
          }
        },
        dataZoom: [
          {
            start: 0,
            end: 100
          }
        ],
        grid: {
          left: '5%',
          right: '5%'
        },
        legend: {
          data: legend
        },
        xAxis: [
          {
            type: 'category',
            data: x,
            axisPointer: {
              type: 'shadow'
            }
          }
        ],
        yAxis: [
          {
            type: 'value',
            position: 'left',
            alignTicks: true,
            axisLine: {
              show: true
            },
            axisLabel: {
              formatter: '{value} %'
            }
          },
          {
            type: 'value',
            position: 'right',
            alignTicks: true,
            axisLine: {
              show: true
            },
            axisLabel: {
              formatter: '{value} %'
            }
          }
        ],
        series
      }
    }
  }
}
</script>

<style lang="scss">
.benefit-overview-page {
  height: 100%;
  background: #fff;
  padding: 0 10px;

  .overview-search {
    background: #fff;
    display: flex !important;

    .el-form {
      width: 100%;
      flex: 1;
    }

    .search-panel {
      width: 56px;
      margin-top: 4px;
      margin-left: 15px;
    }
  }
}
</style>
