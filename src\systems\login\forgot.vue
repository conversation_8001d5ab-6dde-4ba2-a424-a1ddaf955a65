<template>
  <div class="b-login-forgot">
    <div class="b-login--logo">
      <img src="@/assets/images/login/logo.png" alt="logo" height="59">
    </div>
    <div class="b-login-forgot--form">
      <div class="b-login-forgot--form-title">
        <el-link :underline="false" class="el-icon-arrow-left" @click="back">返回</el-link>
        <el-link :underline="false" @click="reset">重置</el-link>
      </div>
      <el-form ref="forgotFormRef" :model="forgotForm" :rules="rules">
        <el-form-item prop="mobile">
          <el-input v-model="forgotForm.mobile" placeholder="请输入手机号码">
            <template #prefix>
              <img src="~@/assets/images/login/user.png" alt="user">
            </template>
          </el-input>
        </el-form-item>
        <jr-form-item
          prop="code"
        >
          <el-input v-model="forgotForm.code" :maxlength="10" autocomplete="new-password" class="b-verify--form-code" placeholder="手机验证码">
            <template #prefix>
              <img src="@/assets/images/login/psw.png" alt="code">
            </template>
            <template #append>
              <el-link :type="isVerifyCode ? 'info' : 'primary'" :underline="false" :disabled="isVerifyCode" @click="sendCode">
                <span v-if="!isVerifyCode">获取手机验证码</span>
                <span v-else>{{ timer }}秒后重发</span>
              </el-link>
            </template>
          </el-input>
        </jr-form-item>
        <jr-form-item
          prop="pwd"
        >
          <el-input v-model="forgotForm.pwd" type="password" autocomplete="new-password" placeholder="请输入密码">
            <template #prefix>
              <img src="~@/assets/images/login/pwd.png" alt="password">
            </template>
          </el-input>
        </jr-form-item>
        <jr-form-item
          prop="confirmPassword"
        >
          <el-input v-model="forgotForm.confirmPassword" type="password" autocomplete="new-password" placeholder="请输入确认密码">
            <template #prefix>
              <img src="~@/assets/images/login/pwd.png" alt="password">
            </template>
          </el-input>
        </jr-form-item>
      </el-form>
      <div class="b-login-forgot--form-btn">
        <el-button
          type="primary"
          class="b-block-button"
          :loading="loading"
          @click="submit"
        >
          确认
        </el-button>
      </div>
    </div>
  </div>
</template>

<script>
import { FindPwdRexSwitch, FindSmJmSwitch } from '@jupiterweb/api/login'
import sm4Encrypt from '@jupiterweb/utils/sm4'
import { forgetPwd, verifyCode } from '@/api/login'
export default {
  name: 'Forgot',
  data() {
    const self = this
    const validateToNextPassword = (rule, value, callback) => {
      if (value === '') {
        callback(new Error(' '))
      } else {
        const isPassed = self.expressionList && self.expressionList.length
        if (
          isPassed &&
          self.expressionList[0].rexList &&
          self.expressionList[0].rexList.length &&
          !new RegExp(self.expressionList[0].rexList[0]).test(self.forgotForm.pwd)
        ) {
          callback(new Error(self.expressionList[0].tip))
        } else if (
          isPassed &&
          self.expressionList[1].rexList &&
          self.expressionList[1].rexList.length &&
          self.expressionList[1].rexList.some(v => !new RegExp(v).test(self.forgotForm.pwd))
        ) {
          callback(new Error(self.expressionList[1].tip))
        } else {
          if (self.forgotForm.confirmPassword !== '') {
            self.$nextTick(() => {
              self.$refs.forgotFormRef.validateField('confirmPassword')
            })
          }
          callback()
        }
      }
    }
    const compareToFirstPassword = (rule, value, callback) => {
      if (value === '') {
        callback(new Error(' '))
      } else if (value !== this.forgotForm.pwd) {
        callback(new Error('两次密码输入不一致，请重新输入'))
      } else {
        callback()
      }
    }
    return {
      forgotForm: {
        mobile: '',
        code: '',
        pwd: '',
        confirmPassword: ''
      },
      loading: false,
      timer: 60,
      isVerifyCode: false,
      isShowPassword: false,
      expressionList: [],
      rules: {
        mobile: [{ required: true, message: '手机号码不能为空', trigger: 'blur' }, {
          validator: (rule, value, callback) => {
            if (value) {
              if (!/^1[3-9]\d{9}$/.test(value)) {
                callback(new Error('手机号格式错误'))
              } else {
                callback()
              }
            } else {
              callback()
            }
          }
        }],
        code: [{ required: true, message: '手机验证码不能为空', trigger: 'blur' }],
        pwd: [{ required: true, message: '密码不能为空', trigger: 'blur' },
          {
            validator: validateToNextPassword,
            trigger: 'blur'
          }],
        confirmPassword: [{ required: true, message: '确认密码不能为空', trigger: 'blur' }, {
          validator: compareToFirstPassword,
          trigger: 'blur'
        }]
      }
    }
  },
  created() {
    this.findPwdRexSwitch()
  },
  methods: {
    // 获取后端配置的正则，验证用户输入的密码是否符合要求
    async findPwdRexSwitch() {
      const result = await FindPwdRexSwitch()
      this.expressionList = result || []
    },
    back() {
      this.$emit('close', 'forgot')
    },
    sendCode() {
      const { mobile } = this.forgotForm
      if (!mobile || !/^1[3-9]\d{9}$/.test(mobile)) {
        return this.msgError('手机号格式错误')
      }
      this.$emit('openCaptcha', () => {
        this.timer = 60
        this.timerFn()
        this.isVerifyCode = true
        verifyCode({
          type: 'F', // L登录 R注册, F忘记密码
          mobile
        }, (isSuccess, data, message) => {
          isSuccess && this.msgSuccess(message || '验证码已发送')
        })
      })
    },
    timerFn() {
      if (this.timer > 1) {
        this.timer--
        setTimeout(this.timerFn, 1000)
      } else {
        this.isVerifyCode = false
      }
    },
    submit() {
      this.$refs.forgotFormRef.validate(async(valid) => {
        if (valid) {
          const result = await FindSmJmSwitch('SM4_FLAG') // 判断密码是否开启国密加密
          const isOpened = result && result.effectflag === this.$dict.EFFECTFLAG_E && result.paravalue === '1'
          const { pwd, ...res } = this.forgotForm
          const sPwd = isOpened ? sm4Encrypt(pwd) : window.btoa(pwd)
          const params = {
            ...res,
            pwd: sPwd,
            pwd2: sPwd
          }
          forgetPwd(params, (isSuccess, data, message) => {
            isSuccess && this.msgSuccess(message || '密码修改成功') && this.back()
          })
        }
      })
    },
    reset() {
      this.$refs.forgotFormRef.resetFields()
    }
  }
}
</script>
<style lang="scss" scoped>
.b-login-forgot {
  width: 100%;
  &--form {
    margin-top: 79px;
    background: #FFFFFF;
    box-shadow: 0px 6px 16px 0px rgba(0,0,0,0.03), 0px 3px 6px -4px rgba(0,0,0,0.03), 0px -6px 16px 0px rgba(0,0,0,0.03);
    border-radius: 8px;
    padding: 32px 40px 50px;
    &-title {
      display: flex;
      justify-content: space-between;
      margin-bottom: 24px;
      line-height: 22px;
    }
    &-btn {
      margin-top: 40px;
    }
  }
}
</style>

