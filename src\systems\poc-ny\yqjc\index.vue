<template>
  <!-- POC专用 -->
  <!-- 舆情监测 -->
  <jr-decorated-table
    custom-id="********************************"
    v-bind="{ ...$attrs, ...$props, menuinfo, handlecollect }"
  />
</template>
<script>
export default {
  props: {
    menuinfo: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
    }
  },
  methods: {
    handlecollect(row) {
      this.msgSuccess('收藏成功！')
      return false
    }
  }
}
</script>
