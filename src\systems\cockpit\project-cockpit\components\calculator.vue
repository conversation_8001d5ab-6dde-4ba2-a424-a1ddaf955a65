<template>
  <div class="calculator">
    <cockpit-header 
      :style="{ width:px2vw(362),height:px2vh(40) }" 
      title="日期&计算器"
    />
    <div class="calculator-inner">
      <div class="calculator-inner-time">
        <span class="calculator-inner-time-num">{{ nowTime | filterTime(0) }}</span>
        <span class="calculator-inner-time-num">{{ nowTime | filterTime(1) }}</span>
        <span>:</span>
        <span class="calculator-inner-time-num">{{ nowTime | filterTime(3) }}</span>
        <span class="calculator-inner-time-num">{{ nowTime | filterTime(4) }}</span>
      </div>
      <div class="calculator-inner-text">
        <span>计算器</span>
        <span></span>
      </div>
    </div>
    <div class="calculator-redirectTabs">
      <div 
        class="calculator-redirectTabs-tab"
        v-for="(tab,index) in redirectTab"
        :key="index"
        @click.stop="tab.clickFunc"
      >
        <img :src="tab.imgSrc" alt="">
        <span>{{ tab.title }}</span>
      </div>
    </div>
  </div>
</template>

<script>
import cockpitHeader from '../../components/cockpit-header.vue';
import cockpitCalcular01 from "@/assets/cockpit/cockpit_calculator01.png"
import cockpitCalcular02 from "@/assets/cockpit/cockpit_calculator02.png"
import cockpitCalcular03 from "@/assets/cockpit/cockpit_calculator03.png"
import cockpitCalcular04 from "@/assets/cockpit/cockpit_calculator04.png"
import moment from 'moment'
import { px2vw,px2vh } from '../../utils/portcss';
export default {
  name: 'Calculator',
  components:{
    cockpitHeader
  },
  data() {
    return {
      redirectTab: [
        {
          title: '融资参考',
          imgSrc: cockpitCalcular01,
          clickFunc: () => {
            this.redirectToMenu(
              '1315721394511069184',
              {
                tabName: 'financingReference'
              }
            )
          }
        },
        {
          title: '发行窗口',
          imgSrc: cockpitCalcular02,
          clickFunc: () => {
            this.redirectToMenu('1315721599545425920')
          }
        },
        {
          title: '重大事项',
          imgSrc: cockpitCalcular03,
          clickFunc: () => {
            this.redirectToMenu('1315723962633076736')
          }
        },
        {
          title: '承销费',
          imgSrc: cockpitCalcular04,
          clickFunc: () => {
            this.redirectToMenu('1364638311628533760')
          }
        }
      ],
      nowTime: '',
      timer: null
    }
  },
  filters: {
    filterTime(val,index){
      if(val.length){
        return val[index]
      }else{
        return ''
      }
    }
  },
  created() {
    this.initNowTime()
  },
  methods:{
    px2vw,
    px2vh,
    /**
     * 路径跳转
     */
    redirectToMenu(pathId,params = {}){
      this.$store.dispatch('tagsView/updateCurrentTab', {
        path: '/' + pathId,
        // 跳转需要刷新的话 refresh传true
        meta: {
          refresh: true,
          params
        }
      })
    },
    /**
     * 获取当前时间
     */
    initNowTime() {
      this.nowTime = moment(+new Date()).format('HH:mm')
      if(this.timer){
        clearInterval(this.timer)
        this.timer = null
      }
      this.timer = setInterval(() => {
        this.nowTime = moment(+new Date()).format('HH:mm')
      },1000)
    }
  },
  beforeDestroy() {
    if(this.timer){
      clearInterval(this.timer)
      this.timer = null
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~assets/styles/portcss.scss';
.calculator {
  width: 100%;
  height: vh(446);
  background-image: url("../../../../assets/cockpit/cockpit_pro_normal_bac.png");
  background-size: 100% 100%;
  padding: vh(8) vw(23);
  &-inner {
    width: 100%;
    margin-top: vh(13);
    height: vh(54);
    display: flex;
    align-items: space-between;
    flex-direction: column;
    &-time {
      width: 100%;
      height: vh(35);
      display: flex;
      justify-content: flex-end;
      align-items: center;
      align-self: flex-start;
      &-num {
        width: vw(21);
        height: vh(35);
        background-image: url('../../../../assets/cockpit/cockpit_timeBac.png');
        background-size: 100% 100%;
        font-size: vh(12);
        line-height: vh(35);
        color: #FFFFFF;
        font-weight: 600;
        text-align: center;
      }
      & > span {
        flex-shrink: 0;
      }
      & > span:nth-of-type(2),
      & > span:nth-of-type(5) {
        margin-left: vw(4); 
      }
      & > span:nth-of-type(3) {
        width: vw(9);
        height: vh(35);
        font-size: vh(12);
        text-align: center;
        line-height: vh(35);
        color: #5B8FF9;
      }
    }
    &-text {
      width: fit-content;
      height: vh(23);
      display: flex;
      flex-direction: column;
      justify-content: center;
      gap: vh(3);
      & > span:nth-of-type(1) {
        height: vh(19);
        font-family: MicrosoftYaHeiSemibold;
        font-size: vh(14);
        color: rgba(255,255,255,0.9);
        line-height: vh(19);
        letter-spacing: 1px;
      }
      & > span:nth-of-type(2) {
        height: vh(1);
        border: vh(1) solid;
        opacity: 0.7;
        border-image: linear-gradient(90deg, rgba(255, 255, 255, 0), rgba(137, 110, 255, 1), rgba(255, 255, 255, 0)) 1 1;
      }
    }
  }
  &-redirectTabs {
    margin-top: vh(16);
    &-tab {
      width: vw(444);
      height: vh(64);
      margin-bottom: vh(12);
      background-image: url('../../../../assets//cockpit/cockpit_calculator_bac.png');
      background-size: 100% 100%;
      padding: vh(14) vw(22);
      display: flex;
      align-items: center;
      cursor: pointer;
      gap: vw(22);
      & > img {
        width: vw(37);
        height: vh(36);
      }
      & > span {
        height: vh(22);
        font-family: MicrosoftYaHeiSemibold;
        font-size: vh(14);
        color: rgba(255,255,255,0.9);
        line-height: vh(22);
      }
    }
  }
}
</style>
