<template>
  <jr-modal
    :visible="visible"
    :append-to-body="false"
    :modal-append-to-body="true"
    :handle-ok="handleCancel"
    :height="600"
    :handle-cancel="handleCancel"
  >
    <span class="modal-title">阈值设置</span>
    <template v-slot:body>
      <div style="width: 100%;">
        <el-form ref="formFields" :model="data">
          <el-form-item label="可编辑表格" prop="riskIndexDetail" class="full-block">
            <jr-table-editor
              ref="editor"
              v-model="data.riskIndexDetail"
              :columns="columns"
              prop="riskIndexDetail"
              :show-delete="false"
              :hiden-add-row="true"
              :disabled="false"
            >
              <el-table-column prop="repayInterest">
                <template slot="header">
                  <p style="width: 100%; text-align: center; margin-bottom: 0px">阈值</p>
                </template>
                <template slot-scope="scope">
                  <jr-number-input v-model="scope.row.threshold" :precision="4" />
                </template>
              </el-table-column>
            </jr-table-editor>
          </el-form-item>
        </el-form>
        <div class="modal-bac" />
      </div>
    </template>
    <template v-slot:footer>
      <div class="modal-footer">
        <div class="modal-footer-tips">
          <jr-svg-icon icon-class="info-circle" />
          <span>当发行参考定价均值低于或等于阈值时，会以发送短信的方式预警</span>
        </div>
        <div class="modal-footer-btn">
          <el-button @click.stop="handleCancel">清空</el-button>
          <el-button type="primary" @click.stop="submit">确定</el-button>
        </div>
      </div>
    </template>
  </jr-modal>
</template>

<script>
import { GetListData } from '@jupiterweb/api/common/active-module-svr'
import { issuanceThresholdBatchModify } from '@/api/issuance/issuance.js'
export default {
  data() {
    return {
      visible: false,
      data: {
        riskIndexDetail: []
      },
      columns: [
        {
          title: '期限',
          prop: 'term',
          disabled: true
        },
        {
          title: '发行方式',
          prop: 'bInfoIssuetypename',
          disabled: true
        },
        {
          title: '是否可续期',
          prop: 'text',
          disabled: true
        }
      ]
    }
  },
  methods: {
    /**
     * 打开弹框
     */
    open() {
      this.visible = true
      this.getThresholdDataApi()
    },
    /**
     * 关闭弹框
     */
    handleCancel() {
      this.visible = false
      this.data.riskIndexDetail = []
    },
    /**
     * 获取自定义列阈值数据
     */
    async getThresholdDataApi() {
      const data = await GetListData({
        params: {
          ownedModuleid: '708631605142536192',
          ccid: '971c822f871f41f2aa9a4e41ca83f213'
        },
        page: {
          pageNo: 1,
          pageSize: 15
        }
      })
      this.data.riskIndexDetail = (data?.pageInfo?.list || []).map((item, index) => {
        item.text = item.isRenewal === 'Y' ? '可续期' : '不可续期'
        item.index = index
        return item
      })
    },
    /**
     * 表单提交
     */
    async submit() {
      await issuanceThresholdBatchModify(this.data.riskIndexDetail)
      this.handleCancel()
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .jr-modal.el-dialog .el-dialog__footer {
  padding: 0px;
  background-color: #ffffff;
}
::v-deep .jr-modal.el-dialog .el-dialog__footer{
  border-top: none;
}
.modal-title{
  font-family: MicrosoftYaHeiSemibold;
  font-size: var(--el-font-size-base);
  color: rgba(0,0,0,0.85);
  line-height: 28px;
  font-weight: 600;
}
.modal-bac{
  width: 100%;
  height: 208px;
  background-image: url('../../../../assets/images/bondEntryBac.png');
  background-size: 100% 100%;
  position: fixed;
  bottom: 0px;
  left: 0px;
}
.modal-footer {
  background-color: #ffffff;
  padding-bottom: 12px;
  &-tips {
    display: flex;
    align-items: center;
    padding-left: 25px;
  }
  &-btn {
    margin-top: 47px;
    & > button {
      width: 88px;
      border-radius: 2px;
    }
  }
}
</style>
