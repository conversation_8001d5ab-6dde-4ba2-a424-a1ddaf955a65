<template>
  <div class="poc-zzj">
    <div class="left">
      <div style="display: flex; justify-content: space-between; margin-bottom: 30px">
        <div class="button-group">
          <template v-for="(item, index) in tagsList">
            <el-button v-if="item.active" :key="index" type="primary">{{ item.name }}</el-button>
            <el-button
              v-else
              :key="index"
              type="text"
              style="color: #333"
              @click="
                () => {
                  tagsList.forEach((item) => {
                    $set(item, 'active', false)
                  })
                  $set(item, 'active', true)
                }
              "
            >
              {{ item.name }}
            </el-button>
          </template>
        </div>
        <div>
          <el-link type="primary">按发布时间排序</el-link>
          <el-divider direction="vertical" />
          <el-link>按期望投资金额排序</el-link>
        </div>
      </div>
      <div>
        <div v-for="(item, index) in qyList" :key="index" class="qy-item">
          <div class="qy-item-top">
            <div class="qy-item-top-left">
              <div
                class="qy-item-top-left-name"
                @click="
                  () => {
                    dialogVisible = true
                    current = item
                  }
                "
              >
                {{ item.name }}
              </div>
              <el-tag>{{ item.tag }}</el-tag>
            </div>
          </div>
          <div class="qy-item-middle">
            <div v-for="(item2, index2) in item.list" :key="index2" class="qy-item-middle-item">
              <div class="qy-item-middle-item-value" :style="item2.color ? 'color: red' : ''">{{ item2.value }}</div>
              <div class="qy-item-middle-item-name">{{ item2.name }}</div>
            </div>
            <jr-svg-icon
              v-if="item.icon"
              :icon-class="item.icon"
              style="position: absolute; font-size: 80px; opacity: 0.6; right: 10px; top: 20px"
            />
          </div>
          <div class="qy-item-bottom">
            <jr-svg-icon icon-class="star-on" style="margin-right: 10px; color: var(--theme--color)" />
            收藏
            <jr-svg-icon icon-class="phone" style="margin-left: 20px; margin-right: 10px" />
            联系客户经理
          </div>
        </div>
      </div>
    </div>
    <div class="right">
      <div class="title">
        <div>
          <span class="">精选</span>
          投资
        </div>
        <span>Selected Projects</span>
      </div>
      <div>
        <div v-for="(item, index) in qy2List" :key="index" class="qy-item qy-item2">
          <div class="qy-item-top">
            <div class="qy-item-top-left">
              <div class="qy-item-top-left-name">{{ item.name }}</div>
              <el-tag>{{ item.tag }}</el-tag>
            </div>
          </div>
          <div class="qy-item-middle qy-item-middle2">
            <div v-for="(item2, index2) in item.list" :key="index2" class="qy-item-middle-item">
              <div class="qy-item-middle-item-value" :style="item2.color ? 'color: red' : ''">{{ item2.value }}</div>
              <div class="qy-item-middle-item-name">{{ item2.name }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <el-dialog class="poc-zzj-dialog" :visible.sync="dialogVisible">
      <div class="dialog-title">
        <div>
          {{ current.name }}
          <el-tag type="success" style="margin: 0 4px" size="mini">民营企业</el-tag>
          <el-tag type="warning" size="mini">控制类</el-tag>
        </div>
        <el-button
          type="primary"
          plain
          @click="
            () => {
              msgSuccess('收藏成功')
            }
          "
        >
          <jr-svg-icon icon-class="star" />
          收藏
        </el-button>
      </div>
      <jr-svg-icon
        v-if="current.icon"
        :icon-class="current.icon"
        style="position: absolute; font-size: 80px; opacity: 0.6; right: 10px; top: 100px"
      />
      <el-descriptions class="margin-top" title="基本信息" :column="3" :size="'small'">
        <el-descriptions-item label="期望投资金额">
          <span style="color: red">10000-100000万元</span>
        </el-descriptions-item>
        <el-descriptions-item label="期望股权出让比例">50%-75%</el-descriptions-item>
        <el-descriptions-item label="期望投资时间">2024-05-08 - 2024-11-08</el-descriptions-item>
        <el-descriptions-item label="期望融资方式">现金、债权</el-descriptions-item>
        <el-descriptions-item label="期望融资方营业收入/年">10000-100000万元</el-descriptions-item>
        <el-descriptions-item label="期望融资方营收增速">5%</el-descriptions-item>
        <el-descriptions-item label="期望融资方净利润">1500-2000万元</el-descriptions-item>
        <el-descriptions-item label="期望融资方净利润增速">5%</el-descriptions-item>
        <el-descriptions-item label="期望企业类型">无形资产</el-descriptions-item>
        <el-descriptions-item label="无形资产类型">特许经营权</el-descriptions-item>
        <el-descriptions-item label="客户经理">张小峰</el-descriptions-item>
        <el-descriptions-item label="客户经理联系方">15999999099</el-descriptions-item>
        <el-descriptions-item label="期望行业" :span="3">
          商务服务业-企业管理服务，计算机、通信和其他电子设备制造业，信息传输、软件和信息技术服务业
        </el-descriptions-item>
        <el-descriptions-item label="期望区域" :span="3">江苏省，安徽省</el-descriptions-item>
        <el-descriptions-item label="并购要求" :span="3">
          期望融资方有稳健的现金流，企业主要产品的市场占有率不低于xx%，企业资产负债率不高于50%。
        </el-descriptions-item>
      </el-descriptions>
    </el-dialog>
  </div>
</template>

<script>
export default {
  data() {
    return {
      dialogVisible: false,
      current: {},
      tagsList: [
        {
          name: '债权'
        },
        {
          name: '股权'
        },
        {
          name: '并购',
          active: true
        }
      ],
      qyList: [
        {
          name: '合肥****企业管理咨询有限公司',
          tag: '民营企业',
          list: [
            {
              name: '期望行业',
              value: '商务服务业'
            },
            {
              name: '期望区域',
              value: '安徽省'
            },
            {
              name: '发布时间',
              value: '2024-03-01'
            },
            {
              name: '期望投资金额(万元)',
              color: 'red',
              value: '10000 - 100000(含)'
            },
            {
              name: '期望股权出让比例',
              value: '75% - 100%'
            }
          ]
        },
        {
          name: '南京****企业管理咨询有限公司',
          tag: '民营企业',
          icon: 'daoqi',
          list: [
            {
              name: '期望行业',
              value: '商务服务业'
            },
            {
              name: '期望区域',
              value: '江苏省'
            },
            {
              name: '发布时间',
              value: '2024-03-01'
            },
            {
              name: '期望投资金额(万元)',
              color: 'red',
              value: '10000 - 100000(含)'
            },
            {
              name: '期望股权出让比例',
              value: '75% - 100%'
            }
          ]
        },
        {
          name: '上海****企业管理咨询有限公司',
          tag: '民营企业',
          icon: 'daoqi',
          list: [
            {
              name: '期望行业',
              value: '商务服务业'
            },
            {
              name: '期望区域',
              value: '上海市'
            },
            {
              name: '发布时间',
              value: '2024-03-01'
            },
            {
              name: '期望投资金额(万元)',
              color: 'red',
              value: '10000 - 100000(含)'
            },
            {
              name: '期望股权出让比例',
              value: '75% - 100%'
            }
          ]
        },
        {
          name: '天津****企业管理咨询有限公司',
          tag: '民营企业',
          list: [
            {
              name: '期望行业',
              value: '商务服务业'
            },
            {
              name: '期望区域',
              value: '天津市'
            },
            {
              name: '发布时间',
              value: '2024-03-01'
            },
            {
              name: '期望投资金额(万元)',
              color: 'red',
              value: '10000 - 100000(含)'
            },
            {
              name: '期望股权出让比例',
              value: '75% - 100%'
            }
          ]
        },
        {
          name: '北京****企业管理咨询有限公司',
          tag: '民营企业',
          list: [
            {
              name: '期望行业',
              value: '商务服务业'
            },
            {
              name: '期望区域',
              value: '北京市'
            },
            {
              name: '发布时间',
              value: '2024-03-01'
            },
            {
              name: '期望投资金额(万元)',
              color: 'red',
              value: '10000 - 100000(含)'
            },
            {
              name: '期望股权出让比例',
              value: '75% - 100%'
            }
          ]
        }
      ],
      qy2List: [
        {
          name: '合肥****企业管理咨询有限公司',
          tag: '民营企业',
          list: [
            {
              name: '期望行业',
              value: '黑色金属'
            },
            {
              name: '期望区域',
              value: '安徽省'
            },
            {
              name: '期望投资金额(万元)',
              color: 'red',
              value: '10000 - 100000(含)'
            }
          ]
        },
        {
          name: '南京****企业管理咨询有限公司',
          tag: '民营企业',
          list: [
            {
              name: '期望行业',
              value: '黑色金属'
            },
            {
              name: '期望区域',
              value: '江苏省'
            },
            {
              name: '期望投资金额(万元)',
              color: 'red',
              value: '10000 - 100000(含)'
            }
          ]
        },
        {
          name: '上海****企业管理咨询有限公司',
          tag: '民营企业',
          list: [
            {
              name: '期望行业',
              value: '黑色金属'
            },
            {
              name: '期望区域',
              value: '上海市'
            },
            {
              name: '期望投资金额(万元)',
              color: 'red',
              value: '10000 - 100000(含)'
            }
          ]
        },
        {
          name: '天津****企业管理咨询有限公司',
          tag: '民营企业',
          list: [
            {
              name: '期望行业',
              value: '黑色金属'
            },
            {
              name: '期望区域',
              value: '天津市'
            },
            {
              name: '期望投资金额(万元)',
              color: 'red',
              value: '10000 - 100000(含)'
            }
          ]
        },
        {
          name: '北京****企业管理咨询有限公司',
          tag: '民营企业',
          list: [
            {
              name: '期望行业',
              value: '黑色金属'
            },
            {
              name: '期望区域',
              value: '北京市'
            },
            {
              name: '期望投资金额(万元)',
              color: 'red',
              value: '10000 - 100000(含)'
            }
          ]
        }
      ]
    }
  }
}
</script>

<style lang="scss">
.poc-zzj {
  background: #fff;
  padding: 12px;
  height: 100%;
  width: 100%;
  overflow: auto;
  display: flex;
  .left {
    width: calc(100% - 330px);
    padding-right: 20px;
    .button-group .el-button {
      font-size: 14px !important;
    }
    .el-link {
      font-size: 14px !important;
    }
  }
  .right {
    width: 330px;
    height: 660px;
    margin-top: 40px;
    box-shadow: 0px 8px 13px 2px #e7e7e7;
    .title {
      height: 100px;
      font-size: 24px;
      padding: 10px 20px;
      margin-bottom: 10px;
      background: linear-gradient(
        45deg,
        var(--theme--color-light-6),
        var(--theme--color-light-4),
        var(--theme--color-light-8)
      );
      div span {
        color: var(--theme--color);
      }
      div {
        font-family: fantasy;
        font-weight: bold;
        font-style: italic;
      }
      & > span {
        font-size: 14px;
        color: var(--theme--color);
      }
    }
  }
  .qy-item {
    padding-bottom: 12px;
    margin-bottom: 12px;
    position: relative;
    &:hover {
      box-shadow: 5px 13px 10px rgba(0, 0, 0, 0.1);
    }
    &.qy-item2 {
      padding: 0 12px;
    }
    &-top {
      display: flex;
      justify-content: space-between;
      align-items: center;
      &-left {
        display: flex;
        align-items: center;
        &-name {
          font-size: 16px;
          cursor: pointer;
          font-weight: 600;
          margin-right: 10px;
        }
      }
    }
    &-middle {
      padding: 12px 120px 12px 0;
      border-radius: 5px;
      margin-top: 10px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      &.qy-item-middle2 {
        padding-right: 12px;
        padding-bottom: 0;
      }
      &-item {
        display: flex;
        flex-direction: column;
        line-height: 24px;
        &-name {
          font-size: 14px;
          margin-right: 10px;
          color: #999;
        }
        &-value {
          font-size: 14px;
        }
      }
    }
    &-bottom {
      display: flex;
      align-items: center;
      color: #999;
    }
  }
}
.poc-zzj-dialog {
  .dialog-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    & > div {
      display: flex;
      align-items: center;
      font-size: 24px;
      margin-bottom: 12px;
      margin-top: -40px;
    }
  }
}
</style>
