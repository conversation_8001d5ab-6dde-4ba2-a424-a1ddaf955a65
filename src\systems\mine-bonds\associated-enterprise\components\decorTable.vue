<template>
  <div>
    <jr-decorated-table
      ref="table"
      :params="{
        ownedModuleid: '708631605142536192',
        ...selectParams
      }"
      :menuinfo="menuinfo"
      stripe
      :custom-id="customId"
      :custom-render="customRender"
      :modal-footer="false"
      style="width: 1000px; height: 300px"
      :handleissuance-materials="handleissuanceMaterials"
      :width="800"
      :default-page-size="10"
      v-bind="{
        ...$attrs,
        ...$props
      }"
    />
    <issuanceMaterials ref="issuanceMaterials" />
    <detail ref="detail" />
  </div>
</template>

<script>
import issuanceMaterials from '../../group-bonds/dialogs/issuanceMaterials.vue'
import { ConvertAmount } from 'jupiterweb/src/utils/common.js'
import detail from '@/systems/bond-market-overview/publish-query/dialog/detail.vue'
export default {
  name: 'DecorTable',
  components: {
    issuanceMaterials,
    detail
  },
  props: {
    customId: {
      type: String,
      default: ''
    },
    // 权限相关
    permitdetail: {
      type: Object,
      default: () => ({})
    },
    menuinfo: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      customRender: {
        exerciseValuation: (h, { row }) => {
          return (
            <span style='color:var(--theme--color);cursor:pointer' onClick={() => this.goToValuationDetails('1', row)}>
              {ConvertAmount('HMU', row.exerciseValuation, 1, 4)}
            </span>
          )
        },
        maturityValuation: (h, { row }) => {
          return (
            <span style='color:var(--theme--color);cursor:pointer' onClick={() => this.goToValuationDetails('2', row)}>
              {ConvertAmount('HMU', row.maturityValuation, 1, 4)}
            </span>
          )
        }
      },
      selectParams: {}
    }
  },
  created() {
    this.setSelectParams()
  },
  methods: {
    showCodeDetail(sInfoWindcode) {
      this.$refs.detail.open({ sinfoWindcode: sInfoWindcode })
    },
    /**
     * 跳转估值详情页面
     * @param {String} isRight '1'为行权估值 '2'为到期估值
     * @param {Object} row 行数据
     */
    goToValuationDetails(isRight, row) {
      this.$store.dispatch('tagsView/updateCurrentTab', {
        path: '/1361645930918469632',
        // 跳转需要刷新的话 refresh传true
        meta: {
          refresh: true,
          params: {
            comp_name: row.bInfoIssuer,
            s_info_windcode: row.sInfoWindcode,
            s_info_name: row.sInfoName,
            b_is_right: isRight
          }
        }
      })
    },
    /**
     * 打开发行材料弹框
     */
    handleissuanceMaterials(row) {
      const params = {
        ownedModuleid: '708631605142536192',
        ccid: '27716d3fb1cb469c970724740aa6291a'
      }
      this.$refs.issuanceMaterials.open(params, row, '发行材料')
    },
    /**
     * 排除掉params中的参数
     */
    setSelectParams() {
      for (const key in this.$attrs) {
        this.$set(this.selectParams, key, this.$attrs[key])
      }
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .jr-decorated-table--top-search .jr-decorated-table--header-left {
  display: none !important;
}
</style>
