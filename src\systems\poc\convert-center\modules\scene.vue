<!--
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023-10-25 18:28:27
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2023-10-26 13:59:29
 * @Description: 常用场景
-->
<template>
  <article>
    <div class="header">
      <span class="title">常用场景</span>
    </div>
    <div class="body">
      <section v-for="data in dataList" :key="data.text" @click="handleMenu(data.id)">
        <jr-svg-icon :icon-class="data.icon || 'question'" />
        <p>{{ data.text }}</p>
      </section>
    </div>
  </article>
</template>

<script>
export default {
  data() {
    return {
      dataList: [
        {
          icon: 'plan',
          id: '1160304421544996864',
          text: '我的组合方案'
        },
        {
          icon: 'trial',
          id: 'PTL_MANAGEMENT_001_003_001',
          text: '模拟试算与转指令'
        },
        {
          icon: 'backtest',
          id: '1160304211905294336',
          text: '组合配置回测'
        },
        {
          icon: 'asset-board',
          id: 'zckb001',
          text: '资产看板'
        },
        {
          icon: 'asset-model',
          id: '1160304546073882624',
          text: '资产配置模型'
        },
        {
          icon: 'market-model',
          id: '1160304486405713920',
          text: '市场估计模型'
        }
      ]
    }
  },
  methods: {
    handleMenu(id) {
      this.$store.dispatch('tagsView/updateCurrentTab', `/${id}`)
    }
  }
}
</script>

<style lang="scss" scoped>
.body {
  display: grid;
  grid-template-columns: repeat(3, 33.3%);
  section {
    text-align: center;
    padding-top: 18px;
    cursor: pointer;
    .jr-svg-icon {
      font-size: 24px;
      line-height: 60px;
      color: var(--theme--color);
      margin-bottom: 13px;
      margin-top: 20px;
    }
    p {
      font-weight: bold;
    }
  }
}
</style>
