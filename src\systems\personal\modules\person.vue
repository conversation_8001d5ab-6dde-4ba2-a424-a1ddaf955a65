<template>
  <div class="person-info">
    <div class="person-info-container">
      <div class="person-info-header">
        <span class="person-info-header-title">{{ $store.getters.personInfo.username }}</span>
        <span class="person-info-header-edit" @click="handleEditPassword">
          <img src="~@/assets/images/personal/edit.png" alt="修改密码">
          修改密码
        </span>
      </div>
      <div class="person-info-content">
        <div class="person-info-content-item">
          <img src="~@/assets/images/personal/mobile.png" alt="手机号" class="person-info-content-item-label">
          <span class="person-info-content-item-value">{{ $store.getters.personInfo.mobile || '--' }}</span>
        </div>
        <div class="person-info-content-item">
          <img src="~@/assets/images/personal/campany.png" alt="公司" class="person-info-content-item-label">
          <span class="person-info-content-item-value">{{ $store.getters.personInfo.companyName || '--' }}</span>
        </div>
      </div>
    </div>
    <!-- 修改密码 -->
    <PersonPasswordModal
      v-if="isShowPasswordModal"
      :close-modal="handleClosePasswordModal"
      :visible="isShowPasswordModal"
    />
  </div>
</template>

<script>
import PersonPasswordModal from './person-password'
export default {
  components: {
    PersonPasswordModal
  },
  props: {
    userInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      isShowPasswordModal: false
    }
  },
  methods: {
    handleClosePasswordModal() {
      this.isShowPasswordModal = false
    },
    handleEditPassword() {
      this.isShowPasswordModal = true
    }
  }
}
</script>
<style lang="scss" scoped>
.person-info {
  height: 391px;
  margin-left: var(--el-padding-left-right);
  width: calc(100% - var(--el-padding-left-right) * 2);
  padding: 45px 40px;
  border-radius: 18px;
  background-image: url('~@/assets/images/personal/person-bg.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  .person-info-container {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 17px;
    border: 3px solid #ffffff;
    height: 300px;
    padding: 58px 37px 65px;
  }
  .person-info-header {
    display: flex;
    align-items: center;
    .person-info-header-title {
      font-weight: bold;
      font-size: 32px;
      color: #333333;
      line-height: 61px;
      text-align: left;
      font-style: normal;
    }
    .person-info-header-edit {
      // font-size: var(--el-font-size-medium);
      color: var(--theme-mix-color);
      margin-left: 20px;
      cursor: pointer;
      img {
        vertical-align: text-top;
      }
    }
  }
  .person-info-content {
    display: flex;
    flex-direction: column;
    margin-top: 38px;
    .person-info-content-item {
      display: flex;
      // font-size: var(--el-font-size-extra-large);
      height: 27px;
      color: #333333;
      line-height: 27px;
      text-align: left;
      font-style: normal;
      align-items: center;
      margin-top: 14px;
      .person-info-content-item-label {
        margin-right: 10px;
      }
    }
  }
}
</style>

