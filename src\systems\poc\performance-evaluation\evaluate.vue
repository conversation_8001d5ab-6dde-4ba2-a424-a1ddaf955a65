<!--
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2024-01-13 16:51:06
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2024-05-22 10:28:21
 * @Description: 评价
-->
<template>
  <div class="performance-evaluation">
    <collapse-panel title="已选产品">
      <el-tag v-for="row in currentRows" :key="row.portfolioId" :closable="currentRows.length > 1" @close="handleRemove(row)">{{ row.portfolioName }}</el-tag>
      <div style="margin-top: 20px;">
        <el-button type="primary" @click="openEvaluateModal">开始评价</el-button>
      </div>
    </collapse-panel>
    <collapse-panel title="评价结果">
      <jr-table v-if="tableConfig.column.length" :columns="tableConfig.column" :data-source="tableConfig.data" :pagination="false" />
      <div v-else class="jr-empty">
        <jr-svg-icon icon-class="empty" />
        <div class="empty-text">暂无数据，请先点击<span class="btn-link" @click="openEvaluateModal">开始评价</span></div>
      </div>
      <jr-modal class="performance-evaluation" :loading="loading" v-bind="{...modalConfig}">
        选择评价模型
        <template #body>
          <div v-for="(mod, indx) in modelConfig.list" :key="mod.modelId" style="margin-bottom: 20px;">
            <h3 v-show="false"><el-radio v-model="modelConfig.form.modelId" name="modelId" :label="mod.modelId">{{ mod.modelName }}</el-radio></h3>
            <div class="layout-flex">
              <label for="">时间范围：</label>
              <el-checkbox @change="handleCheck(indx)">近一年</el-checkbox>
              <el-date-picker
                v-model="modelConfig.form.dateList[indx].date"
                class="w-300"
                type="daterange"
                range-separator="-"
                value-format="yyyy-MM-dd"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              />
            </div>
            <jr-table :columns="modelConfig.columns" :data-source="mod.detail" :pagination="false" />
          </div>
        </template>
      </jr-modal>
    </collapse-panel>
    <div slot="modal-footer">
      <el-button type="primary" :loading="loading" :disabled="!tableConfig.column.length" @click="handleOk">发布</el-button>
      <el-button @click="closeModal">取消</el-button>
    </div>
  </div>
</template>

<script>
import moment from 'moment'
import * as API from '@/api/poc/performance-evaluation'
import { getModalProps } from '@/systems/mixins'
import { FormatDate } from 'jupiterweb/src/utils/common'
export default {
  mixins: [getModalProps],
  data() {
    const self = this

    return {
      loading: false,
      currentRows: [],
      // 评价模型弹框
      modelConfig: {
        list: [],
        form: {
          modelId: '',
          dateList: [{ date: [] }, { date: [] }, { date: [] }]
        },
        columns: [{
          prop: 'indexType',
          title: '一级指标'
        }, {
          prop: 'indexName',
          title: '二级指标'
        }, {
          prop: 'weight',
          title: '权重(%)',
          type: 'rate'
        }]
      },
      // 评价弹框
      modalConfig: {
        visible: false,
        okText: '开始评价',
        size: 'small',
        handleOk: self.handleEvaluate,
        handleCancel: self.handleEvaluateCancel
      },
      tableConfig: {
        modelId: '',
        data: [],
        column: []
      }
    }
  },
  created() {
    this.init()
    this.currentRows = [...this.selectedRows]
  },
  methods: {
    // 初始化数据
    async init() {
      this.modelConfig.list = await API.QueryRatingModel({})
    },
    // 移除选中产品
    handleRemove(row) {
      this.currentRows = this.currentRows.filter(a => a.portfolioId !== row.portfolioId)
    },
    // 打开评价弹框
    openEvaluateModal() {
      this.modalConfig.visible = true
      this.modelConfig.form.modelId = this.modelConfig.list[0].modelId
    },
    // 开始评价
    handleEvaluate() {
      const { modelId, dateList } = this.modelConfig.form
      const indx = this.modelConfig.list.findIndex(l => l.modelId === modelId)
      this.loading = true
      API.Evaluate({
        vdate: dateList[indx]?.date[0],
        mdate: dateList[indx]?.date[1],
        ...this.modelConfig.list.find(l => l.modelId === modelId),
        portfolioIdList: this.currentRows.map(a => a.portfolioId)
      }, (isSuccess, { modelId, data, column }) => {
        this.loading = false
        if (isSuccess) {
          this.tableConfig = { modelId, data, column: column.map(col => {
            return {
              ...col,
              render: (h, { row }) => {
                const val = row[col.prop]
                if (col.prop === 'prodName') {
                  return <span>{this.currentRows.find(c => c.portfolioId === val).portfolioName}</span>
                }
                if (col.type === 'star') {
                  return <el-rate value={val}></el-rate>
                }
                return <span>{val}</span>
              }
            }
          })
          }
          this.handleEvaluateCancel()
        }
      })
    },
    // 评价模型时间选择
    handleCheck(indx) {
      const systemTime = this.$store.getters.systemTime
      const platDate = FormatDate(systemTime, 'yyyy-MM-dd')
      this.modelConfig.form.dateList[indx].date = [moment(new Date(systemTime)).subtract(1, 'years').format('YYYY-MM-DD'), platDate]
    },
    // 评价模型取消
    handleEvaluateCancel() {
      this.modalConfig.visible = false
    },
    // 发布
    handleOk() {
      this.loading = true
      API.Publish({ ...this.tableConfig, column: undefined }, (isSuccess) => {
        this.loading = false
        if (isSuccess) {
          this.msgSuccess('评价成功')
          this.closeModal(isSuccess)
        }
      })
    }
  }
}
</script>

<style lang="scss">
.performance-evaluation {
  .w-300.el-date-editor {
    width: 300px !important;
  }
  .layout-flex {
    display: flex;
    line-height: 36px;
    align-items: center;
    &>* {
      margin-right: 10px !important;
    }
  }
  .el-tag {
    margin-right: 10px;
  }
  .btn-link {
    cursor: pointer;
    color: var(--theme--color);
    font-size: 16px;
  }
}
</style>
