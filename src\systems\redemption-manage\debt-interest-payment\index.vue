<template>
  <div style="height: calc(100% - 20px)" class="coin-bonds-form">
    <div class="main">
      <div class="top-content public-table-search-container long-form">
        <el-form inline :model="form" class="coin-bonds-form-content" label-width="80">
          <jr-form-item label="发行人">
            <el-input v-model="form.issuer" style="height: 32px" placeholder="请输入发行人" />
          </jr-form-item>
          <jr-form-item label="债券简称">
            <el-input v-model="form.bondName" style="height: 32px" placeholder="请输入债券简称" />
          </jr-form-item>
          <jr-form-item label="支付状态">
            <jr-combobox
              v-model="form.paymentStatus"
              style="height: 32px"
              placeholder="请选择"
              clearable
              filterable
              :data="paymentStateList"
              option-value="itemcode"
              option-label="cnname"
              :multiple="true"
              :collapse-tags="true"
            />
          </jr-form-item>
          <jr-form-item label="兑付类型">
            <jr-combobox
              v-model="form.paymentType"
              style="height: 32px"
              placeholder="请选择"
              clearable
              filterable
              :data="redemptionTypeList"
              option-value="itemcode"
              option-label="cnname"
              :multiple="true"
              :collapse-tags="true"
            />
          </jr-form-item>
          <jr-form-item label="支付日期">
            <el-date-picker
              v-model="form.customDateRange"
              style="height: 32px"
              type="daterange"
              unlink-panels
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="yyyy-MM-dd"
              class="date-picker"
              @change="changeDate"
            />
          </jr-form-item>
          <jr-form-item style="height: 32px">
            <jr-radio-group v-model="form.include_expired" :data="checkboxList" cancelable @change="changeCustomDate" />
          </jr-form-item>
        </el-form>
        <div class="submit-btn">
          <el-button type="primary" @click="submit">查询</el-button>
        </div>
      </div>

      <div class="bottom-content">
        <custom-debt-actions
          :menuinfo="menuinfo"
          :permitdetail="permitdetail"
          @edit="edit"
          @calculate="calculate"
          @exportData="exportData"
        />
        <jr-decorated-table
          ref="jrTable"
          :params="tableParams"
          style="height: calc(100% - 100px) !important"
          custom-id="8e92f32055574a9c84adeffd33022651"
          :custom-render="tableColumns"
          :menuinfo="menuinfo"
          v-bind="{ ...$attrs, ...$props }"
          :handle-set-payment-status="handleSetPaymentStatus"
          :handle-module-download="handleModuleDownload"
          :handle-payment-notice="handlePaymentNotice"
          @handleSelectionChange="getSelectRows"
          @refreshed="callFn"
        />

        <paymentDetails v-if="false" />

        <div class="coin-bonds-tips" style="padding-left: 16px">
          <span><jr-svg-icon icon-class="info-circle" /></span>
          <span>债项付息信息仅供参考，支付金额以收款机构提供的支付信息为准。</span>
        </div>

        <div class="coin-bonds-tips" style="padding-left: 16px">
          <span><jr-svg-icon icon-class="info-circle" /></span>
          <span>部分发行方式为私募的债券暂未纳入统计范围。</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import customDebtActions from '../components/custom-debt-actions.vue'
import paymentDetails from '../components/payment-details.vue'
import { exportExcelByCustomColumn } from '@/api/public/public'
import moment from 'moment'
const REDEMPTIONTYPE = 'REDEMPTION_TYPE' // 兑付类型字典字段
import { GetComboboxList } from '@/api/home'
import {
  bondRedemptionSavePaymentInfo,
  bondRedemptionUpdatePaymentStatus
} from '@/api/redemption-manage/redemption-manage'
export default {
  components: {
    customDebtActions,
    paymentDetails
  },
  props: {
    menuinfo: {
      type: Object,
      default: () => ({})
    },
    permitdetail: {
      type: Object,
      default: () => ({})
    },
    params: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      form: {},
      checkboxList: [
        {
          label: '近一周',
          value: '1'
        },
        {
          label: '近一月',
          value: '2'
        }
      ],
      tableParams: {
        status: ''
      },
      tableColumns: {},
      columns: [],
      selectRows: [],
      activePopoverCode: null,
      redemptionTypeList: [],
      paymentStateList: [
        { itemcode: 0, cnname: '未支付' },
        { itemcode: 1, cnname: '已支付' }
      ]
    }
  },
  created() {
    this.getDictOptionsApi()
    this.tableColumns = {
      sInfoName: (h, { rowIndex, row }) => {
        return (
          <el-popover
            placement='right-start'
            width='1324'
            trigger='click'
            class='custom-popover-item-debt'
            onShow={() => this.handlePopoverShow(rowIndex, row)}
          >
            {this.activePopoverCode === rowIndex && (
              <paymentDetails
                menuinfo={this.menuinfo}
                permitdetail={this.permitdetail}
                sInfoWindcode={row.sInfoWindcode}
              />
            )}
            <span
              style='color:var(--theme--color);cursor:pointer'
              slot='reference'
              onClick={() => {
                this.handleButtonClick(row, rowIndex)
              }}
            >
              {row.sInfoName}
            </span>
          </el-popover>
        )
      },
      actPaySum: (h, { rowIndex, row }) => {
        return (
          <jr-amount-input
            v-model={row.actPaySum}
            precision={2}
            placeholder='请输入'
            class='cell-padding'
            onBlur={() => {
              this.saveRowInputData(row)
            }}
          />
        )
      },
      realPaymentDate: (h, { rowIndex, row }) => {
        return (
          <el-date-picker
            value={row.realPaymentDate}
            type='date'
            placeholder='请选择日期'
            format='yyyy-MM-dd'
            class='cell-padding'
            style='width:220px !important'
            onInput={(e) => {
              this.saveRowInputData(row, e)
            }}
          />
        )
      }
    }
  },
  methods: {
    resetSearch(obj) {
      if (Object.hasOwnProperty.call(obj, 'searchFlag')) {
        delete obj['searchFlag']
      } else {
        obj['searchFlag'] = '1'
      }
      return obj
    },
    handleSetPaymentStatus(...args) {
      if (Array.isArray(args) && args.length >= 3) {
        const rowData = args[2]
        console.log('handleSetPaymentStatus', rowData)

        const params = {
          paymentStatus: 1,
          objectId: rowData.objectId
        }

        bondRedemptionUpdatePaymentStatus(params).then(() => {
          this.$message({
            type: 'success',
            message: '修改成功'
          })

          this.submit()
        })
      }
    },
    handleModuleDownload(...args) {
      if (Array.isArray(args) && args.length >= 3) {
        const rowData = args[2]
        console.log('handleModuleDownload', rowData)
      }
    },
    handlePaymentNotice(...args) {
      if (Array.isArray(args) && args.length >= 3) {
        const rowData = args[2]
        console.log('handlePaymentNotice', rowData)
      }
    },
    callFn(data) {
      console.log('data', data)
      if (data.columns && Array.isArray(data.columns) && data.columns[10]) {
        data.columns[10].width = 250
        data.columns[10].ellipsis = false
      }

      this.columns = data.config.columns
      this.sort = data.sort
      this.direction = data.direction
    },
    getSelectRows(rows, listData) {
      this.selectRows = rows
    },
    async exportData() {
      const params = {
        params: {
          pageInfo: {},
          filename: '债项付息兑付',
          column: this.columns,
          selectData: Array.isArray(this.selectRows) && this.selectRows.length > 0 ? this.selectRows : null,
          ccid: '8e92f32055574a9c84adeffd33022651',
          ownedModuleid: '1352317240954667008'
        },
        page: {
          sort: this.sort,
          direction: this.direction
        }
      }
      await exportExcelByCustomColumn(params)
    },
    edit() {},
    calculate() {},
    saveRowInputData(row, date) {
      console.log(row)

      console.log('actPaySum', row.actPaySum, 'realPaymentDate', row.realPaymentDate)

      const params = {
        actPaySum: row.actPaySum,
        realPaymentDate: row.realPaymentDate || date ? moment(row.realPaymentDate || date).format('YYYYMMDD') : null,
        objectId: row.objectId
      }

      bondRedemptionSavePaymentInfo(params).then(() => {
        this.$message({
          type: 'success',
          message: '修改成功'
        })

        this.submit()
      })
    },
    handleSearchParams() {
      const obj = {
        issuer: this.form.issuer,
        bondName: this.form.bondName,
        status:
          Array.isArray(this.form.paymentStatus) && this.form.paymentStatus.length > 0
            ? this.form.paymentStatus.join(',')
            : '',
        types: this.form.paymentType
      }
      if (Array.isArray(this.form.customDateRange) && this.form.customDateRange.length > 0) {
        obj.paymentDateStart = moment(this.form.customDateRange[0]).format('YYYY-MM-DD')
        obj.paymentDateEnd = moment(this.form.customDateRange[1]).format('YYYY-MM-DD')
      } else {
        obj.paymentDateStart = null
        obj.paymentDateEnd = null
      }

      return {
        ...this.tableParams,
        ...obj
      }
    },
    submit() {
      this.tableParams = this.resetSearch(this.handleSearchParams())
    },
    /**
     * 自定義時間清空
     *  **/
    changeDate(e) {
      this.form.include_expired = null
    },
    changeCustomDate(e) {
      // 获取当前日期作为结束时间
      const endDate = new Date()
      if (e === '1') {
        // 计算一周前的日期
        const oneWeekAgo = new Date()
        oneWeekAgo.setDate(oneWeekAgo.getDate() - 7)
        this.form.customDateRange = [oneWeekAgo, endDate]
      } else if (e === '2') {
        // 计算一月前的日期
        const oneMonthAgo = new Date()
        oneMonthAgo.setMonth(oneMonthAgo.getMonth() - 1)
        this.form.customDateRange = [oneMonthAgo, endDate]
      }
    },
    handlePopoverShow(index, row) {
      this.$nextTick(() => {
        const elList = document.querySelectorAll('.custom-popover-item-debt')

        for (let i = 0; i < elList.length; i++) {
          const popoverInstance = elList[i].__vue__

          if (i !== index) {
            if (popoverInstance?.doClose) popoverInstance.doClose()
          }
        }

        this.activePopoverCode = index
      })
    },
    handleButtonClick(row, index) {
      this.activePopoverCode = index
    },
    async getDictOptionsApi() {
      const res = await GetComboboxList([REDEMPTIONTYPE])
      this.redemptionTypeList = res[REDEMPTIONTYPE]
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .jr-decorated-table--header {
  display: none !important;
}

::v-deep .el-range-separator {
  line-height: 30px !important;
}

::v-deep .jr-table{
  .cell:has(.cell-padding) {
    padding: 0 !important;
    display: flex !important;
    align-items: center !important;
  }
}

.main {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #ffffff;

  .top-content {
    position: relative;
    padding: 0 16px 16px 16px;
    border-bottom: 1px solid #eae9e9;

    ::v-deep .el-date-editor {
      padding-top: 0 !important;
      padding-bottom: 0 !important;
    }
  }

  .bottom-content {
    flex: 1;
    background-color: #ffffff;
    position: relative;
    display: flex;
    flex-direction: column;
    padding-bottom: 16px;
    height: calc(100% - 80px);

    ::v-deep .cell:has(.el-date-editor) {
      text-overflow: clip !important;
    }
  }
}

.coin-bonds-form {
  .vertical-layout {
    background: #fff;
    padding: 0 16px;
  }

  &-content {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    width: calc(100% - 64px);

    ::v-deep .el-form-item {
      display: flex;
      align-items: center;
      width: calc(25% - 10px);

      .el-form-item__label {
        // padding-top: 10px !important;
        flex-shrink: 0;
        display: flex !important;
        align-items: center;
        padding-top: 0 !important;
        width: 68px;
      }

      .el-form-item__content {
        flex: 1 !important;
        flex-shrink: 0;
        display: flex !important;
        align-items: center;
        .el-date-editor {
          flex: 1 !important;
          flex-shrink: 0;
          width: auto !important;
        }
      }
    }

    ::v-deep .no-label {
      .el-form-item__label {
        width: 0;
      }
    }

    .coin-bonds-tips {
      height: 19px;
      font-family: MicrosoftYaHei;
      font-size: var(--el-font-size-base);
      color: rgba(0, 0, 0, 0.6);
      line-height: 19px;
      text-align: left;
      font-style: normal;
      display: flex;
      align-items: center;
      gap: 3px;
    }

    ::v-deep .el-radio {
      margin-right: 0 !important;
      min-width: 80px !important;
    }
  }

  .submit-btn {
    position: absolute;
    top: 8px;
    right: 16px;
  }
}

@media screen and (max-width: 1600px) {
  .coin-bonds-form {
    &-content {

      ::v-deep .el-form-item {
        width: calc(33.33% - 10px);
      }
    }
  }
}
</style>
