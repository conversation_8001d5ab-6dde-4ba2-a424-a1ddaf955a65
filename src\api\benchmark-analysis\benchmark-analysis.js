import { GetListInfo, UpdateFn, ExportFn } from '@jupiterweb/utils/api'

// 债券对标API前缀
const APIPRE_BOND = '/benchmarking/benchmarkingConfig/'

/**
 * 查询获取对标分析分组信息
 * @param {*} params 查询参数
 */
export const getBenchmarkGroupList = params => GetListInfo(`${APIPRE_BOND}getBenchmarkingGroup`, params)

/**
 * 查询获取对标企业信息
 * @param {*} params 查询参数
 */
export const getBenchmarkCompanyList = params => GetListInfo(`${APIPRE_BOND}getBenchmarkingCompany`, params)

/**
 * 查询获取已配置的对标企业信息
 * @param {*} params 查询参数
 */
export const getBenchmarkCompanyConfigList = params => GetListInfo(`${APIPRE_BOND}getBenchmarkingRelation`, params)

/**
 * 查询获取已配置的对标企业下拉选择信息
 * @param {*} params 查询参数
 */
export const getBenchmarkCompanySelectList = params => GetListInfo(`${APIPRE_BOND}getBenchmarkingRelationSelect`, params)

/**
 * 查询获取对标企业债券类型下拉选择信息
 * @param {*} params 查询参数
 */
export const getBenchmarkBondTypeSelectList = params => GetListInfo('/bond/bondDescription/queryDictBondType', params)

/**
 * 更新对标企业配置信息
 * @param {*} params 输入参数
 * @param {*} cb 回调函数
 */
export const updateBenchmarkingConfig = (params, cb) => UpdateFn(`${APIPRE_BOND}updateBenchmarkingConfig`, params, cb)

/**
 * 查询获取债券对标chart数据
 * @param {*} params 查询参数
 */
export const getBondBenchmarkChartData = params => GetListInfo(`${APIPRE_BOND}getBenchmarkingBond${params.benchmarkType}Chart`, params)

/**
 * 查询获取债券对标列表数据
 * @param {*} params 查询参数
 */
export const getBondBenchmarkList = params => GetListInfo(`${APIPRE_BOND}getBenchmarkingBond${params.benchmarkType}List`, params)

/**
 * 导出债券对标数据到Excel
 * @param {*} params 导出参数
 */
export const exportBondBenchmarkToExcel = params => ExportFn(`${APIPRE_BOND}exportExcelByBond${params.benchmarkType}Column`, params)

/**
 * 查询获取财务对标报告期下拉选择信息
 * @param {*} params 查询参数
 */
export const getFinancialBenchmarkReportPeriod = params => GetListInfo(`${APIPRE_BOND}getBenchmarkingAssetReportYear`, params)

/**
 * 查询获取财务对标数据
 * @param {*} params 查询参数
 */
export const getFinancialBenchmarkList = params => {
  // 财务对标类型：0-指标对比，1-趋势分析
  const methodName = params.benchmarkType === 1 ? 'getBenchmarkingAssetTrend' : 'getBenchmarkingAssetList'
  return GetListInfo(`${APIPRE_BOND}${methodName}`, params)
}

/**
 * 查询获取财务指标数据
 * @param {*} params 查询参数
 */
export const getFinancialIndicatorsList = params => GetListInfo(`${APIPRE_BOND}getBenchmarkingAssetSetting`, params)

/**
 * 保存财务指标信息
 * @param {*} params 输入参数
 * @param {*} cb 回调函数
 */
export const saveFinancialIndicators = (params, cb) => UpdateFn(`${APIPRE_BOND}addWprcCustomSetting`, params, cb)

/**
 * 导出财务对标数据到Excel
 * @param {*} params 导出参数
 */
export const exportFinancialBenchmarkToExcel = params => ExportFn(`${APIPRE_BOND}exportExcelByAssetColumn`, params)
