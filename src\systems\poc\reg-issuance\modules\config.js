export const moduleConfig = {

  configTable: {
    loading: false,
    columns: [
      {
        title: '对标企业',
        prop: 'field1',
        width: 200
      },
      {
        title: 'MTN',
        prop: 'field2'
      },
      {
        title: 'PPN',
        prop: 'field3'
      },
      {
        title: '企业债劵',
        prop: 'field4'
      },
      {
        title: '公募公司债',
        prop: 'field5'
      },
      {
        title: '私募公司债',
        prop: 'field5'
      }
    ],
    data: [],
    pagination: {
      pageNo: 1,
      pageSize: window.screen.height > 1000 ? 25 : 15,
      pageSizeOptions: [15, 25, 50, 100],
      total: 0,
      showSizeChanger: true
    }
  },
  configTable1: {
    loading: false,
    columns: [
      {
        title: '债券简称',
        prop: 'field1',
        width: 200
      },
      {
        title: '债券类型',
        prop: 'field2'
      },
      {
        title: '余额(亿)',
        prop: 'field3'
      },
      {
        title: '起息日',
        prop: 'field4'
      },
      {
        title: '剩余期限',
        prop: 'field5'
      },
      {
        title: '行权日',
        prop: 'field5'
      },
      {
        title: '到期日',
        prop: 'field6'
      },
      {
        title: '票面利率(%)',
        prop: 'field7'
      },
      {
        title: '重债估值(%)',
        prop: 'field8'
      },
      {
        title: '估值日期',
        prop: 'field9'
      }
    ],
    data: [],
    pagination: {
      pageNo: 1,
      pageSize: window.screen.height > 1000 ? 25 : 15,
      pageSizeOptions: [15, 25, 50, 100],
      total: 0,
      showSizeChanger: true
    }
  },
  configTable2: {
    loading: false,
    columns: [
      {
        title: '交易日期',
        prop: 'field1'
      },
      {
        title: '债券简称',
        prop: 'field2',
        with: 200
      },
      {
        title: '剩余年限',
        prop: 'field3'
      },
      {
        title: '成交来源',
        prop: 'field4'
      },
      {
        title: '收盘YTM(%)',
        prop: 'field5'
      },
      {
        title: '前一日中债估值(%)',
        prop: 'field5'
      },
      {
        title: 'YTM偏离(BP)',
        prop: 'field6'
      },
      {
        title: '收盘市价(元)',
        prop: 'field7'
      },
      {
        title: '成交量(万)',
        prop: 'field8'
      },
      {
        title: '成交额(万)',
        prop: 'field9'
      }
    ],
    data: [],
    pagination: {
      pageNo: 1,
      pageSize: window.screen.height > 1000 ? 25 : 15,
      pageSizeOptions: [15, 25, 50, 100],
      total: 0,
      showSizeChanger: true
    }
  },
  configTable3: {
    loading: false,
    columns: [
      {
        title: '文件名称',
        prop: 'field1'
      },
      {
        title: '上传人',
        prop: 'field2',
        with: 200
      },
      {
        title: '上传时间',
        prop: 'field3'
      },
      {
        title: '操作',
        prop: 'field4'
      }
    ],
    data: [],
    pagination: {
      pageNo: 1,
      pageSize: window.screen.height > 1000 ? 25 : 15,
      pageSizeOptions: [15, 25, 50, 100],
      total: 0,
      showSizeChanger: true
    }
  },
  configForm: {
    model: {},
    data: [
      {
        title: '预计价格区间(%)',
        prop: 'field1',
        type: 'text'
      },
      {
        title: '顶计报备区向(%)',
        prop: 'field2',
        type: 'text'
      },
      {
        title: '拟发行金额(亿)',
        prop: 'field3',
        type: 'text'
      },
      {
        title: '发行期限',
        prop: 'field4',
        type: 'rangeDate'
      },
      {
        title: '预计薄记日',
        prop: 'field5',
        type: 'rangeDate'
      },
      {
        title: '预计缴款日',
        prop: 'field6',
        type: 'rangeDate'
      },
      {
        title: '预计到账日',
        prop: 'field7',
        type: 'rangeDate'
      },
      {
        title: '预计起息日',
        prop: 'field8',
        type: 'rangeDate'
      },
      {
        title: '预计到期日',
        prop: 'field9',
        type: 'rangeDate'
      }
    ]
  },
  chartOption: {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        crossStyle: {
          color: '#333'
        }
      }
    },
    grid: {
      bottom: '20%',
      top: '15%',
      left: 50,
      right: 60
    },
    legend: {
      x: 'center',
      y: 'bottom',
      orient: 'horizontal'
    },
    xAxis: [{
      type: 'category',
      axisTick: {
        show: false
      },
      axisLine: {
        show: true,
        linestyle: {
          color: '#dddddd'
        }
      },
      axisLabel: {
        show: true,
        intervel: 0,
        rotate: 0,
        textstyle: {
          color: '#8c8c8c',
          fontsize: '12'
        }
      },
      data: []
    }],
    yAxis: [{
      type: 'value',
      show: true,
      nane: '单位：亿',
      axisTick: {
        show: true
      },
      axisLine: {
        show: true,
        linestyle: {
          color: '#909090'
        }
      },
      splitLine: {
        show: true,
        linestyle: {
          color: '#dddddd'
        }
      }
    }],
    series: [
      {
        name: '市场发行量',
        type: 'bar',
        barWidth: '30%',
        label: {
          normal: {
            show: false
          }
        },
        itemStyle: {

        },
        z: 1,
        data: [3000, 6000, 4700, 6700, 4800, 4200]
      },
      {
        name: '协会发行量',
        type: 'bar',
        barGap: '-70%',
        barWidth: '30%',
        label: {
          normal: {
            show: false
          }
        },
        itemStyle: {

        },
        z: 2,
        data: [2000, 5000, 3700, 5700, 3800, 3200]
      },
      {
        name: '净融资额',
        type: 'line',
        showSymbol: true,
        symbol: 'circle',
        hoverAnimation: false,
        smooth: true,
        z: 3,
        itemstyle: {
          normal: {
            color: '#00d2db'
          }
        },
        data: [-3000, 1200, 2100, 1800, 1600, 1400]
      }
    ]
  }
}
