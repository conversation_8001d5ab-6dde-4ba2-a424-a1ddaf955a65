import { GetInfoFn, UpdateFn } from '@jupiterweb/utils/api'

// 获取组合树-单一计算
export const getAllPortfolioBySort = params => GetInfoFn('invest/riskredis/quota/superviseindicator/RskSupervisionIndicator002/getAllPortfolioBySort', params)

// 获取组合树-联合计算
export const getAllPortfolioBag = params => GetInfoFn('invest/riskredis/quota/superviseindicator/RskSupervisionIndicator002/getAllPortfolioBag', params)

export const editDelCheck = (params, cb) => UpdateFn('invest/riskredis/quota/portfolioindicator/RskSupervisionPortfPoint003/checkDelete', params, cb)

export const deleteData = (params, cb) => UpdateFn('invest/riskredis/quota/portfolioindicator/RskSupervisionPortfPoint003/delete', params, cb)

// 获取筛选监督指标
export const getRelatedIndList = params => GetInfoFn('invest/riskredis/quota/portfolioindicator/RskSupervisionPortfPoint002/queryRelatedIndList', params)
// 获取组合
// export const getAllPortfolioBySort = params => GetInfoFn('invest/riskredis/quota/superviseindicator/RskSupervisionIndicator002/getAllPortfolioBySort', params)
