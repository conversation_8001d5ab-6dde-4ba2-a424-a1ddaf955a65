<template>
  <el-table :data="tableData" border style="width: 220px">
    <el-table-column prop="code" label="码值" width="80" align="center"></el-table-column>
    <el-table-column prop="range" label="债券期限区间" align="center"></el-table-column>
  </el-table>
</template>

<script>
export default {
  data() {
    return {
      tableData: [
        { code: '1M', range: '(0-1.5M]' },
        { code: '2M', range: '(1.5-2.5M]' },
        { code: '3M', range: '(2.5-4.5M]' },
        { code: '6M', range: '(4.5M-7.5M]' },
        { code: '9M', range: '(7.5M-10.5M]' },
        { code: '1Y', range: '(10.5M-1.5Y]' },
        { code: '2Y', range: '(1.5Y-2.5Y]' },
        { code: '3Y', range: '(2.5Y-3.5Y]' },
        { code: '4Y', range: '(3.5Y-4.5Y]' },
        { code: '5Y', range: '(4.5Y-5.5Y]' },
        { code: '6Y', range: '(5.5Y-6.5Y]' },
        { code: '7Y', range: '(6.5Y-7.5Y]' },
        { code: '8Y', range: '(7.5Y-8.5Y]' },
        { code: '9Y', range: '(8.5Y-9.5Y]' },
        { code: '10Y', range: '(大于9.5Y]' }
      ]
    }
  }
}
</script>

<style scoped lang="scss">
::v-deep .el-table__cell {
  padding: 0 !important;
  background-color: #303133 !important;


  .cell {
    font-size: 12px !important;
    background-color: #303133 !important;
    color: #fff !important;
  }
}
</style>
