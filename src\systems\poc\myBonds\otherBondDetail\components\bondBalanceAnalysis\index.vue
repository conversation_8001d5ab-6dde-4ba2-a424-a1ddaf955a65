<!-- 债券余额分析 -->
<template>
  <div class="bondBalanceAnalysis">
    <div class="bondBalanceAnalysisTitle">
      债券发行规模统计
      <jr-combobox
        v-model="tzjg"
        class="combobox"
        placeholder="请选择"
        clearable
        filterable
        multiple
        collapse-tags
        :data="tzjgList"
        option-value="id"
        option-label="text"
      />
    </div>
    <div class="content">
      <div class="leftContent">
        <div class="total">
          存续期境外债券/境内区内债券共
          <span class="num"> 4 </span>只，美元债<span class="num"> 2 </span>只， 规模<span class="num"> 21.4200 </span>亿美元。按照最新汇率折算，余额总计<span class="num"> 36.2757 </span>
          亿元
        </div>
        <Table />
      </div>
      <div class="rightContent">
        <TemplateModule chart-type="PIE" chart-seq="d886ddb682e34b60ae62ca5cc49778b3" />
      </div>
    </div>
  </div>
</template>

<script>
import TemplateModule from '@jupiterweb/components/template-module'
import Table from './table.vue'
export default {
  components: { Table, TemplateModule },
  data() {
    return {
      tzjg: '01',
      tzjgList: [
        { id: '01', text: '河北承德投资集团' }
      ]
    }
  },
  methods: {}
}
</script>
<style lang="scss">
.bondBalanceAnalysis {
    padding: 6px 10px 6px 10px;
    background: #fff;
    .bondBalanceAnalysisTitle {
        font-size: 16px;
        color: #303133;
        font-weight: 700;
        padding-bottom: 6px;
        border-bottom: 1px solid #EBEEF5;
        position: relative;
        .combobox {
          width: 200px;
          position: absolute;
          top: -1;
          left: 150px;
        }
    }
    .total {
        text-align: center;
        padding: 10px;
        color: #606266;
        .num {
            color: #E6A23C;
        }
    }
    .content {
        display: flex;
        .leftContent {
            flex: 1;
        }
        .rightContent {
            flex: 1;
        }
    }
}
</style>
