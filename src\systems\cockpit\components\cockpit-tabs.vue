<template>
  <div class="cockpitTabs">
    <div ref="tabArea" class="cockpitTabs-tabArea">
      <div
        v-for="(tab, index) in tabs"
        :key="index"
        ref="tab"
        class="cockpitTabs-tabArea-tab"
        @click.stop="tabChange(tab, index)"
      >
        <span :class="tab.tabName === activeTab.tabName ? 'cockpitTabs-tabArea-tab-active' : ''">{{ tab.tabName }}</span>
        <img v-show="tab.tabName === activeTab.tabName" src="@/assets/cockpit/cockpit_active_tab.png"/>
      </div>
      <slot />
    </div>
    <div v-if="hideMore" class="cockpitTabs-more" @click.stop="handleMore">
      <span>{{ moreTitle }}</span>
      <img src="@/assets/cockpit/more_icon.png" alt="">
    </div>
  </div>
</template>

<script>
export default {
  name: 'CockpitTabs',
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    tabs: {
      type: Array,
      default: () => []
    },
    moreTitle: {
      type: String,
      default: '更多'
    },
    hideMore: {
      type: Boolean,
      default: true
    },
    type :{
      type: String,
      default: 'deep'
    }
  },
  data() {
    return {
      activeTab: {},
      left: 0
    }
  },
  watch: {
    tabs: {
      immediate: true,
      deep: true,
      handler() {
        if (this.tabs.length > 0) {
          this.activeTab = this.tabs[0]
          this.$emit('change', this.activeTab)
        }
      }
    }
  },
  methods: {
    /**
     * tab切换触发
     * @param tab 
     * @param index 
     */
    tabChange(tab, index) {
      const activeIndex = this.tabs.findIndex((item) => item.tabName === this.activeTab.tabName)
      if (this.activeTab.tabName !== tab.tabName) {
        this.activeTab = tab
        this.$emit('change', this.activeTab)
        const rect = this.$refs.tab[index].getBoundingClientRect()
        if (index < activeIndex) {
          this.left = this.left - rect.width / 2
        } else {
          this.left = this.left + rect.width / 2
        }
        this.$refs.tabArea.scrollTo({
          left: this.left,
          behavior: 'smooth'
        })
      }
    },
    /**
     * 处理更多区域的点击事件
     */
    handleMore(){
      this.$emit("handleMore")
    },
  }
}
</script>

<style lang="scss" scoped>
@import '~assets/styles/portcss.scss';
.cockpitTabs {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: vw(16);
  &-tabArea {
    width: 100%;
    display: flex;
    overflow-x: scroll;
    &-tab {
      min-width: vw(60);
      display: flex;
      align-items: center;
      flex-direction: column;
      margin-right: vw(16);
      gap: vh(3);
      cursor: pointer;
      flex-shrink: 0;
      text-align: center;
      & > span:nth-of-type(1) {
        height: vh(19);
        font-size: vh(14);
        color: rgba(0,0,0,0.6);
        line-height: vh(19);
        letter-spacing: vw(1);
      }
      & > img {
        width: vw(60);
        height: vh(3);
      }
      &-active {
        color: #FF8E2B !important;
      }
    }
  }
  &-tabArea::-webkit-scrollbar {
    display: none; /* Chrome/Safari/Opera */
    width: 0;
  }
  &-more {
    flex-shrink: 0;
    height: vh(19);
    font-size: vh(14);
    color: #FF8E2B;
    line-height: vh(19);
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: vw(8);
    img{
      width: vw(6);
      height: vh(11);
    }
  }
}
</style>
