/**
* @description:组合分析  -》资产配置分析
* @param {type}
* @return: api
*/

import {
  GetListInfo,
  ExportFn
//   GetInfoFn
} from '@jupiterweb/utils/api'
// 查询列表数据invest/portfolio/ptlpositionanalysis/PtlPositionAnalysis001/findPage
export const findPage = params => GetListInfo('invest/portfolio/ptlpositionanalysis/PtlPositionAnalysis001/findPage', params)
export const findPageForAssetList = params => GetListInfo('invest/portfolio/ptlpositionanalysis/PtlPositionAnalysis001/findPageForAssetList', params)
// 导出
export const exportInfo = params => ExportFn(`invest/portfolio/ptlpositionanalysis/PtlPositionAnalysis001/export`, params)
