<template>
  <div>
    <collapse-panel title="基础组件">
      <jr-form-item-create :validate-rules="validateRules" :data="configData1" :model="data" />
    </collapse-panel>

    <collapse-panel title="组合按钮配置">
      <jr-form-item-create :validate-rules="validateRules" :data="configData2" :model="data" />
    </collapse-panel>

    <collapse-panel title="联动控制">
      <jr-form-item-create :validate-rules="validateRules" :data="configData3" :model="data" />
    </collapse-panel>

    <collapse-panel title="自定义检验">
      <jr-form-item-create :validate-rules="validateRules" :data="configData4" :model="data" />
    </collapse-panel>

    <collapse-panel title="可编辑表格">
      <jr-table-editor v-model="data.table" prop="table" :columns="configTableEdit.columns" />
    </collapse-panel>
  </div>
</template>

<script>
import { validEmail } from '@jupiterweb/utils/validate'
import { getModalProps, getModalComputed } from '@/systems/mixins'
// 使用 getModalProps 混入公共弹窗内的 prop，注意！，这里是示例，
// 有些 prop 参数可能无法获取到，通过自定义列按钮点击，过来的都能获取到

export default {
  mixins: [getModalProps, getModalComputed],
  // 手写一览列表这种最顶级入口页面可以直接通过菜单信息获取页面标示，this.getInit.menuinfo.pageId，子级比如弹窗内的 this.getInit.pageId
  // 通过公共弹窗打开的首个页面无需配置请求地址，会自动去读取，多层嵌套的弹窗需自己传入地址发送请求
  // mixins: [getInit('/invest/basicdata/mstcommissionset/MstCommissionSet002')], // 会自动拼上 /init，无需传入，示例接口为(交易所费用设置菜单)
  data() {
    return {
      configData1: [],
      configData2: [],
      configData3: [],
      configData4: [],
      configTableEdit: {
        columns: [
          {
            prop: 'code1',
            title: 'demo.flatIndex.update.table.code1',
            type: 'text',
            description: 'demo.flatIndex.update.table.code1.desc'
          },
          {
            prop: 'code2',
            title: 'demo.flatIndex.update.table.code2',
            type: 'date'
          },
          {
            prop: 'code3',
            title: 'demo.flatIndex.update.table.code3',
            type: 'amount'
          },
          {
            prop: 'code4',
            title: 'demo.flatIndex.update.table.code4',
            type: 'rate'
          },
          {
            prop: 'code5',
            title: 'demo.flatIndex.update.table.code5',
            type: 'select',
            options: [
              { text: '1111', value: '1111' },
              { text: '2222', value: '2222' },
              { text: '3333', value: '3333' }
            ],
            required: true
          }
        ]
      }
    }
  },
  created() {
    console.log('--页面初始化信息--：', this.getInit)
    this.init()
  },
  methods: {
    init() {
      const self = this

      // 基础组件
      self.configData1 = [
        {
          title: '开关',
          prop: 'field1',
          type: 'switch'
        },
        {
          title: '输入框',
          prop: 'field2',
          type: 'text',
          required: true
        },
        {
          title: '利率',
          prop: 'field3',
          type: 'rate',
          required: true
        },
        {
          title: '金额(元)',
          prop: 'field4',
          type: 'amount',
          change(v, s) {
            console.log('--333---', v, s)
          }
        },
        {
          title: '金额(万元)',
          prop: 'field5',
          type: 'tenThousand'
        },
        {
          title: '利率(区间型)',
          prop: 'field6',
          type: 'rateInputRange'
        },
        {
          title: '金额(元区间型)',
          prop: 'field7',
          type: 'amountInputRange'
        },
        {
          title: '金额(万元区间型)',
          prop: 'field8',
          type: 'tenThousandInputRange',
          required: true
        },
        {
          title: '日期',
          prop: 'field9',
          type: 'date'
        },
        {
          title: '日期区间',
          prop: 'field10',
          type: 'rangeDate'
        },
        {
          title: '下拉框',
          prop: 'field11',
          type: 'select',
          uiProps: {
            selectedLabel: 'id' // 可以指定选中后显示的字段 默认取 text
          },
          optionValue: 'id', // 可以指定绑定值的字段，默认取 vlaue
          options: self.getInit?.PS || [],
          // options: [
          //   { text: '1111', vlaue: '1' id: '1' },
          //   { text: '2222', value: '2' id: '2' },
          //   { text: '3333', value: '3', id: '3' }
          // ],
          change(value, row) {
            console.log(value, row)
            /*
            * 自动触发其他组件的 change 事件
            * trigger(eventName = "change", value)
            */

            self.$formItemCreate.prop('field13').trigger('change')
            // 赋值并支持链式操作 触发 change、设置置灰、必填 等所有在元素上定义的属性
            // self.$formItemCreate.prop('field13').options([{ text: '单选11', value: '11' }]).title('单选888').trigger('change', '11').disabled(true).required(false)
          },
          // 自定义插槽(一般无需使用，仅供特殊需求 )
          slot: {
            option: (h, { item }) => (
              <div>{ item.text } - { item.value } - 自定义插槽</div>
            )
          }
        },
        {
          title: '下拉表格',
          prop: 'field12',
          type: 'inputSelect',
          tableKey: 'demoTable',
          valueKey: 'bondInnerCode',
          displayKey: 'bondInnerCode'
        },
        {
          title: '单选',
          prop: 'field13',
          type: 'radio',
          options: [
            { text: '单选1', value: '1' },
            { text: '单选2', value: '2' }
          ],
          change(value, row) {
            console.log('--单选（field13） change --：', value)
          }
        },
        {
          title: '复选框',
          prop: 'field14',
          type: 'checkbox'
        },
        {
          title: '复选框组',
          prop: 'field15',
          type: 'checkboxgroup',
          options: [
            { text: '复选1', value: '1' },
            { text: '复选2', value: '2' }
          ]
        },
        {
          title: '下拉本地搜索',
          prop: 'field16',
          type: 'select',
          filterable: true,
          uiProps: {
            selectedLabel: 'value' // 可以指定选中后显示的字段 默认取 text
          },
          options: [
            { text: '数据一', value: '1111' },
            { text: '数据二', value: '2222' },
            { text: '数据三', value: '3333' }
          ]
        },
        {
          title: '下拉远程搜索',
          prop: 'field17',
          type: 'remoteSelect',
          // 定义 api 后将自动发起请求，无需配置下面 remoteMethod
          api: '/223/2323/34556',
          // remoteMethod 定义后上面的 api 配置失效，交由开发自己处理
          async remoteMethod(str) {
            // 模拟异步请求
            const data = await new Promise(resolve => {
              setTimeout(() => {
                resolve([
                  { text: '数据一', value: '1' },
                  { text: '数据二', value: '2' },
                  { text: '数据三', value: '3' }
                ])
              }, 2000)
            })

            return data
          }
        },
        {
          title: '输入远程搜索',
          prop: 'field18',
          type: 'remoteInputSelect',
          // 定义 api 后将自动发起请求，无需配置下面 remoteMethod
          api: '/223/2323/34556',
          // remoteMethod 定义后上面的 api 配置失效，交由开发自己处理
          async remoteMethod(str) {
            // 模拟异步请求
            const data = await new Promise(resolve => {
              setTimeout(() => {
                resolve([
                  { text: '数据一', value: '1' },
                  { text: '数据二', value: '2' },
                  { text: '数据三', value: '3' }
                ])
              }, 2000)
            })

            return data
          }
        },
        {
          title: '文本域',
          prop: 'field19',
          type: 'textarea'
        }
      ]

      // 组合按钮配置
      self.configData2 = [
        {
          title: '输入框',
          prop: 'field20',
          type: 'text',
          button: {
            // type: 'modal',
            type: 'iframe',
            save: 'api/test', // 初始化 init 的接口地址，必传(这里随便写的，开发需填写真实接口地址)
            componenturl: '/tams/invest/portfolio/ptlmanagementinfo/PtlManagementInfo002/init?dialog=1&pageModel=2&portfolioId=000078',
            itemData: {} // 传值
          }
        },
        {
          title: '下拉框',
          prop: 'field21',
          type: 'select',
          options: [
            { text: '1111', value: '1111' },
            { text: '2222', value: '2222' },
            { text: '3333', value: '3333' }
          ],
          button: {
            type: 'modal',
            save: 'api/test', // 初始化 init 的接口地址，必传(这里随便写的，开发需填写真实接口地址)
            componenturl: 'demo/update', // 跳转组建的 路径
            itemData: {} // 传值
          }
        }
      ]

      // 联动控制
      self.configData3 = [
        {
          title: '下拉框',
          prop: 'field22',
          type: 'select',
          options: [
            { text: '手动赋值', value: '1' },
            { text: '置灰', value: '2' },
            { text: '必填', value: '3' },
            { text: '隐藏', value: '4' }
          ],
          change(value, row, data) {
            self.$set(self.data, 'field23', value === '1' ? `自动赋值-${value}` : '')
          }
        },
        {
          title: '输入框',
          prop: 'field23',
          type: 'text',
          required: true,
          expr: {
            // 控制显示，等同于 v-if
            show: 'field22!=="4"',
            // 控制置灰
            disabled: 'field22==="2"',
            // 控制必填，优先级最大，这里设置了上面的 required 失效
            required: 'field22==="3"'
          }
        }
      ]

      // 自定义检验
      self.configData4 = [
        {
          title: '邮箱格式',
          prop: 'field24',
          type: 'text',
          rules: [
            { required: true, trigger: 'blur' },
            { validator: self.validator, trigger: 'blur' }
          ]
        }
      ]
    },
    validator(rules, value, callback) {
      let message

      if (!validEmail(value)) {
        message = '邮箱格式不正确'
      }

      callback(message)
    }
  }
}
</script>

<style>

</style>
