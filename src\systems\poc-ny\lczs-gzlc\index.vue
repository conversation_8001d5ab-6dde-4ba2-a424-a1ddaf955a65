<template>
  <div class="lczs-gzlc">
    <jr-radio-group v-model="current" type="button" :data="list" size="mini" />
    <b-template-module :key="current" :chart-seq="current" :custom-options="current === '********************************' ? {...customOptions, ...baseCustomOptions} : {...baseCustomOptions}" style="margin-bottom: 20px;" />
    <el-button class="float-right" type="text">
      <jr-svg-icon icon-class="export" />
      导出
    </el-button>
    <el-form class="form-items-container" :model="form">
      <jr-form-item-create :data="fieldList.slice(0, current === '********************************' ? 2: 3)" :model="form" :column="current === '********************************' ? 2: 3" />
      <el-button type="primary" @click="submit">查询</el-button>
      <el-checkbox v-show="current === '********************************'" v-model="form.ydq" checked>含已到期</el-checkbox>
    </el-form>
    <template-module style="height: 200px" :chart-seq="list.find(a => a.value === current).tableId" chart-type="TABLE" />
  </div>
</template>
<script>
export default {
  data() {
    return {
      current: '********************************',
      list: [
        {
          text: '上市首日估值偏离度',
          value: '********************************',
          tableId: 'ae13e3a8bbb44362a1c2479e4837b603'
        },
        {
          text: '信用利差',
          value: 'ea8496957fe040ecb9770a095263f5b2',
          tableId: 'eff24878640b49ee852d8bbc46e0b257'
        },
        {
          text: '超额利差',
          value: '2c71df806e504b8b9675038970080092',
          tableId: '709f87b40b9a431cac2f32aa4082ab82'
        }
      ],
      form: {
        qymc: 'hbsd',
        gzrq: '2024-09-06'
      },
      baseCustomOptions: {
        legend: {
          orient: 'horizontal',
          left: '20',
          top: 10,
          textStyle: {
            color: '#333'
          },
          show: true
        }
      },
      customOptions: {
        series: [
          {
            type: 'scatter'
          }
        ]
      },
      fieldList: [
        {
          title: '债券名称',
          prop: 'zqmc',
          type: 'select',
          options: [
          ]
        },
        {
          title: '企业名称',
          prop: 'qymc',
          type: 'select',
          options: [
            {
              text: '河北顺德投资集团有限公司',
              value: 'hbsd'
            }
          ]
        },
        {
          title: '估值日期',
          type: 'date',
          prop: 'gzrq',
          uiProps: {
            valueFormat: 'yyyy-MM-dd'
          }
        }
      ]
    }
  },
  methods: {
    submit() {
      console.log(this.form)
    }
  }
}
</script>
<style lang="scss" scoped>
.lczs-gzlc {
  background: #fff;
  width: 100%;
  height: 100%;
  padding: 12px;
  .float-right {
  }
  .el-form.form-items-container {
    display: flex;
    align-items: center;
    padding-right: 30%;
    .el-button {
      margin-left: 20px;
      margin-right: 20px;
    }
  }
}
</style>
