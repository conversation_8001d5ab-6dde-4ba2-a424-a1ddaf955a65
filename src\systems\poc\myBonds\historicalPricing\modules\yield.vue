<template>
  <div class="page-market-rate--yield">
    <div class="page-market-rate--yield-title">
      <span class="title-text">发行定价估值
        <el-tooltip class="item" effect="dark" content="历史定价曲线由系统每隔一定周期采集的定价估值构成，展示定价估值变动趋势" placement="top">
          <jr-svg-icon icon-class="exclamation-circle" />
        </el-tooltip>
      </span>
    </div>
    <div class="page-market-rate--yield-content">
      <div v-for="item in list" :key="item.yield" class="list-item" :class="activeName === item.yield && 'active'" @click="activeName = item.yield">
        <h3>{{ item.yield }}</h3>
        <div>
          <label>发行方式</label>
          <span class="val">{{ item.fxfs }}</span>
        </div>
        <div>
          <label>是否可续期</label>
          <span class="bp">{{ item.sfkxq }}</span>
        </div>
        <div>
          <label>定价估值</label>
          <span class="bp">{{ item.djgz }}</span>
        </div>
        <div>
          <label>是否可续期</label>
          <span class="bp">{{ item.sfkxq }}</span>
        </div>
        <div>
          <label>估值日期</label>
          <span class="bp">{{ item.gzrq }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>

export default {
  data() {
    return {
      activeName: '10Y',
      list: [
        {
          yield: '9M',
          fxfs: '公募',
          sfkxq: '不可续期',
          djgz: '2.191',
          gzrq: '2024-09-25'
        },
        {
          yield: '3Y',
          fxfs: '公募',
          sfkxq: '不可续期',
          djgz: '2.401',
          gzrq: '2024-09-25'
        }
      ]
    }
  },
  methods: {}
}
</script>

<style lang="scss" scoped>
.page-market-rate--yield {
  width: 100%;
  padding: 12px 12px 70px 12px;
  &-title {
    display: flex;
    justify-content: space-between;
    font-size: 14px;
    font-weight: bold;
  }
  &-content {
    margin-top: 12px;
    display: flex;
    column-gap: 30px;
    .list-item {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      height: 170px;
      width: 18%;
      border: 1px solid #e8e8e8;
      padding: 12px 16px;
      cursor: pointer;
      transition: background-color 0.2s ease-in-out;
      h3 {
        font-size: 16px;
        font-weight: bold;
      }
      div {
        display: flex;
        justify-content: space-between;
        font-size: 12px;
        label {
          color: #999;
        }
        .bp,.val {
          font-weight: bold;
        }
        .bp-red {
          color: #f56c6c;
        }
        .bp-green {
          color: #67c23a;
        }
      }
      &.active {
        background-color: var(--theme--color);
        h3 {
          color: var(--el-theme-color-warning);
        }
        .val,.bp,
        div label {
          color: #fff;
        }
      }
    }
  }
}
</style>
