<template>
  <div class="page-market-rate--yield">
    <div class="page-market-rate--yield-title">
      <span class="title-text">中债国开债到期收益率</span>
      <el-link>更多估值></el-link>
    </div>
    <div class="page-market-rate--yield-content">
      <div v-for="item in list" :key="item.yield" class="list-item" :class="activeName === item.yield && 'active'" @click="activeName = item.yield">
        <h3>{{ item.yield }}</h3>
        <div>
          <label>最新估值</label>
          <span class="val">{{ item.val.toFixed(4) }}</span>
        </div>
        <div>
          <label>涨跌BP</label>
          <span
            class="bp"
            :class="[item.bp > 0 && 'bp-red', item.bp < 0 && 'bp-green', , item.bp === 0 && 'bp-normal']"
          >
            {{ `${item.bp > 0 ? '+' : ''}${item.bp.toFixed(2)}` }}
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>

export default {
  data() {
    return {
      activeName: '10Y',
      list: [
        {
          yield: '1Y',
          val: 1.4352,
          bp: -1.23
        },
        {
          yield: '3Y',
          val: 1.5508,
          bp: 0.08
        },
        {
          yield: '5Y',
          val: 1.779,
          bp: 0.29
        },
        {
          yield: '7Y',
          val: 2.009,
          bp: -0.21
        },
        {
          yield: '10Y',
          val: 2.1388,
          bp: 0.8
        }
      ]
    }
  },
  methods: {}
}
</script>

<style lang="scss" scoped>
.page-market-rate--yield {
  width: 100%;
  padding: 12px;
  &-title {
    display: flex;
    justify-content: space-between;
    font-size: 14px;
    font-weight: bold;
  }
  &-content {
    margin-top: 12px;
    display: flex;
    column-gap: 30px;
    .list-item {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      height: 100px;
      width: 18%;
      border: 1px solid #e8e8e8;
      padding: 12px 16px;
      cursor: pointer;
      transition: background-color 0.2s ease-in-out;
      h3 {
        font-size: 16px;
        font-weight: bold;
      }
      div {
        display: flex;
        justify-content: space-between;
        font-size: 12px;
        label {
          color: #999;
        }
        .bp,.val {
          font-weight: bold;
        }
        .bp-red {
          color: #f56c6c;
        }
        .bp-green {
          color: #67c23a;
        }
      }
      &.active {
        background-color: var(--theme--color);
        h3 {
          color: var(--el-theme-color-warning);
        }
        .val,
        div label {
          color: #fff;
        }
      }
    }
  }
}
</style>
