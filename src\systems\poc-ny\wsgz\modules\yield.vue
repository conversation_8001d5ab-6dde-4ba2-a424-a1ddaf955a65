<template>
  <div class="page-wsgz--yield">
    <div class="page-wsgz--yield-title">
      <span class="title-text">我司新券行情
        <span class="title-text-sub">(含境外债券/境内区内债券)</span>
      </span>
      <el-link @click="openSetting"><jr-svg-icon icon-class="setting" />设置</el-link>
    </div>
    <div class="page-wsgz--yield-content">
      <jr-decorated-table custom-id="e480aabf6e414cb3a69006c26878caa2" v-bind="{ initDisplayMode: 'card', customRender, rowClick, noPagination: true, ...$attrs, ...$props}" @refreshed="refreshed" />
    </div>
    <modal-non-process v-bind="{ ...settingConfigModal }" />
  </div>
</template>

<script>

export default {
  data() {
    return {
      // activeName: '10Y',
      settingConfigModal: {
        visible: false,
        joinType: true,
        modalType: 'edit',
        modalTypeName: '设置',
        saveText: '完成',
        closeModal: () => {
          this.settingConfigModal.visible = false
        },
        menuinfo: {
          componenturl: 'poc-ny/wsgz/modules/setting.vue'
        }
      },
      customRender: {
        zdbp: (h, { row }) => {
          return (
            <span class={[row.zdbp > 0 && 'bp-red', row.zdbp < 0 && 'bp-green', row.zdbp === 0 && 'bp-normal']}>
              {`${row.zdbp > 0 ? '+' : ''}${row.zdbp.toFixed(2)}`}
            </span>
          )
        },
        zxytm: (h, { row }) => {
          return (
            <span class={[row.zxytm > 0 && 'bp-red', row.zxytm < 0 && 'bp-green', row.zxytm === 0 && 'bp-normal']}>
              {`${row.zxytm > 0 ? '+' : ''}${row.zxytm.toFixed(2)}`}
            </span>
          )
        }
      }
    }
  },
  methods: {
    openSetting() {
      Object.assign(this.settingConfigModal, {
        visible: true
      })
    },
    rowClick(row) {
      this.$emit('currentRow', row)
    },
    refreshed(ins) {
      ins.$refs.tableCard.handleRowClick(ins.listData[0], { stopPropagation: () => {} })
    }
  }
}
</script>

<style lang="scss" scoped>
.page-wsgz--yield {
  width: 100%;
  padding: 12px;
  height: 300px;
  &-title {
    display: flex;
    justify-content: space-between;
    font-size: 14px;
    font-weight: bold;
    .title-text-sub {
      font-size: 12px;
      font-weight: normal;
      color: #999;
    }
  }
  &-content {
    margin-top: 12px;
    min-height: 300px;
    height: calc(100% - 40px);
    ::v-deep .el-card.current-row {
      background-color: #6e6e6e;
      color: #fff;
    }
    ::v-deep .jr-decorated-table--header {
      display: none;
    }
    ::v-deep .el-card__header {
      border-bottom: none;
    }
    ::v-deep .table-card-list .el-card__body > div > label {
      font-weight: normal;
    }
    ::v-deep .table-card-list .el-card__body > div > span {
      text-align: right;
      font-weight: bold;
      .bp-red {
        color: #f56c6c;
      }
      .bp-green {
        color: #67c23a;
      }
    }
    // display: flex;
    // column-gap: 30px;
    .list-item {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      height: 100px;
      width: 18%;
      border: 1px solid #e8e8e8;
      padding: 12px 16px;
      cursor: pointer;
      transition: background-color 0.2s ease-in-out;
      h3 {
        font-size: 16px;
        font-weight: bold;
      }
      div {
        display: flex;
        justify-content: space-between;
        font-size: 12px;
        label {
          color: #999;
        }
        .bp,.val {
          font-weight: bold;
        }
      }
      &.active {
        background-color: var(--theme--color);
        h3 {
          color: var(--el-theme-color-warning);
        }
        .val,
        div label {
          color: #fff;
        }
      }
    }
  }
}
</style>
