<!-- 组合归因分析 -> N大收益贡献 -->
<template>
  <div class="home-poc-item has-fullscreen contribute-page">
    <div class="home-poc-item--header">N大收益贡献
      <el-form :model="form" style="position: absolute; left: 120px; top: 2px; line-height: normal;">
        <jr-form-item-create :data="cols" :model="form" :column="2" />
      </el-form>

      <fullscreen v-on="{ ...$listeners }" />
    </div>

    <div class="content-panel">
      <jr-decorated-table
        custom-id="caf7890bc89540a79c87580e4c2cc0fd"
        v-bind="{
          ...$attrs,
          params: queryParams,
          noPagination: true,
          menuinfo: {
            pageId: 'PtlAttributeAnalysis001',
            btnList: [{
              btnPosition: 'HEAD',
              btnkey: 'export',
              btnnm: '导出',
              componenturl: 'export',
              parameter: JSON.stringify({ noSelect: true })
            }]
          }
        }"
      />

      <jr-decorated-table
        custom-id="dfe84030fd4440d29adac62226a11669"
        v-bind="{
          ...$attrs,
          params: queryParams,
          noPagination: true,
          menuinfo: {
            pageId: 'PtlAttributeAnalysis001',
            btnList: [{
              btnPosition: 'HEAD',
              btnkey: 'export',
              btnnm: '导出',
              componenturl: 'export',
              parameter: JSON.stringify({ noSelect: true })
            }]
          }
        }"
      />
    </div>
  </div>
</template>

<script>
import { getInit } from '@/systems/mixins'
import fullscreen from '../../common/fullscreen'

export default {
  components: {
    fullscreen
  },
  mixins: [getInit('/invest/portfolio/bondposanalysis/BondPosAnalysis001')],
  props: {
    params: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      form: {
        assetType: 'F01',
        countN: '5'
      },
      cols: []
    }
  },
  computed: {
    queryParams() {
      const { assetType, countN } = this.form
      return { ...this.params, assetType, countN }
    }
  },
  created() {
    this.init()
  },
  methods: {
    init() {
      const { PINAN_ASSET_TYPE } = this.getInit

      this.cols = [{
        title: '资产大类',
        prop: 'assetType',
        type: 'select',
        optionValue: 'id',
        options: PINAN_ASSET_TYPE
      }, {
        title: 'N大贡献',
        type: 'select',
        prop: 'countN',
        showCode: false,
        options: [{ value: '5', text: '5' }, { value: '10', text: '10' }, { value: '15', text: '15' }]
      }]
    }
  }
}
</script>

<style lang="scss" scoped>
@import "../../common/poc.scss";
</style>

<style lang="scss">
.contribute-page {
  height: 100%;

  .home-poc-item--header {
    .home-poc-item--fullscreen {
      border-left: 1px solid #DCDFE6 !important;
    }
  }

  &.home-poc-item.has-fullscreen {
    .jr-decorated-table--header {
      padding-right: 10px !important;
    }
  }

  .content-panel {
    height: calc(100% - 40px);
    display: flex;

    & > div {
      width: 100%;
      height: 100%;
      overflow: hidden !important;
    }
  }
}
</style>
