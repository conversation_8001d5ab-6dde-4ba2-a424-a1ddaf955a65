<template>
  <!-- 报告管理 -->
  <flat-index :query="handleQuery" :reset="handleReset">
    <template v-slot:form>
      <el-form class="packRules">
        <jr-form-item-create :column="0" :data="configData" :model="form" />
      </el-form>
    </template>
    <template v-slot:table-header-button>
      <el-button type="primary">新建报表模块</el-button>
    </template>
    <template v-slot:table-list="{ height }">
      <jr-table
        :height="height"
        :muti-select="true"
        :columns="configTable.columns"
        :data-source="configTable.data"
        :loading="configTable.loading"
        :pagination="configTable.pagination"
        :on-change="handleQuery"
        border
        @handleSelectionChange="SelectRow"
      />
    </template>
  </flat-index>
</template>
<script>
import * as API from '@/api/invest/riskredis/quota/rskportautorelatepoint'
export default {
  data() {
    return {
      form: {},
      selectData: [],
      configData: [],
      configTable: {
        loading: false,
        columns: [
          {
            title: '报表名称',
            prop: 'portfolioId',
            sorter: true
          },
          {
            title: '报告类别',
            prop: 'portfolioName',
            sorter: true
          },
          {
            title: '报表方向',
            prop: 'vdate',
            sorter: true
          },
          {
            title: '创建时间',
            prop: 'mdate',
            sorter: true
          },
          {
            title: '创建者',
            prop: 'profitTypeStr',
            sorter: true
          },
          {
            title: '操作',
            prop: 'id',
            align: 'center',
            width: 130,
            render: (h, { row }) => {
              const btns = [
                <el-tooltip content='预览'><jr-svg-icon icon-class='eye'/></el-tooltip>,
                <el-tooltip content='导出报告'><jr-svg-icon icon-class='export'/></el-tooltip>,
                <el-tooltip content='编辑模板'><jr-svg-icon icon-class='edit'/></el-tooltip>,
                <el-tooltip content='删除'><jr-svg-icon icon-class='delete'/></el-tooltip>
              ]
              return <span class='table-action-box'>{btns}</span>
            }
          }
        ],
        data: [
        ],
        pagination: {
          pageNo: 1,
          pageSize: window.screen.height > 1000 ? 25 : 15,
          pageSizeOptions: [15, 25, 50, 100],
          total: 0,
          showSizeChanger: true
        }
      }
    }
  },
  created() {
    this.init()
    this.handleQuery()
  },
  methods: {
    init() {
      const self = this
      self.configData = [
        {
          title: '报表名称',
          prop: 'portfolioName',
          type: 'text'
        }
      ]
    },
    // 查询
    handleQuery() {
      this.configTable.data = [
        { portfolioId: '投后报告', portfolioName: '投后产品', vdate: '纵向', mdate: '2022/1/14', profitTypeStr: '云小通' },
        {
          portfolioId: '资产配置报告',
          portfolioName: '资产配置',
          vdate: '纵向',
          mdate: '2022/1/17',
          profitTypeStr: '云小通'
        },
        {
          portfolioId: '私募尽调报告',
          portfolioName: '私募公司',
          vdate: '纵向',
          mdate: '2022/2/28',
          profitTypeStr: '云小通'
        },
        {
          portfolioId: '公募经理报告',
          portfolioName: '公募经理',
          vdate: '纵向',
          mdate: '2022/3/21',
          profitTypeStr: '云小通'
        },
        { portfolioId: '业绩快报', portfolioName: '资产配置', vdate: '纵向', mdate: '2022/5/13', profitTypeStr: '云小通' },
        {
          portfolioId: '公募通用基金报告',
          portfolioName: '公募基金',
          vdate: '纵向',
          mdate: '2023/2/11',
          profitTypeStr: '云小通'
        },
        {
          portfolioId: '公募权益类基金报告',
          portfolioName: '公募基金',
          vdate: '纵向',
          mdate: '2023/2/11',
          profitTypeStr: '云小通'
        },
        {
          portfolioId: '公募固收类基金报告',
          portfolioName: '公募基金',
          vdate: '纵向',
          mdate: '2023/2/11',
          profitTypeStr: '云小通'
        },
        {
          portfolioId: '私募通用基金报告',
          portfolioName: '私募基金',
          vdate: '纵向',
          mdate: '2023/2/11',
          profitTypeStr: '云小通'
        },
        {
          portfolioId: '私募权益类基金报告',
          portfolioName: '私募基金',
          vdate: '纵向',
          mdate: '2023/2/11',
          profitTypeStr: '云小通'
        }
      ]
      this.configTable.pagination.total = 10
    },
    // 重置
    handleReset() {
      this.$set(this.data, 'portfolioName', '')
      this.$set(this.data, 'profitType', '')
      this.$set(this.data, 'openType', '')
      this.$set(this.data, 'raiseType', '')
      this.$set(this.data, 'investNature', '')
      this.$set(this.data, 'portfolioType', '')
    },
    // 导出
    exportAll() {
      const params = {
        ptlBagId: this.itemData.ptlBagId,
        pageId: this.getInit.pageId,
        elementId: this.getInit.pageId + '_btn_export',
        tableId: 'dataTable',
        isRule: 'Y'
      }
      API.PackRuleExport(params)
    },
    exportSelected() {
      const params = {
        elementId: this.getInit.pageId + '_btn_exportCheck',
        ptlBagId: this.itemData.ptlBagId,
        pageId: this.getInit.pageId,
        tableId: 'dataTable',
        isRule: 'Y',
        selectData: this.selectData
      }
      if (this.selectData.length === 0) {
        this.confirm('请从列表中选择数据!')
      } else {
        API.PackRuleExport(params)
      }
    },
    SelectRow(rowData) {
      this.selectData = rowData
    }
  }
}
</script>
