<template>
  <div>
    <el-form ref="pay-plan-form" :model="formData">
      <el-form-item prop="tableData" class="full-block">
        <div class="zj-content">
          <jr-table-editor
            v-model="formData.tableData"
            :show-action="
              () => {
                return false
              }
            "
            @change="changeTableData"
          >
            <el-table-column v-if="customTableColumnControl('payDate')" prop="payDate" width="150px" align="left">
              <template slot="header">
                <p style="width: 100%; text-align: left; margin-bottom: 0px;padding-left: 12px;">支付日期</p>
              </template>
              <template slot-scope="scope">
                <el-date-picker
                  v-model="scope.row.payDate"
                  type="date"
                  format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd"
                  @change="saveRowData(scope)"
                />
              </template>
            </el-table-column>

            <el-table-column v-if="customTableColumnControl('feeType')" prop="feeType" align="left">
              <template slot="header">
                <p style="width: 100%; text-align: left; margin-bottom: 0px">费用类型</p>
              </template>
              <template slot-scope="scope">
                <el-select v-model="scope.row.feeType" placeholder="请选择" @change="saveRowData(scope)">
                  <el-option
                    v-for="item in feeTypeList"
                    :key="item.itemcode"
                    :label="item.cnname"
                    :value="item.itemcode"
                  />
                </el-select>
              </template>
            </el-table-column>

            <el-table-column v-if="customTableColumnControl('payeeOrg')" prop="payeeOrg" align="left">
              <template slot="header">
                <p style="width: 100%; text-align: left; margin-bottom: 0px">收款机构</p>
              </template>
              <template slot-scope="scope">
                <el-select v-model="scope.row.payeeOrg" placeholder="请选择" @change="saveRowData(scope)">
                  <el-option v-for="item in orgList" :key="item.code" :label="item.name" :value="item.code" />
                </el-select>
              </template>
            </el-table-column>
            <el-table-column v-if="customTableColumnControl('feeRate')" prop="feeRate" width="120px" align="right">
              <template slot="header">
                <p style="width: 100%; text-align: right; margin-bottom: 0px">年承销费率(%)</p>
              </template>
              <template slot-scope="scope">
                <jr-rate-input
                  v-model="scope.row.feeRate"
                  :precision="4"
                  @keyup.enter="saveRowData(scope)"
                  @blur="saveRowData(scope)"
                />
              </template>
            </el-table-column>

            <el-table-column v-if="customTableColumnControl('payAmount')" prop="payAmount" align="right">
              <template slot="header">
                <p style="width: 100%; text-align: right; margin-bottom: 0px">支付金额(元)</p>
              </template>
              <template slot-scope="scope">
                <jr-number-input
                  v-model="scope.row.payAmount"
                  :precision="2"
                  append-text="元"
                  @keyup.enter="saveRowData(scope)"
                  @blur="saveRowData(scope)"
                />
              </template>
            </el-table-column>
            <el-table-column v-if="customTableColumnControl('payType')" prop="payType" align="left">
              <template slot="header">
                <p style="width: 100%; text-align: left; margin-bottom: 0px">支付类型</p>
              </template>
              <template slot-scope="scope">
                <el-select v-model="scope.row.payType" placeholder="请选择" @change="saveRowData(scope)">
                  <el-option
                    v-for="item in payTypeList"
                    :key="item.itemcode"
                    :label="item.cnname"
                    :value="item.itemcode"
                  />
                </el-select>
              </template>
            </el-table-column>
            <el-table-column v-if="customTableColumnControl('payStateStr')" prop="payStateStr" align="left">
              <template slot="header">
                <p style="width: 100%; text-align: left; margin-bottom: 0px">支付状态</p>
              </template>
              <template slot-scope="scope">
                <span style="color: var(--theme--color); cursor: pointer; padding-left: 10px; font-size: var(--el-font-size-base)">
                  {{ scope.row.payStateStr }}
                </span>
              </template>
            </el-table-column>
            <el-table-column v-if="customTableColumnControl('action')" prop="action" align="center">
              <template slot="header">
                <p style="width: 100%; text-align: center; margin-bottom: 0px">操作</p>
              </template>
              <template slot-scope="scope">
                <div style="display: flex; height: 100%; justify-content: space-evenly; align-items: center">
                  <el-button
                    type="text"
                    icon="el-icon-edit"
                    style="width: 13px; color: #000; padding: 0 !important; margin: 0 5px"
                    @click="handleEdit(scope.row)"
                  />

                  <el-popconfirm
                    title="确定要删除吗？"
                    confirm-button-text="确定"
                    cancel-button-text="取消"
                    @confirm="handleDelete(scope)"
                  >
                    <el-button
                      slot="reference"
                      type="text"
                      icon="el-icon-delete"
                      style="width: 13px; color: #000; padding: 0 !important; margin: 0 5px"
                    />
                  </el-popconfirm>
                </div>
              </template>
            </el-table-column>
            <el-table-column v-if="customTableColumnControl('changeState')" prop="changeState" width="120" align="center">
              <template slot="header">
                <p style="width: 100%; text-align: center; margin-bottom: 0px">支付状态修改</p>
              </template>
              <template slot-scope="scope">
                <el-button
                  :disabled="scope.row.payState !== '20' || !scope.row.id"
                  @click="handleChangeState(scope.row)"
                >
                  <span style="color: var(--theme--color); font-size: var(--el-font-size-base)">设为已支付</span>
                </el-button>
              </template>
            </el-table-column>
          </jr-table-editor>
        </div>
      </el-form-item>
    </el-form>

    <UpdateModal
      v-if="isShowUpdateModal"
      :close-modal="closeModal"
      :visible="isShowUpdateModal"
      :permitdetail="permitdetail"
      :row="currentRow"
      :orglist="orgList"
      :open-type="'edit'"
    />
  </div>
</template>

<script>
import UpdateModal from './update-service-payment-modal.vue'
import {
  queryFeePlanForAll,
  deletePayPlan,
  deleteFeeAgencyInfoPayPlan,
  changePaymentStatus,
  feeUnderwritingInfoSaveOrModify,
  feeAgencyInfoSaveOrModify,
  feeAgencyInfoQueryFeePlanForAll,
  changeFeeAgencyInfoPaymentStatus
} from '@/api/redemption-manage/redemption-manage'
import { queryLeadUnderwriter } from '@/api/home'
import moment from 'moment'
import { EventBus } from '../event-bus'
export default {
  name: 'PayPlan',
  components: { UpdateModal },
  props: {
    row: {
      type: Object,
      default() {
        return {}
      }
    },
    payTypeList: {
      type: Array,
      default() {
        return []
      }
    },
    payStateList: {
      type: Array,
      default() {
        return []
      }
    },
    feeTypeList: {
      type: Array,
      default() {
        return []
      }
    },
    permitdetail: {
      type: Object,
      default: () => ({})
    },
    tableType: {
      type: String,
      default() {
        return 'service-payment'
      }
    }
  },
  data() {
    return {
      formData: {
        tableData: []
      },
      orgList: [],
      isShowUpdateModal: false,
      currentRow: {},
      lastChangeIndex: null,
      lastChangeRow: '',
      serviceColumn: ['payDate', 'payeeOrg', 'feeRate', 'payAmount', 'payType', 'payStateStr', 'action', 'changeState'],
      intermediaryColumn: ['payDate', 'feeType', 'payAmount', 'payStateStr', 'payeeOrg', 'action', 'changeState'],
      otherColumn: ['payDate', 'feeType', 'payAmount', 'payStateStr', 'action', 'payeeOrg', 'changeState']
    }
  },
  created() {
    EventBus.$on('refresh-pay-plan-list', () => {
      this.getListData()
    })
  },
  beforeDestroy() {
    // 组件销毁时移除事件监听
    EventBus.$off('refresh-service-payment-list')
  },
  mounted() {
    this.getListData()
    if (Object.prototype.hasOwnProperty.call(this.row, 'sInfoWindcode') && this.row.sInfoWindcode) {
      this.getOrgList(this.row.sInfoWindcode)
    }
  },
  methods: {
    // 查询债券机构
    getOrgList(code) {
      queryLeadUnderwriter({
        bondCode: code
      }).then((data) => {
        this.orgList = data
      })
    },
    // 控制表格显示元素
    customTableColumnControl(propName) {
      switch (this.tableType) {
        case 'intermediary-fee':
          return this.intermediaryColumn.indexOf(propName) > -1
        case 'service-payment':
          return this.serviceColumn.indexOf(propName) > -1
        case 'other':
          return this.otherColumn.indexOf(propName) > -1
      }
    },
    // 表格数据监听
    changeTableData(arr) {
      this.formData.tableData.map((v) => {
        if (Object.keys(v).length === 0) {
          v.payState = '20'
          v.payStateStr = '未支付'
          v.bondShortName = this.row.infoname
          v.bondCode = this.row.sInfoWindcode
        }
      })
    },
    // 格式化支付状态
    formatPayState(state) {
      if (!state) return ''

      if (this.payStateList.length > 0) {
        const arr = this.payStateList.filter((v) => {
          return v.itemcode === state
        })
        return arr.length > 0 ? arr[0].cnname : state
      } else {
        return state
      }
    },
    // 获取列表数据
    async getListData() {
      if (this.tableType === 'service-payment') {
        queryFeePlanForAll({
          bondCode: this.row.sInfoWindcode
        }).then((data) => {
          if (data && Object.keys(data).length) {
            data.map((v) => {
              v.payDate = moment(v.payDate).format('YYYY-MM-DD')
              v.payStateStr = this.formatPayState(v.payState)
            })
          }
          this.formData.tableData = data
        })
      } else if (this.tableType === 'intermediary-fee' || this.tableType === 'other') {
        feeAgencyInfoQueryFeePlanForAll({
          bondCode: this.row.sInfoWindcode
        }).then((data) => {
          if (data && Object.keys(data).length) {
            data.map((v) => {
              v.payDate = moment(v.payDate).format('YYYY-MM-DD')
              v.payStateStr = this.formatPayState(v.payState)
            })
          }
          this.formData.tableData = data
        })
      }
    },
    // 保存单行数据
    saveRowData(scope) {
      // 有关键信息未填不进行保存
      console.log(this.getAllParamsReady(scope.row));
      
      if (!this.getAllParamsReady(scope.row)) {
        return
      }

      const params = {
        id: scope.row.id || '',
        bondShortName: scope.row.bondShortName || '',
        bondCode: scope.row.bondCode || '',
        payState: scope.row.payState || '',
        feeType: scope.row.feeType || '',
        payType: scope.row.payType || '',
        payDate: scope.row.payDate || '',
        payAmount: scope.row.payAmount || '',
        payeeOrg: scope.row.payeeOrg || '',
        feeRate: scope.row.feeRate || ''
      }

      // 同行需要校验两次信息是否一致，不同行则直接保存
      if (this.lastChangeIndex !== scope.$index) {
        this.lastChangeIndex = scope.$index
        this.saveData(params)
      } else {
        // 同行数据有更换进行保存
        if (this.lastChangeRow !== JSON.stringify(scope.row)) {
          this.saveData(params)
        } else {
          return
        }
      }
    },
    // 保存单行数据
    saveData(params) {
      switch (this.tableType) {
        case 'service-payment':
          feeUnderwritingInfoSaveOrModify(params).then((res) => {
            this.$message({
              type: 'success',
              message: params.id ? '修改成功' : '新增成功'
            })
            this.getListData()
          })
          break
        case 'intermediary-fee':
        case 'other':
          feeAgencyInfoSaveOrModify(params).then((res) => {
            this.$message({
              type: 'success',
              message: params.id ? '修改成功' : '新增成功'
            })
            this.getListData()
          })
          break
      }
    },
    // 检测单行数据是否齐全
    getAllParamsReady(obj) {
      let keyArr = []
      switch (this.tableType) {
        case 'service-payment':
          keyArr = ['bondShortName', 'bondCode', 'payType', 'payDate', 'payAmount', 'payeeOrg', 'feeRate']
          break
        case 'intermediary-fee':
        case 'other':
          keyArr = ['bondShortName', 'bondCode', 'payDate', 'payAmount', 'payeeOrg', 'feeType']
          break
      }

      for (const item of keyArr) {
        if (!Object.prototype.hasOwnProperty.call(obj, item)) {
          return false
        }
      }

      for (const key in obj) {
        if (Object.prototype.hasOwnProperty.call(obj, key)) {
          if ((obj[key] == null || obj[key] === '') && keyArr.indexOf(key) > -1) {
            return false
          }
        }
      }
      return true
    },
    // 编辑
    handleEdit(row) {
      this.currentRow = row
      this.openModal()
    },
    deleteTableData(id) {
      switch (this.tableType) {
        case 'service-payment':
          deletePayPlan({
            id: id
          }).then((data) => {
            this.getListData()
          })
          break
        case 'intermediary-fee':
        case 'other':
          deleteFeeAgencyInfoPayPlan({
            id: id
          }).then((data) => {
            this.getListData()
          })
          break
      }
    },
    // 删除
    async handleDelete(scope) {
      const index = scope.$index

      if (scope.row.id) {
        this.deleteTableData(scope.row.id)
      } else {
        this.formData.tableData.splice(index, 1)
      }
    },
    changeState(id) {
      switch (this.tableType) {
        case 'service-payment':
          changePaymentStatus({
            id: id
          }).then((data) => {
            this.getListData()
          })
          break
        case 'intermediary-fee':
        case 'other':
          changeFeeAgencyInfoPaymentStatus({
            id: id
          }).then((data) => {
            this.getListData()
          })
          break
      }
    },
    // 更改支付状态
    async handleChangeState(row) {
      if (row.id) {
        this.changeState(row.id)
      }
    },
    // 关闭弹窗
    closeModal() {
      this.isShowUpdateModal = false
      this.currentRow = {}
    },
    // 打开弹窗
    openModal() {
      this.isShowUpdateModal = true
    }
  }
}
</script>

<style scoped lang="scss">
.zj-content {
  max-height: 200px;
  overflow: scroll;
}
</style>
