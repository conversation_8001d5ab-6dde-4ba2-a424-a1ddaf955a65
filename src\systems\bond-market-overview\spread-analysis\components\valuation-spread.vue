<template>
  <div class="issue-spread">
    <div class="issue-spread-container">
      <div class="issue-spread-container-left">
        <div class="public-radio-group">
          <el-radio-group v-model="headerBtns" @input="changeType">
            <el-radio-button label="mtn">上市首日估值偏离度</el-radio-button>
            <el-radio-button label="lgfv">
              <el-tooltip placement="right" effect="dark">
                <span>信用利差</span>
                <div slot="content">分析债券估值与同期限国开债收益率之间的利差</div>
              </el-tooltip>
            </el-radio-button>
          </el-radio-group>
        </div>
        <el-form
          inline
          :model="form"
          label-width="80"
          style="display: flex; align-items: center; margin-top: 16px; height: 32px"
        >
          <jr-form-item label="发行截止日">
            <el-date-picker
              v-model="form.customDateRange"
              style="width: 328px; height: 32px"
              type="daterange"
              unlink-panels
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="yyyy-MM-dd"
              class="date-picker"
              @change="handleCustomDateRangeChange"
            />
          </jr-form-item>
        </el-form>

        <div class="issue-spread-container-left-search">
          <CustomSaveSearchForm
            v-if="headerBtns === 'mtn'"
            key="mtn"
            :curvename="curveName()"
            :tiptext="tiptext()"
            :outsidelist="searchParamsList"
            type="valuation"
            @getAllList="getParamsList"
          />
          <creditSpreadSearch
            v-else
            ref="creditSpreadSearchRef"
            key="lgfv"
            :curvename="curveName()"
            :tiptext="tiptext()"
            :tabname="tabActiveName"
            :params="creditParams"
            :out-effective="outEffective"
            @params-change="creditSearchChange"
          />
        </div>
      </div>

      <div class="issue-spread-container-right">
        <CustomSpreadCurve
          :chartdata="chartOptions"
          :height="360"
          :title="headerBtns === 'mtn' ? '上市首日估值偏离度曲线' : '信用利差曲线'"
          chartTitle="票面利率VS基准曲线"
        />
      </div>
    </div>

    <div class="issue-spread-table">
      <div class="public-tabs-container">
        <el-tabs v-model="tabActiveName" class="issue-spread-table-tabs" @tab-click="changeTabs">
          <el-tab-pane
            :label="$store.getters.sysVersion === $dict.COMPANY_VER_group ? '集团债券' : '我司债券'"
            name="CreditSpread"
          />
          <el-tab-pane label="参考债券" name="IssueSpread" />
          <el-tab-pane label="自选债券" name="ValuationSpread" />
        </el-tabs>
      </div>

      <div>
        <div class="issue-spread-table-header">
          <el-form inline :model="form" label-width="90" class="issue-spread-table-header-form">
            <jr-form-item label="发行截止日">
              <el-date-picker
                v-model="form.customDate"
                style="height: 32px; max-width: 220px"
                type="date"
                unlink-panels
                placeholder="请选择日期"
                format="yyyy-MM-dd"
                class="date-picker"
                :picker-options="pickerOptions"
                :disabled="
                  (Array.isArray(form.customDateRange) && form.customDateRange.length === 0) || !form.customDateRange
                "
              />
            </jr-form-item>
            <jr-form-item v-if="tabActiveName !== 'ValuationSpread'" label="债券简称">
              <el-autocomplete
                v-model="form.s_info_name"
                :fetch-suggestions="querySearchAsync"
                placeholder="请输入债券简称"
                clearable
                style="height: 32px"
                value-key="text"
              />
            </jr-form-item>
            <jr-form-item v-if="$store.getters.sysVersion === $dict.COMPANY_VER_group" label="企业名称">
              <el-autocomplete
                v-model="form.compName"
                style="width: 246px"
                placeholder="请输入企业全称"
                clearable
                remote
                :fetch-suggestions="remoteSearch"
                :loading="loading"
                @clear="handleClear"
                @select="handleSelect"
              />
            </jr-form-item>

            <el-button type="primary" @click="submit">查询</el-button>
            <el-checkbox v-model="form.checked">含已到期</el-checkbox>

            <div class="issue-spread-table-header-form-left">
              <el-button
                v-if="tabActiveName === 'ValuationSpread'"
                type="text"
                style="height: 32px; border: 1px solid #ccc; background-color: #fff"
                @click="visible = true"
              >
                <jr-svg-icon icon-class="bars" />
                自选
              </el-button>

              <SelfDefinedList
                v-if="visible"
                :visible="visible"
                :permitdetail="permitdetail"
                :menuinfo="menuinfo"
                :module="headerBtns === 'mtn' ? 'deviation' : 'valuation'"
                @close="closemodal"
              />

              <el-button
                type="text"
                style="height: 32px; border: 1px solid #ccc; background-color: #fff"
                @click="resetSelectRows"
              >
                <jr-svg-icon icon-class="delete" />
                清空所选
              </el-button>
            </div>
          </el-form>
        </div>
        <jr-decorated-table
          ref="jrTable"
          :params="tableParams"
          :key="jrDecoraterTableKey"
          style="height: 516px"
          :custom-id="ccid"
          v-bind="{ ...$attrs, ...$props }"
          :permitdetail="{
            ...permitdetail,
            export: { icon: 'upload', name: '导出', componenturl: 'export', btnkey: 'export' }
          }"
          :handleexport="exportData"
          :default-page-size="10"
          :initPagination="{
            pageSizeOptions: [10, 20, 50, 100]
          }"
          @handleSelectionChange="getSelectRows"
          @refreshed="callFn"
        />
      </div>
    </div>
  </div>
</template>

<script>
import moment from 'moment'
import customSingleSearchForm from './custom-single-search-form.vue'
import CustomSaveSearchForm from './custom-save-search-form'
import SelfDefinedList from './self-defined-tables'
import CustomSpreadCurve from './custom-spread-curve'
import creditSpreadSearch from './credit-spread-search'
import { queryBondShortName } from '@/api/bonds/bonds'
import { debounce } from 'lodash'
import {
  queryCompName,
  queryCurveRequestdata,
  saveCurveRequestdata,
  queryValuationDeviation,
  queryValuationSpread,
  queryDeviation
} from '@/api/bonds/bonds'
import { getDictOptionsApi, setChartOptions, formatterDateString } from '../get-dictionary'
import { exportExcelByCustomColumn } from '@/api/public/public'
const DICTIONARYARRAY = [
  'issueTermCDB',
  'issueTermGB',
  'issueTermMTN',
  'issueTermLGFV',
  'issueTermCB',
  'issueRatingMTN',
  'issueRatingLGFV',
  'issueRatingCB'
]
/**
 * 字典项数组对应值
 *
 *发行利差期限(国开债)
 *发行利差期限(国债)
 *发行利差期限(中短期票据)
 *发行利差期限(城投债)
 *发行利差期限(企业债)
 *发行利差期限(自定义利率曲线)
 *发行利差主体评级(中短期票票据)
 *发行利差主体评级(城投债)
 *发行利差主体评级(企业债)
 */
export default {
  components: {
    CustomSaveSearchForm,
    SelfDefinedList,
    CustomSpreadCurve,
    customSingleSearchForm,
    creditSpreadSearch
  },
  props: {
    // 权限相关
    permitdetail: {
      type: Object,
      default: () => ({})
    },
    menuinfo: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      outEffective: true,
      jrDecoraterTableKey: 'mtn_CreditSpread',
      ccid: 'c9d874d75e154510ba8c137e2eeafecf',
      series: [],
      xAxis: [],
      selectRows: [],
      visible: false,
      tableParams: {
        ownedModuleid: '1352315117735067648'
      },
      tabActiveName: 'CreditSpread',
      headerBtns: 'mtn',
      form: {
        customDate: '',
        checked: true,
        compName: '',
        compId: '',
        customDateRange: null
      },
      myChart: null,
      valuationDeviationSettingsId: null,
      valuationScatterSettings: [],
      valuationScatterSettingsId: null,
      creditValuationScatterSettingsId: null,
      creditValuationScatterSettings: [],
      searchParamsList: [],
      loading: false, // 远程搜索加载状态
      chartOptions: {},
      pickerOptions: {},
      dictionaryObject: {},
      refresh: true,
      columns: [],
      rateLeftData: {
        xAxis: [],
        series: []
      },
      rateRightData: {
        xAxis: [],
        series: []
      },
      rateBottomData: {
        xAxis: [],
        series: []
      },
      creditParams: {}
    }
  },
  mounted() {
    // 监听表格数据请求完成，设置默认选中行数据
    this.$watch(
      () => this.$refs.jrTable.$children[0].listData,
      (newData) => {
        const selectRowIds = []
        let arr = this.haderBtns === 'lgfv' ? this.creditValuationScatterSettings : this.valuationScatterSettings
        for (let index = 0; index < arr.length; index++) {
          const item = arr[index]
          selectRowIds.push(item.uuid)
        }

        this.setTableDefaultSelected(selectRowIds, newData)
      },
      { immediate: true, deep: true } // 立即监听 + 深度监听
    )
  },
  async created() {
    // 默认查询本年数据
    this.form.customDateRange = this.getYearStartAndEnd()
    this.pickerOptions.disabledDate = this.disabledDate

    this.submit()

    this.dictionaryObject = await getDictOptionsApi(DICTIONARYARRAY)

    // 初始化存储参数设置
    this.initSearchLists()

    // 页面请求操作过多，进行防抖处理
    this.debouncedRemoteSearch = debounce(this.rawRemoteSearch, 500)
    this.getChartData = debounce(this.getChartData, 500)
    this.getSelectRows = debounce(this.getSelectRows, 500)
    this.creditSearchChange = debounce(this.creditSearchChange, 500)

    this.getChartData()
  },
  methods: {
    creditSearchChange(searchParams, effective) {
      this.outEffective = effective
      if (this.tabActiveName === 'IssueSpread' && this.outEffective === true) {
        this.creditParams = { ...searchParams }
        this.tableParams = { ...this.creditParams, ...this.handleSearchParams() }
      } else {
        this.creditParams = {}
        this.tableParams = this.resetSearchParams()
      }
    },
    curveName() {
      return this.headerBtns === 'mtn' ? '上市首日估值平均偏离度曲线' : '参考债券'
    },
    tiptext() {
      return this.headerBtns === 'mtn'
        ? '在所选择时间范围内，计算每一日符合配置条件的成功发行债券的上市首日估值偏离度的平均值，形成上市首日估值平均偏离度曲线。统计的债券范围为协会债、公司债、企业债各细分品种'
        : '在所选择时间范围内，筛选每一日符合配置条件的存续债券，便于债券间的信用利差比较'
    },
    // 获取表格tab标题
    getFileName() {
      let name = ''

      switch (this.tabActiveName) {
        case 'CreditSpread':
          name = this.$store.getters.sysVersion === this.$dict.COMPANY_VER_group ? '集团债券' : '我司债券'
          break
        case 'IssueSpread':
          name = '参考债券'
          break
        case 'ValuationSpread':
          name = '自选债券'
          break
      }

      return `${name}发行利差`
    },
    // 导出
    async exportData() {
      const params = {
        params: {
          filename: this.getFileName(),
          column: this.columns,
          selectData: Array.isArray(this.selectRows) && this.selectRows.length > 0 ? this.selectRows : null,
          ccid: this.ccid,
          ownedModuleid: '1352315117735067648'
        },
        page: {
          sort: this.sort,
          direction: this.direction
        }
      }
      await exportExcelByCustomColumn(params)
    },
    getDefaultDate(dateRange) {
      const start = new Date(dateRange[0])
      start.setHours(0, 0, 0, 0)
      const end = new Date(dateRange[1])
      end.setHours(0, 0, 0, 0)
      const today = new Date()
      today.setHours(0, 0, 0, 0)

      if (today >= start && today <= end) {
        return moment(today).format('YYYY-MM-DD')
      } else {
        return ''
      }
    },
    resetSearchParams(customTableParams) {
      const obj = {
        issue_status: this.form.checked ? '1' : '0',
        b_issue_lastissue: this.form.customDate ? moment(new Date(this.form.customDate)).format('YYYYMMDD') : '',
        s_info_name: this.form.s_info_name,
        b_info_issuercode: this.form.compId,
        webTime: new Date().getTime(),
        queryFlag: this.getQueryFlag()
      }

      return { ...customTableParams, ...obj }
    },
    // 切换tab页
    changeTabs() {
      this.form.checked = true
      this.form.compName = ''
      this.form.s_info_name = ''

      this.form.compId = ''

      if (this.headerBtns === 'mtn') {
        this.form.customDate = ''
      } else {
        this.form.customDate = this.getDefaultDate(this.form.customDateRange)
      }

      let customTableParams = {}

      switch (this.tabActiveName) {
        case 'CreditSpread':
          if (this.headerBtns === 'mtn') {
            this.ccid = 'c9d874d75e154510ba8c137e2eeafecf'
          } else {
            this.ccid = 'f717172865ff4ebe842d407b8331ca56'
          }
          customTableParams = {}
          this.tableParams = this.resetSearchParams(customTableParams)
          this.jrDecoraterTableKey = `${this.headerBtns}_${this.tabActiveName}`
          break
        case 'IssueSpread':
          if (this.headerBtns === 'lgfv') {
            this.ccid = '1fe6a8c846d94631aab66bf823809f93'

            this.$nextTick(() => {
              // 轮询等待子组件处理参数
              if (!this.outEffective) {
                customTableParams = {}
                this.tableParams = this.resetSearchParams(customTableParams)
                this.jrDecoraterTableKey = `${this.headerBtns}_${this.tabActiveName}`
              } else {
                let getChildrenFormData = null
                let retryCount = 0
                const MAX_RETRIES = 100
                getChildrenFormData = setInterval(() => {
                  const form = { ...this.$refs.creditSpreadSearchRef.getChildrenForm() }

                  if (Object.hasOwnProperty.call(form, 'termStreAll')) {
                    clearInterval(getChildrenFormData)
                    customTableParams = { ...this.$refs.creditSpreadSearchRef.getChildrenForm() }
                    this.tableParams = this.resetSearchParams(customTableParams)
                    this.jrDecoraterTableKey = `${this.headerBtns}_${this.tabActiveName}`
                  } else {
                    retryCount++
                    if (retryCount >= MAX_RETRIES) {
                      clearInterval(getChildrenFormData)
                    }
                  }
                }, 100)
              }
            })
          } else {
            this.ccid = 'ddbdfc211a39496fa3035aefd53f60d7'
            customTableParams = {}
            this.tableParams = this.resetSearchParams(customTableParams)
            this.jrDecoraterTableKey = `${this.headerBtns}_${this.tabActiveName}`
          }
          break
        case 'ValuationSpread':
          if (this.headerBtns === 'mtn') {
            this.ccid = '9386124439704d27becfddea58052485'
          } else {
            this.ccid = '8cf28489a03946289397686cc1dddda3'
          }
          customTableParams = {}
          this.tableParams = this.resetSearchParams(customTableParams)
          this.jrDecoraterTableKey = `${this.headerBtns}_${this.tabActiveName}`
          break
      }

      this.getChartData()
    },
    // 默认发行截止日为本年
    getYearStartAndEnd() {
      // 获取当前年份
      const currentYear = new Date().getFullYear()

      // 创建本年1月1日的日期对象
      const startOfYear = new Date(currentYear, 0, 1)

      // 创建本年12月31日的日期对象
      const endOfYear = new Date(currentYear, 11, 31)

      // 返回开始和结束的日期
      return [moment(startOfYear).format('YYYY-MM-DD'), moment(endOfYear).format('YYYY-MM-DD')]
    },
    // 设置禁止选择区间
    disabledDate(time) {
      if (
        !this.form.customDateRange ||
        (Array.isArray(this.form.customDateRange) && this.form.customDateRange.length === 0)
      ) {
        return false
      }

      const start = new Date(this.form.customDateRange[0])
      const end = new Date(this.form.customDateRange[1])
      const current = new Date(time)

      // 不在范围内则禁用
      return current < start || current > end
    },
    // 发行截止日改变，发行截止日清空
    handleCustomDateRangeChange() {
      this.form.customDate = null
      this.getChartData()
    },
    // 表格数据勾选事件
    getSelectRows(rows, listData) {
      const currentUnselectedIds = []
      const currentSelectedIds = []
      for (let index = 0; index < rows.length; index++) {
        const subItem = rows[index]
        currentSelectedIds.push(subItem.uuid)
      }
      for (let index = 0; index < listData.length; index++) {
        const item = listData[index]
        if (!currentSelectedIds.includes(item.uuid)) {
          currentUnselectedIds.push(item.uuid)
        }
      }

      this.selectRows = rows
      let arr = this.haderBtns === 'lgfv' ? this.creditValuationScatterSettings : this.valuationScatterSettings

      const selectParams = [...arr]
      for (let index = 0; index < this.selectRows.length; index++) {
        const item = this.selectRows[index]
        selectParams.push({
          uuid: item.uuid,
          queryDate: moment(item.bIssueLastissue).format('YYYYMMDD'),
          bondCode: item.sInfoWindcode
        })
      }
      if (this.haderBtns === 'lgfv') {
        this.creditValuationScatterSettings = selectParams.filter((item) => {
          return !currentUnselectedIds.includes(item.uuid)
        })
      } else {
        this.valuationScatterSettings = selectParams.filter((item) => {
          return !currentUnselectedIds.includes(item.uuid)
        })
      }

      this.getChartData()
    },
    // 表格组件渲染回调
    callFn(data) {
      this.columns = data.config.columns
      this.sort = data.sort
      this.direction = data.direction

      let getTableDataTimeInterval = null
      let retryCount = 0
      const MAX_RETRIES = 30

      getTableDataTimeInterval = setInterval(() => {
        const tableData = [...data.listData]

        if (tableData.length > 0) {
          clearInterval(getTableDataTimeInterval)
          const selectRowIds = []
          let arr = this.haderBtns === 'lgfv' ? this.creditValuationScatterSettings : this.valuationScatterSettings
          for (let index = 0; index < arr.length; index++) {
            const item = arr[index]
            selectRowIds.push(item.uuid)
          }

          this.setTableDefaultSelected(selectRowIds, tableData)
        } else {
          retryCount++
          if (retryCount >= MAX_RETRIES) {
            clearInterval(getTableDataTimeInterval)
          }
        }
      }, 100)
    },
    // 设置表格默认勾选
    setTableDefaultSelected(selectRowIds, tableData) {
      const elTableRef = this.getElTableRef()

      this.$nextTick(() => {
        tableData.forEach((row) => {
          if (selectRowIds.includes(row.uuid)) {
            elTableRef?.toggleRowSelection(row, true)
          }
        })
      })
    },
    // 获取el-table ref对象
    getElTableRef() {
      if (
        this.$refs.jrTable &&
        this.$refs.jrTable.$children &&
        Array.isArray(this.$refs.jrTable.$children) &&
        this.$refs.jrTable.$children.length > 0 &&
        this.$refs.jrTable.$children[0] &&
        this.$refs.jrTable.$children[0].$el &&
        this.$refs.jrTable.$children[0].$el.__vue__ &&
        this.$refs.jrTable.$children[0].$el.__vue__.$children &&
        Array.isArray(this.$refs.jrTable.$children[0].$el.__vue__.$children) &&
        this.$refs.jrTable.$children[0].$el.__vue__.$children.length > 0 &&
        this.$refs.jrTable.$children[0].$el.__vue__.$children[0] &&
        this.$refs.jrTable.$children[0].$el.__vue__.$children[0].$el &&
        this.$refs.jrTable.$children[0].$el.__vue__.$children[0].$el.__vue__ &&
        this.$refs.jrTable.$children[0].$el.__vue__.$children[0].$el.__vue__.$children
      ) {
        return this.$refs.jrTable.$children[0].$el.__vue__.$children[0].$el.__vue__.$el.__vue__.$children[0]
      } else {
        return null
      }
    },
    // 重置表格复选款已选
    resetSelectRows() {
      this.$nextTick(() => {
        const elTableRef = this.getElTableRef()
        this.selectRows.forEach((row) => {
          elTableRef?.toggleRowSelection(row, false)
        })

        if (this.headerBtns === 'mtn') {
          this.valuationScatterSettings = []
        } else {
          this.creditValuationSettings = []
        }

        this.getChartData()
      })
    },
    // 获取图表数据
    async getChartData() {
      if (this.headerBtns === 'mtn') {
        const checkedParams = []
        for (let index = 0; index < this.searchParamsList.length; index++) {
          const item = this.searchParamsList[index]
          if (item.checked) {
            checkedParams.push({
              bondRating:
                Array.isArray(item.bondRating) && item.bondRating.length > 0 ? item.bondRating : item.bondRatingAll,
              bondType: Array.isArray(item.bondType) && item.bondType.length > 0 ? item.bondType : item.bondTypeAll,
              compProperty:
                Array.isArray(item.compProperty) && item.compProperty.length > 0
                  ? item.compProperty
                  : item.compPropertyAll,
              curvename: item.curvename,
              district: item.district || [],
              implyRating:
                Array.isArray(item.implyRating) && item.implyRating.length > 0 ? item.implyRating : item.implyRatingAll,
              isguarantee: item.isguarantee || [],
              isright: item.isright || '',
              termStr: Array.isArray(item.termStr) && item.termStr.length > 0 ? item.termStr : item.termStreAll,
              tradeDtEnd: item.tradeDtEnd,
              tradeDtStart: item.tradeDtStart
            })
          }
        }
        this.saveCustomCurveRequestdata(
          'valuationDeviationSettings',
          JSON.stringify(this.searchParamsList),
          this.valuationDeviationSettingsId
        )
        const res = await queryValuationDeviation(checkedParams)
        this.rateLeftData = this.formatLeftChartData(res)

        if (Array.isArray(this.valuationScatterSettings)) {
          this.saveCustomCurveRequestdata(
            'valuationScatterSettings',
            JSON.stringify(this.valuationScatterSettings),
            this.valuationScatterSettingsId
          )
          const res = await queryDeviation(this.valuationScatterSettings)
          this.rateBottomData = this.processScatterData(res)
        }

        const xAxis = Array.from(new Set([...this.rateLeftData.xAxis, ...this.rateBottomData.xAxis])).sort(
          (a, b) => new Date(a) - new Date(b)
        )
        const series = [...this.rateLeftData.series, ...this.rateBottomData.series]
        this.chartOptions = setChartOptions(xAxis, series)
      } else {
        if (Array.isArray(this.creditValuationScatterSettings)) {
          const params = {
            bondCode: this.creditValuationScatterSettings,
            tradeDtEnd: Array.isArray(this.form.customDateRange)
              ? moment(this.form.customDateRange[1]).format('YYYYMMDD')
              : '',
            tradeDtStart: Array.isArray(this.form.customDateRange)
              ? moment(this.form.customDateRange[0]).format('YYYYMMDD')
              : ''
          }
          this.saveCustomCurveRequestdata(
            'creditValuationScatterSettings',
            JSON.stringify(this.creditValuationScatterSettings),
            this.creditValuationScatterSettingsId
          )
          const res = await queryValuationSpread(params)
          this.rateRightData = this.formatRightChartData(res)

          const xAxis = Array.from(new Set([...this.rateRightData.xAxis])).sort((a, b) => new Date(a) - new Date(b))
          const series = [...this.rateRightData.series]
          this.chartOptions = setChartOptions(xAxis, series)
        }
      }
    },
    // 前端处理标准利率曲线数据
    formatLeftChartData(rateData) {
      const series = []
      const xAxisSet = new Set()

      // 首先收集所有可能的日期
      Object.values(rateData).forEach((curve) => {
        curve.forEach((item) => {
          xAxisSet.add(formatterDateString(item.b_issue_lastissue))
        })
      })

      // 将日期排序
      const xAxisData = Array.from(xAxisSet).sort((a, b) => {
        return new Date(a) - new Date(b)
      })

      // 处理每条曲线
      Object.entries(rateData).forEach(([curveName, curveData]) => {
        // 创建日期到利率的映射
        const dateRateMap = {}
        curveData.forEach((item) => {
          dateRateMap[formatterDateString(item.b_issue_lastissue)] = item.couponrate
        })

        // 为当前曲线生成数据点
        const seriesData = xAxisData.map((date) => {
          return {
            name: date,
            value: dateRateMap[date] || null // 没有数据时设为null
          }
        })

        series.push({
          name: curveName,
          type: 'line',
          data: seriesData,
          symbol: 'circle',
          symbolSize: 8,
          lineStyle: {
            width: 3
          },
          // 处理null值，使曲线断开
          connectNulls: false
        })
      })

      return {
        xAxis: xAxisData,
        series: series
      }
    },
    // 前端处理自定义利率曲线数据
    formatRightChartData(rateData) {
      const series = []
      const xAxisSet = new Set()

      // 首先收集所有可能的日期
      Object.values(rateData).forEach((curve) => {
        xAxisSet.add(formatterDateString(curve.trade_dt))
      })

      // 将日期排序
      const xAxisData = Array.from(xAxisSet).sort((a, b) => {
        return new Date(a) - new Date(b)
      })

      const bondGroups = {}

      rateData.forEach((item) => {
        if (!bondGroups[item.s_info_name]) {
          bondGroups[item.s_info_name] = []
        }
        bondGroups[item.s_info_name].push(item)
      })

      Object.keys(bondGroups).map((bondName) => {
        const bondData = bondGroups[bondName]
        // 创建一个日期到利差的映射，确保数据对齐
        const dateToSpread = {}
        bondData.forEach((item) => {
          dateToSpread[formatterDateString(item.trade_dt)] = item.spread
        })

        // 生成对应日期的数据，没有数据的日期用null表示
        const seriesData = xAxisData.map((date) => {
          return {
            name: date,
            value: dateToSpread[date] || null // 没有数据时设为null
          }
        })

        series.push({
          name: bondName,
          type: 'line',
          data: seriesData,
          smooth: true,
          symbol: 'circle',
          symbolSize: 8,
          lineStyle: {
            width: 3
          },
          // 处理null值，使曲线断开
          connectNulls: false
        })
      })

      return {
        xAxis: xAxisData,
        series: series
      }
    },
    // 前端处理散点数据
    processScatterData(rawData) {
      // 1. 按日期排序
      const sortedData = [...rawData].sort(
        (a, b) => new Date(formatterDateString(a.lastissuedate)) - new Date(formatterDateString(b.lastissuedate))
      )

      // 2. 提取x轴数据（去重排序后的日期）
      const xAxis = [...new Set(sortedData.map((item) => formatterDateString(item.lastissuedate)))]

      // 3. 生成series数据
      const series = sortedData.map((item) => ({
        data: [
          {
            name: item.abbrname,
            value: [formatterDateString(item.lastissuedate), item.couponrate],
            symbolSize: 10
          }
        ],
        name: item.abbrname,
        type: 'scatter'
      }))

      return {
        xAxis,
        series
      }
    },
    // 关闭自选弹窗
    closemodal() {
      this.visible = false
      this.submit()
    },
    // 企业名称选择回调
    handleSelect(value) {
      if (value) {
        // 保存选中的数据
        this.form.compId = value.compId
        this.form.compName = value.compName
      }
    },
    // 企业名称清空回调
    handleClear() {
      // 清空选中的数据
      this.form.compId = ''
      this.form.compName = ''
    },
    // 查询企业名称
    remoteSearch(query, cb) {
      if (!query) return cb([])
      this.loading = true
      this.debouncedRemoteSearch(query, cb)
    },
    rawRemoteSearch(query, cb) {
      const compId = '502036608'
      queryCompName(query, compId) // 企业id 暂时为空
        .then((res) => {
          // 转换数据格式，确保每个选项有value字段
          const suggestions = res.map((item) => ({
            value: item.compName, // 显示文本
            ...item // 保留其他字段
          }))
          cb(suggestions)
        })
        .finally(() => {
          this.loading = false
        })
    },
    // 自定义利率曲线监听
    getParamsList(arr) {
      for (let index = 0; index < arr.length; index++) {
        const v = arr[index]
        v.curvename = `上市首日估值平均偏离度曲线${this.numberToChinese(index + 1)}`
        v.tradeDtEnd = Array.isArray(this.form.customDateRange)
          ? moment(this.form.customDateRange[1]).format('YYYYMMDD')
          : ''
        v.tradeDtStart = Array.isArray(this.form.customDateRange)
          ? moment(this.form.customDateRange[0]).format('YYYYMMDD')
          : ''
      }
      this.searchParamsList = arr
      this.getChartData()
    },
    // 数字转汉字
    numberToChinese(num) {
      if (isNaN(num) || num > 9999999999999999) {
        return '数字超出范围'
      }

      if (num === 0) {
        return '零'
      }

      // 定义数字对应的汉字
      const chineseNums = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九']
      // 定义单位
      const units = ['', '十', '百', '千']
      const bigUnits = ['', '万', '亿', '兆']

      // 处理负数
      let prefix = ''
      if (num < 0) {
        prefix = '负'
        num = Math.abs(num)
      }

      // 将数字转为字符串并分割为4位一组
      const numStr = Math.floor(num).toString()
      const segments = []
      for (let i = numStr.length; i > 0; i -= 4) {
        segments.unshift(numStr.slice(Math.max(0, i - 4), i))
      }

      let result = ''

      segments.forEach((segment, index) => {
        let segmentStr = ''
        let hasZero = false

        for (let i = 0; i < segment.length; i++) {
          const digit = parseInt(segment[i])
          const pos = segment.length - 1 - i

          if (digit === 0) {
            hasZero = true
          } else {
            if (hasZero) {
              segmentStr += chineseNums[0]
              hasZero = false
            }
            segmentStr += chineseNums[digit] + units[pos]
          }
        }

        if (segmentStr !== '') {
          segmentStr += bigUnits[segments.length - 1 - index]
        }

        result += segmentStr
      })

      // 处理连续的零
      result = result.replace(/零+/g, '零')
      // 去除末尾的零
      result = result.replace(/零+$/, '')
      // 处理一十开头的特殊情况
      if (result.startsWith('一十')) {
        result = result.substring(1)
      }

      return prefix + result
    },
    /**
     * 债券简称远程搜索
     */
    querySearchAsync(queryString, cb) {
      if (queryString) {
        queryBondShortName({
          text: queryString
        }).then((data) => {
          if (data && Object.keys(data).length) {
            cb(data)
          } else {
            cb([])
          }
        })
      } else {
        cb([])
      }
    },
    // 切换tab
    changeType() {
      this.changeTabs()
    },
    // 保存企业用户页面设置参数
    async saveCustomCurveRequestdata(tabType, requestData, id) {
      const res = await saveCurveRequestdata([
        { tabType: tabType, requestData: requestData, id: id, tab: 'valuation' + this.$store.getters.sysVersion }
      ])
      if (Object.hasOwnProperty.call(res, 'valuationDeviationSettings')) {
        this.valuationDeviationSettingsId = res.valuationDeviationSettings || null
      }
      if (Object.hasOwnProperty.call(res, 'valuationScatterSettings')) {
        this.valuationScatterSettingsId = res.valuationScatterSettings || null
      }
      if (Object.hasOwnProperty.call(res, 'creditValuationSettings')) {
        this.creditValuationSettingsId = res.creditValuationSettings || null
      }
    },
    // 初始化设置参数
    initSearchLists() {
      queryCurveRequestdata({ tab: 'valuation' + this.$store.getters.sysVersion }).then((res) => {
        if (Object.hasOwnProperty.call(res, 'valuationDeviationSettings')) {
          this.valuationDeviationSettingsId = res['valuationDeviationSettings'].id

          this.searchParamsList = JSON.parse(res['valuationDeviationSettings'].requestData)
        } else {
          this.searchParamsList = []
        }

        if (Object.hasOwnProperty.call(res, 'valuationScatterSettings')) {
          this.valuationScatterSettingsId = res['valuationScatterSettings'].id
          this.valuationScatterSettings = JSON.parse(res['valuationScatterSettings'].requestData) || []
        } else {
          this.valuationScatterSettings = []
        }

        if (Object.hasOwnProperty.call(res, 'creditValuationSettings')) {
          this.creditValuationSettingsId = res['creditValuationSettings'].id
          this.creditValuationSettings = JSON.parse(res['creditValuationSettings'].requestData) || []
        } else {
          this.creditValuationSettings = []
        }
      })
    },
    // 新增
    // 处理表格查询参数
    handleSearchParams() {
      const obj = {
        issue_status: this.form.checked ? '1' : '0', // 含已到期
        b_issue_lastissue: this.form.customDate ? moment(new Date(this.form.customDate)).format('YYYYMMDD') : '', // 截止日期
        s_info_name: this.form.s_info_name, // 债券简称
        b_info_issuercode: this.form.compId,
        webTime: new Date().getTime(),
        queryFlag: this.getQueryFlag()
      }

      return { ...this.tableParams, ...obj }
    },
    getQueryFlag() {
      switch (this.headerBtns) {
        case 'lgfv':
          if (this.form.customDate) return 'Y'
          return 'N'
        default:
          return 'Y'
      }
    },
    submit() {
      this.tableParams = this.handleSearchParams()
    }
  }
}
</script>

<style lang="scss" scoped>
@import '../css/valuation-spread-temporary.scss';
</style>
