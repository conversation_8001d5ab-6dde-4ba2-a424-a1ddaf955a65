<template>
  <div class="b-login-register">
    <div class="b-login-register--form">
      <h1>注册</h1>
      <el-form ref="registerForm" :model="registerForm" :rules="rules">
        <div class="item-title">企业信息</div>
        <el-form-item
          prop="companyName"
          label="企业名称"
        >
          <el-input v-model="registerForm.companyName" placeholder="请输入企业名称" @change="getOrgCode" />
        </el-form-item>
        <el-form-item
          prop="orgCode"
          label="组织机构代码"
        >
          <el-input v-model="registerForm.orgCode" placeholder="请输入组织机构代码" />
        </el-form-item>
        <div class="item-title">用户信息</div>
        <el-form-item
          prop="username"
          label="用户姓名"
        >
          <el-input v-model="registerForm.username" placeholder="请输入用户姓名" />
        </el-form-item>
        <el-form-item
          prop="mobile"
          label="用户手机号"
        >
          <el-input v-model="registerForm.mobile" placeholder="请输入手机号码" />
        </el-form-item>
        <el-form-item
          label="手机验证码"
          prop="code"
        >
          <el-input v-model="registerForm.code" :maxlength="10" autocomplete="new-password" class="b-verify--form-code" placeholder="手机验证码">
            <template #append>
              <el-link :type="isVerifyCode ? 'info' : 'primary'" :underline="false" :disabled="isVerifyCode" @click="sendCode">
                <span v-if="!isVerifyCode">获取手机验证码</span>
                <span v-else>{{ timer }}秒后重发</span>
              </el-link>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item
          prop="pwd"
          label="密码"
        >
          <el-input v-model="registerForm.pwd" type="password" autocomplete="new-password" placeholder="请输入密码" />
        </el-form-item>
        <el-form-item
          prop="pwd2"
          label="确认密码"
        >
          <el-input v-model="registerForm.pwd2" type="password" autocomplete="new-password" placeholder="请输入密码" />
        </el-form-item>
        <el-form-item
          prop="title"
          label="职务"
        >
          <el-input v-model="registerForm.title" placeholder="请输入职务" :maxlength="200" />
        </el-form-item>
        <el-form-item
          prop="dept"
          label="部门"
        >
          <el-input v-model="registerForm.dept" placeholder="请输入部门" :maxlength="200" />
        </el-form-item>
        <el-form-item
          prop="recommender"
          label="推荐人工号（如有）"
        >
          <el-input v-model="registerForm.recommender" placeholder="请输入推荐人工号" :maxlength="200" />
        </el-form-item>
      </el-form>
      <div class="b-login-register--form-btn">
        <el-button
          type="primary"
          class="b-block-button"
          :loading="loading"
          @click="submit"
        >
          注册
        </el-button>
      </div>
      <div class="b-login-register--form-bottom">
        <el-link :underline="false" class="el-icon-arrow-left" @click="back">返回</el-link>
        <el-link :underline="false" @click="reset">清空</el-link>
      </div>
    </div>
  </div>
</template>

<script>
import { registerUser, verifyCode, findOrgCode } from '@/api/login'
import { FindPwdRexSwitch, FindSmJmSwitch } from '@jupiterweb/api/login'
import sm4Encrypt from '@jupiterweb/utils/sm4'
export default {
  name: 'Register',
  data() {
    const self = this
    const validateToNextPassword = (rule, value, callback) => {
      if (value) {
        const isPassed = self.expressionList && self.expressionList.length
        if (
          isPassed &&
          self.expressionList[0].rexList &&
          self.expressionList[0].rexList.length &&
          !new RegExp(self.expressionList[0].rexList[0]).test(self.registerForm.pwd)
        ) {
          callback(new Error(self.expressionList[0].tip))
        } else if (
          isPassed &&
          self.expressionList[1].rexList &&
          self.expressionList[1].rexList.length &&
          self.expressionList[1].rexList.some(v => !new RegExp(v).test(self.registerForm.pwd))
        ) {
          callback(new Error(self.expressionList[1].tip))
        } else {
          if (self.registerForm.pwd2 !== '') {
            self.$nextTick(() => {
              self.$refs.registerForm.validateField('pwd2')
            })
          }
          callback()
        }
      }
    }
    const compareToFirstPassword = (rule, value, callback) => {
      if (value === '') {
        callback(new Error(' '))
      } else if (value !== this.registerForm.pwd) {
        callback(new Error('两次密码输入不一致，请重新输入'))
      } else {
        callback()
      }
    }
    return {
      registerForm: {},
      expressionList: [],
      timer: 60,
      loading: false,
      isVerifyCode: false,
      rules: {
        companyName: [{ required: true, message: '企业名称不能为空', trigger: 'blur' }],
        orgCode: [{ required: true, message: '组织机构代码不能为空', trigger: 'blur' }],
        username: [{ required: true, message: '用户姓名不能为空', trigger: 'blur' }],
        pwd: [{ required: true, message: '密码不能为空', trigger: 'blur' },
          {
            validator: validateToNextPassword,
            trigger: 'blur'
          }],
        pwd2: [{ required: true, message: '确认密码不能为空', trigger: 'blur' }, {
          validator: compareToFirstPassword,
          trigger: 'blur'
        }],
        mobile: [{ required: true, message: '手机号码不能为空', trigger: 'blur' }, {
          validator: (rule, value, callback) => {
            if (value) {
              if (!/^1[3-9]\d{9}$/.test(value)) {
                callback(new Error('手机号格式错误'))
              } else {
                callback()
              }
            } else {
              callback()
            }
          }
        }],
        code: [{ required: true, message: '手机验证码不能为空', trigger: 'blur' }]
      }
    }
  },
  created() {
    this.findPwdRexSwitch()
  },
  methods: {
    // 获取后端配置的正则，验证用户输入的密码是否符合要求
    async findPwdRexSwitch() {
      const result = await FindPwdRexSwitch()
      this.expressionList = result || []
    },
    back() {
      this.$emit('close', 'register')
    },
    // 获取组织机构代码
    async getOrgCode(val) {
      const res = val ? await findOrgCode({ companyName: val }) : null
      this.$set(this.registerForm, 'orgCode', res || '')
    },
    // 发送验证码
    sendCode() {
      const { mobile } = this.registerForm
      if (!mobile || !/^1[3-9]\d{9}$/.test(mobile)) {
        return this.msgError('请输入正确的手机号')
      }
      this.$emit('openCaptcha', () => {
        this.timer = 60
        this.timerFn()
        this.isVerifyCode = true
        verifyCode({
          type: 'R', // L登录 R注册, F忘记密码
          mobile
        }, (isSuccess, data, message) => {
          isSuccess && this.msgSuccess(message || '验证码已发送')
        })
      })
    },
    timerFn() {
      if (this.timer > 1) {
        this.timer--
        setTimeout(this.timerFn, 1000)
      } else {
        this.isVerifyCode = false
      }
    },
    submit() {
      this.$refs.registerForm.validate(async(valid) => {
        if (valid) {
          this.loading = true
          const result = await FindSmJmSwitch('SM4_FLAG') // 判断密码是否开启国密加密
          const isOpened = result && result.effectflag === this.$dict.EFFECTFLAG_E && result.paravalue === '1'
          const { pwd, ...res } = this.registerForm
          const sPwd = isOpened ? sm4Encrypt(pwd) : window.btoa(pwd)
          const params = {
            ...res,
            pwd: sPwd,
            pwd2: sPwd
          }
          registerUser(params, (isSuccess, data, message) => {
            this.loading = false
            isSuccess && this.msgSuccess(message || '注册成功') && this.back()
          })
        }
      })
    },
    reset() {
      this.$refs.registerForm.resetFields()
    }
  }
}
</script>
<style lang="scss" scoped>
.b-login-register {
  width: 100%;
  &--form {
    margin-top: 0;
    background: #FFFFFF;
    box-shadow: 0px 6px 16px 0px rgba(0,0,0,0.03), 0px 3px 6px -4px rgba(0,0,0,0.03), 0px -6px 16px 0px rgba(0,0,0,0.03);
    border-radius: 8px;
    padding: 40px 0 24px 40px;
    h1 {
      text-align: center;
      margin-bottom: 24px;
      font-size: 24px;
      color: rgba(0, 0, 0, 0.85);
    }
    .el-form {
      overflow: auto;
      height: 60.19vh;
      padding-right: 40px;
      ::v-deep .el-form-item.is-required:not(.is-no-asterisk)>.el-form-item__label:before {
        font-family: PingFangSC, PingFang SC;
        width: 8px;
        height: 22px;
        margin-left: 4px;
        font-size: var(--el-font-size-base);
        color: #F23639;
      }
    }
    ::v-deep .item-title {
      font-size: var(--el-font-size-medium);
      color: rgba(0, 0, 0, 0.85) !important;
      border-left-color: var(--theme--color);
      border-left-width: 3px;
      margin-top: 24px;
      margin-bottom: 24px;
      padding-left: 11px;
    }
    ::v-deep .el-form-item__label {
      display: flex;
      padding-top: 0 !important;
      flex-direction: row-reverse;
      line-height: 22px !important;
      margin-bottom: 4px !important;
    }
    ::v-deep .el-input__inner {
      padding: 9px 22px 9px 12px !important;
    }
    &-bottom {
      display: flex;
      justify-content: space-between;
      margin-top: 16px;
      line-height: 22px;
      padding-right: 40px;
    }
    ::v-deep .el-form-item + .el-form-item {
      margin-top: 24px !important;
    }
    &-btn {
      margin-top: 40px;
      padding-right: 40px;
    }
  }
}
</style>

