<template>
  <div class="publish-query-analyse">
    <div class="publish-query-analyse-top">发行总览</div>
    <div class="publish-query-analyse-tip">
      {{ queryText }}
      根据筛选条件，市场共发行债券
      <span>{{ sumData.sumAmount }}</span>
      亿元，平均发行期限
      <span>{{ sumData.sumTerm }}</span>
      Y，平均利率
      <span>{{ sumData.sumCouponRate }}%</span>
      ，票面加权
      <span>{{ sumData.sumAmountRate }}%</span>
      。
    </div>
    <div class="publish-query-analyse-box box-left">
      <div class="box-top">债券概览</div>
      <div class="box-content">
        <jr-table stripe :border="false" :data-source="configTable.data" :loading="configTable.loading">
          <el-table-column prop="bondTypeName" label="债券类型" />
          <el-table-column prop="bondCount" align="right" label="债券数(只)" />
          <el-table-column prop="sumAmount" align="right" label="规模(亿)" />
          <el-table-column prop="sumAmountRate" align="right" label="票面加权(%)" />
        </jr-table>
      </div>
    </div>
    <div class="publish-query-analyse-box box-right">
      <div class="box-top">
        规模分布
        <el-tooltip effect="dark" placement="top">
          <div slot="content">
            <div v-for="(item, index) in scaleContent" :key="index" class="publish-analyse-tip">{{ item }}</div>
          </div>
          <jr-svg-icon icon-class="info-circle" />
        </el-tooltip>
      </div>
      <div class="box-content">
        <div class="box-content-charts">
          <Echart ref="scaleCharts" :options="scaleOptions" :styles="{ height: '100%' }" />
        </div>
        <div class="box-content-legend">
          <div class="box-content-legend-top">
            <span v-if="scaleSel == '01'">债券类型</span>
            <span v-if="scaleSel == '02'">发行期限</span>
            <span v-if="scaleSel == '03'">评级</span>
            <span>规模数(亿)</span>
            <span>债券数(只)</span>
          </div>
          <div v-for="(item, index) in scaleLegendList" :key="index" class="box-content-legend-item">
            <span>{{ item.name }}</span>
            <span>{{ item.value }}</span>
            <span>{{ item.num }}</span>
          </div>
        </div>
      </div>
      <div class="box-screen">
        <jr-combobox
          v-model="scaleSel"
          placeholder="请选择"
          clearable
          filterable
          :data="scaleList"
          option-value="value"
          option-label="label"
        />
        <el-dropdown trigger="click" @command="scaleCommand">
          <el-button>
            <jr-svg-icon class="el-icon--right" icon-class="upload" />
            导出
            <i class="el-icon-caret-bottom" />
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command="tp">
              <jr-svg-icon icon-class="picture" />
              导出图片
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>
    </div>
    <div class="publish-query-analyse-box box-bottom">
      <div class="box-top">
        利率分布
        <el-tooltip effect="dark" placement="top">
          <div slot="content">
            <div v-for="(item, index) in rateContent" :key="index" class="publish-analyse-tip">{{ item }}</div>
          </div>
          <jr-svg-icon icon-class="info-circle" />
        </el-tooltip>
      </div>
      <div class="box-content">
        <Echart ref="rateCharts" :options="rateOptions" :styles="{ height: '100%' }" />
      </div>
      <div class="box-screen">
        <el-checkbox v-model="rateChecked">含权债</el-checkbox>
        <jr-combobox
          v-model="rateSel"
          placeholder="请选择"
          clearable
          filterable
          :data="rateList"
          option-value="value"
          option-label="label"
        />
        <div class="box-screen-dropdown">
          <el-dropdown trigger="click" @command="rateCommand">
            <el-button>
              <jr-svg-icon class="el-icon--right" icon-class="upload" />
              导出
              <i class="el-icon-caret-bottom" />
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item command="tp">
                <jr-svg-icon icon-class="picture" />
                导出图片
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import Echart from '@jupiterweb/components/echarts'
import {
  getBondTypeTotal,
  getBondTermTotal,
  getBondRatingTotal,
  getBondRateFirstDateTotal,
  getBondRateTermTotal
} from '@/api/bonds/bonds'
import { get } from 'lodash'
export default {
  components: {
    Echart
  },
  props: {
    permitdetail: {
      type: Object,
      default: () => ({})
    },
    menuinfo: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      scaleSel: '01',
      scaleList: [
        {
          label: '按债券类型统计',
          value: '01'
        },
        {
          label: '按发行期限统计',
          value: '02'
        },
        {
          label: '按评级统计',
          value: '03'
        }
      ],
      rateChecked: false,
      rateSel: '01',
      rateList: [
        {
          label: '按发行时间统计',
          value: '01'
        },
        {
          label: '按发行期限统计',
          value: '02'
        }
      ],
      scaleChart: null,
      scaleChartParams: {},
      scaleOptions: {
        toolbox: {
          show: false
        }
      },
      scaleLegendList: [],
      rateChart: null,
      rateChartParams: {},
      rateOptions: {
        toolbox: {
          show: false
        }
      },
      configTable: {
        loading: true,
        data: []
      },
      sumData: {},
      formData: {},
      queryText: '',
      scaleContent: ['按债券类型统计:展示发行时间范围内，所选区域发行成功的债券按照债券类型分类，统计不同债券类型的债券规模占比；', '按发行期限统计:展示发行时间范围内,所选区域发行成功的债券按照发行期限分类,统计不同发行期限的债券规模占比；', '按评级统计:展示发行时间范围内，所选区域发行成功的债券按照评级分类,统计不同评级的债券规模占比,对于无债项评级的债券，优先取担保人评级，其次取发行人主体评级，暂不支持境外债规模按评级统计。'],
      rateContent: ['按发行时间统计:展示发行时间范围内，所选区域成功发行的债券按照评级分组后，其规模加权平均票面的分布情况;', '按发行期限统计:展示发行时间范围内，所选区域符合关键期限的债券按照评级分组后，其规模加权平均票面的分布情况。对于无债项评级的债券，优先取担保人评级，其次取发行人主体评级。']
    }
  },
  watch: {
    scaleSel(val) {
      const seriesData = []
      const legendData = []
      if (val == '01') {
        getBondTypeTotal(this.formData).then((res) => {
          if (Array.isArray(res)) {
            for (const i of res) {
              if (i.bondTypeName !== '合计') {
                seriesData.push({
                  name: i.bondTypeName,
                  value: i.sumAmount,
                  num: i.bondCount
                })
              }
              legendData.push({
                name: i.bondTypeName,
                value: i.sumAmount,
                num: i.bondCount
              })
            }
          }
        })
      } else if (val == '02') {
        getBondTermTotal(this.formData).then((res) => {
          if (Array.isArray(res)) {
            for (const i of res) {
              if (i.bondTypeName !== '合计') {
                seriesData.push({
                  name: i.term,
                  value: i.sumAmount,
                  num: i.bondCount
                })
              }
              legendData.push({
                name: i.term,
                value: i.sumAmount,
                num: i.bondCount
              })
            }
          }
        })
      } else if (val == '03') {
        getBondRatingTotal(this.formData).then((res) => {
          if (Array.isArray(res)) {
            for (const i of res) {
              if (i.bondTypeName !== '合计') {
                seriesData.push({
                  name: i.bondratingConversion,
                  value: i.sumAmount,
                  num: i.bondCount
                })
              }
              legendData.push({
                name: i.bondratingConversion,
                value: i.sumAmount,
                num: i.bondCount
              })
            }
          }
        })
      }
      this.drawScaleChart(seriesData)
      this.changeScaleLegend(legendData)
    },
    rateSel(val) {
      let xAxisData = []
      const seriesData = []
      if (val == '01') {
        getBondRateFirstDateTotal({ ...this.formData, isRight: this.rateChecked ? '1' : '0' }).then((res) => {
          if (res && res.length > 0) {
            for (const i of res) {
              xAxisData.push(i.bIssueFirstIssue)
              seriesData.push([
                i.bIssueFirstIssue,
                i.sumAmountRate
              ])
            }
          }
          xAxisData.sort(function(a, b) {
            return a - b;
          })
          this.drawRateChart(xAxisData, seriesData)
        })
      } else if (val == '02') {
        getBondRateTermTotal({ ...this.formData, isRight: this.rateChecked ? '1' : '0' }).then((res) => {
          if (res && res.length > 0) {
            for (const i of res) {
              xAxisData.push(i.term)
              seriesData.push([
                i.term,
                i.sumAmountRate
              ])
            }
          }
          this.drawRateChart(xAxisData, seriesData)
        })
      }
    },
    rateChecked(val) {
      let xAxisData = []
      const seriesData = []
      if (this.rateSel == '01') {
        getBondRateFirstDateTotal({ ...this.formData, isRight: val ? '1' : '0' }).then((res) => {
          if (res && res.length > 0) {
            for (const i of res) {
              xAxisData.push(i.bIssueFirstIssue)
              seriesData.push([
                i.bIssueFirstIssue,
                i.sumAmountRate
              ])
            }
          }
          xAxisData.sort(function(a, b) {
            return a - b;
          })
          this.drawRateChart(xAxisData, seriesData, true)
        })
      } else if (this.rateSel == '02') {
        getBondRateTermTotal({ ...this.formData, isRight: val ? '1' : '0' }).then((res) => {
          if (res && res.length > 0) {
            for (const i of res) {
              xAxisData.push(i.term)
              seriesData.push([
                i.term,
                i.sumAmountRate
              ])
            }
          }
          this.drawRateChart(xAxisData, seriesData)
        })
      }
    }
  },
  mounted() {
    const params = get(this, 'menuinfo.meta.params', {})
    this.sumData = params.sumData || {}
    this.formData = params.formData || {}

    if(this.formData.firstDtStart && this.formData.firstDtEnd) {
      this.queryText = `${this.formData.firstDtStart}至${this.formData.firstDtEnd}，`
    } else {
      this.queryText = ''
    }

    getBondTypeTotal(this.formData).then((res) => {
      this.configTable.loading = false
      const seriesData = []
      const legendData= []
      if (Array.isArray(res)) {
        this.configTable.data = res
        for (const i of res) {
          if (i.bondTypeName !== '合计') {
            seriesData.push({
              name: i.bondTypeName,
              value: i.sumAmount,
              num: i.bondCount
            })
          }
          legendData.push({
            name: i.bondTypeName,
            value: i.sumAmount,
            num: i.bondCount
          })
        }
      }
      this.drawScaleChart(seriesData)
      this.changeScaleLegend(legendData)
    })
    getBondRateFirstDateTotal({ ...this.formData, isRight: '0' }).then((res) => {
      let xAxisData = []
      const seriesData = []
      if (res && Array.isArray(res)) {
        for (const i of res) {
          xAxisData.push(i.bIssueFirstIssue)
          seriesData.push([
            i.bIssueFirstIssue,
            i.sumAmountRate
          ])
        }
      }
      xAxisData.sort(function(a, b) {
        return a - b;
      })
      this.drawRateChart(xAxisData, seriesData, true)
    })
  },
  methods: {
    // 导出下拉菜单点击事件
    scaleCommand(type) {
      if (type === 'tp') {
        // 下载图片
        const exportOptions = {
          backgroundColor: '#ffffff',
          pixelRatio: 2 // 提高导出清晰度（可选）
        }
        // // 生成图片 URL 并触发下载
        const chart = this.$refs.scaleCharts.myChart
        const imgUrl = chart.getDataURL(exportOptions)
        const link = document.createElement('a')
        link.href = imgUrl
        link.download = `规模分布.png`
        link.click()
      }
    },
    rateCommand(type) {
      if (type === 'tp') {
        // 下载图片
        const exportOptions = {
          backgroundColor: '#ffffff',
          pixelRatio: 2 // 提高导出清晰度（可选）
        }
        // // 生成图片 URL 并触发下载
        const chart = this.$refs.rateCharts.myChart
        const imgUrl = chart.getDataURL(exportOptions)
        const link = document.createElement('a')
        link.href = imgUrl
        link.download = `利率分布.png`
        link.click()
      }
    },
    scaleChartCallback(refData) {
      this.scaleChart = refData
    },
    rateChartCallback(refData) {
      this.rateChart = refData
    },
    drawScaleChart(data) {
      this.scaleOptions = {
        toolbox: {
          show: false
        },
        tooltip: {
          show: false
        },
        legend: {
          show: false
        },
        series: [
          {
            type: 'pie',
            center: ['50%', '50%'],
            radius: ['40%', '60%'],
            itemStyle: {
              borderColor: '#fff',
              borderWidth: 1
            },
            label: {
              show: true,
              color: 'inherit',
              formatter: function (params) {
                return `{value|${params.value}}\n{name|${params.name},${params.data.num}只}`
              },
              rich: {
                value: {
                  fontSize: 12
                },
                name: {
                  fontSize: 12,
                  color: 'rgba(0, 0, 0, 0.85)'
                }
              }
            },
            labelLine: {
              show: true
            },
            data: data
          }
        ]
      }
    },
    drawRateChart(xAxisData, seriesData, formatDate) {
      this.rateOptions = {
        grid: {
          top: '56',
          left: '56',
          right: '24',
          bottom: '24'
        },
        toolbox: {
          show: false
        },
        tooltip: {
          formatter: function (params) {
            let text = params.value[0]
            if (formatDate && text) {
              text = `${text.substring(0, 4)}/${text.substring(4, 6)}/${text.substring(6, 8)}`
            }
            return `<span>${params.marker}${text}：${params.value[1]}</span>`
          }
        },
        color: '#5B8FF9',
        xAxis: {
          axisLine: {
            lineStyle: {
              color: 'rgba(0, 0, 0, 0.45)'
            }
          },
          axisTick: {
            alignWithLabel: true
          },
          data: xAxisData,
          axisLabel: {
            formatter: function (val) {
              let text = val
              if (formatDate) {
                text = `${text.substring(0, 4)}/${text.substring(4, 6)}/${text.substring(6, 8)}`
              }
              return text
            }
          }
        },
        yAxis: {
          name: '单位(%)'
        },
        series: [
          {
            symbolSize: 12,
            data: seriesData,
            type: 'scatter'
          }
        ]
      }
    },
    changeScaleLegend(data) {
      this.scaleLegendList = data
    }
  }
}
</script>
<style lang="scss">
.publish-query-analyse {
  position: relative;
  height: 100%;
  background: #fff;

  &-top {
    height: 70px;
    line-height: 70px;
    color: rgba(0, 0, 0, 0.85);
    font-size: var(--el-font-size-large);
    font-family: MicrosoftYaHeiSemibold;
    font-weight: bold;
  }

  &-tip {
    padding: 0 16px;
    height: 46px;
    line-height: 46px;
    background: #fef4ee;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;

    span {
      color: var(--theme--color);
    }
  }

  .publish-query-analyse-box {
    position: absolute;
    border: 1px solid #eae9e9;
    border-radius: 4px;

    &.box-left {
      top: 132px;
      left: 16px;
      width: calc(50% - 24px);
      height: calc(50% - 124px);
    }

    &.box-right {
      top: 132px;
      right: 16px;
      width: calc(50% - 24px);
      height: calc(50% - 124px);

      .box-content {
        position: relative;

        &-charts {
          width: 50%;
          height: 100%;
        }

        &-legend {
          position: absolute;
          top: 0;
          left: 50%;
          right: 0;
          bottom: 0;
          overflow: auto;

          &-top {
            height: 40px;

            span {
              float: left;
              width: 33.33%;
              height: 40px;
              line-height: 40px;
              padding-left: 16px;
              color: rgba(0, 0, 0, 0.6);
            }
          }

          &-item {
            margin-top: 8px;
            height: 40px;
            background: rgba(250, 250, 250, 1);

            &:hover {
              background: #FFF3E9;
            }

            span {
              float: left;
              width: 33.33%;
              height: 40px;
              line-height: 40px;
              padding-left: 16px;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              color: #8C8C8C;
            }
          }
        }
      }

      .box-screen {
        .el-select {
          margin-right: 16px;
        }
      }
    }

    &.box-bottom {
      left: 16px;
      right: 16px;
      bottom: 16px;
      height: calc(50% - 40px);

      .box-screen {
        &-dropdown {
          margin-top: 16px;
          text-align: right;
        }
      }
    }

    .box-top {
      position: relative;
      padding-left: 27px;
      height: 64px;
      line-height: 64px;
      font-size: var(--el-font-size-medium);
      color: rgba(0, 0, 0, 0.85);

      &::before {
        content: '';
        position: absolute;
        top: 24px;
        left: 16px;
        width: 3px;
        height: 16px;
        background: var(--theme--color);
      }

      span {
        color: rgba(0, 0, 0, 0.6);
      }
    }

    .box-content {
      margin: 0 16px;
      height: calc(100% - 80px);
      overflow: auto;
    }

    .box-screen {
      position: absolute;
      top: 16px;
      right: 16px;

      .el-checkbox {
        margin-right: 16px;
      }

      .el-select {
        width: 280px;
      }
    }
  }
}

.publish-analyse-tip {
  margin: 8px 0 0;

  &:first-child {
    margin: 0;
  }
}
</style>
