<template>
  <div>
    <el-dialog title="开庭公告详情" :visible.sync="visible" width="1144px" :before-close="handleCancel">
      <div class="dialog-content">
        <el-descriptions :column="3" :size="size" border>
          <el-descriptions-item>
            <template slot="label">开庭时间</template>
            2025-02-05
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">案号</template>
            2025沪公刑诉字第056号
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">案由</template>
            --
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">承办部门</template>
            --
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">审判长或主理人</template>
            --
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">开庭法院</template>
            成都市中级人民法院
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">身份</template>
            --
          </el-descriptions-item>
          <el-descriptions-item :span="2">
            <template slot="label">公告内容</template>
            --
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">当事人</template>
            张三
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  data() {
    return {
      visible: false,
      title: '',
      data: {},
      size: ''
    }
  },
  computed: {},
  methods: {
    /**
     * 弹框打开
     * @param {Object} row 行内数据
     * @param {String} title 展示表格名称
     */
    open(data) {
      this.visible = true
      this.data = data
    },
    /**
     * 弹框关闭
     */
    handleCancel() {
      this.visible = false
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-dialog__title {
  font-size: var(--el-font-size-extra-large);
  font-family: MicrosoftYaHeiSemibold;
  color: rgba($color: #000000, $alpha: 0.85);
  font-weight: bold;
}
::v-deep .el-dialog__body {
  padding: 0;

  .dialog-content {
    padding: 16px;
    background: #F0F2F5;

    .el-descriptions {
      padding: 16px;
      height: 552px;
      background: url(../../../../assets/images/dialog-bg.png) no-repeat left bottom;
      background-size: 100% auto;
      background-color: #ffffff;

      th.el-descriptions-item__cell.el-descriptions-item__label.is-bordered-label {
        background: #F7F8FA;
      }

      td.el-descriptions-item__cell.el-descriptions-item__content {
        width: 192px;
      }
    }
  }
}
</style>
