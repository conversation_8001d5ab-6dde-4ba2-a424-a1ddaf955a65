<template>
  <div class="quotesCenter">
    <cockpitHeader title="行情中心" :style="{ width: px2vw(374), height: px2vh(42) }" type="light" />
    <cockpitTabs v-model="activeTab" :tabs="tabs" :style="{ marginTop: px2vh(15) }" @change="tabChange" @handleMore="redirectToMenu" />
    <div class="quotesCenter-select">
      <cockpitSelect
        :style="{ width: px2vw(116), flexShrink: 0 }"
        :options="dictList[activeTab.termSelectName]"
        :default-value="select.bAnalCurveterm"
        label-key="text"
        @change="val=>selectChange('bAnalCurveterm',val)"
      />
      <cockpitSelect
        v-if="activeTab.tabName === '中短期票据'"
        :style="{ width: px2vw(116), flexShrink: 0 }"
        :options="dictList.MIDSHORT_BOND_MAIN_RATING"
        :default-value="select.mainRating"
        label-key="text"
        @change="val=>selectChange('mainRating',val)"
      />
      <cockpitSelect
        v-if="activeTab.tabName === '城投债'"
        :style="{ width: px2vw(116), flexShrink: 0 }"
        :options="dictList.CITYINVEST_BOND_MAIN_RATING"
        :default-value="select.mainRating"
        @change="val=>selectChange('mainRating',val)"
      />
      <p>
        <span>最新估值：</span>
        <span style="color: #ff8e2b">
          {{ bAnalYield }}%(
          <span :style="computedBPStyle">{{ bAnalYieldAdd * 1 > 0 ? '+' : '' }}{{ bAnalYieldAdd }}BP</span>
          )
        </span>
      </p>
    </div>
    <div style="width: 100%; height: 70.7%">
      <echarts :options="options" style="width: 100%; height: 100%" />
    </div>
  </div>
</template>

<script>
import cockpitHeader from '../../components/cockpit-header.vue'
import cockpitTabs from '../../components/cockpit-tabs.vue'
import cockpitSelect from '../../components/cockpit-select.vue'
import echarts from '@jupiterweb/components/echarts'
import { GetInfoFn } from '@jupiterweb/utils/api'
import * as op from '@/systems/market-quotation/components/chartParams'
import { series, options } from '@/systems/market-quotation/components/chartParams'
import { marketRatioCurve } from '@/api/poc/performance-evaluation'
import { cockpitGetLprCurveList } from '@/api/cockpit/cockpit'
import { deepClone } from '@jupiterweb/utils/common'
import { px2vw, px2vh } from '../../utils/portcss'
import { date } from '@/utils/common'
import moment from 'moment'
const  BOND_TERM_MARKET_CURVE_RATIO = 'BOND_TERM_MARKET_CURVE_RATIO'
const  MIDSHORT_BOND_MAIN_RATING = 'MIDSHORT_BOND_MAIN_RATING'
const  CITYINVEST_BOND_MAIN_RATING = 'CITYINVEST_BOND_MAIN_RATING'
const  BOND_TERM_MIDSHORT_RATIO = 'BOND_TERM_MIDSHORT_RATIO'
const  BOND_TERM_CITYINVEST_RATIO = 'BOND_TERM_CITYINVEST_RATIO'
const  LPR_YEAR = 'LPR_YEAR'
export default {
  name: 'QuotesCenter',
  components: {
    cockpitHeader,
    cockpitTabs,
    cockpitSelect,
    echarts
  },
  data() {
    return {
      tabs: [
        {
          tabName: '国债',
          value: '中债国债收益率曲线',
          menuId: '1351141647784513536',
          bAnalCurveterm: '5',
          termSelectName: 'BOND_TERM_MARKET_CURVE_RATIO',
          params: {
            tabName: 'first'
          }
        },
        {
          tabName: '国开债',
          value: '中债国开债收益率曲线',
          menuId: '1351141647784513536',
          bAnalCurveterm: '10',
          termSelectName: 'BOND_TERM_MARKET_CURVE_RATIO',
          params: {
            tabName: 'second'
          }
        },
        {
          tabName: '中短期票据',
          value: '中债中短期票据收益率曲线',
          menuId: '1351141879192653824',
          bAnalCurveterm: '0.5',
          mainRating: 'AAA-',
          termSelectName: 'BOND_TERM_MIDSHORT_RATIO',
          params: {
            tabName: 'first'
          }
        },
        {
          tabName: '城投债',
          value: '中债城投债收益率曲线',
          menuId: '1351141879192653824',
          bAnalCurveterm: '0.5',
          mainRating: 'AA(2)',
          termSelectName: 'BOND_TERM_CITYINVEST_RATIO',
          params: {
            tabName: 'second'
          }
        },
        {
          tabName: 'LPR',
          value: '贷款市场报价利率曲线',
          menuId: '1351141785684840448',
          bAnalCurveterm: 'LPR1Y.IR',
          termSelectName: 'LPR_YEAR',
        }
      ],
      options: op.options,
      dictList: [],
      select: {
        bAnalCurveterm: '0.5',
        ccid: '12ef221ca55f42e396528c6beecce30a',
        ownedModuleid: '1369319476859256832',
        bAnalCurveName: '中债国债收益率曲线',
        date: [date().subtract(0.5, 'y'), date().now()]
      },
      bAnalYield: '',
      bAnalYieldAdd: '',
      activeTab: {}
    }
  },
  computed: {
    computedBPStyle() {
      const num = this.bAnalYieldAdd * 1
      if(num > 0){
        return { color: '#E76056' }
      }else if(num === 0){
        return { color: '#FF9233' }
      }else{
        return { color: '#2BA270' }
      }
    }
  },
  created() {
    this.getDictList()
    this.select.bAnalCurveName = this.tabs[0].value
    this.getChartDataApi()
  },
  methods: {
    px2vw,
    px2vh,
    /**
     * 发行期限 字典
     */
    async getDictList() {
      const data = await GetInfoFn('DICT', [
        BOND_TERM_MARKET_CURVE_RATIO,
        MIDSHORT_BOND_MAIN_RATING,
        CITYINVEST_BOND_MAIN_RATING,
        BOND_TERM_MIDSHORT_RATIO,
        BOND_TERM_CITYINVEST_RATIO,
        LPR_YEAR
      ])
      this.dictList = data || []
    },
    /**
     * 下拉选择切换
     */
    selectChange(key,val) {
      this.select[key] = val
      if(this.activeTab.tabName === 'LPR'){
        this.getLprChartDataApi()
      }else{
        this.getChartDataApi()
      }
    },
    /**
     * 获取图表数据
     */
    async getChartDataApi() {
      const params = JSON.parse(JSON.stringify(this.select))
      if(params.mainRating){
        params.mainRating = [params.mainRating]
      }else{
        delete params.mainRating
      }
      if(params.bAnalCurveterm){
        params.bAnalCurveterm = [params.bAnalCurveterm]
      }else{
        delete params.bAnalCurveterm
      }
      const info = await marketRatioCurve(
        {
          ...params,
          tradeDtStart: params.date.length > 0 ? params.date[0] : '',
          tradeDtEnd: params.date.length > 0 ? params.date[1] : ''
        },
        '/marketdata/market/bondYieldCurve'
      )
      this.getNewAndBP(info)
      const data = []
      const legend = []
      const seriesNew = deepClone(series)
      const optionsNew = deepClone(options)
      // const colorNew = deepClone(color)
      if (!info || !Object.keys(info)) {
        return optionsNew
      }
      if (Object.keys(info).length > 2) delete seriesNew.markPoint

      Object.keys(info).forEach((k, i) => {
        const seriesData = []
        info[k].forEach((g) => {
          seriesData.push([
            moment(g.tradeDt).format("YYYY/MM/DD"),
            g.bAnalYield,
            g.fullCurveName,
            params.dimension === '2' ? g.mainRating : g.bAnalCurveterm
          ])
        })
        data.push({ ...seriesNew, data: seriesData, smooth: true, name: k, itemStyle: { color: '#FF8E2B' } })
        legend.push({ name: k, textStyle: { fontSize: 12, padding: [1, 0, 0, 6], color: '#333' } })
      })
      optionsNew.legend.data = legend
      optionsNew.series = data
      optionsNew.legend.show = false
      optionsNew.grid = [
        {
          bottom: 50,
          left: 30,
          right: 16,
          top: 40
        }
      ]
      optionsNew.xAxis[0].axisLabel.textStyle.color = 'rgba(0,0,0,0.6)'
      optionsNew.yAxis[0].axisLabel.textStyle.color = 'rgba(0,0,0,0.6)'
      optionsNew.yAxis[0].splitLine.lineStyle.color = '#7DC1E032'
      optionsNew.series[0]?.smooth && (optionsNew.series[0].smooth = true)
      this.options = optionsNew
    },
    /**
     * 获取LPR图表数据
     */
    async getLprChartDataApi() {
      const info = await cockpitGetLprCurveList(this.select)
      this.bAnalYield = info[info.length - 1].bAnalYield
      this.bAnalYieldAdd = info[info.length - 1].bAnalYieldAdd
      const data = []
      const legend = []
      const seriesNew = deepClone(series)
      const optionsNew = deepClone(options)
      const seriesData = []
      info.forEach((g) => {
        seriesData.push([
          moment(g.tradeDt).format("YYYY/MM/DD"),
          g.bAnalYield,
          g.fullCurveName,
          params.dimension === '2' ? g.mainRating : g.bAnalCurveterm
        ])
      })
      data.push({ ...seriesNew, data: seriesData, smooth: true, name: k, itemStyle: { color: '#FF8E2B' } })
      legend.push({ name: k, textStyle: { fontSize: 12, padding: [1, 0, 0, 6], color: '#333' } })
      optionsNew.legend.data = legend
      optionsNew.series = data
      optionsNew.legend.show = false
      optionsNew.grid = [
        {
          bottom: 50,
          left: 30,
          right: 16,
          top: 40
        }
      ]
      optionsNew.xAxis[0].axisLabel.textStyle.color = 'rgba(0,0,0,0.6)'
      optionsNew.yAxis[0].axisLabel.textStyle.color = 'rgba(0,0,0,0.6)'
      optionsNew.yAxis[0].splitLine.lineStyle.color = '#7DC1E032'
      optionsNew.series[0]?.smooth && (optionsNew.series[0].smooth = true)
      this.options = optionsNew
    },
    /**
     * 获取最新估值与涨跌BP
     */
    getNewAndBP(data) {
      for (let key in data) {
        this.bAnalYield = data[key][data[key].length - 1].bAnalYield
        this.bAnalYieldAdd = data[key][data[key].length - 1].bAnalYieldAdd
      }
    },
    /**
     * 切换tabs
     */
    tabChange(tab) {
      this.select.bAnalCurveName = tab.value
      this.select.bAnalCurveterm = tab.bAnalCurveterm
      this.select.mainRating = tab.mainRating
      this.activeTab.menuId = tab.menuId
      if(tab.tabName === 'LPR'){
        this.getLprChartDataApi()
      }else{
        this.getChartDataApi()
      }
    },
    /**
     * 路径跳转
     */
    redirectToMenu() {
      console.log(this.activeTab,"activeTab")
      this.$store.dispatch('tagsView/updateCurrentTab', {
        path: '/' + this.activeTab.menuId,
        // 跳转需要刷新的话 refresh传true
        meta: {
          refresh: true,
          params: this.activeTab.params
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~assets/styles/portcss.scss';
.quotesCenter {
  width: 100%;
  height: vh(436);
  background: rgba(255, 255, 255, 0.5);
  box-shadow: 0px 4px 20px -2px rgba(0, 0, 0, 0.06);
  border: vh(2) solid transparent;
  border-radius: vh(12);
  background-clip: padding-box, border-box;
  background-origin: padding-box, border-box;
  background-image: linear-gradient(to right, rgba(255, 255, 255, 1), rgba(255, 255, 255, 0.5)),
    radial-gradient(rgba(255, 255, 255, 1), rgba(255, 255, 255, 0.5));
  padding: 0px vw(16);
  &-select {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: vh(15);
    p {
      margin-bottom: 0px;
      span {
        height: vh(20);
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: vh(12);
        color: rgba(0, 0, 0, 0.6);
        line-height: vh(20);
      }
    }
  }
}
</style>
