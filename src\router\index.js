/**
 * 路由配置文件
 */
export default {
  /**
   * 路由定义
   * @example {path: "/demo", name: "demoPage", component: "demo/Index"}
   * @description
   * path用以指定前端路由地址
   */
  routes: [
    // DEMO 演示
    {
      path: '/demo',
      component: (resolve) => require(['@/systems/demo'], resolve)
    },
    // 登录
    {
      path: '/login',
      component: (resolve) => require(['@/systems/login'], resolve)
    },
    // 登录
    {
      path: '/jupiter/login',
      component: (resolve) => require(['@/systems/login'], resolve)
    }
  ]
}
