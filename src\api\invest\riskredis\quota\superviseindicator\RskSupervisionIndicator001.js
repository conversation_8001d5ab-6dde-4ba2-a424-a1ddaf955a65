import { GetInfoFn, UpdateFn } from '@jupiterweb/utils/api'

// 判断名称是否重复
export const chkIndNameRepeat = (params) => GetInfoFn(`/invest/riskredis/quota/superviseindicator/RskSupervisionIndicator002/chkIndNameRepeat`, params, 'post')
// 获取联合计算树
export const getAllPortfolioBag = (params) => GetInfoFn(`/invest/riskredis/quota/superviseindicator/RskSupervisionIndicator002/getAllPortfolioBag`, params, 'post')
// 获取单一计算树
export const getAllPortfolioBySort = (params) => GetInfoFn(`/invest/riskredis/quota/superviseindicator/RskSupervisionIndicator002/getAllPortfolioBySort`, params, 'post')
// 保存check
export const check = (params, cb) => UpdateFn(`/invest/riskredis/quota/superviseindicator/RskSupervisionIndicator002/check`, params, cb)
// 获取枚举类阈值
export const getForbidEnumInfo = (params) => GetInfoFn(`/invest/riskredis/quota/superviseindicator/RskSupervisionIndicator002/getForbidEnumInfo`, params, 'post')
