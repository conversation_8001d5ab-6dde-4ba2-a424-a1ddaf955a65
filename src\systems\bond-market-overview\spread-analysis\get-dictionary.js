import { GetComboboxList } from '@/api/home'
import { queryLoginCompanyRating } from '@/api/public/public'
import store from '@jupiterweb/store'
import dict from '@/utils/config/dict'
import echartNoDataGraphic from '@/assets/js/echartsNoData'
let implyrating = '' // 中债隐含评级

// 通用字典方法
async function getDictOptionsApi(DICTIONARYARRAY) {
  return await GetComboboxList(DICTIONARYARRAY)
}

// 获取公司评级
async function getCompanyRating() {
  const res = await queryLoginCompanyRating()
  return res.implyrating || ''
}

// 获取对应字典值
function getdictionarybystr(obj, str) {
  if (!obj) return []

  if (Object.hasOwnProperty.call(obj, str)) {
    return obj[str] || []
  }
  return []
}

// 处理多选框为空传所有code值
function getAllSelected(options) {
  if (Array.isArray(options) && options.length === 0) return ''
  return options.map((option) => option.value).join(',')
}

// 获取默认标准利率曲线利差曲线设置
async function getDefaultSettings(dictionaryObject) {
  implyrating = await getCompanyRating()
  return {
    multipleSelectList: multipleDefaultSearchSetting(dictionaryObject),
    changeList1: [singleDefaultSearchSetting(dictionaryObject, 1)],
    changeList2: [singleDefaultSearchSetting(dictionaryObject, 2)],
    changeList3: [singleDefaultSearchSetting(dictionaryObject, 3)]
  }
}
// 标准利率曲线利差曲线设置多选设置项
function multipleDefaultSearchSetting(dictionaryObject) {
  if (store.getters.sysVersion === dict.COMPANY_VER_company) {
    return [
      {
        checked: false,
        value: [],
        valueAllStr: getAllSelected(getdictionarybystr(dictionaryObject, 'issueTermMy')),
        label: '我司发行定价曲线',
        subtext: '',
        showtooltip: true,
        tiptext: '发行方式为“公募”，是否可续期为“不可续期',
        selectlist: getdictionarybystr(dictionaryObject, 'issueTermMy'),
        iconColor: '#5B8FF9'
      },
      {
        checked: true,
        value: ['10Y'],
        valueAllStr: getAllSelected(getdictionarybystr(dictionaryObject, 'issueTermCDB')),
        label: '中债国开债到期收益率',
        subtext: '',
        showtooltip: false,
        tiptext: '',
        selectlist: getdictionarybystr(dictionaryObject, 'issueTermCDB'),
        iconColor: '#269A99'
      },
      {
        checked: false,
        value: ['5Y'],
        valueAllStr: getAllSelected(getdictionarybystr(dictionaryObject, 'issueTermGB')),
        label: '中债国债到期收益率',
        subtext: '',
        showtooltip: false,
        tiptext: '',
        selectlist: getdictionarybystr(dictionaryObject, 'issueTermGB'),
        iconColor: '#5AD8A6'
      }
    ]
  } else {
    return [
      {
        checked: true,
        value: ['10Y'],
        valueAllStr: getAllSelected(getdictionarybystr(dictionaryObject, 'issueTermCDB')),
        label: '中债国开债到期收益率',
        subtext: '',
        showtooltip: false,
        tiptext: '',
        selectlist: getdictionarybystr(dictionaryObject, 'issueTermCDB'),
        iconColor: '#269A99'
      },
      {
        checked: false,
        value: ['5Y'],
        valueAllStr: getAllSelected(getdictionarybystr(dictionaryObject, 'issueTermGB')),
        label: '中债国债到期收益率',
        subtext: '',
        showtooltip: false,
        tiptext: '',
        selectlist: getdictionarybystr(dictionaryObject, 'issueTermGB'),
        iconColor: '#5AD8A6'
      }
    ]
  }
}

// 标准利率曲线利差曲线设置单选设置项
function singleDefaultSearchSetting(dictionaryObject, type) {
  switch (type) {
    case 1:
      return {
        checked: false,
        value: 'AAA-',
        label: '中债中短期票据到期收益率',
        subtext: '',
        showtooltip: true,
        tiptext: `评级选项为中债隐含评级${
          implyrating && store.getters.sysVersion === dict.COMPANY_VER_company
            ? '贵公司评级为 <span style="color:#F56C6C;">' + implyrating + '</span>'
            : ''
        } `,
        selectlist: getdictionarybystr(dictionaryObject, 'issueRatingMTN'),
        otherselectlist: getdictionarybystr(dictionaryObject, 'issueTermMTN'),
        othervalue: '6M',
        iconColor: '#E1B01E'
      }
    case 2:
      return {
        checked: false,
        value: 'AA(2)',
        label: '中债城投债到期收益率',
        subtext: '',
        showtooltip: true,
        tiptext: `评级选项为中债隐含评级${
          implyrating && store.getters.sysVersion === dict.COMPANY_VER_company
            ? '贵公司评级为 <span style="color:#F56C6C;">' + implyrating + '</span>'
            : ''
        } `,
        othervalue: '6M',
        selectlist: getdictionarybystr(dictionaryObject, 'issueRatingLGFV'),
        otherselectlist: getdictionarybystr(dictionaryObject, 'issueTermLGFV'),
        iconColor: '#E8684A'
      }
    case 3:
      return {
        checked: false,
        value: 'AAA',
        label: '中债企业债到期收益率',
        subtext: '',
        showtooltip: true,
        tiptext: `评级选项为中债隐含评级${
          implyrating && store.getters.sysVersion === dict.COMPANY_VER_company
            ? '贵公司评级为 <span style="color:#F56C6C;">' + implyrating + '</span>'
            : ''
        } `,
        selectlist: getdictionarybystr(dictionaryObject, 'issueRatingCB'),
        otherselectlist: getdictionarybystr(dictionaryObject, 'issueTermCB'),
        othervalue: '6M',
        iconColor: '#66BDFF'
      }
  }
}

function formatterDateString(str) {
  return str.replace(/^(\d{4})(\d{2})(\d{2})$/, '$1/$2/$3')
}

// 设置图表配置项
function setChartOptions(xAxis, series) {
  return {
    tooltip: {
      trigger: 'axis',
      confine: true, // 限制在图表区域内
      extraCssText: `
        max-height: 200px;
        overflow-y: auto;
          pointer-events: auto !important; /* 允许鼠标交互 */
        `,
      renderMode: 'html',
      enterable: true,
      formatter: function (params) {
        // 生成带颜色标记的HTML
        return `
        ${params.length > 0 ? `<div style="font-weight: bold; color: #333;">${params[0].axisValue}</div>` : ''}
      <div>
        ${params
          .map((p) => {
            if (!p.value) return ''
            return `
          <div style="display: flex; align-items: center; margin: 3px 0;">
            <span style="
              display: inline-block;
              width: 10px;
              height: 10px;
              background: ${p.color};
              margin-right: 6px;
              border-radius: 50%;
            "></span>
            <span>${p.seriesName}: ${p.value}</span>
          </div>
        `
          })
          .join('')}
      </div>
    `
      }
    },
    toolbox: {
      show: false
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: [...xAxis]
    },
    grid: {
      top: 36,
      left: 50,
      bottom: 60,
      right: 36
    },
    legend: {
      orient: 'horizontal',
      top: 330,
      left: 'center',
      type: 'scroll', // 设置为可滚动
      pageIconColor: '#000', // 分页箭头颜色
      pageIconInactiveColor: '#333', // 禁用时分页箭头颜色
      pageTextStyle: {
        color: '#000' // 页码文字颜色
      },
      textStyle: {
        color: '#000' // 设置文字颜色为深灰色
      },
      pageButtonItemGap: 5, // 分页按钮与图例项的间隔
      pageButtonGap: 10, // 分页按钮与图例组件外框的间隔
      pageButtonPosition: 'end', // 分页按钮位置
      pageFormatter: '{current}/{total}', // 页码显示格式
      padding: 5 // 图例内边距
    },
    yAxis: {
      name: '单位(BP)',
      nameTextStyle: {
        padding: [0, 0, 0, 24] // 左内边距增加
      },
      type: 'value',
      axisLabel: {
        formatter: '{value}'
      },
      axisPointer: {
        snap: true
      }
    },
    series: [...series],
    ...echartNoDataGraphic(xAxis)
  }
}

// 获取标准利率曲线利差曲线请求参数
function getStandard(obj) {
  const standard = []
  if (
    Object.hasOwnProperty.call(obj, 'multipleSelectList') &&
    Array.isArray(obj.multipleSelectList) &&
    obj.multipleSelectList.length > 0
  ) {
    obj.multipleSelectList.map((v) => {
      if (v.checked) {
        standard.push({
          curvename: v.label,
          termStr: Array.isArray(v.value) && v.value.length > 0 ? v.value.join(',') : v.valueAllStr,
          bondRating:
            Array.isArray(v.othervalue) && v.othervalue.length > 0 ? v.value.join(',') : v.othervalueAllStr || ''
        })
      }
    })
  }

  if (Object.hasOwnProperty.call(obj, 'changeList1') && Array.isArray(obj.changeList1) && obj.changeList1.length > 0) {
    obj.changeList1.map((v) => {
      if (v.checked) {
        standard.push({
          curvename: v.label,
          bondRating: v.value || v.valueAllStr,
          termStr: v.othervalue || v.othervalueAllStr
        })
      }
    })
  }

  if (Object.hasOwnProperty.call(obj, 'changeList2') && Array.isArray(obj.changeList2) && obj.changeList2.length > 0) {
    obj.changeList2.map((v) => {
      if (v.checked) {
        standard.push({
          curvename: v.label,
          bondRating: v.value || v.valueAllStr,
          termStr: v.othervalue || v.othervalueAllStr
        })
      }
    })
  }

  if (Object.hasOwnProperty.call(obj, 'changeList3') && Array.isArray(obj.changeList3) && obj.changeList3.length > 0) {
    obj.changeList3.map((v) => {
      if (v.checked) {
        standard.push({
          curvename: v.label,
          bondRating: v.value || v.valueAllStr,
          termStr: v.othervalue || v.othervalueAllStr
        })
      }
    })
  }

  return standard
}

// 判断是否为json字符串
function isJson(str) {
  try {
    JSON.parse(str)
    return true
  } catch (e) {
    return false
  }
}

// 信用利差绘制表格函数暂存
/**
 * 

function getHeaderSeries() {
  return {
    type: 'custom',
    renderItem: (params, api) => {
      const cellWidth = api.getWidth() / Math.max(1, this.headers.length)
      const headerHeight = 50
      const startY = 0

      const children = []

      if (Array.isArray(this.headers) && this.headers.length > 0) {
        // 表头背景
        children.push({
          type: 'rect',
          shape: {
            x: 0,
            y: startY,
            width: api.getWidth(),
            height: headerHeight
          },
          style: {
            fill: '#F5F5FB'
          },
          silent: true
        })

        // 表头文字和分隔线
        for (let index = 0; index < this.headers.length; index++) {
          const text = this.headers[index] || ''

          children.push({
            type: 'text',
            style: {
              text: text,
              x: index === 0 ? 20 : cellWidth * index + cellWidth / 2,
              y: startY + headerHeight / 2,
              fill: '#333',
              textAlign: index === 0 ? 'left' : 'center',
              textVerticalAlign: 'middle',
              fontSize: 16,
              fontWeight: 'bold'
            },
            silent: true
          })

          if (index > 0) {
            children.push({
              type: 'line',
              shape: {
                x1: cellWidth * index,
                y1: startY,
                x2: cellWidth * index,
                y2: startY + headerHeight
              },
              style: {
                stroke: '#e0e3e8',
                lineWidth: 1
              },
              silent: true
            })
          }
        }

        // 底部边框
        children.push({
          type: 'line',
          shape: {
            x1: 0,
            y1: startY + headerHeight,
            x2: api.getWidth(),
            y2: startY + headerHeight
          },
          style: {
            stroke: '#e0e3e8',
            lineWidth: 1
          },
          silent: true
        })
      }

      return {
        type: 'group',
        children: children,
        silent: true
      }
    },
    data: [0], // 必须有数据项
    z: 100
  }
}
function getDataSeries(rows) {
  return {
    type: 'custom',
    renderItem: (params, api) => {
      // 更安全的数据获取方式
      const dataIndex = params.dataIndex
      const rowData = rows[dataIndex] || { name: '', values: [] }
      const values = rowData.values || []
      const cellWidth = api.getWidth() / Math.max(1, this.headers.length)
      const rowHeight = 64
      const y = api.coord([0, dataIndex])[1] // 使用dataIndex获取y坐标

      console.log(y)

      const children = []

      // 单元格背景
      children.push({
        type: 'rect',
        shape: {
          x: 0,
          y: y - rowHeight / 2,
          width: cellWidth,
          height: rowHeight
        },
        style: {
          fill: '#F5F5FB', // 背景色
          stroke: '#e0e3e8', // 边框色
          lineWidth: 1
        },
        z: 1
      })

      // 行名称
      children.push({
        type: 'text',
        style: {
          text: rowData.name,
          x: cellWidth / 2,
          y: y,
          fill: '#333', // 文字颜色
          textAlign: 'center',
          textVerticalAlign: 'middle',
          fontSize: 16
        },
        z: 2
      })

      // 数据单元格
      for (let index = 0; index < values.length; index++) {
        const value = values[index]
        const x = cellWidth * (index + 1)

        // 单元格背景1
        children.push({
          type: 'rect',
          shape: {
            x: x,
            y: y - rowHeight / 2,
            width: cellWidth,
            height: rowHeight
          },
          style: {
            fill: '#fff',
            stroke: '#EAE9E9',
            lineWidth: 1
          },
          z: 1
        })

        // 单元格背景2
        if (value !== 0) {
          // 进度条背景
          children.push({
            type: 'rect',
            shape: {
              x: x + 16,
              y: y - rowHeight / 2 + 16,
              width: cellWidth - 32,
              height: rowHeight - 26
            },
            style: {
              fill: '#F4F4F4',
              stroke: '#F4F4F4',
              lineWidth: 1
            },
            z: 2
          })
          children.push({
            type: 'rect',
            shape: {
              x: x + 16,
              y: y - rowHeight / 2 + 16,
              width:
                ((cellWidth - 32) * this.progressPercentage(value === this.emptySymbol ? 0 : this.formatValue(value))) /
                100,
              height: rowHeight - 26
            },
            style: {
              fill: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 1,
                y2: 0,
                colorStops: [
                  {
                    offset: 0,
                    color: '#FFCC74' // 渐变起始颜色
                  },
                  {
                    offset: 0.5,
                    color: '#ecc479' // 渐变中间颜色
                  },
                  {
                    offset: 1,
                    color: '#FFAE68' // 渐变结束颜色
                  }
                ]
              },
              stroke: '#e0e3e8',
              lineWidth: 1
            },
            z: 3
          })
        }

        // 单元格内容
        children.push({
          type: 'text',
          style: {
            text: value === this.emptySymbol ? this.emptySymbol : this.formatValue(value),
            x: x + 52,
            y: y + 4,
            fill: '#333',
            textAlign: 'center',
            textVerticalAlign: 'middle',
            fontSize: 16
          },
          z: 4
        })
      }

      return {
        type: 'group',
        children: children
      }
    },
    data: rows,
    encode: {
      // 明确指定数据映射
      y: (params) => params.dataIndex // 使用数据索引作为y轴坐标
    }
  }
}

function getChartOption() {
  // 处理空值并反转行数据顺序
  const processedRows = this.rows
    .map((row) => ({
      name: row.name || '',
      values: Array.isArray(row.values)
        ? row.values.map((v) => (v === null || v === undefined || v === 0 ? this.emptySymbol : v))
        : new Array(this.headers.length - 1).fill(this.emptySymbol)
    }))
    .reverse()

  return {
    grid: {
      top: 54,
      left: 100,
      right: 50,
      bottom: 0,
      containLabel: true
    },
    xAxis: {
      type: 'value',
      show: false,
      max: this.calculateMaxValue()
    },
    yAxis: {
      type: 'category',
      axisLine: { show: false },
      axisTick: { show: false },
      data: processedRows.map((row) => row.name),
      axisLabel: {
        margin: 30,
        fontSize: 14,
        align: 'left'
      }
    },
    series: [this.getHeaderSeries(), this.getDataSeries(processedRows)]
  }
}

 */

export {
  singleDefaultSearchSetting,
  getdictionarybystr,
  getDictOptionsApi,
  getAllSelected,
  multipleDefaultSearchSetting,
  setChartOptions,
  getStandard,
  getDefaultSettings,
  isJson,
  formatterDateString
}
