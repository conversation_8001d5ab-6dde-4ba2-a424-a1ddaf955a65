/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023-10-10 15:56:49
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2023-10-10 15:56:50
 * @Description: 描述
 */
/**
 * @description demo 示例
 */
const demo = {

  'demo.flatIndex.query.name': '名称',
  'demo.flatIndex.query.address': '地区',

  'demo.flatIndex.table.name': '名称',
  'demo.flatIndex.table.date': '日期',
  'demo.flatIndex.table.amount': '金额(元)',
  'demo.flatIndex.table.amount2': '金额(百元)',
  'demo.flatIndex.table.tenThousand': '金额(万元)',
  'demo.flatIndex.table.rate': '利率',

  'demo.flatIndex.update.table.code1': '文本',
  'demo.flatIndex.update.table.code2': '日期',
  'demo.flatIndex.update.table.code3': '金额(元)',
  'demo.flatIndex.update.table.code4': '利率',
  'demo.flatIndex.update.table.code5': '下拉',
  'demo.flatIndex.update.table.code1.desc': '描述',

  // 交易所费用设置
  'demo.flatIndex.update.table.C1': '分档条件',
  'demo.flatIndex.update.table.C2': '分档金额(万元)',
  'demo.flatIndex.update.table.chargeValue': '费用值',
  'demo.flatIndex.update.table.chargeRate': '费率值(%)',
  'demo.flatIndex.update.table.feeMaxi': '最大值(元)',
  'demo.flatIndex.update.table.feeMini': '最小值(元)',

  // 公募基金存续期
  'demo.flatIndex.update.table.portfolioId': '组合代码',
  'demo.flatIndex.update.table.stockName': '组合名称',
  'demo.flatIndex.update.table.secManageAcctId': '证券托管户',
  'demo.flatIndex.update.table.bconUnitCprice': '折算前份额',
  'demo.flatIndex.update.table.afterUnitCprice': '折算前后额',
  'demo.flatIndex.update.table.registDate': '权益登记日份额',
  'demo.flatIndex.update.table.unitCprice': '单位分红净值',
  'demo.flatIndex.update.table.tradeAmt': '分红金额'
}
export default demo
