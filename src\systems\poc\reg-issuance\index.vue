<template>
  <div class="reg-issuance" style="height: 100%;">
    <flat-index :query="handleQuery" :reset="handleReset" :pagination="configTable.pagination">
      <template v-slot:form>
        <el-form ref="elForm" :model="configForm.model">
          <jr-form-item-create :column="0" :data="configForm.data" :model="configForm.model" />
        </el-form>
      </template>
      <template v-slot:right-button>
        <el-button>
          <jr-svg-icon icon-class="export" />
        </el-button>
      </template>
      <template v-slot:table-list="{ height }">
        <jr-table
          :height="height"
          :columns="configTable.columns"
          :data-source="configTable.data"
          :loading="configTable.loading"
          :pagination="configTable.pagination"
          :on-change="handleQuery"
          border
        >
          <template v-slot:index>
            <el-table-column
              type="index"
              width="50px"
              align="center"
              :label="InitialMessage('common.columns.index')"
            >
              <template slot-scope="scope">
                <span>{{ (configTable.pagination.pageNo - 1) * configTable.pagination.pageSize + scope.$index +1 }}</span>
              </template>
            </el-table-column>
          </template>
        </jr-table>
      </template>

    </flat-index>
    <modulesIndex ref="modulesIndex" />
  </div>
</template>
<script>
import * as API from '@/api/demo/flat-index'
import modulesIndex from './modules/index.vue'

export default {
  components: {
    modulesIndex
  },
  props: {
    // 平台日期
    date: {
      type: [Number, String],
      default: null
    },
    // 权限相关
    permitdetail: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      // 查询区域
      configForm: {
        model: {},
        data: [
          {
            title: '债券类型',
            prop: 'field1',
            type: 'select',
            options: [
              { text: '近期已发行', value: '1' },
              { text: '今日发行', value: '2' },
              { text: '即将发行', value: '3' },
              { text: '推迟发行', value: '4' },
              { text: '取消发行', value: '5' }
            ],
            change(value, row) {
              console.log('--债券类型 change --：', value)
            }
          },
          {
            title: '年龄',
            prop: 'field2',
            type: 'select',
            options: [
              { text: '18', value: '1' },
              { text: '19', value: '2' },
              { text: '20', value: '3' },
              { text: '21', value: '4' },
              { text: '22', value: '5' }
            ],
            change(value, row) {
              console.log('--年龄 change --：', value)
            }
          },
          {
            title: '预计挂网日',
            prop: 'field3',
            type: 'rangeDate'
          },
          {
            title: '更新日期',
            prop: 'field4',
            type: 'rangeDate'
          }
        ]
      },
      // 表格数据
      configTable: {
        loading: false,
        columns: [
          {
            title: '期数',
            prop: 'code',
            width: 200,
            render: (h, { row, column, rowIndex }) => {
              return <span class='table-action-box'>
                <el-button type='text' onClick={this.handleOpen.bind(this, row)}>{row.code}</el-button>
              </span>
            }
          },
          {
            title: '债券类型',
            prop: 'name'
          },
          {
            title: '发行期限',
            prop: 'user'
          },
          {
            title: '规模(亿元)',
            prop: 'agent'
          },
          {
            title: '预计价格区间(%)',
            prop: 'rate1',
            type: 'rate'
          },
          {
            title: '预计挂网日',
            prop: 'detail1'
          },
          {
            title: '预计薄记日',
            prop: 'rate2',
            type: 'rate'
          },
          {
            title: '预计到账日',
            prop: 'rate3',
            type: 'rate'
          },
          {
            title: '更新日期',
            prop: 'detail2'
          },
          {
            prop: 'id',
            title: '操作',
            align: 'center',
            render: () => {
              return <span class='table-action-box'>
                <el-tooltip content='修改'>
                  <jr-svg-icon icon-class='edit' onClick={this.handleEdit} />
                </el-tooltip>
                <el-tooltip content='删除'>
                  <jr-svg-icon icon-class='delete' onClick={this.handleDelete} />
                </el-tooltip>
                <el-tooltip content='下载'>
                  <jr-svg-icon icon-class='export' onClick={this.handleExport} />
                </el-tooltip>
              </span>
            }
          }
        ],
        data: [
          {
            code: '2023年度第一期',
            name: 'MTN',
            user: '3Y',
            agent: '光大证券股份有限公司',
            rate1: 50.00,
            detail1: '光大证券股份有限公司:10亿',
            rate2: 0.00,
            rate3: 0.00,
            detail2: '光大证券股份有限公司'
          },
          {
            code: '2023年度第一期',
            name: 'MTN',
            user: '3Y',
            agent: '光大证券股份有限公司',
            rate1: 50.00,
            detail1: '光大证券股份有限公司:10亿',
            rate2: 0.00,
            rate3: 0.00,
            detail2: '光大证券股份有限公司'
          },
          {
            code: '2023年度第二期',
            name: 'MTN',
            user: '3Y',
            agent: '光大证券股份有限公司',
            rate1: 50.00,
            detail1: '光大证券股份有限公司:10亿',
            rate2: 0.00,
            rate3: 0.00,
            detail2: '光大证券股份有限公司'
          },
          {
            code: '2023年度第二期',
            name: 'MTN',
            user: '3Y',
            agent: '光大证券股份有限公司',
            rate1: 50.00,
            detail1: '光大证券股份有限公司:10亿',
            rate2: 0.00,
            rate3: 0.00,
            detail2: '光大证券股份有限公司'
          },
          {
            code: '2023年度第二期',
            name: 'MTN',
            user: '3Y',
            agent: '(2+N)Y',
            rate1: '13.00',
            detail1: '2.37-2.79',
            rate2: 0.00,
            rate3: 0.00,
            detail2: '光大证券股份有限公司'
          }
        ],
        pagination: {
          pageNo: 1,
          pageSize: window.screen.height > 1000 ? 25 : 15,
          pageSizeOptions: [15, 25, 50, 100],
          total: 0,
          showSizeChanger: true
        }
      }
    }
  },
  methods: {
    handleOpen(row, event) {
      console.log(event)
      this.$refs.modulesIndex.initData(row)
    },
    handleDelete() {},
    handleEdit(row, event) {
      this.$refs.modulesIndex.initData(row)
    },
    handleExport() {},
    // 查询
    handleQuery() {
      const self = this
      const { configForm, configTable } = self

      self.$refs.elForm.validate(async valid => {
        if (valid) {
          configTable.loading = true

          const params = {
            data: { ...configForm },
            page: { ...configTable.pagination }
          }

          const { list = [], total = 0 } = { ...await API.GetListData(params) }

          if (list.length) {
            configTable.loading = false
            configTable.data = list
            configTable.pagination.total = total
          }
        }
      })
    },
    // 重置
    handleReset() {

    }
  }
}
</script>

  <style lang="scss">

  </style>
