<!--
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023-04-20 19:49:22
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2024-06-05 17:41:02
 * @Description: 组合头寸
-->
<template>
  <div class="home-poc-item ptl-postion-cash">
    <div class="home-poc-item--header">组合头寸<fullscreen v-on="{ ...$listeners }" /></div>
    <div style="display: flex;justify-content: space-between;line-height: 28px;padding: 0 10px;">
      <div class="detail-text">
        <label style="width: auto;">组合</label><span>{{ params ? params.portfolioName : '---' }}</span>
      </div>
      <div class="detail-text">
        <label>更新日期</label><span>{{ params ? params.cdate : '---' }}</span>
      </div>
      <div class="detail-text">
        <label>单位</label><span>元</span>
      </div>
    </div>
    <jr-decorated-table
      custom-id="94b4b2a06ab748b781c02211631e9d94"
      v-bind="{ ...$attrs, params, defaultExpandAll: true, noPagination: true, menuinfo: { pageId: 'PositionCash' } }"
    />
  </div>
</template>

<script>
import fullscreen from '../common/fullscreen'

export default {
  components: {
    fullscreen
  },
  props: {
    params: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      form: {
      },
      platDate: JSON.parse(sessionStorage.getItem('platDate'))
    }
  }
}
</script>

<style lang="scss">
.ptl-postion-cash .jr-decorated-table {
  height: calc(100% - 68px);
}
.ptl-postion-cash .jr-decorated-table--header {
  display: none;
}
.ptl-postion-cash .el-table__body tbody > tr:nth-child(3) td {
  background: #f7f7f7 !important;
  font-weight: bold;
  &:nth-child(2),
  &:first-child {
    border-right-color: #e8e8e8 !important;
  }
}
</style>

<style lang="scss" scoped>
@import "../common/poc.scss";
</style>
