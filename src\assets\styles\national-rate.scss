
.national-rate{
    $hot: #FFF0F0;
    $fall: #ECFCE8;
    $buff: #FFF4EA;
    $up: #F53F3F;
    $down: #1EB42A;
    $ye: #FF8E2B;
    .flex{
        display: flex;
        align-items: center;
    }
    .classify{
        margin-right: 5px;
        color: #333333;
    }
    h3{
      color: #959EB2;
      font-size: 12px;
    }
    .el-table {
      border: 1px solid #ffffff !important; /* 外部边框 */
    }

    .el-table td, .el-table th {
      border-bottom: 4px solid #ffffff !important; /* 单元格下边框 */
    }

    .el-table--border {
      border: 0px solid #ffffff !important; /* 带边框表格的外边框 */
    }

    .el-table--border td,
    .el-table--border th {
      border-right: 4px solid #ffffff !important; /* 内部垂直边框 */
    }
    .jr-table .el-table table td.el-table__cell>.cell, .jr-table .el-table table td>.cell,.jr-table .el-table table th.el-table__cell>.cell, .jr-table .el-table table th>.cell{
      padding: 0; background:#F5F5FB;
      height: 64px !important;line-height: 64px !important;
    }
    .jr-table .el-table table th.el-table__cell>.cell, .jr-table .el-table table th>.cell {
      font-size: 14px;
    }
    .jr-table .el-table table th, .jr-table .el-table table th.el-table__cell{
      background: #ffffff !important;
    }
    .el-table--border::after {
      width: 0 !important;  /* 右外边框 */
    }
    .el-table::before {
      height: 0 !important; /* 下边框 */
    }
    .el-table__header-wrapper .el-table__header th .cell{
      padding-left: 16px !important;
      font-weight: bold;
      font-size: 16px;
    }
    .curveName{
      padding-left: 16px;font-weight: bold;
    }
    .props-col{
      padding-left: 16px;font-weight: bold;line-height: 1;
      height: 100%;
      display: flex;
      align-content: center;
      justify-content: center;
      flex-direction: column;
      p{
        margin-bottom: 10px;
      }
    }
    .size-color{
      font-size: 14px;
      color: #333333;
    }
    .hot{
      background: $hot;
      color: $up;
    }
    .fall{
      background: $fall;
      color: $down;
    }
    .no{
      background-color: $buff;
      color: $ye;
    }
  }