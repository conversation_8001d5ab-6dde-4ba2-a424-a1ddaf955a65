<template>
  <div class="valuationCenter">
    <cockpitHeader title="估值中心" :style="{ width: px2vw(362), height: px2vh(40) }" type="light" />
    <cockpitTabs :tabs="[{ tabName: '我司新券估值' }]" :hide-more="false" :style="{ marginTop: px2vh(15) }" />
    <div class="valuationCenter-cardArea">
      <el-carousel ref="carousel" trigger="click" style="width: 100%; height: 100%" indicator-position="none" arrow="never">
        <el-carousel-item v-for="(data,index) in cardData" :key="index">
          <div class="valuationCenter-cardArea-cardContent">
            <div v-for="(card, cIndex) in data" :key="cIndex" class="valuationCenter-cardArea-cardContent-card" @click.stop="redirectToMenu('1352319956321910784')">
              <p>{{ card.sInfoName }}</p>
              <p>{{ card.latestValuation }}</p>
              <p>
                <span>最新估值(%)</span>
                <span :class="card.riseFallBp < 0 ? 'valuationCenter-cardArea-cardContent-card-down' : ''">{{ card.riseFallBp > 0 ? '+' : '' }} {{ card.riseFallBp }}</span>
              </p>
            </div>
          </div>
        </el-carousel-item>
      </el-carousel>
    </div>
    <div class="valuationCenter-swiper">
      <img src="@/assets/cockpit/swiperLeft.png" alt="" @click.stop="carouselChange('prev')" />
      <img src="@/assets/cockpit/swiperRight.png" alt="" @click.stop="carouselChange('next')" />
    </div>
    <cockpitTabs :tabs="[{ tabName: '发行利差' }]" :style="{ marginTop: px2vh(24) }" @handleMore="redirectToMenu('1352315117735067648')" >
      <el-tooltip effect="dark" content="Top Left 提示文字" placement="top-start" :teleported="false">
        <div class="toolTip">
          <img src="@/assets/cockpit/info_circle.png" alt="" :style="{ width: px2vw(17),height: px2vw(17) }">
          <span>利差明细</span>
        </div>
        <div slot="content">
          <p>上市首日估值偏离度：债券票面利率与债券首次中债估值之间的利差</p>
          <p>同期限国开债利差：在债券发行截止日，债券票面利率与同债券发行期限的中债国开债到期收益率之间的利差</p>
          <p>同期限国债利差：在债券发行截止日，债券票面利率与同债券发行期限的中债国债到期收益率之间的利差</p>
          <p>同期限同评级基准利差：在债券发行截止日，债券票面利率与同债券发行期限的中债中短期票据/城投债到期收益率之间的利差</p>
        </div>
      </el-tooltip>
    </cockpitTabs>
    <div class="valuationCenter-echarts">
      <echarts :options="options" style="width: 100%; height: 100%" />
    </div>
  </div>
</template>

<script>
import cockpitHeader from '../../components/cockpit-header.vue'
import cockpitTabs from '../../components/cockpit-tabs.vue'
import echarts from '@jupiterweb/components/echarts'
import { GetListData } from '@jupiterweb/api/common/active-module-svr'
import * as op from '@/systems/market-quotation/components/chartParams'
import { cockpitGetIssueDifferenceList } from '@/api/cockpit/cockpit.js'
import { px2vw, px2vh } from '../../utils/portcss'
import moment from 'moment'
export default {
  name: 'ValuationCenter',
  components: {
    cockpitHeader,
    cockpitTabs,
    echarts
  },
  data() {
    return {
      cardParams: {
        params: {
          ownedModuleid: '708631605142536192',
          ccid: '6d61d587625d429faabb52c851a1cbec'
        },
        page: {
          pageNo: 1,
          pageSize: 50
        }
      },
      options: op.options,
      cardData: []
    }
  },
  created() {
    this.getCardDataApi()
    this.getChartDataApi()
  },
  methods: {
    px2vw,
    px2vh,
    /**
     * 获取卡片数据
     */
    async getCardDataApi() {
      const data = await GetListData({
        ...this.cardParams
      })
      const list = data?.pageInfo?.list || []
      let tempArr = []
      this.cardData = list.reduce((pre, current, index) => {
        tempArr.push(current)
        if (tempArr.length >= 12) {
          pre.push(tempArr)
          tempArr = []
        }
        if (index === list.length - 1 && tempArr.length > 0) {
          pre.push(tempArr)
          tempArr = []
        }
        return pre
      }, [])
    },
    /**
     * 走马灯切换
     */
    carouselChange(funcName){
      this.$refs.carousel[funcName]()
    },
    /**
     * 获取图表数据
     */
    async getChartDataApi() {
      const data = await cockpitGetIssueDifferenceList()
      const sysVersion = localStorage.getItem('sysVersion')
      const rateName = sysVersion === 'group' ? '集团债券票面利率' : '我司债券票面利率'
      const xAxisData = []
      for (let key in data) {
        for (let inKey in data[key]) {
          if (!xAxisData.includes(inKey)) {
            xAxisData.push(inKey)
          }
        }
      }
      console.log(xAxisData,'data')
      const circleSeries = []
      for (let key in data[rateName]) {
        data[rateName][key].forEach((item) => {
          circleSeries.push({
            name: rateName,
            type: 'scatter',
            xAxisIndex: 0,
            yAxisIndex: 1,
            data: [{
              value: [moment(key).format("YYYY/MM/DD"), item.couponrate],
              bondName: item.bondName || '',
              bondRating: item.bondRating || '',
              term: item.term || ''
            }],
            itemStyle: {
              color: '#0DA3EC'
            }
          })
        })
      }
      this.options = {
        title: {
          show: false
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross'
          },
          formatter: function(params) {
            // 如果参数是数组（axis trigger）
            console.log(params,'params')
            if (Array.isArray(params)) {
              let result = params[0].axisValue + '<br/>'
              params.forEach(function(item) {
                if (item.seriesType === 'line') {
                  result += (item.marker || '') + ' ' + (item.seriesName || '') + ': ' + (item.value || '0') + 'BP<br/>'
                } else if (item.seriesType === 'scatter') {
                  const data = item.data
                  result += item.marker + ' ' + item.seriesName + ': ' + item.value[1] + '%<br/>'
                  
                  // 构建附加信息，用括号包围
                  const additionalInfo = []
                  if (data.bondName) additionalInfo.push(data.bondName)
                  if (data.bondRating) additionalInfo.push(data.bondRating)
                  if (data.term) additionalInfo.push(data.term)
                  
                  if (additionalInfo.length > 0) {
                    result += `(${additionalInfo.join(', ')})<br/>`
                  }
                }
              })
              return result
            } else {
              // 如果是单个参数（item trigger，用于散点图单独hover）
              if (params.seriesType === 'scatter') {
                const data = params.data
                const bondName = data.bondName || ''
                const bondRating = data.bondRating || ''
                const term = data.term || ''
                
                let result = `${params.marker}${params.seriesName}<br/>`
                result += `日期: ${params.value[0]}<br/>`
                result += `票面利率: ${params.value[1]}%<br/>`
                
                // 构建附加信息，用括号包围
                const additionalInfo = []
                if (bondName) additionalInfo.push(bondName)
                if (bondRating) additionalInfo.push(bondRating)
                if (term) additionalInfo.push(term)
                
                if (additionalInfo.length > 0) {
                  result += `(${additionalInfo.join(', ')})`
                }
                
                return result
              }
              return params.name + ': ' + params.value
            }
          }
        },
        legend: {
          data: [...circleSeries.map((item) => item.name),...Object.keys(data)],
          orient: 'horizontal',
          top: 33,
          left: 'center',
          type: 'scroll', // 设置为可滚动
          pageIconColor: '#786C6C', // 分页箭头颜色
          pageIconInactiveColor: '#786C6C', // 禁用时分页箭头颜色
          pageTextStyle: {
            color: 'rgba(0,0,0,0.6);' // 页码文字颜色
          },
          textStyle: {
            color: 'rgba(0,0,0,0.9)' // 设置文字颜色为深灰色
          },
          pageButtonItemGap: 5, // 分页按钮与图例项的间隔
          pageButtonGap: 10, // 分页按钮与图例组件外框的间隔
          pageButtonPosition: 'end', // 分页按钮位置
          pageFormatter: '{current}/{total}', // 页码显示格式
          padding: [0, 90, 0, 45], // 图例内边距
          right: 50, //距离右侧
          left: 50
        },
        grid: {
          left: '5%',
          top: '20%',
          right: '4%',
          bottom: '10%',
          containLabel: true
        },
        toolbox: {
          show: false
        },
        xAxis: [
          {
            type: 'category',
            boundaryGap: false,
            data: xAxisData.map(item=>moment(item).format("YYYY/MM/DD")).sort((a, b) => new Date(a) - new Date(b)),
            axisLabel: {
              show: true,
              textStyle: {
                color: 'rgba(0, 0, 0, 0.60)'
              }
            },
            axisLine: {
              lineStyle: {
                color: 'rgba(0,174,255,0.6)',
                width: 1 //这里是为了突出显示加上的
              }
            }
          },
        ],
        yAxis: [
          {
            type: 'value',
            name: '单位：BP',
            alignTicks: true,
            splitLine: {
              //保留网格线
              show: true,
              lineStyle: {
                //y轴网格线设置
                color: 'rgba(125, 193, 224, 0.20)',
                width: 1,
                type: 'solid'
              }
            }
          },
          {
            type: 'value',
            name: '单位：%',
            alignTicks: true,
            splitLine: {
              //保留网格线
              show: true,
              lineStyle: {
                //y轴网格线设置
                color: 'rgba(125, 193, 224, 0.20)',
                width: 1,
                type: 'solid'
              }
            }
          }
        ],
        series: [
          {
            name: '上市首日估值偏离度',
            type: 'line',
            smooth: true,
            xAxisIndex: 0,
            yAxisIndex: 0,
            data: xAxisData.map((date) => {
              return {
                name: moment(date).format("YYYY/MM/DD"),
                value: data['上市首日估值偏离度'][date]
              }
            }),
            symbol: 'circle', //设定为实心点
            symbolSize: 7, //设定实心点的大小
            itemStyle: {
              normal: {
                color: '#35C1C0', //折现点颜色
                lineStyle: {
                  color: '#35C1C0' //折线颜色
                }
              }
            }
          },
          {
            name: '同期限同评级中短期票据到期收益率利差',
            type: 'line',
            smooth: true,
            xAxisIndex: 0,
            yAxisIndex: 0,
            data: xAxisData.map((date) => {
              return {
                name: moment(date).format("YYYY/MM/DD"),
                value: data['同期限同评级中短期票据到期收益率利差'][date]
              }
            }),
            symbol: 'circle',
            symbolSize: 7,
            itemStyle: {
              normal: {
                color: '#9C75DD',
                lineStyle: {
                  color: '#9C75DD'
                }
              }
            }
          },
          {
            name: '同期限同评级城投债到期收益率利差',
            type: 'line',
            smooth: true,
            xAxisIndex: 0,
            yAxisIndex: 0,
            data: xAxisData.map((date) => {
              return {
                name: moment(date).format("YYYY/MM/DD"),
                value: data['同期限同评级城投债到期收益率利差'][date]
              }
            }),
            symbol: 'circle',
            symbolSize: 7,
            itemStyle: {
              normal: {
                color: '#269A99',
                lineStyle: {
                  color: '#269A99'
                }
              }
            }
          },
          {
            name: '同期限国债利差',
            type: 'line',
            smooth: true,
            xAxisIndex: 0,
            yAxisIndex: 0,
            data: xAxisData.map((date) => {
              return {
                name: moment(date).format("YYYY/MM/DD"),
                value: data['同期限国债利差'][date]
              }
            }),
            symbol: 'circle',
            symbolSize: 7,
            itemStyle: {
              normal: {
                color: '#E8684A',
                lineStyle: {
                  color: '#E8684A'
                }
              }
            }
          },
          {
            name: '同期限国开债利差',
            type: 'line',
            smooth: true,
            xAxisIndex: 0,
            yAxisIndex: 0,
            data: xAxisData.map((date) => {
              return {
                name: moment(date).format("YYYY/MM/DD"),
                value: data['同期限国开债利差'][date]
              }
            }),
            symbol: 'circle',
            symbolSize: 7,
            itemStyle: {
              normal: {
                color: '#E1B01E',
                lineStyle: {
                  color: '#E1B01E'
                }
              }
            }
          },
          ...circleSeries
        ]
      }
    },
    /**
     * 路径跳转
     */
    redirectToMenu(menuId) {
      this.$store.dispatch('tagsView/updateCurrentTab', {
        path: '/' + menuId,
        // 跳转需要刷新的话 refresh传true
        meta: {
          refresh: true,
          params: {}
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~assets/styles/portcss.scss';
.valuationCenter {
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.5);
  box-shadow: 0px 4px 20px -2px rgba(0, 0, 0, 0.06);
  border: vh(2) solid transparent;
  border-radius: vh(12);
  background-clip: padding-box, border-box;
  background-origin: padding-box, border-box;
  background-image: linear-gradient(to right, rgba(255, 255, 255, 1), rgba(255, 255, 255, 0.5)),
    radial-gradient(rgba(255, 255, 255, 1), rgba(255, 255, 255, 0.5));
  padding: 0 vw(24);
  &-cardArea {
    width: 100%;
    height: vh(344);
    margin-top: vh(16);
    &-cardContent {
      display: flex;
      align-items: flex-start;
      flex-wrap: wrap;
      column-gap: vw(16);
      row-gap: vh(16);
      width: 100%;
      height: 100%;
      align-content: flex-start;
      &-card {
        width: vw(190);
        height: vh(104);
        background: linear-gradient(180deg, #ffe8d1 0%, #faf8f6 100%);
        box-shadow: 0px 4px 20px -2px rgba(0, 0, 0, 0.06);
        border: vh(2) solid transparent;
        border-radius: vh(8);
        background-clip: padding-box, border-box;
        background-origin: padding-box, border-box;
        background-image: linear-gradient(180deg, #ffe8d1 0%, #faf8f6 100%),
          radial-gradient(rgba(255, 255, 255, 1), rgba(255, 255, 255, 0.5));
        padding: vh(12) vw(12);
        cursor: pointer;
        & > p {
          margin-bottom: 0px;
        }
        & > p:nth-of-type(1) {
          width: 100%;
          text-overflow: ellipsis;
          overflow: hidden;
          height: vh(19);
          font-size: vh(14);
          color: rgba(0, 0, 0, 0.6);
          line-height: vh(19);
          letter-spacing: 1px;
        }
        & > p:nth-of-type(2) {
          height: vh(22);
          font-weight: bold;
          font-size: vh(16);
          color: rgba(0, 0, 0, 0.9);
          line-height: vh(22);
          letter-spacing: 1px;
          margin-top: vh(17);
        }
        & > p:nth-of-type(3) {
          display: flex;
          justify-content: space-between;
          margin-top: vh(3);
          & > span:nth-of-type(1) {
            height: vh(19);
            font-size: vh(14);
            color: rgba(0, 0, 0, 0.6);
            line-height: vh(19);
            letter-spacing: vw(1);
          }
          & > span:nth-of-type(2) {
            width: vw(39);
            height: vh(18);
            background: rgba(245, 63, 63, 0.2);
            border-radius: 2px;
            font-size: vh(12);
            color: #f53f3f;
            line-height: vh(18);
            text-align: center;
          }
        }
        &-down {
          background: rgba(43, 162, 112, 0.2) !important;
          color: #22835A !important;
        }
      }
    }
  }
  &-swiper {
    width: 100%;
    margin-top: vh(23);
    display: flex;
    justify-content: center;
    gap: vw(64);
    img {
      width: vw(32);
      height: vh(29);
      cursor: pointer;
    }
  }
  &-echarts {
    width: 100%;
    height: vh(360);
  }
}
.toolTip {
  display: flex;
  align-items: center;
  position: relative;
  left: vw(-14);
  padding-bottom: vh(6);
  span{
    height: vh(19);
    font-family: MicrosoftYaHei;
    font-size: vh(14);
    color: rgba(0,0,0,0.6);
    line-height: vh(19);
    margin-left: vw(4);
  }
}
</style>
