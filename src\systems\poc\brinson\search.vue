<!--
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2024-01-13 16:55:33
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2024-01-19 11:38:35
 * @Description: <PERSON><PERSON>son归因分析 - 头部公共查询
-->
<template>
  <div class="brinson-search header-search-panel">
    <el-form ref="form" :model="form">
      <jr-form-item-create :data="cols" :model="form" :column="3" style="line-height: 0;" />
    </el-form>

    <el-button type="primary" class="search-panel" @click="query">
      查询
    </el-button>
  </div>
</template>

<script>
export default {
  data() {
    return {
      form: {
        basicline: '01',
        dim: '01',
        period: '01',
        dateRange: ['2023-08-01', '2023-08-30'],
        portfolioId: 'HYZZL01'
      },
      cols: [{
        title: '组合名称',
        prop: 'portfolioId',
        type: 'remoteSelect',
        required: true,
        optionValue: 'id',
        api: '/invest/portfolio/ptlincomeanalysis/PtlIncomeAnalysis001/getPortfolioId'
      }, {
        title: '市场基准',
        prop: 'basicline',
        type: 'select',
        required: true,
        options: [{ text: '沪深300', value: '01' }]
      }, {
        title: '时间区间',
        prop: 'dateRange',
        type: 'rangeDate',
        required: true
      }, {
        title: '归因频率',
        prop: 'period',
        type: 'select',
        required: true,
        options: [{ text: '日频', value: '01' }, { text: '周频', value: '02', disabled: true }, { text: '月频', value: '02', disabled: true }]
      }, {
        title: '归因维度',
        prop: 'dim',
        type: 'select',
        required: true,
        options: [{ text: '申万一级', value: '01' }]
      }]
    }
  },
  created() {
  },
  methods: {
    query() {
      this.$refs.form.validate(valid => {
        if (valid) {
          const { dateRange, ...other } = this.form
          const params = {
            ...other,
            startDate: dateRange[0],
            endDate: dateRange[1]
          }
          const { layouts = [], chartSeq } = this.$attrs
          const moduleList = layouts.filter(f => f.chartSeq !== chartSeq)
          this.$emit('setTargetParams', moduleList.reduce((pre, cur) => {
            pre[cur.chartSeq] = params
            return pre
          }, {}))
        }
      })
    }
  }
}
</script>

<style lang="scss">
.brinson-search {
  background: #fff;
  display: flex !important;
  padding: 0 10px;

  .el-form {
    width: 100%;
    flex: 1;
  }

  .search-panel {
    width: 56px;
    margin-top: 4px;
    margin-left: 15px;
  }
}
</style>
