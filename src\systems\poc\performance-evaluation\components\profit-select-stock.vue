<!--
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2024-01-12 15:36:09
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2024-01-13 20:12:50
 * @Description: 选股择时能力
-->
<template>
  <div>
    <h2>选股择时能力</h2>
    <div class="float-form">
      <jr-form-item-create :model="searchForm" :data="searchConfig" :column="1" />
      <el-button type="primary" @click="handleQuery">查询</el-button>
    </div>
    <TemplateModule style="height: 300px;" :detail="{packageId: '86b7fa49be9448bfb481907cd881ce5e'}" :params="{ ...params, ...queryParams }" chart-type="TAB" v-bind="$attrs" />
  </div>
</template>

<script>
import TemplateModule from '@jupiterweb/components/template-module'
export default {
  components: {
    TemplateModule
  },
  props: {
    params: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      queryParams: {
        curveCode: 'POCHS300'
      },
      searchForm: {
        curveCode: 'POCHS300'
      },
      searchConfig: [
        {
          prop: 'curveCode',
          title: '对比指数',
          showCode: false,
          type: 'select',
          options: ['沪深300'].map(a => ({ text: a, value: 'POCHS300' }))
        }
      ]
    }
  },
  methods: {
    handleQuery() {
      Object.assign(this.queryParams, {
        ...this.searchForm
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.float-form {
  display: flex;
  justify-content: space-between;
  position: absolute;
  left: 70px;
  top: 44px;
  width: 200px;
  height: 28px;
  z-index: 999;
  ::v-deep .el-form-item__label {
    white-space: nowrap !important;
  }
  .el-button {
    margin-left: 10px;
  }
}
</style>

