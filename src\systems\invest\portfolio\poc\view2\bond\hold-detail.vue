<!-- 债券持仓分析 -> 债券持仓明细 -->
<template>
  <div class="home-poc-item has-fullscreen plt-base-info">
    <div class="home-poc-item--header float-left">债券持仓明细 <fullscreen v-on="{ ...$listeners }" /></div>
    <jr-decorated-table
      custom-id="43434da5d0654ed082c646f26b71be6c"
      v-bind="{
        ...$attrs,
        params: { portfolioId: portFolioId, ...params },
        noPagination: true,
        menuinfo: {
          pageId: 'BondPosAnalysis001',
          btnList: [{
            btnPosition: 'HEAD',
            btnkey: 'export',
            btnnm: '导出',
            componenturl: 'export',
            effectflag: 'E',
            moduleid: 'BondPosAnalysis001_002_001',
            moduletype: 'btn',
            orde: 4,
            parameter: JSON.stringify({ noSelect: true }),
            permitflag: 'C',
            permittag: '02',
            showflag: 'Y',
            tmFlag: 'E'
          }]
        },
        handlequery
      }"
      @refreshed="queryListEnd"
    />
  </div>
</template>

<script>
import fullscreen from '../../common/fullscreen'
import { mapGetters } from 'vuex'

export default {
  components: {
    fullscreen
  },
  props: {
    params: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      isFirst: true
    }
  },
  computed: {
    ...mapGetters(['portFolioId'])
  },
  methods: {
    handlequery({ searchDate, portfolioId }) {
      const p = { mDate: searchDate, portFolioId: portfolioId || '' }

      this.$emit('setTargetParams', {
        '74966861555845b8bdbafecfbe91a420': p, // 平均到期收益率
        'bcd7a8d653254ac3900117c3fd90336a': p, // 久期&平均剩余期限
        '6758bc8898854253a1127e5adde70ec0': p, // 持仓分布（左/右）
        'ce17c93d062e4c8bb2b9673bee9ab913': p, // 期末前10大集中度（左/右）
        '22f0809ce7a84d3dae7d32e343c8b4f2': p // 持仓趋势明细
      })

      this.$store.commit('CHSNGE_PORTFOLIOID', portfolioId)

      return true
    },
    queryListEnd(ins) {
      if (this.isFirst) {
        this.isFirst = false
        const { searchDate, portfolioId = '' } = ins.getCommonParams('searchForm')
        this.handlequery({ searchDate, portfolioId })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import "../../common/poc.scss";
</style>
