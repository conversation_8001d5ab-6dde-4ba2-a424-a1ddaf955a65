<template>
  <el-tabs v-model="active" class="demo-page" @tab-click="handleClick">
    <el-tab-pane label="标准利率曲线利差" name="first">
      <flat-index />
    </el-tab-pane>

    <el-tab-pane label="自定义利率曲线利差" name="second" lazy>
      <flat-index-horizontal />
    </el-tab-pane>

  </el-tabs>
</template>

<script>
import FlatIndex from './flat-index.vue'
import FlatIndexHorizontal from './flat-index-horizontal.vue'

export default {
  components: { FlatIndex, FlatIndexHorizontal },
  data() {
    return {
      active: 'first'
    }
  },
  methods: {
    handleClick(tab, event) {
      // console.log(tab, event)
    }
  }
}
</script>

<style lang="scss">
  .demo-page {
    height: 100%;
    margin: 10px;

    .el-tabs__content {
      height: 100%;

      .el-tab-pane {
        height: 100%;
      }
    }
  }
</style>
