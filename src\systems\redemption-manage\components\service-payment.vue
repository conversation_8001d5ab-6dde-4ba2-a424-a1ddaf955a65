<template>
  <div style="height: calc(100% - 76px)" class="service-payment-form" @click="handleClickOutside">
    <div class="main">
      <div class="top-content public-table-search-container">
        <el-form inline :model="form" class="service-payment-form-content" label-width="80">
          <jr-form-item label="债券简称">
            <el-autocomplete
              v-model="form.s_info_name"
              :fetch-suggestions="querySearchAsync"
              placeholder="请输入债券简称"
              value-key="text"
              clearable
              style="height: 32px; margin-right: 24px"
            />
          </jr-form-item>
          <jr-form-item label="支付状态">
            <jr-combobox
              v-model="form.payState"
              style="margin-right: 24px; height: 32px"
              placeholder="请选择"
              clearable
              filterable
              :data="stateList"
              option-value="itemcode"
              option-label="cnname"
            />
          </jr-form-item>
          <jr-form-item label="支付日期">
            <el-date-picker
              v-model="form.customDateRange"
              style="height: 32px; margin-right: 24px"
              type="daterange"
              range-separator="至"
              unlink-panels
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="yyyy-MM-dd"
              class="date-picker"
              @change="changeDate"
            />
          </jr-form-item>
          <jr-form-item style="height: 32px">
            <jr-radio-group v-model="form.include_expired" :data="checkboxList" cancelable @change="changeCustomDate" />
          </jr-form-item>

          <div class="submit-btn">
            <el-button type="primary" @click="submit">查询</el-button>
          </div>
        </el-form>
      </div>
      <div class="bottom-content">
        <customTableActions
          :menuinfo="menuinfo"
          :permitdetail="permitdetail"
          @add="add"
          @calculate="calculate"
          @exportData="exportData"
        />
        <AddServicePayment v-if="showAdd" :permitdetail="permitdetail" :close-modal="closeModal" :visible="showAdd" />
        <countServicePayment v-if="showCount" :permitdetail="permitdetail" :close-modal="closeModal" :visible="showCount" />
        <jr-decorated-table
          ref="jrTable"
          :params="tableParams"
          style="height: calc(100% - 100px)"
          :custom-id="params.ccid"
          :custom-render="tableColumns"
          :menuinfo="menuinfo"
          v-bind="{ ...$attrs, ...$props }"
          @refreshed="callFn"
        />

        <PayPLan v-if="false" />

        <div class="service-payment-tips" style="padding-left: 16px">
          <span><jr-svg-icon icon-class="info-circle" /></span>
          <span>服务费用信息仅供参考，支付金额以收款机构提供的支付信息为准。</span>
        </div>

        <div class="service-payment-tips" style="padding-left: 16px">
          <span><jr-svg-icon icon-class="info-circle" /></span>
          <span>列表费用数值将根据查询时间区间变化。</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import moment from 'moment'
import { GetComboboxList } from '@/api/home'
const PAYSTATE = 'payState' // 支付状态(查询)字典项
const PAYTYPE = 'payType' // 支付类型
const PAYSTATUS = 'payStatus' // 支付状态
import { serviceExport } from '@/api/redemption-manage/redemption-manage'
import PayPLan from './pay-plan.vue'
import { EventBus } from '../event-bus'
import customTableActions from './custom-table-actions'
import AddServicePayment from './update-service-payment-modal.vue'
import countServicePayment from './count-modal.vue'
import { queryBondShortName } from '@/api/bonds/bonds'

export default {
  name: 'ServicePayment',
  components: { PayPLan, customTableActions, AddServicePayment, countServicePayment },
  props: {
    menuinfo: {
      type: Object,
      default: () => ({})
    },
    permitdetail: {
      type: Object,
      default: () => ({})
    },
    params: {
      type: Object,
      default: () => ({})
    },
    currentTab: {
      type: String,
      default() {
        return ''
      }
    }
  },
  data() {
    return {
      activePopoverCode: '',
      isExpanded: {}, // 记录每行的展开状态
      tableParams: {
        ownedModuleid: '1364638311628533760'
      },
      currentRow: {},
      isShowUpdateModal: false,
      form: {
        s_info_name: '',
        payState: '',
        customDateRange: [],
        include_expired: ''
      },
      checkboxList: [
        {
          label: '近一周',
          value: '1'
        },
        {
          label: '近一月',
          value: '2'
        }
      ],
      payTypeList: [],
      stateList: [],
      payStatusList: [],
      tableColumns: {},
      columns: [],
      showAdd: false,
      showCount: false
    }
  },
  computed: {
    tabPath() {
      return this.currentTab
    }
  },
  watch: {
    tabPath(val) {
      if (val !== '/ports/1364638311628533760') {
        this.handleClickOutside()
      }
    }
  },
  async created() {
    console.log(this.menuinfo.btnList)
    console.log('permitdetail', this.permitdetail)

    await this.getDictOptionsApi()

    this.tableColumns = {
      infoname: (h, { rowIndex, row }) => {
        return (
          <el-popover
            placement='right-start'
            width='1324'
            trigger='manual'
            class='custom-popover-item'
            onShow={() => this.handlePopoverShow(rowIndex, row)}
            onHide={() => {
              this.submit()
            }}
          >
            {this.activePopoverCode === row.sInfoWindcode && (
              <PayPLan
                payTypeList={this.payTypeList}
                row={row}
                permitdetail={this.permitdetail}
                payStateList={this.payStatusList}
                feeTypeList={[]}
                tableType='service-payment'
                key={row.sInfoWindcode}
              />
            )}

            <span
              style='color:var(--theme--color);cursor:pointer'
              slot='reference'
              onClick={() => {
                this.handleButtonClick(row, rowIndex)
              }}
            >
              {row.infoname}
            </span>
          </el-popover>
        )
      }
    }

    EventBus.$on('refresh-service-payment-list', () => {
      this.activePopoverCode = ''
      this.submit()
    })
  },
  methods: {
    // 点击外部关闭el-popover
    handleClickOutside() {
      this.$nextTick(() => {
        const elList = document.querySelectorAll('.custom-popover-item')
        for (let i = 0; i < elList.length; i++) {
          const popoverInstance = elList[i].__vue__
          if (popoverInstance?.doClose) popoverInstance.doClose()
        }
        this.activePopoverCode = null
      })
    },
    closeModal() {
      this.showAdd = false
      this.showCount = false
    },
    exportData() {
      this.handleCExport()
    },
    add() {
      this.showAdd = true
    },
    calculate() {
      this.showCount = true
    },
    callFn(data) {
      this.columns = data.config.columns
      this.sort = data.sort
      this.direction = data.direction
    },
    // 自定义导出
    async handleCExport() {
      const obj = this.handleSearchParams()
      const params = {
        params: {
          ...obj,
          column: this.columns,
          selectData: [],
          ccid: this.params.ccid,
          filename: '承销费'
        },
        page: {
          sort: this.sort,
          direction: this.direction
        },
        opType: '1',
        feeClassify: '',
        payDateBegin: obj['payDateBegin'],
        payDateEnd: obj['payDateEnd']
      }
      await serviceExport(params)
    },
    // 刷新弹出层
    handlePopoverShow(index, row) {
      this.$nextTick(() => {
        const elList = document.querySelectorAll('.custom-popover-item')

        for (let i = 0; i < elList.length; i++) {
          const popoverInstance = elList[i].__vue__

          if (i !== index) {
            if (popoverInstance?.doClose) popoverInstance.doClose()
          }
        }

        this.activePopoverCode = row.sInfoWindcode
      })
    },
    handleButtonClick(row, index) {
      // this.activePopoverCode = row.sInfoWindcode
      this.$nextTick(() => {
        const elList = document.querySelectorAll('.custom-popover-item')

        for (let i = 0; i < elList.length; i++) {
          const popoverInstance = elList[i].__vue__

          if (i === index) {
            if (popoverInstance && popoverInstance.showPopper) {
              popoverInstance.doClose()
            } else if (popoverInstance && !popoverInstance.showPopper) {
              popoverInstance.doShow()
            }
            break
          }
        }

        this.activePopoverCode = row.sInfoWindcode
      })
    },
    // 处理查询参数
    handleSearchParams() {
      const obj = {
        s_info_name: this.form.s_info_name,
        payState: this.form.payState,
        include_expired: this.form.include_expired ? this.form.include_expired : '0'
      }
      if (Array.isArray(this.form.customDateRange) && this.form.customDateRange.length > 0) {
        obj.payDateBegin = moment(this.form.customDateRange[0]).format('YYYY-MM-DD')
        obj.payDateEnd = moment(this.form.customDateRange[1]).format('YYYY-MM-DD')
      } else {
        obj.payDateBegin = null
        obj.payDateEnd = null
      }

      return {
        ...this.tableParams,
        ...obj,
        webTime: new Date().getTime()
      }
    },
    /**
     * 查詢提交
     *  **/
    submit() {
      console.log('查询')

      this.tableParams = this.handleSearchParams()
    },
    /**
     * 自定義時間清空
     *  **/
    changeDate(e) {
      this.form.include_expired = null
    },
    /**
     * 设置时间区域为一周或者一月
     *  **/
    changeCustomDate(e) {
      // 获取当前日期作为结束时间
      const endDate = new Date()
      if (e === '1') {
        // 计算一周前的日期
        const oneWeekAgo = new Date()
        oneWeekAgo.setDate(oneWeekAgo.getDate() - 7)
        this.form.customDateRange = [oneWeekAgo, endDate]
      } else if (e === '2') {
        // 计算一月前的日期
        const oneMonthAgo = new Date()
        oneMonthAgo.setMonth(oneMonthAgo.getMonth() - 1)
        this.form.customDateRange = [oneMonthAgo, endDate]
      }
    },
    /**
     * 获取字典字段
     */
    async getDictOptionsApi() {
      const res = await GetComboboxList([PAYSTATE, PAYTYPE, PAYSTATUS])
      this.stateList = res[PAYSTATE]
      this.payTypeList = res[PAYTYPE]
      this.payStatusList = res[PAYSTATUS]
    },
    /**
     * 债券简称远程搜索
     */
    querySearchAsync(queryString, cb) {
      if (queryString) {
        queryBondShortName({
          text: queryString
        }).then((data) => {
          if (data && Object.keys(data).length) {
            cb(data)
          } else {
            cb([])
          }
        })
      } else {
        cb([])
      }
    }
  }
}
</script>

<style scoped lang="scss">
::v-deep .jr-decorated-table--header {
  display: none !important;
}
.main {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
  .top-content {
    padding: 0 16px 16px 16px;
    border-bottom: 1px solid #eae9e9;

    ::v-deep .el-date-editor {
      padding-top: 0 !important;
      padding-bottom: 0 !important;
    }
  }

  .bottom-content {
    flex: 1;
    background-color: #ffffff;
    position: relative;
    display: flex;
    flex-direction: column;
    padding-bottom: 16px;
  }
}

.service-payment-form {
  .vertical-layout {
    background: #fff;
    padding: 0 16px;
  }

  &-content {
    display: flex;
    align-items: center;
    // padding-top: 16px;

    ::v-deep .el-form-item {
      max-width: 20% !important;
      display: flex;
      align-items: center;

      .el-form-item__label {
        // padding-top: 10px !important;
        flex-shrink: 0;
        display: flex !important;
        align-items: center;
        padding-top: 0 !important;
      }

      .el-form-item__content {
        flex: 1 !important;
        flex-shrink: 0;
        display: flex !important;
        align-items: center;
        .el-date-editor {
          flex: 1 !important;
          flex-shrink: 0;
          width: auto !important;
        }
      }
    }

    .submit-btn {
      margin-left: auto;
    }

    .service-payment-tips {
      height: 19px;
      font-family: MicrosoftYaHei;
      font-size: var(--el-font-size-base);
      color: rgba(0, 0, 0, 0.6);
      line-height: 19px;
      text-align: left;
      font-style: normal;
      display: flex;
      align-items: center;
      gap: 3px;
    }

    ::v-deep .el-radio {
      margin-right: 0 !important;
      min-width: 80px !important;
    }
  }
}
</style>
