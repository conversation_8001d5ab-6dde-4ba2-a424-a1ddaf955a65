<!--
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023-11-09 10:26:39
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2023-11-09 16:02:27
 * @Description: 描述
-->
<template>
  <ActiveModule v-bind="{...$attrs, ...$props, groupId: 'b808da5b25154a7b879ac8a17a6a2c82' }" />
</template>

<script>
import ActiveModule from '@jupiterweb/components/active-module/index.vue'
export default {
  components: {
    ActiveModule
  }
}
</script>

<style lang="scss" scoped></style>
