.benchmark-analysis {
  .subtitle {
    height: 70px;
    display: flex;
    align-items: center;

    &-label {
      font-family: MicrosoftYaHeiSemibold;
      font-size: var(--el-font-size-base) !important;
      color: rgba(0,0,0,0.85);
      padding-right: 16px;
    }
  }

  .custom-radio {
    display: inline-block;
    .el-radio {
      margin-right: 16px;
      &__label {
        padding-left: 8px;
      }
    }
  }

  .search-form {
    .el-form--inline {
      .el-form-item {
        vertical-align: middle;
        margin-right: 16px;
      }
      .el-button {
        margin-top: 2px;
      }
    } 

    .el-form-item__label {
      line-height: 36px;
      padding-top: 0px;
    }

    .radio-group {
      margin-right: 16px;
      .el-radio-button__inner {
        width: 32px;
        height: 32px;
        margin-bottom: 2px;
        padding: 9px 8px;
        color: rgba(0,0,0,0.9);
      }
      .el-radio-button.is-active .el-radio-button__inner{
        color: #ffffff;
      }
    }

    .export-btn {
      padding-left: 13px !important;
      padding-right: 12px !important;
    }
    .export-btnpic {
      width: 32px;
      height: 32px;
      padding-left: 1px !important;
      padding-right: 0px !important;
    }
    .export-btn-label {
      padding: 0 4px 0 6px;
    }
  }

  .search-form--flex {
    .el-form--inline {
      display: flex;
      flex-wrap: wrap;
      .align-right {
        margin-left: auto;
      }
    }
  }

  .comp-tags {
    margin-left: 68px;
    margin-top: 10px;

    &-item {
      height: 24px;
      background: rgba(133,111,254,0.1);
      border-radius: 2px;
      color: #856FFE;
      border: 0px;
      margin-right: 12px;

      .el-icon-close {
        background: #856FFE;
        color: #ffffff;
        &::before {
          font-size: var(--el-font-size-base) !important;
        }
      }
    }
  }
}