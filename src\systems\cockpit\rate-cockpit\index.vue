<template>
  <div class="cockpit" style="padding: 0px">
    <div class="cockpit-header">
      <div class="cockpit-header-left">
        <img src="@/assets/cockpit/cockpit_logo.png" alt="" />
        <span class="cockpit-header-left-company">
          <img src="~@/assets/cockpit/company_icon.png" alt="公司" />
          <span :title="$store.getters.personInfo.companyName">
            {{ $store.getters.personInfo.companyName | companyFormat }}
          </span>
        </span>
        <el-dropdown @command="changeSysVersion">
          <span class="cockpit-header-left-dropdown">
            <img src="~@/assets/images/home/<USER>" alt="切换" />
            <span>{{ $store.getters.sysVersionName }}</span>
            <img src="~@/assets/images/home/<USER>" alt="切换" />
          </span>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item
              v-for="item in $store.getters.personInfo.companyVerList || []"
              :key="item.id"
              :command="item.id"
            >
              {{ item.text }}
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>
      <div class="cockpit-header-right">
        <span>主体评级：{{ companyLevel }}</span>
        <span>{{ $store.getters.personInfo.username }}</span>
        <jr-svg-icon
          :icon-class="isFullScreen ? 'compress' : 'full-screen-rate'"
          :style="{ width: px2vw(16), height: px2vh(16), color: '#000000',cursor: 'pointer' }"
          @click="enterFullscreen"
        />
        <img 
          src="@/assets/cockpit/cockpit_close.png" 
          alt="" 
          :style="{ width: px2vw(16), height: px2vh(17), color: '#000000',cursor: 'pointer' }"
          @click="closeMenu"
        >
      </div>
    </div>
    <div class="cockpit-content" :key="$store.getters.sysVersion">
      <div class="cockpit-content-left">
        <strategy-center :companyList="companyList"/>
        <quotes-center />
      </div>
      <div class="cockpit-content-middle">
        <valuationCenter />
      </div>
      <div class="cockpit-content-right">
        <calendar-center />
        <information-center />
      </div>
    </div>
    <div class="cockpit-footer">
      <div class="cockpit-footer-inner">
        <div class="cockpit-footer-inner-tabs">
          <div class="cockpit-footer-inner-tabs-tabPro" @click.stop="redirectPro"></div>
          <div class="cockpit-footer-inner-tabs-tabRate"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import strategyCenter from './components/strategy-center'
import quotesCenter from './components/quotes-center'
import calendarCenter from './components/calendar-center.vue'
import informationCenter from './components/information-center.vue'
import valuationCenter from './components/valuation-center.vue'
import router, { createRouter } from '@jupiterweb/router'
import Paths from '@jupiterweb/utils/systopath'
import { toggleFullScreen,isFull } from '@/utils/fullScreen'
import { publicCompanyList } from "@/api/public/public"
import { px2vw, px2vh } from '../utils/portcss'
export default {
  components: {
    strategyCenter,
    quotesCenter,
    calendarCenter,
    informationCenter,
    valuationCenter
  },
  data() {
    return {
      companyList:[],
      isFullScreen:false
    }
  },
  computed: {
    companyLevel() {
      return this.companyList.find(item  => item.companyName === this.$store.getters.personInfo.companyName)?.rating || ''
    }
  },
  created() {
    this.getPublicCompanyList()
  },
  mounted() {
    this.isFullScreen = isFull()
  },
  filters: {
    companyFormat(val) {
      if(val){
        return val.length > 9 ? val.substring(0, 9) + '...' : val
      }else{
        return val;
      }
    },
  },
  methods: {
    px2vw,
    px2vh,
    changeSysVersion(companyVer) {
      if (this.$store.getters.personInfo?.companyVerList?.length <= 1) return
      this.$store.commit('system/CHANGE_SYS_VERSION', companyVer)
      router.matcher = createRouter().matcher
      this.$store.dispatch('GetMenuAuth').then((res) => {
        if (Object.keys(res).length && JSON.stringify(this.$store.getters.currentSystem) !== '{}') {
          const { sysId, routerflag: sysflag } = this.$store.getters.currentSystem
          const dashboard = Paths()[sysId]
          const plainMenus = this.$store.getters.permissions[sysId] || []
          this.$store.dispatch('GenerateRoutes', { menus: plainMenus, dashboard, sysflag }).then((accessRoutes) => {
            // 根据menus权限生成可访问的路路由表
            router.addRoutes(accessRoutes) // 动态添加可访问路由表
            this.$store.dispatch('tagsView/delOthersViews', this.visitedViews[0])
            this.$store.dispatch('tagsView/updateCurrentTab', this.visitedViews[0].pageKey)
          })
        }
      })
    },
    /**
     * 全屏状态切换
     */
    enterFullscreen(e) {
      e.preventDefault()
      toggleFullScreen(document.documentElement)
      this.isFullScreen = !this.isFullScreen
    },
    /**
     * 退出菜单
     */
    closeMenu() {
      this.$emit('close')
    },
    /**
     * 获取所有企业相关信息
     */
    async getPublicCompanyList() {
      const data = await publicCompanyList()
      this.companyList = data
    },
    /**
     * 跳转利率驾驶舱
     */
    redirectPro() {
      this.$store.dispatch('tagsView/updateCurrentTab', {
        path: '/1369319537827659776',
        // 跳转需要刷新的话 refresh传true
        meta: {
          refresh: true,
          params: {}
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~assets/styles/portcss.scss';
.cockpit {
  position: fixed;
  top: 0px;
  left: 0px;
  background-image: url('../../../assets/cockpit/cockpit_lighter_bac.png');
  background-size: 100% 100%;
  width: 100%;
  height: 100%;
  z-index: 1002;
  &-header {
    width: 100%;
    height: vh(76);
    background-image: url('../../../assets/cockpit/cockpit_header.png');
    background-size: 100% 100%;
    padding-left: vw(32);
    padding-right: vw(32);
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    color: #ffffff;
    &-left {
      display: flex;
      align-items: center;
      & > img {
        width: vw(263);
        height: vh(33);
      }
      &-company {
        display: flex;
        align-items: center;
        margin-left: vw(32);
        & > img {
          width: vw(15);
          height: vh(13);
        }
        & > span {
          height: vh(22);
          font-family: MicrosoftYaHeiSemibold;
          font-size: vh(14);
          color: rgba(0, 0, 0, 0.9);
          line-height: vh(22);
          margin-left: vw(9);
        }
      }
      &-dropdown {
        margin-left: vw(34);
        cursor: pointer;
        & > img:nth-of-type(1) {
          width: vw(12);
          height: vh(13);
        }
        & > img:nth-of-type(2) {
          width: vw(12);
          height: vh(13);
        }
        & > span {
          height: vh(22);
          font-family: MicrosoftYaHeiSemibold;
          font-size: vh(14);
          color: #ff8e2b;
          line-height: vh(22);
          margin-left: vw(10);
          margin-right: vw(8);
        }
      }
    }
    &-right {
      display: flex;
      gap: vw(32);
      align-items: center;
      span {
        cursor: pointer;
        height: vh(22);
        font-family: MicrosoftYaHeiSemibold;
        font-size: vh(14);
        color: rgba(0, 0, 0, 0.9);
        line-height: vh(22);
      }
    }
  }
  &-content {
    width: 100%;
    height: vh(928);
    display: flex;
    padding: vh(24) vw(32) vh(8);
    gap: vw(24);
    & > div {
      flex-shrink: 0;
    }
    &-left {
      width: vw(474);
      height: 100%;
      display: flex;
      flex-direction: column;
      gap: vh(24);
      flex-shrink: 0;
    }
    &-middle {
      width: vw(860);
      height: 100%;
    }
    &-right {
      width: vw(474);
      height: 100%;
      display: flex;
      flex-direction: column;
      gap: vh(24);
      flex-shrink: 0;
    }
  }
  &-footer {
    width: 100%;
    height: vh(76);
    &-inner {
      width: vw(1404);
      height: 100%;
      background-image: url('../../../assets/cockpit/cockpitFootBac.png');
      background-size: 100% 65.79%;
      background-position: center bottom;
      background-repeat: no-repeat;
      margin: 0 auto;
      display: flex;
      justify-content: center;
      &-tabs {
        width: vw(635);
        height: vh(50);
        display: flex;
        justify-content: center;
        align-items: center;
        &-tabPro {
          width: vw(337);
          height: vh(49);
          background-image: url('../../../assets/cockpit/cockpitTabPro.png');
          background-size: 100% 100%;
          position: relative;
          left: vw(20);
          top: vh(6);
          cursor: pointer;
        }
        &-tabRate {
          width: vw(337);
          height: vh(49);
          background-image: url('../../../assets/cockpit/cockpitTabRate.png');
          background-size: 100% 100%;
          position: relative;
          left: vw(-20);
          top: vh(6);
          cursor: pointer;
        }
      }
    }
  }
}
</style>
