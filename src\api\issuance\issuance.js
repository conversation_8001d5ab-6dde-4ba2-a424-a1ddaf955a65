import { GetListInfo, UpdateFn, ExportFn } from '@jupiterweb/utils/api'

// 存续期债券明细列表各种费用失焦保存
export const issuanceCompreCostAdd = (params) => GetListInfo('/issueManage/issueProductChoose/saveOrModify', params)
// 票据到期收益率曲线
export const issuanceYieldCurve = (params) => GetListInfo('/issueManage/issueProductChoose/queryYtm', params)
// 发行管理-发行窗口选择
export const issuanceGetWindowCardData = (params) => GetListInfo('/issueManage/issueWindow/queryIssueWindow', params)
// 发行管理-发行定价-参考债券列表
export const issuanceQueryBondList = (params) => GetListInfo('/issueManage/issuePricing/queryRealtimePricing', params)
// 发行管理-发行定价-阈值批量保存
export const issuanceThresholdBatchModify = (params) =>
  GetListInfo('/issueManage/issuePricing/thresHoldModifyBatch', params)
// 发行管理-募集资金用途-募集资金用途详情
export const issuancePurposeDetail = (id) => GetListInfo(`/issueManage/rasiedFunds/queryRasiedFundsDetail?id=${id}`)
// 发行管理-募集资金用途-修改债券类募集资金用途
export const issuancePurposeModify = (params) => GetListInfo('/issueManage/rasiedFunds/updateRasiedFunds', params)
// 发行管理-募集资金用途-导入
export const issuancePurposeImport = (params, cb) =>
  UpdateFn('/issueManage/rasiedFunds/import', params, cb, 'post', false, false)
// 发行管理-查询债券公共模块-查询发行人列表
export const issuanceQueryIssuerList = (params) => GetListInfo('/common/queryBondType/queryComp', params)
// 发行管理-查询债券公共模块-查询债券简称
export const issuanceQueryBondShortNameList = (params) => GetListInfo('/common/queryBondType/queryBondName', params)
// 发行管理-获取发行人下拉项
export const issuanceQueryIssurList = (params) => GetListInfo('/issueManage/issuePricing/queryIssuer', params)
// 发行管理-获取债券类型下拉项
export const issuanceQueryBondTypeList = (params) => GetListInfo('/common/queryBondType/queryAllBondType', params)
// 发行管理-募集资金用途-导入下载失败数据
export const rasiedFundsExport = (params) => ExportFn('/issueManage/rasiedFunds/export', params)
