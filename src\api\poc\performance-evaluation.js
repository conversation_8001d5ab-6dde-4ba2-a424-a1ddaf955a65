/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2024-01-12 11:38:32
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2024-01-12 11:39:46
 * @Description: 业绩评价API
 */
import { GetInfoFn, UpdateFn } from '@jupiterweb/utils/api'

// 获取详情数据
export const GetViewData = (params) => GetInfoFn('/poc/performanceRating/banner', params)

// 获取评价模型
export const QueryRatingModel = (params) => GetInfoFn('/poc/performanceRating/queryRatingModel', params)
// 评价
export const Evaluate = (params, cb) => UpdateFn('/poc/performanceRating/evaluate', params, cb)
// 发布
export const Publish = (params, cb) => UpdateFn('/poc/performanceRating/publish', params, cb)
// 评价记录
export const QueryEvaluateLog = (params) => GetInfoFn('/poc/performanceRating/evaluateList', params)
// 市场行情 折线图
export const marketRatioCurve = (params, url) => GetInfoFn(url, params)
// 利率面板 和 信用债面板
export const panel = (params, url) => GetInfoFn(url, params)

