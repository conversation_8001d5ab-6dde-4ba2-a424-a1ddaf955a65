<template>
  <div class="zj-content service-payment-modal">
    <UpdateModal v-if="isShowUpdateModal" :close-modal="closeModal" :visible="isShowUpdateModal" :permitdetail="permitdetail" :row="row" />
  </div>
</template>

<script>
import UpdateModal from './update-service-payment-modal.vue'
export default {
  name: 'AddServicePayment',
  components: { UpdateModal },
  props: {
    permitdetail: {
      type: Object,
      default() {
        return {}
      }
    },
    row: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  data() {
    return {
      isShowUpdateModal: true
    }
  },
  mounted() {
    // console.log(this.permitdetail)
  },
  methods: {
    // 关闭弹窗
    closeModal(isSuccess) {
      this.isShowUpdateModal = false

      isSuccess && this.getlistData()
    },
    openModal() {
      this.isShowUpdateModal = true
    }
  }
}
</script>

<style></style>
