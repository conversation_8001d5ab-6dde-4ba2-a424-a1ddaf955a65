<template>
  <flat-index :query="handleQuery" :reset="handleReset" :pagination="configTable.pagination">
    <template v-slot:form>
      <el-form ref="elForm" :model="configForm.model">
        <jr-form-item-create :column="0" :data="configForm.data" :model="configForm.model" />
      </el-form>
    </template>

    <template v-slot:right-button>
      <el-button>
        <jr-svg-icon icon-class="export" />
      </el-button>
    </template>

    <template v-slot:table-list="{ height }">
      <jr-table
        :height="height"
        :columns="configTable.columns"
        :data-source="configTable.data"
        :loading="configTable.loading"
        :pagination="configTable.pagination"
        :on-change="handleQuery"
        border
      >
        <template v-slot:index>
          <el-table-column
            type="index"
            width="50px"
            align="center"
            :label="InitialMessage('common.columns.index')"
          >
            <template slot-scope="scope">
              <span>{{ (configTable.pagination.pageNo - 1) * configTable.pagination.pageSize + scope.$index +1 }}</span>
            </template>
          </el-table-column>
        </template>
      </jr-table>
    </template>

  </flat-index>
</template>

<script>
import * as API from '@/api/demo/flat-index'

export default {
  props: {
    // 平台日期
    date: {
      type: [Number, String],
      default: null
    },
    // 权限相关
    permitdetail: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      // 查询区域
      configForm: {
        model: {},
        data: [
          {
            title: '区域名称',
            prop: 'field1',
            type: 'select',
            options: [
              { text: '1111', value: '1111' },
              { text: '2222', value: '2222' },
              { text: '3333', value: '3333' }
            ]
          },
          {
            title: '数据年份',
            prop: 'field2',
            type: 'select',
            options: [
              { text: '1111', value: '1111' },
              { text: '2222', value: '2222' },
              { text: '3333', value: '3333' }
            ]
          }
        ]
      },
      // 表格数据
      configTable: {
        loading: false,
        columns: [
          {
            title: '区域名称',
            prop: 'name'
          },
          {
            title: 'GDP(亿)',
            prop: 'gdp',
            type: 'tenThousand'
          },
          {
            title: '国有资本经营收入(亿)',
            prop: 'amount1',
            type: 'tenThousand'
          },
          {
            title: '国有资本经营支出(亿)',
            prop: 'amount2',
            type: 'tenThousand'
          },

          {
            title: '地方政府债务余额(亿)',
            prop: 'amount3',
            type: 'tenThousand'
          },
          {
            title: '一般债务余额(亿)',
            prop: 'amount4',
            type: 'tenThousand'
          },
          {
            title: '专项债务余额(亿)',
            prop: 'amount5',
            type: 'tenThousand'
          },
          {
            title: '地方政府债务限额(亿)',
            prop: 'amount6',
            type: 'tenThousand'
          },
          {
            title: '一般债务限额(亿)',
            prop: 'amount7',
            type: 'tenThousand'
          },
          {
            title: '专项债务限额(亿)',
            prop: 'amount8',
            type: 'tenThousand'
          }
        ],
        data: [
          {
            name: '广东省',
            gdp: 135673.1600,
            amount1: 135673.1600,
            amount2: 135673.1600,
            amount3: 135673.1600,
            amount4: 135673.1600,
            amount5: 135673.1600,
            amount6: 135673.1600,
            amount7: 135673.1600,
            amount8: 135673.1600
          },
          {
            name: '江苏省',
            gdp: 135673.1600,
            amount1: 135673.1600,
            amount2: 135673.1600,
            amount3: 135673.1600,
            amount4: 135673.1600,
            amount5: 135673.1600,
            amount6: 135673.1600,
            amount7: 135673.1600,
            amount8: 135673.1600
          },
          {
            name: '山东省',
            gdp: 135673.1600,
            amount1: 135673.1600,
            amount2: 135673.1600,
            amount3: 135673.1600,
            amount4: 135673.1600,
            amount5: 135673.1600,
            amount6: 135673.1600,
            amount7: 135673.1600,
            amount8: 135673.1600
          },
          {
            name: '浙江省',
            gdp: 135673.1600,
            amount1: 135673.1600,
            amount2: 135673.1600,
            amount3: 135673.1600,
            amount4: 135673.1600,
            amount5: 135673.1600,
            amount6: 135673.1600,
            amount7: 135673.1600,
            amount8: 135673.1600
          },
          {
            name: '四川省',
            gdp: 135673.1600,
            amount1: 135673.1600,
            amount2: 135673.1600,
            amount3: 135673.1600,
            amount4: 135673.1600,
            amount5: 135673.1600,
            amount6: 135673.1600,
            amount7: 135673.1600,
            amount8: 135673.1600
          }
        ],
        pagination: {
          pageNo: 1,
          pageSize: window.screen.height > 1000 ? 25 : 15,
          pageSizeOptions: [15, 25, 50, 100],
          total: 0,
          showSizeChanger: true
        }
      }
    }
  },
  methods: {
    // 查询
    handleQuery() {
      const self = this
      const { configForm, configTable } = self

      self.$refs.elForm.validate(async valid => {
        if (valid) {
          configTable.loading = true

          const params = {
            data: { ...configForm },
            page: { ...configTable.pagination }
          }

          const { list = [], total = 0 } = { ...await API.GetListData(params) }

          if (list.length) {
            configTable.loading = false
            configTable.data = list
            configTable.pagination.total = total
          }
        }
      })
    },
    // 重置
    handleReset() {

    }
  }
}
</script>

<style lang="scss">

</style>

