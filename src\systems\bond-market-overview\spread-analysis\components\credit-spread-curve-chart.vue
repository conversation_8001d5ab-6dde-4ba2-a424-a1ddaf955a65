<template>
  <div class="template-module-chart">
    <div class="custom-actions">
      <el-form inline :model="form" label-width="80" style="display: flex; align-items: center">
        <jr-form-item>
          <jr-radio-group v-model="form.level" :data="checkboxList" @change="flagChange" />
        </jr-form-item>
        <jr-form-item label="日期区间">
          <el-date-picker
            v-model="form.customDateRange"
            type="daterange"
            unlink-panels
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="yyyy-MM-dd"
            class="date-picker"
            @change="emitSearchParams"
          />
        </jr-form-item>

        <jr-form-item v-if="form.level === '1'" label="评级">
          <jr-combobox
            v-model="form.rating"
            placeholder="请选择"
            clearable
            filterable
            :data="params.headerBtns === 'mtn' ? mtnRateList : lgfvRateList"
            option-value="itemcode"
            option-label="cnname"
            :multiple="true"
            key="multiplerating"
            collapse-tags
            @change="emitSearchParams"
          />
        </jr-form-item>
        <jr-form-item v-if="form.level === '0'" label="评级">
          <jr-combobox
            v-model="form.rating"
            placeholder="请选择"
            clearable
            filterable
            :data="params.headerBtns === 'mtn' ? mtnRateList : lgfvRateList"
            option-value="itemcode"
            option-label="cnname"
            :multiple="false"
            key="singlerating"
            collapse-tags
            @change="emitSearchParams"
          />
        </jr-form-item>

        <jr-form-item v-if="form.level === '0'" label="期限">
          <jr-combobox
            v-model="form.term"
            placeholder="请选择"
            clearable
            filterable
            :data="termList"
            option-value="itemcode"
            option-label="cnname"
            :multiple="true"
            key="multipleterm"
            collapse-tags
            @change="emitSearchParams"
          />
        </jr-form-item>
        <jr-form-item v-if="form.level === '1'" label="期限">
          <jr-combobox
            v-model="form.term"
            placeholder="请选择"
            clearable
            filterable
            :data="termList"
            option-value="itemcode"
            option-label="cnname"
            :multiple="false"
            key="singleterm"
            collapse-tags
            @change="emitSearchParams"
          />
        </jr-form-item>
      </el-form>
    </div>
    <el-dropdown @command="handleCommand" @visible-change="visibleChange">
      <ws-button class="el-dropdown-link" :loading="loading">
        <jr-svg-icon icon-class="upload" />
        导出
        <i :class="visible ? 'el-icon-caret-top' : 'el-icon-caret-bottom'" />
      </ws-button>
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item command="tp">
          <jr-svg-icon icon-class="picture" />
          导出图片
        </el-dropdown-item>
        <el-dropdown-item command="bg">
          <jr-svg-icon icon-class="table" />
          导出表格
        </el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>

    <publicCharts
      ref="publicCharts"
      :chartdata="chartsOptions"
      :height="430"
      style="padding-top: 40px"
      class="chart-content"
    />
  </div>
</template>
<script>
import wsButton from '@/components/ws-button'
import { GetComboboxList } from '@/api/home'
import {
  exportBondMyEstimateInfo,
  spreadAnalysisQueryCreditSpreadCurve,
  exportSpreadAnalysisQueryCreditSpreadCurve
} from '@/api/bonds/bonds'
import publicCharts from './public-charts.vue'
import moment from 'moment'
const MTNRATING = 'creditMTNRating'
const LGFVRATING = 'creditLgfvRating'
const TERM = 'creditTerm'
import { debounce } from 'lodash'
import echartNoDataGraphic from '@/assets/js/echartsNoData'
export default {
  name: 'Echarts1',
  inject: ['parant'],
  components: {
    wsButton,
    publicCharts
  },
  props: {
    params: {
      type: Object,
      default() {
        return {}
      }
    },
    options: {
      type: Object,
      default: () => ({})
    },
    styles: {
      type: Object,
      default: () => ({ width: '100%', height: '500px' })
    },
    permitdetail: {
      type: Object,
      default() {
        return {}
      }
    },
    title: {
      type: String,
      default() {
        return ''
      }
    }
  },
  data() {
    return {
      myChart: null,
      loading: false,
      visible: false,
      form: {
        level: '0',
        dtEnd: '',
        dtStart: '',
        customDateRange: [],
        rating: null,
        term: null,
        ratingArr: null,
        termArr: null
      },
      checkboxList: [
        {
          label: '同评级',
          value: '0'
        },
        {
          label: '同期限',
          value: '1'
        }
      ],
      mtnRateList: [],
      lgfvRateList: [],
      termList: [],
      chartsOptions: {}
    }
  },
  computed: {
    chartParams() {
      return {
        flag: this.form.level,
        tab: this.params.headerBtns,
        dtStart: this.form.dtStart,
        dtEnd: this.form.dtEnd,
        term: this.form.termArr,
        curvename: this.form.ratingArr,
        chartType: 'MULTILINE'
      }
    },
    changeParams() {
      return this.params // 计算属性返回特定值
    }
  },
  watch: {
    changeParams: {
      handler(newVal, oldVal) {
        console.log(newVal)
        console.log(oldVal)

        if (
          newVal.hasOwnProperty('flag') &&
          ((oldVal && JSON.stringify(newVal) === JSON.stringify(oldVal)) || !oldVal)
        ) {
          this.form.level = '0'
          this.form.dtEnd = ''
          this.form.dtStart = ''
          this.form.webTime = new Date().getTime()
          this.flagChange(this.form.level)
          this.form.customDateRange = this.getDefaultDateRange()
        }
      },
      deep: true // 开启深度监听
    }
  },
  async created() {
    this.emitSearchParams = debounce(this.emitSearchParams, 300)

    await this.getDictOptionsApi()
    this.flagChange('0')
    this.form.customDateRange = this.getDefaultDateRange()
  },
  methods: {
    async getChartData() {
      const res = await spreadAnalysisQueryCreditSpreadCurve(this.chartParams)
      this.chartsOptions = this.formatterChartData(res)
      console.log(this.chartsOptions, 'chartsOptions')
    },
    getDefaultDateRange() {
      const today = new Date()

      // 获取一年前的日期
      const oneYearAgo = new Date()
      oneYearAgo.setFullYear(today.getFullYear() - 1)

      return [moment(oneYearAgo).format('YYYY-MM-DD'), moment(today).format('YYYY-MM-DD')]
    },
    // templateModule 组件回调函数，获取图表实例
    chartCallback(refData) {
      console.log('refData', refData)

      this.myChart = refData
    },
    /**
     * 获取字典字段
     */
    async getDictOptionsApi() {
      const res = await GetComboboxList([MTNRATING, LGFVRATING, TERM])
      this.mtnRateList = res[MTNRATING]
      this.lgfvRateList = res[LGFVRATING]
      this.termList = res[TERM]
    },
    flagChange(e) {
      this.form.rating = null
      this.form.ratingArr = null
      this.form.term = null
      this.form.termArr = null
      switch (e) {
        case '0':
          this.form.rating = this.params.flag === '0' ? 'AAA-' : 'AA+'
          this.form.ratingArr = this.params.flag === '0' ? ['AAA-'] : ['AA+']
          if (Array.isArray(this.form.ratingArr)) {
            const flag = this.params.flag
            for (let index = 0; index < this.form.ratingArr.length; index++) {
              if (flag === '0') {
                this.form.ratingArr[index] = `中债中短期票据收益率曲线(${this.form.ratingArr[index]})`
              } else {
                this.form.ratingArr[index] = `中债城投债收益率曲线(${this.form.ratingArr[index]})`
              }
            }
          }
          this.form.term = ['1', '3', '5']
          this.form.termArr = ['1', '3', '5']
          break

        case '1':
          this.form.rating = this.params.flag === '0' ? ['AAA', 'AAA-', 'AA+'] : ['AAA', 'AA+', 'AA']
          this.form.ratingArr = this.params.flag === '0' ? ['AAA', 'AAA-', 'AA+'] : ['AAA', 'AA+', 'AA']
          if (Array.isArray(this.form.ratingArr)) {
            const flag = this.params.flag
            for (let index = 0; index < this.form.ratingArr.length; index++) {
              if (flag === '0') {
                this.form.ratingArr[index] = `中债中短期票据收益率曲线(${this.form.ratingArr[index]})`
              } else {
                this.form.ratingArr[index] = `中债城投债收益率曲线(${this.form.ratingArr[index]})`
              }
            }
          }
          this.form.term = '3'
          this.form.termArr = ['3']
          break
      }
      this.emitSearchParams()
    },
    handleSearch() {
      console.log('handleSearch')
      this.emitSearchParams()
      this.$emit('search', this.chartParams)
    },
    emitSearchParams() {
      if (Array.isArray(this.form.customDateRange) && this.form.customDateRange.length > 0) {
        this.form.dtStart = moment(this.form.customDateRange[0]).format('YYYYMMDD')
        this.form.dtEnd = moment(this.form.customDateRange[1]).format('YYYYMMDD')
      } else {
        this.form.dtStart = ''
        this.form.dtEnd = ''
      }

      if (!Array.isArray(this.form.rating) && this.form.level === '0') {
        this.form.ratingArr = this.form.rating ? [this.form.rating] : []
      } else {
        this.form.ratingArr =
          Array.isArray(this.form.rating) && this.form.rating.length > 0
            ? JSON.parse(JSON.stringify(this.form.rating))
            : []
      }
      if (Array.isArray(this.form.ratingArr)) {
        const flag = this.params.flag
        for (let index = 0; index < this.form.ratingArr.length; index++) {
          if (flag === '0') {
            this.form.ratingArr[index] = `中债中短期票据收益率曲线(${this.form.ratingArr[index]})`
          } else {
            this.form.ratingArr[index] = `中债城投债收益率曲线(${this.form.ratingArr[index]})`
          }
        }
      }

      if (!Array.isArray(this.form.term) && this.form.level === '1') {
        this.form.termArr = this.form.term ? [this.form.term] : []
      } else {
        this.form.termArr = Array.isArray(this.form.term) && this.form.term.length > 0 ? this.form.term : []
      }

      this.form.webTime = new Date().getTime()

      this.getChartData()
    },
    dataURLtoBlob(dataurl) {
      const arr = dataurl.split(',')
      const mime = arr[0].match(/:(.*?);/)[1]
      const bstr = atob(arr[1])
      let n = bstr.length
      const u8arr = new Uint8Array(n)
      while (n--) {
        u8arr[n] = bstr.charCodeAt(n)
      }
      return new Blob([u8arr], { type: mime })
    },
    handleCommand(v) {
      if (v === 'tp') {
        // 下载图片

        const exportOptions = {
          backgroundColor: '#ffffff',
          pixelRatio: 2 // 提高导出清晰度（可选）
        }
        // // 生成图片 URL 并触发下载
        const chart = this.$refs.publicCharts.getMyChart()
        console.log(chart, 'chart')
        const imgUrl = chart.getDataURL(exportOptions)
        const link = document.createElement('a')
        link.href = imgUrl
        link.download = `${this.title}.png`
        link.click()
      } else {
        const params = {
          ...this.chartParams,
          chartSeq: '21210e6994554794a22654e5bd34e39a'
        }
        exportSpreadAnalysisQueryCreditSpreadCurve(params).then((res) => {
          console.log(res, 'res')
        })
      }
    },
    // 下拉菜单显示和隐藏
    visibleChange(v) {
      this.visible = v
    },
    formatterDateString(str) {
      return str.replace(/^(\d{4})(\d{2})(\d{2})$/, '$1/$2/$3')
    },
    formatterChartData(originalData) {
      // 1. 去重处理（保留同日期同曲线的最新数据）
      const uniqueMap = new Map()
      originalData.forEach((item) => {
        const key = JSON.stringify(item)
        uniqueMap.set(key, item)
      })
      const uniqueData = Array.from(uniqueMap.values())

      // 2. 获取所有唯一日期并排序
      const allDates = [...new Set(uniqueData.map((item) => this.formatterDateString(item.X)))]
      const sortedDates = allDates.sort((a, b) => new Date(a) - new Date(b))

      // 3. 按曲线名称分组
      const curveGroups = {}
      uniqueData.forEach((item) => {
        const curveName = `${item.b_anal_curvename}(${item.curveterm})`
        if (!curveGroups[curveName]) {
          curveGroups[curveName] = []
        }
        curveGroups[curveName].push(item)
      })

      console.log(curveGroups, 'curveGroups')

      // 4. 为每条曲线创建数据系列
      const seriesData = []
      Object.keys(curveGroups).forEach((curveName) => {
        const curveItems = curveGroups[curveName]

        const dateValueMap = {}

        // 创建日期到值的映射
        curveItems.forEach((item) => {
          const date = this.formatterDateString(item.X)
          dateValueMap[date] = item
        })

        console.log(dateValueMap, 'dateValueMap')
        let currentLegend = null

        // 为当前曲线创建数据点数组
        const dataPoints = sortedDates.map((date) => {
          currentLegend = dateValueMap[date].legend
          return {
            name: date,
            value: dateValueMap[date] ? dateValueMap[date].Y : null,
            curveterm: dateValueMap[date] ? dateValueMap[date].curveterm : null,
            legend: dateValueMap[date] ? dateValueMap[date].legend : null,
            updown: dateValueMap[date] ? dateValueMap[date].updown : null
          }
        })

        seriesData.push({
          name: `${curveName}-国开债(${currentLegend})`,
          type: 'line',
          data: dataPoints,
          symbol: 'circle',
          symbolSize: 6,
          connectNulls: true,
          smooth: true
        })
      })

      return {
        yAxis: {
          name: '单位(BP)',
          nameTextStyle: {
            padding: [0, 0, 0, 24] // 左内边距增加
          },
          axisLabel: {
            formatter: '{value} '
          },
          type: 'value'
        },
        xAxis: {
          data: sortedDates,
          splitLine: {
            show: false
          },
          type: 'category',
          boundaryGap: false
        },
        grid: {
          top: 32,
          left: 36,
          bottom: 70,
          right: 36
        },
        legend: {
          orient: 'horizontal',
          top: 400,
          left: 'center',
          type: 'scroll', // 设置为可滚动
          pageIconColor: '#000', // 分页箭头颜色
          pageIconInactiveColor: '#333', // 禁用时分页箭头颜色
          pageTextStyle: {
            color: '#000' // 页码文字颜色
          },
          textStyle: {
            color: '#000' // 设置文字颜色为深灰色
          },
          pageButtonItemGap: 5, // 分页按钮与图例项的间隔
          pageButtonGap: 10, // 分页按钮与图例组件外框的间隔
          pageButtonPosition: 'end', // 分页按钮位置
          pageFormatter: '{current}/{total}', // 页码显示格式
          padding: 5 // 图例内边距
        },
        toolbox: {
          show: false
        },
        tooltip: {
          trigger: 'axis',
          renderMode: 'html',
          formatter: function (params) {
            return (
              '<div>' +
              params[0].axisValue +
              '<br>' +
              params.map((p) => `${p.seriesName} : ${p.data.value}`).join('<br>') +
              '</div>'
            )
          }
        },
        series: seriesData,
        ...echartNoDataGraphic(sortedDates)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import '../css/credit-spread-curve-chart-temporary.scss';
</style>
