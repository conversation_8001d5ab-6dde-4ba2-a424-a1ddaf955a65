<!-- 主承排名 -->
<template>
  <div class="mainRanking">
    <div class="mainRankingTitle">主承排名</div>
    <div class="content">
      <div class="leftContent">
        <jr-decorated-table
          style="height: 100%"
          custom-id="7464294efe0a4ff7b0116cf87a24b84d"
          v-bind="{...$attrs, ...$props}"
        />
      </div>
      <div class="rightContent">
        <TemplateModule chart-type="BAR" chart-seq="cd3ba3df43a24a07b9a8e8cb30a19588" style="height: 400px; width: 100%" />
      </div>
    </div>

  </div>
</template>

<script>
import TemplateModule from '@jupiterweb/components/template-module'
export default {
  components: { TemplateModule },
  data() {
    return {
    }
  },
  methods: {}
}
</script>
<style lang="scss">
.mainRanking {
    margin-top: 10px;
    padding: 6px 10px 6px 10px;
    background: #fff;
    min-height: 400px;
    .el-table {
        height: 300px !important;
    }
    .mainRankingTitle {
        font-size: 16px;
        color: #303133;
        font-weight: 700;
        padding-bottom: 6px;
        border-bottom: 1px solid #EBEEF5;
    }
    .content {
        display: flex;
        .leftContent {
            flex: 1;
        }
        .rightContent {
            flex: 1.5;
        }
    }
}
</style>
