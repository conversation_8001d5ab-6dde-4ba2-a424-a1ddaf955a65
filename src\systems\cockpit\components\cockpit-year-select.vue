<template>
  <el-date-picker
    v-model="year"
    type="year"
    @change="change"
    style="width: 100% !important;height: 100%;"
    value-format="yyyy"
    prefix-icon="none"
    clear-icon="none"
    placeholder="选择年">
  </el-date-picker>
</template>

<script>
export default {
  name:"CockpitYearSelect",
  model: {
    prop: 'value',
    event: 'change'
  },
  props:{
    defaultValue: {
      type: String,
      default: ''
    },
  },
  data(){
    return{
      year:''
    }
  },
  created(){
    this.year = this.defaultValue
  },
  methods:{
    /**
     * 选择change事件
     */
    change() {
      this.$emit('change', this.year)
    }
  }
}
</script>

<style lang="scss" scoped>

</style>