// 自定义全局引用
import Vue from 'vue'
import BTemplateModule from '@/components/template-module'
import TemplateModule from '@jupiterweb/components/template-module'
import Navbar from '@/components/navbar'
import directives from './directive/index'
import { API_CODE } from '@jupiterweb/constants'
Vue.use(directives)
Vue.component('b-template-module', BTemplateModule)
Vue.component('template-module', TemplateModule)
Vue.component(Navbar.name, Navbar)

import store from '@jupiterweb/store'

Vue.prototype.$axiosRequest = (config) => {
  config.headers['Sys-Version'] = store.getters.sysVersion || localStorage.getItem('sysVersion')
}
let confirmIns
const refreshPage = () => {
  confirmIns = null
  window.sessionStorage.clear()
  location.href = location.pathname
  window.location.reload(true)
}
// 增加失效状态码
Vue.prototype.$apiResult = {
  ...API_CODE,
  noauth: API_CODE.noauth.concat('006')
}
// 重写token失效方法
Vue.prototype.$tokenInvalid = ({ data }) => {
  if (['003', '004', '005', '006'].includes(data.code)) {
    if (!confirmIns) {
      confirmIns = Vue.prototype.confirm(data.message, {
        showCancelButton: false,
        showClose: false,
        confirmButtonText: '好的'
      }).then(refreshPage).catch(() => {
        confirmIns = null
      })
    }
    return false
  }
}
Vue.prototype.$getPagination = (options = {}) => {
  // 计算页面尺寸
  const calculatePageSize = () => {
    const pageHeight = Math.max(window.screen.height, document.documentElement.clientHeight)
    if (pageHeight < 900) return 15
    if (pageHeight < 1100) return 20
    if (pageHeight < 1300) return 30
    if (pageHeight < 1500) return 40
    return 50
  }
  const pageSize = calculatePageSize()
  // 默认配置
  const defaultConfig = {
    showSizeChanger: true,
    pageSize,
    pageSizeOptions: Array.from(new Set([15, 20, pageSize, 50, 100]))
  }

  // 合并用户配置
  return {
    ...defaultConfig,
    ...options,
    pageSizeOptions: Array.from(new Set([...defaultConfig.pageSizeOptions, calculatePageSize()]))
  }
}
store.commit('settings/CHANGE_SETTING', [
  // 设置为垂直模式
  { key: 'isVertical', value: false },
  // 主题切换
  { key: 'showThemeSwitch', value: window.S_CONFIG.showThemeSwitch || false },
  // 隐藏搜索
  { key: 'showSearch', value: true },
  // 隐藏sitemap
  { key: 'showSiteMap', value: false },
  // 隐藏首页
  { key: 'showDashboard', value: false }
])
