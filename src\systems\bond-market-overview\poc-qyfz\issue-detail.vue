<template>
  <!-- POC专用 -->
  <!-- 关联企业发债 -->
  <jr-decorated-table
    custom-id="cc82165eded741529a99d867f6b8c18d"
    style="margin: -40px -20px 0;"
    :menuinfo="{
      ...menuinfo,
      btnList: [
        { btnkey: 'doc', btnnm: '发行材料', icon: 'file', componenturl: '/bond-market-overview/poc-qyfz/issue-doc.vue' }
      ]
    }"
  />
</template>

<script>
export default {
  props: {
    itemData: {
      type: Object,
      default: () => ({})
    },
    menuinfo: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {}
  }
}
</script>

<style lang="scss" scoped></style>
