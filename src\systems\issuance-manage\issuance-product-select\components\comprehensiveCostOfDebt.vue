<template>
  <div class="comprehensiveCostOfDebt">
    <div class="public-title-container" style="padding-left: 16px">
      <span>存续期债券信息</span>
      <issuance-popover text="计算说明" prop-class="issuance-explanation-popover">
        <issuanceProductPopinner />
      </issuance-popover>
    </div>
    <div class="comprehensiveCostOfDebt-cardArea">
      <div class="comprehensiveCostOfDebt-cardArea-card">
        <img src="@/assets/images/comprehensiveCostOfDebt01.png" alt="" />
        <span>
          <span>存续期债券数</span>
          <span>{{ cardAreaData.num }}</span>
        </span>
      </div>
      <div class="comprehensiveCostOfDebt-cardArea-card">
        <img src="@/assets/images/comprehensiveCostOfDebt02.png" alt="" />
        <span>
          <span>债券余额(亿)</span>
          <span>{{ ConvertAmount('HMU', cardAreaData.balance, 1, 4) }}</span>
        </span>
      </div>
      <div class="comprehensiveCostOfDebt-cardArea-card">
        <img src="@/assets/images/comprehensiveCostOfDebt03.png" alt="" />
        <span>
          <span>行权久期|到期久期</span>
          <span>{{ cardAreaData.exerciseduration }}|{{ cardAreaData.maturityduration }}</span>
        </span>
      </div>
      <div class="comprehensiveCostOfDebt-cardArea-card">
        <img src="@/assets/images/comprehensiveCostOfDebt04.png" alt="" />
        <span>
          <span>短期/长期</span>
          <span>{{ cardAreaData.short }}:{{ cardAreaData.long }}</span>
        </span>
      </div>
    </div>
    <div class="public-title-container" style="padding-left: 16px">
      <span>债券明细列表</span>
    </div>
    <div class="comprehensiveCostOfDebt-set public-table-search-container">
      <div>
        <el-dropdown @command="openPath">
          <el-button type="primary">服务费维护</el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command="ServicePayment">承销费</el-dropdown-item>
            <el-dropdown-item command="IntermediaryFee">中介费</el-dropdown-item>
            <el-dropdown-item command="OtherServicesPayment">其他服务费</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>
      <div class="comprehensiveCostOfDebt-set-cost">
        <span>
          <span>综合成本(%)：</span>
          <el-popover placement="bottom-start" trigger="click" @show="togglePopover(true)" @hide="togglePopover(false)">
            <div style="width: 764px; height: 309px; position: relative">
              <jr-decorated-table
                v-if="popoverStatus"
                style="height: 240px"
                :params="{
                  ...selfTableParams,
                  ownedModuleid: '1315721394511069184'
                }"
                custom-id="7841c34d0b3a45e89eb6fa470239e0bb"
                v-bind="{
                  ...$props
                }"
                :custom-config="{
                  dataConfig: {
                    noPagination: true
                  }
                }"
                :menuinfo="{
                  moduleid: '1315721394511069184'
                }"
              />
              <p class="comprehensiveCostOfDebt-set-cost-tips">
                <jr-svg-icon icon-class="info-circle" />
                <span>仅展示非含权债券管理期限的综合成本</span>
              </p>
            </div>
            <span slot="reference" style="cursor: pointer">{{ aggregatecost }}</span>
          </el-popover>
        </span>
        <span>
          <el-checkbox v-model="checkBoxData.ispm" @change="getAggregatecostApi" />
          <span>票面利率</span>
        </span>
        <span>
          <el-checkbox v-model="checkBoxData.iscx" @change="getAggregatecostApi" />
          <span>承销费率</span>
        </span>
        <span>
          <el-checkbox v-model="checkBoxData.iszj" @change="getAggregatecostApi" />
          <span>中介费率</span>
        </span>
        <span>
          <el-checkbox v-model="checkBoxData.isqt" @change="getAggregatecostApi" />
          <span>其他服务费率</span>
        </span>
      </div>
      <div style="margin-left: auto">
        <el-input
          v-model="tempShortName"
          placeholder="请输入债券简称"
          prefix-icon="el-icon-search"
          style="width: 260px"
          @input="shortNameInput"
        />
      </div>
    </div>
    <jr-decorated-table
      :params="{
        ...selfTableParams,
        s_info_name: shortName,
        webTime: webTime,
        ownedModuleid: '1315721394511069184'
      }"
      style="height: 476px; width: 100%; padding-top: 8px; background-color: #fff"
      custom-id="6840fe04ebd748d7a7e4987ff76f910c"
      :custom-render="customRender"
      :default-page-size="10"
      :initPagination="{
        pageSizeOptions: [10, 15, 20, 50, 100]
      }"
      v-bind="{
        ...$props
      }"
      :menuinfo="{
        moduleid: '1315721394511069184'
      }"
      @refreshed="callFn"
    />
  </div>
</template>

<script>
import { GetListData } from '@jupiterweb/api/common/active-module-svr'
import { issuanceCompreCostAdd } from '@/api/issuance/issuance'
import IssuancePopover from '../../components/issuance-popover.vue'
import issuanceProductPopinner from './issuance-product-popinner.vue'
import { debounce } from 'lodash'
import { ConvertAmount } from '@jupiterweb/utils/common'
export default {
  name: 'ComprehensiveCostOfDebt',
  components: {
    IssuancePopover,
    issuanceProductPopinner
  },
  data() {
    return {
      webTime: new Date().getTime(),
      cardAreaParams: {
        params: {
          ownedModuleid: '1315721394511069184',
          ccid: '478c1a8b22c44b01bc247755d4ad8c0e'
        },
        page: {
          pageNo: 1,
          pageSize: 25
        }
      },
      cardAreaData: {},
      customRender: {
        feeRate: (h, { row }) => {
          return (
            <jr-number-input
              value={row.feeRate}
              precision={4}
              max={100}
              onChange={(val) => this.saveRateApi(row, 'feeRate', val)}
              min={0}
            />
          )
        },
        agencyRate: (h, { row }) => {
          return (
            <jr-number-input
              value={row.agencyRate}
              precision={4}
              max={100}
              min={0}
              onChange={(val) => this.saveRateApi(row, 'agencyRate', val)}
            />
          )
        },
        otherRate: (h, { row }) => {
          return (
            <jr-number-input
              value={row.otherRate}
              precision={4}
              max={100}
              min={0}
              onChange={(val) => this.saveRateApi(row, 'otherRate', val)}
            />
          )
        }
      },
      checkBoxData: {
        ispm: true,
        iscx: false,
        iszj: false,
        isqt: false
      },
      aggregatecost: '',
      shortName: '',
      tempShortName: '',
      popoverStatus: false
    }
  },
  computed: {
    selfTableParams() {
      return {
        ispm: this.checkBoxData.ispm ? '1' : '',
        iscx: this.checkBoxData.iscx ? '1' : '',
        iszj: this.checkBoxData.iszj ? '1' : '',
        isqt: this.checkBoxData.isqt ? '1' : ''
      }
    }
  },
  async created() {
    this.getCardAreaDataApi()
  },
  methods: {
    callFn(data) {
      console.log(data)
      console.log(data.columns[4].sorter)

      console.log('2D' - '211D')
    },
    ConvertAmount,
    /**
     * 获取卡片区域数据
     */
    async getCardAreaDataApi() {
      const data = await GetListData(this.cardAreaParams)
      this.cardAreaData = data?.pageInfo?.list[0] || {}
      this.aggregatecost = this.cardAreaData?.aggregatecost || '0.0000'
    },
    /**
     * 费率保存
     */
    async saveRateApi(row, key, val) {
      this.$set(row, key, val)
      const keyAnalysis = {
        feeRate: 'feeRate',
        agencyRate: 'agencyRate',
        otherRate: 'otherRate'
      }
      const params = {
        id: row.id,
        bondCode: row.sInfoWindcode,
        bondShortName: row.bInfoFullname,
        [keyAnalysis[key]]: val
      }
      await issuanceCompreCostAdd(params)
      this.webTime = new Date().getTime()

      await this.getAggregatecostApi()
    },
    /**
     * 计算综合成本展示
     */
    async getAggregatecostApi() {
      const data = await GetListData({
        page: {
          pageNo: 1,
          pageSize: 1
        },
        params: {
          ccid: 'fe9847e7cd7c4f159fabc149cf6d9842',
          ownedModuleid: '1315721394511069184',
          ...this.selfTableParams
        }
      })
      this.aggregatecost = data?.pageInfo?.list[0]?.aggregatecost || '0.0000'
    },
    /**
     * 切换是否展示下拉自定义列表
     */
    togglePopover(status) {
      this.popoverStatus = status
    },
    /**
     * 防抖处理债券简称筛选
     */
    shortNameInput: debounce(function () {
      this.shortName = this.tempShortName
    }, 500),
    /**
     * 跳转服务费用支付
     */
    openPath(command) {
      this.$store.dispatch('tagsView/updateCurrentTab', {
        path: '/1364638311628533760',
        meta: {
          refresh: true,
          params: {
            defaultTabActiveName: command
          }
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .jr-decorated-table--top-search .jr-decorated-table--header-left {
  height: 0px;
  padding: 0px;
}
::v-deep .el-table {
  .cell {
    display: flex !important;
    align-items: center !important;
    padding: 0 16px !important;
  }
}

.comprehensiveCostOfDebt {
  // margin-top: 1px;
  height: calc(100% - 56px);
  &-title {
    width: 100%;
    box-sizing: border-box;
    background-color: #ffffff;
    display: flex;
    align-items: center;
    & > span:nth-of-type(1) {
      height: 22px;
      font-size: var(--el-font-size-base);
      color: rgba(0, 0, 0, 0.85);
      line-height: 22px;
      font-weight: 600;
    }
    & > span:nth-of-type(2) {
      height: 19px;
      font-size: var(--el-font-size-base);
      color: rgba(0, 0, 0, 0.6);
      line-height: 19px;
      margin-left: 6px;
    }
  }
  &-cardArea {
    width: 100%;
    padding: 0px 16px;
    box-sizing: border-box;
    display: flex;
    gap: 24px;
    background-color: #ffffff;
    justify-content: space-between;
    &-card {
      width: calc((100% - 72px) / 4);
      height: 124px;
      background: rgba(0, 0, 0, 0.02);
      border-radius: 8px;
      border: 1px solid #f0f0f0;
      padding: 36px 0px 36px 24px;
      box-sizing: border-box;
      display: flex;
      gap: 14px;
      & > img {
        width: 52px;
        height: 52px;
      }
      & > span {
        display: flex;
        flex-direction: column;
        gap: 8px;
        & > span:nth-of-type(1) {
          height: 21px;
          font-size: var(--el-font-size-base);
          color: rgba(0, 0, 0, 0.6);
          line-height: 21px;
        }
        & > span:nth-of-type(2) {
          height: 24px;
          font-weight: bold;
          font-size: var(--el-font-size-base);
          color: rgba(0, 0, 0, 0.9);
          line-height: 36px;
        }
      }
    }
  }
  &-set {
    width: 100%;
    height: 48px;
    display: flex;
    background-color: #ffffff;
    padding: 0px 16px 16px;
    align-items: center;
    button {
      height: 32px;
      border-radius: 2px;
    }
    &-cost {
      display: flex;
      align-items: center;
      & > span {
        margin-left: 16px;
        display: flex;
        align-items: center;
        gap: 8px;
      }
      & > span:nth-of-type(1) {
        gap: 0px;
        & > span:nth-of-type(1) {
          height: 22px;
          font-size: var(--el-font-size-base);
          color: rgba(0, 0, 0, 0.9);
          line-height: 22px;
        }
        & > span:nth-of-type(2) {
          height: 22px;
          font-size: var(--el-font-size-base);
          color: var(--theme--color);
          line-height: 22px;
          text-decoration-line: underline;
        }
      }
      &-tips {
        display: flex;
        align-items: center;
        margin-bottom: 0px;
        position: absolute;
        left: 0px;
        bottom: 0px;
        & > span {
          height: 19px;
          font-size: var(--el-font-size-base);
          color: rgba(0, 0, 0, 0.6);
          line-height: 19px;
        }
      }
    }
  }
}
</style>
