<template>
  <el-dialog
    :visible.sync="dialogVisible"
    :width="px2vw(1120)"
    :before-close="handleClose"
    :modal-append-to-body="true"
    :append-to-body="true"
  >
    <div class="dialog">
      <div class="dialog-title">
        <span>日历中心</span>
        <jr-svg-icon icon-class="close" style="color: #ffffff;cursor: pointer;" @click.stop="handleClose"/>
      </div>
      <div class="dialog-content">
        <div>
          <div class="dialog-content-header">
            <cockpitYearSelect
              v-model="selectParams.year"
              :defaultValue="selectParams.year"
              :style="{ width: `${px2vw(124)} !important`,height: px2vh(32) ,marginRight: '20px' }"
            />
            <div class="dialog-content-header-box" @click="handleMonthChange('prev')">
              <span> {{ '<' }} </span>
            </div>
            <cockpitSelect
              v-model="selectParams.month"
              :style="{ width: px2vw(80),height: px2vh(32), marginLeft: '4px' }"
              label-key="text"
              :options="monthOptions"
              :defaultValue="selectParams.month"
              labelKey="cname"
              valueKey="id"
            />
            <div class="dialog-content-header-box" :style="{marginRight: px2vw(8)}" @click="handleMonthChange('next')">
              <span>></span>
            </div>
            <div class="dialog-content-header-legend" 
              v-for="(legend,index) in legendArr" 
              :key="index"
              @click.stop="handleLegendClick(legend)"
            >
              <span :style="{background: legend.color}"/>
              <span :style="{color: hideLegend.includes(legend.text) ? '#00000040' : ''}">{{ legend.text | titleFormat }}</span>
              <el-tooltip v-if="legend.text === '对标企业新券发行'" effect="dark" placement="bottom-start" :teleported="false">
                <div style="display: flex;align-items: center">
                  <img src="@/assets/cockpit/info_circle.png" alt="" :style="{ width: px2vw(16),height: px2vh(17) }">
                </div>
                <div slot="content">
                  <p style="margin-bottom: 0px">仅展示对标分析菜单“我的对标”分组下企业新发行债券</p>
                </div>
              </el-tooltip>
            </div>
          </div>
          <div class="dialog-content-calendar">
            <div class="dialog-content-calendar-weekend">
              <div class="dialog-content-calendar-weekend-day" v-for="(week, index) in weekDays" :key="index">
                星期{{ week }}
              </div>
            </div>
            <div class="dialog-content-calendar-dates">
              <div class="block" v-for="(day,index) in calendarData" :key="index">
                <div 
                  class="dialog-content-calendar-dates-date" 
                  :class="computedClass(day)"
                  @click.stop="openCalendarDetail(day)"
                >
                  <p class="dialog-content-calendar-dates-date-text">{{ day.date | dayFormat}}</p>
                  <div class="dialog-content-calendar-dates-date-data" v-for="(data,dIndex) in day.data" :key="dIndex">
                    <template v-if="!data.hide">
                      <div class="dialog-content-calendar-dates-date-data-title">
                        <span :style="{backgroundColor: data.color}"/>
                        <span :style="{color: data.color}">{{ data.title | titleFormat }}</span>
                      </div>
                      <p v-for="(text,tIndex) in data.innerData" :key="tIndex">{{ text }}</p>
                    </template>
                  </div>
                  <div class="hide"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import cockpitSelect from '../../components/cockpit-select.vue'
import cockpitYearSelect from '../../components/cockpit-year-select.vue'
import { cockpitGetCalendarList } from "@/api/cockpit/cockpit"
import moment from 'moment'
import { px2vw, px2vh } from '../../utils/portcss'
import store from '@jupiterweb/store'
export default {
  name: 'CalendarCenterDate',
  components: {
    cockpitSelect,
    cockpitYearSelect
  },
  data() {
    return {
      dialogVisible: false,
      weekDays: ['一', '二', '三', '四', '五', '六', '日'],
      monthOptions:[
        {
          cname: '1月',
          id: '01'
        },
        {
          cname: '2月',
          id: '02'
        },
        {
          cname: '3月',
          id: '03'
        },
        {
          cname: '4月',
          id: '04'
        },
        {
          cname: '5月',
          id: '05'
        },
        {
          cname: '6月',
          id: '06'
        },
        {
          cname: '7月',
          id: '07'
        },
        {
          cname: '8月',
          id: '08'
        },
        {
          cname: '9月',
          id: '09'
        },
        {
          cname: '10月',
          id: '10'
        },
        {
          cname: '11月',
          id: '11'
        },
        {
          cname: '12月',
          id: '12'
        },
      ],
      selectParams: {
        year: '',
        month: ''
      },
      legendArr:[
        {
          text:"债券付息兑付",
          color: "#5B8FF9",
          handleData: arr => {
            return arr.map((item) => {
              const textArr = [];
              ['sInfoName', 'bIssueAmountact', 'couponRate', 'term'].forEach(key=>{
                if(item[key]){
                  textArr.push(item[key])
                }
              })
              return textArr.join(",")
            })
          }
        },
        {
          text:"我司新券发行",
          color: "#269A99",
          handleData: arr => {
            return arr.map((item) => {
              const textArr = [];
              ['sInfoName', 'bIssueAmountact', 'latestCouponrate', 'term'].forEach(key=>{
                if(item[key]){
                  textArr.push(item[key])
                }
              })
              return textArr.join(",")
            })
          }
        },
        {
          text:"对标企业新券发行",
          color: "#E1B01E",
          handleData: arr => {
            return arr.map((item) => {
              const textArr = [];
              ['sInfoName', 'bIssueAmountact', 'latestCouponrate', 'term'].forEach(key=>{
                if(item[key]){
                  textArr.push(item[key])
                }
              })
              return textArr.join(",")
            })
          }
        },
        {
          text:"行权日",
          color: "#E8684A"
        },
        {
          text:"持有人会议",
          color: "#9C75DD"
        },
      ],
      calendarData:[],
      today:'',
      hideLegend: []
    }
  },
  filters:{
    dayFormat(val){
      return moment(val).format('DD')
    },
    titleFormat(val){
      if(store.getters.sysVersion === 'group' && val === '我司新券发行'){
        return '集团新券发行'
      }else{
        return val
      }
    }
  },
  watch:{
    selectParams:{
      deep:true,
      handler(){
        this.initCalendar()
        this.getCalendarDataApi()
      }
    }
  },
  methods: {
    px2vw,
    px2vh,
    /**
     * 打开弹框
     */
    open(){
      this.initSelectParams()
      this.initToday()
      this.dialogVisible = true
    },
    /**
     * 关闭弹框
     */
    handleClose() {
      this.dialogVisible = false
    },
    /**
     * 计算类名
     */
    computedClass(day){
      let str = ''
      if(day.date === this.today){
        str += ' today'
      }
      if(day.disabled){
        str += ' disabled'
      }else{
        str += ' normal'
      }
      return str
    },
    /**
     * 初始化今天的日期
     */
    initToday() {
      this.today = moment(+new Date()).format('YYYY-MM-DD')
    },
    /**
     * 初始化筛选条件
     */
    initSelectParams() {
      const today = +new Date()
      const year = moment(today).format('YYYY')
      const month = moment(today).format('MM')
      this.selectParams = {
        year,
        month
      }
      this.initCalendar()
      this.getCalendarDataApi()
    },
    /**
     * 生成日历
     */
    initCalendar() {
      this.calendarData = []
      const yearMonth = this.selectParams.year + this.selectParams.month
      const startOfMonthTime = +new Date(moment(yearMonth).startOf('month'));
      const endOfMonthTime = +new Date(moment(yearMonth).endOf('month'));
      // 补全前置日期
      const firstWeek = moment(startOfMonthTime).isoWeekday()
      for(let i = firstWeek - 1; i >= 1; i--){
        this.calendarData.push({
          date: moment(startOfMonthTime - i * 60 * 60 * 24 * 1000).format("YYYY-MM-DD"),
          disabled:true
        })
      }
      for(let i = startOfMonthTime; i <= endOfMonthTime; i += 60 * 60 * 24 * 1000){
        this.calendarData.push({
          date: moment(i).format("YYYY-MM-DD")
        })
      }
      const lastWeek = moment(endOfMonthTime).isoWeekday()
      for(let i = lastWeek + 1; i <= 7; i++){
        this.calendarData.push({
          date: moment(endOfMonthTime + (i - lastWeek) * 60 * 60 * 24 * 1000).format("YYYY-MM-DD"),
          disabled:true
        })
      }
    },
    /**
     * 获取日历数据
     */
    async getCalendarDataApi(){
      const data = await cockpitGetCalendarList('',this.selectParams.year + this.selectParams.month)
      this.calendarData = this.calendarData.map(item=>{
        if(!item.disabled){
          const value = data[item.date]
          let saveData = []
          for(let key in value){
            if(typeof value[key] === 'object'){
              if(value[key] instanceof Array && value[key].length > 0){
                saveData.push({
                  title:key,
                  color:this.legendArr.find(item=>item.text === key).color,
                  innerData: this.legendArr.find(item=>item.text === key).handleData(value[key]),
                  hide:false
                })
              }
            }else if(typeof value[key] === 'boolean' && value[key]){
              saveData.push({
                title:key,
                color:this.legendArr.find(item=>item.text === key).color,
                hide:false
              })
            }
          }
          this.$set(item,"data",saveData)
        }
        return item
      })
    },
    /**
     * 打开日历详情弹框
     */
    openCalendarDetail(day){
      this.$emit("handleOpen",day.date)
    },
    /**
     * 点击图例关闭展示
     */
    handleLegendClick(legend) {
      if(!this.hideLegend.includes(legend.text)){
        this.calendarData = this.calendarData.map(item=>{
          if(item.data && item.data.length > 0){
            item.data = item.data.map(inner=>{
              if(legend.text === inner.title){
                inner.hide = true
              }else{
                inner.hide = false
              }
              return inner
            })
          }
          return item
        })
        this.hideLegend.push(legend.text)
      }else{
        this.calendarData = this.calendarData.map(item=>{
          if(item.data && item.data.length > 0){
            item.data = item.data.map(inner=>{
              if(legend.text === inner.title){
                inner.hide = false
              }
              return inner
            })
          }
          return item
        })
        this.hideLegend = this.hideLegend.filter(item=>item !== legend.text)
      }
    },
    /**
     * 切换月份
     */
    handleMonthChange(type){
      const index = this.monthOptions.findIndex(item=>item.id === this.selectParams.month)
      if(type === 'prev'){
        if(index > 0){
          this.selectParams.month = this.monthOptions[index - 1].id
        }else {
          this.selectParams.month = '12'
        }
      }else{
        if(index < 11){
          this.selectParams.month = this.monthOptions[index + 1].id
        }else{
          this.selectParams.month = '01'
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~assets/styles/portcss.scss';
::v-deep .el-dialog__header {
  display: none;
}
::v-deep .el-dialog__body {
  padding: 0px;
}
::v-deep .el-input--medium .el-input__inner {
  height: vh(32);
  line-height: vh(32);
}
.dialog {
  width: 100%;
  height: vh(794);
  background: #FBFBF9;
  border-radius: 2px;
  &-title {
    width: 100%;
    height: vh(56);
    background-image: url('../../../../assets/cockpit/calendarBac.png');
    background-size: 100% 100%;
    border-radius: 2px 2px 0px 0px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px 25px;
    span {
      height: vh(28);
      font-family: MicrosoftYaHeiSemibold;
      font-size: vh(20);
      color: #ffffff;
      line-height: vh(28);
    }
  }
  &-content {
    padding: vh(14) vw(6) vh(16) vw(16);
    width: 100%;
    height: vh(708);
    box-shadow: inset 0px 0px 48px 0px rgba(140, 214, 255, 0.04);
    backdrop-filter: blur(2px);
    & > div {
      width: 100%;
      background: rgba(255,255,255,0.5);
      box-shadow: 0px 4px 20px -2px rgba(0,0,0,0.06);
      backdrop-filter: blur(0px);
    }
    &-header {
      width: 100%;
      height: vh(72);
      display: flex;
      align-items: center;
      padding: vh(16) vw(16);
      box-shadow: inset 0px -1px 0px 0px #EAE9E9;
      &-box {
        width: vw(32);
        height: vh(32);
        background: #FFFFFF;
        border-radius: 2px;
        border: 1px solid #CCCCCC;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        margin-left: 4px;
      }
      &-legend {
        display: flex;
        align-items: center;
        gap: vw(4);
        margin-left: vw(16);
        cursor: pointer;
        &>span:nth-of-type(1){
          width: 6px;
          height: 6px;
          border-radius: 50%;
        }
        &>span:nth-of-type(2){
          height: vh(20);
          font-family: MicrosoftYaHei;
          font-size: vh(12);
          color: #000000;
          line-height: vh(20);
        }
      }
    }
    &-calendar {
      width: 100%;
      height: vh(636);
      overflow-y: auto;
      padding: vh(16) vw(16) vh(10);
      &-weekend {
        width: 100%;
        height: vh(38);
        display: flex;
        gap: vw(4);
        &-day {
          width: vw(148);
          height: vh(38);
          font-family: MicrosoftYaHeiSemibold;
          font-size: vh(16);
          color: #000000;
          line-height: vh(22);
          padding: vh(8) vw(0) vh(8) vw(12);
        }
      }
      &-dates {
        width: 100%;
        height: vh(608);
        display: flex;
        flex-wrap: wrap;
        column-gap: vw(3);
        row-gap: vh(4);
        align-content: flex-start;
        p {
          margin-bottom: 0;
        }
        .block {
          width: vw(148);
          height: vh(109);
          position: relative;
        }
        &-date {
          width: vw(148);
          height: vh(109);
          background: linear-gradient(180deg, #ffe8d1 0%, #faf8f6 100%);
          box-shadow: 0px 4px 20px -2px rgba(0, 0, 0, 0.06);
          border: vh(2) solid transparent;
          border-radius: vh(4);
          background-clip: padding-box, border-box;
          background-origin: padding-box, border-box;
          background-image: linear-gradient(180deg, #ffe8d1 0%, #faf8f6 100%),
            radial-gradient(rgba(255, 255, 255, 1), rgba(255, 255, 255, 0.5));
          padding: vh(8) vw(12) vh(12);
          overflow: hidden;
          cursor: pointer;
          &-text {
            width: vw(24);
            height: vh(24);
            padding-top: vh(3);
            padding-left: vw(5);
            font-family: MicrosoftYaHeiSemibold;
            font-size: vh(14);
            color: #000000;
            line-height: vh(19);
          }
          &-data {
            margin-top: vh(8);
            &-title {
              display: flex;
              gap: vw(4);
              align-items: center;
              & > span:nth-of-type(1) {
                width: 5px;
                height: 5px;
                background: #5b8ff9;
                border: 1px solid #ffffff;
                border-radius: 50%;
              }
              & > span:nth-of-type(2) {
                height: vh(19);
                font-family: MicrosoftYaHei;
                font-size: vh(13);
                color: #000000;
                line-height: vh(19);
              }
            }
            & > p {
              margin-top: vh(4);
              height: vh(34);
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              font-size: vh(12);
              color: #000000;
              line-height: vh(17);
            }
          }
        }
        .normal:hover {
          position: absolute;
          left: 0px;
          top: 0px;
          width: vw(148);
          height: vh(193);
          background: linear-gradient(180deg, #ffe8d1 0%, #faf8f6 100%);
          box-shadow: 0px 4px 20px -2px rgba(0, 0, 0, 0.06);
          border: vh(2) solid transparent;
          border-radius: vh(4);
          background-clip: padding-box, border-box;
          background-origin: padding-box, border-box;
          background-image: linear-gradient(180deg, #ffe8d1 0%, #faf8f6 100%),
            radial-gradient(rgba(255, 255, 255, 1), rgba(255, 255, 255, 0.5));
          overflow-y: scroll;
          z-index: 2;
        }
        .today {
          background: linear-gradient( 180deg, #FFE8D1 0%, #FAF8F6 100%);
          box-shadow: 0px 4px 20px -2px rgba(0,0,0,0.06);
          border-radius: 4px;
          border: 1px solid #FF8E2B;
          backdrop-filter: blur(0px);
          .dialog-content-calendar-dates-date-text{
            width: vw(24);
            height: vh(24);
            padding: 0;
            background: #FF8E2B;
            font-size: vh(14);
            line-height: vh(24);
            text-align: center;
            border-radius: 50%;
            color: #FFFFFF;
          }
        }
        .today::after{
          content:"今";
          display: block;
          position: absolute;
          right: 0px;
          top: 0px;
          width: vw(24);
          height: vh(24);
          background: linear-gradient( 180deg, #FFDBA4 0%, #FCB03B 100%);
          border-radius: 0px 3px 0px 3px;
          font-size: vh(14);
          line-height: vh(24);
          text-align: center;
          color: #FFFFFF;
        }
        .disabled {
          background: linear-gradient(180deg, #FFE8D1 0%, #FAF8F6 100%);
          box-shadow: 0px 4px 20px -2px rgba(0, 0, 0, 0.06);
          border: vh(2) solid transparent;
          border-radius: vh(4);
          background-clip: padding-box, border-box;
          background-origin: padding-box, border-box;
          background-image: linear-gradient(180deg, #FFE8D1 0%, #FAF8F6 100%),
            radial-gradient(rgba(255, 255, 255, 1), rgba(255, 255, 255, 0.5));
          opacity: 0.4;
          .dialog-content-calendar-dates-date-text{
            color: rgba(0,0,0,0.25) !important;
          }
        }
      }
    }
  }
}
</style>
