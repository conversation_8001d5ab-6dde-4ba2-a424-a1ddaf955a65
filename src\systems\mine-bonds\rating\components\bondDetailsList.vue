<!-- 债券明细列表 -->
<template>
  <div class="bond-details-list">
    <jr-decorated-table
      stripe
      :date="1662022042"
      :params="params"
      custom-id="57c8079478ea415d84dff7de35939c2b"
      :row-click="clickFunc"
      :row-dbl-click="dblClickFunc"
      :menuinfo="{ moduleid: 'xxxx' }"
    />
  </div>
</template>

<script>
export default {
  props: {
    params: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
    }
  },
  methods: {}
}
</script>
<style lang="scss">
.bond-details-list {
	// margin-top: 10px;
	padding: 0;
	background: #fff;

	.bondDetailsListTitle {
    font-size: var(--el-font-size-base) !important;
		color: #303133;
		font-weight: 700;
		padding-bottom: 6px;
		border-bottom: 1px solid #EBEEF5;
	}
}
</style>
