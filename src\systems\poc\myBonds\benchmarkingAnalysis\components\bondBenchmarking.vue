<!-- 债券对标 -->
<template>
  <div class="bondBenchmarking">
    <div class="bondBenchmarkingTitle">
      债券对标
      <span class="radio">
        <el-radio-group v-model="searchForm.benchmarkingType">
          <el-radio :label="0">存续对标</el-radio>
          <el-radio :label="1">发行对标</el-radio>
        </el-radio-group>
      </span>
    </div>
    <div class="searchForm">
      <el-form :model="searchForm" label-width="90px">
        <jr-form-item label="对标企业" class="formItem">
          <jr-combobox
            v-model="searchForm.enterprise"
            placeholder="请选择"
            clearable
            filterable
            :data="enterpriseList"
            option-value="id"
            option-label="text"
          />
        </jr-form-item>
        <jr-form-item label="债券类型" class="formItem">
          <jr-combobox
            v-model="searchForm.bondType"
            placeholder="请选择"
            clearable
            filterable
            :data="bondTypeList"
            option-value="id"
            option-label="text"
          />
        </jr-form-item>
        <el-button type="primary" class="btn" @click="queryData">查询</el-button>
      </el-form>
    </div>
    <div class="content">
      <div class="leftContent">
        <TemplateModule v-if="searchForm.benchmarkingType === 0" chart-type="BAR" chart-seq="da7e0118989e408f8e22b2efd6aa2910" />
        <TemplateModule v-else chart-type="BAR" chart-seq="c8c6672635ad4651a8ee42d4105be31a" />
      </div>
      <div class="rightContent">
        <jr-decorated-table
          style="height: 100%"
          custom-id="6ab91cf1ca4244f090dbf41c319a9ba3"
          v-bind="{...$attrs, ...$props}"
          :custom-render="customRender"
        />
      </div>
    </div>
    <DetailModal v-bind="{...modalConfig}" @setVisible="setVisible" />
  </div>
</template>

<script>
import DetailModal from './detailModal.vue'
import TemplateModule from '@jupiterweb/components/template-module'
export default {
  components: {
    TemplateModule, DetailModal
  },
  data() {
    return {
      modalConfig: {
        isVisible: false,
        itemData: {}
      },
      searchForm: {
        enterprise: '',
        bondType: '',
        benchmarkingType: 0
      },
      enterpriseList: [
        { id: '雪佛兰', text: '雪佛兰' },
        { id: '英国石油', text: '英国石油' },
        { id: '道达尔', text: '道达尔' }
      ],
      bondTypeList: [
        { id: '城投债', text: '城投债' }
      ],
      customRender: {
        fxr: (h, { row }) => {
          return <el-link type='primary' onClick={this.rowClick.bind(this, row)}>{row.fxr}</el-link>
        }
      }
    }
  },
  methods: {
    queryData() {
      //
    },
    rowClick(row) {
      Object.assign(this.modalConfig, {
        isVisible: true,
        itemData: row
      })
    },
    setVisible() {
      this.modalConfig.isVisible = false
    }
  }
}
</script>
<style lang="scss">
.bondBenchmarking {
    margin-top: 10px;
    padding: 6px 10px 6px 10px;
    background: #fff;
    .radio {
        display: inline-block;
        margin-left: 30px;
    }
    .el-table {
        height: 300px !important;
    }
    .bondBenchmarkingTitle {
        font-size: 16px;
        color: #303133;
        font-weight: 700;
        padding-bottom: 6px;
        border-bottom: 1px solid #EBEEF5;
    }
    .formItem {
        display: inline-block;
        width: 300px
    }
    .btn {
        display: inline-block;
        margin-left: 10px;
    }
    .searchForm {
        padding: 10px 0 10px 0;
        background: #fff;
        margin-bottom: 10px;
        .el-tag {
            margin: 10px;
        }
    }
    .content {
        display: flex;
        .leftContent {
            flex: 1;
        }
        .rightContent {
            flex: 1;
        }
    }
}
</style>

