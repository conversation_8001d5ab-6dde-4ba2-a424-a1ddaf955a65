<template>
  <div class="page-market-rate--chart">
    <div class="page-market-rate--chart-search">
      <label>日期区间</label>
      <el-date-picker
        v-model="form.dateRange"
        type="daterange"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        @change="form.interval = ''"
      />
      <jr-radio-group v-model="form.interval" :data="dateList" class="width-auto" @change="changeInterval" />
    </div>
    <div class="page-market-rate--chart-content">
      <template-module
        chart-seq="a806582186ee45dd92829a27e3a4c150"
        chart-type="LINE"
        :params="chartParams"
      />
    </div>
  </div>
</template>

<script>
import TemplateModule from '@jupiterweb/components/template-module'
import { mapGetters } from 'vuex'
import moment from 'moment'
export default {
  components: {
    TemplateModule
  },
  props: {
    params: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  data() {
    const today = moment(this.systemTime).format('YYYY-MM-DD')
    const yearsAgo = moment(new Date(today)).subtract(1, 'years').format('YYYY-MM-DD')
    return {
      form: {
        dateRange: [yearsAgo, today],
        interval: '1Y'
      },
      dateList: [
        {
          text: '近一年',
          value: '1Y'
        },
        {
          text: '近半年',
          value: '6M'
        },
        {
          text: '近三月',
          value: '3M'
        },
        {
          text: '近一月',
          value: '1M'
        }
      ]
    }
  },
  computed: {
    ...mapGetters(['systemTime']),
    today() {
      return moment(this.systemTime).format('YYYY-MM-DD')
    },
    chartParams() {
      return {
        ...this.params,
        ...this.form
      }
    }
  },
  methods: {
    changeInterval(v) {
      const map = {
        '1Y': [1, 'years'],
        '6M': [6, 'months'],
        '3M': [3, 'months'],
        '1M': [1, 'months']
      }
      this.form.dateRange = [
        moment(this.systemTime)
          .subtract(...map[v])
          .format('YYYY-MM-DD'),
        this.today
      ]
    }
  }
}
</script>

<style lang="scss" scoped>
.page-market-rate--chart {
  padding: 0 12px;
  height: 100%;
}
.page-market-rate--chart-search {
  display: flex;
  column-gap: 10px;
  align-items: center;
  .el-date-editor {
    max-width: 300px;
  }
}
.page-market-rate--chart-content {
  height: calc(100% - 40px);
  padding-top: 10px;
}
</style>
