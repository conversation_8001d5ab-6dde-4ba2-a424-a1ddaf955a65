<template>
  <!-- 司法诉讼 -->
  <div class="judicial-trial">
    <div class="page-btn-list">
      <el-button
        v-for="(item, index) in pageList"
        :key="index"
        :type="activePage == item.val ? 'primary' : ''"
        @click="changeJudicialPage(item.val)"
      >
        {{ item.name }}
      </el-button>
    </div>
    <component :is="activePage" />
  </div>
</template>
<script>
import project from '../judicial-page/project.vue'
import courtSession from '../judicial-page/courtSession.vue'
import lawsuit from '../judicial-page/lawsuit.vue'
export default {
  components: {
    project,
    courtSession,
    lawsuit
  },
  props: {},
  data() {
    return {
      activePage: 'project',
      pageList: [
        {
          name: '立项变更',
          val: 'project'
        },
        {
          name: '开庭公告',
          val: 'courtSession'
        },
        {
          name: '法律诉讼',
          val: 'lawsuit'
        }
      ]
    }
  },
  methods: {
    handleClick() {
      //
    },
    changeJudicialPage(val) {
      this.activePage = val
    }
  }
}
</script>
<style lang="scss">
.judicial-trial {
  height: calc(100% - 56px);

  .page-btn-list {
    padding: 16px 0 0 16px;
    height: 56px;
    background: #ffffff;

    .el-button {
      width: 104px;
      height: 40px;
      font-size: var(--el-font-size-large);
    }
  }
}
</style>
