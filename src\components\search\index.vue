<template>
  <el-form ref="form" label-width="70px" :model="form" @submit.native.prevent>
    <slot />
    <el-row type="flex" :gutter="20">
      <el-col :span="8"><slot name="search-left" /></el-col>
      <el-col :span="8" />
      <el-col :span="8" style="text-align: right">
        <slot name="btns-left" />
        <ws-button type="primary" @click="submit">查询</ws-button>
        <ws-button @click="reset">重置</ws-button>
        <slot name="btns-right" />
      </el-col>
    </el-row>
  </el-form>
</template>

<script>
import wsButton from '@/components/ws-button'
export default {
  components: {
    wsButton
  },
  props: {
    form: {
      type: Object,
      default: () => ({})
    },
    formOld: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {}
  },
  mounted() {},
  methods: {
    submit() {
      this.$emit('submit')
    },
    reset() {
      Object.keys(this.form).forEach((key) => {
        this.form[key] = this.formOld[key]
      })
      this.$emit('reset')
    }
  }
}
</script>

<style lang="scss" scoped>
.el-col-8 {
  padding: 0;
  position: relative;
}
::v-deep .jr-radio-group .el-radio {
  min-width: 88px;
  margin: 0;
}
::v-deep .jr-checkbox-group .el-checkbox {
  min-width: 88px;
  padding-top: 1px;
  margin: 0;
}
::v-deep .el-input__inner,
.jr-radio-group {
  height: 32px !important;
  line-height: 32px !important;
}
::v-deep .el-checkbox-group {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}
</style>
