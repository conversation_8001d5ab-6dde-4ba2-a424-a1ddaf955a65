<template>
  <div class="bazaar" style="padding: 0 16px !important;">
    <h3 class="card-title">上海银行间同业拆放利率（SHIBOR）</h3>
    <jr-layout-horizontal>
      <template slot="left">
        <card-list ref="cardList" v-bind="{ isType, params, mainTitle, subtitle }" :on-active="onActive" />
      </template>
      <template slot="right">
        <form-list v-bind="{ submit, reset, form }" :term-flag="false" :tips-flag="false">
          <jr-form-item label="发行期限">
            <jr-checkbox-group
              v-model="form.bAnalCurvetermList"
              :data="dictList"
              :min="0"
              :max="10"
              option-label="cnname"
              option-value="itemcode"
              @change="checkChange"
            />
          </jr-form-item>
        </form-list>
        <chart
          :options="chartOptions"
          :name="nameFn()"
        />
      </template>
    </jr-layout-horizontal>
  </div>
</template>

<script>
import formList from '../../components/form-list.vue'
import chart from '../../components/chart.vue'
import * as op from '../../components/chartParams'
import { date } from '@/utils/common'
import { GetInfoFn } from '@jupiterweb/utils/api'
import cardList from '../../components/card-list.vue'
export default {
  name: 'NationalDebt',
  provide() {
    return { parant: this }
  },
  components: {
    formList,
    chart,
    cardList
  },
  data() {
    return {
      paramsDatas: {},
      mainTitle: '最新利率(%)',
      subtitle: '最新变动BP',
      chartOptions: op.options,
      list: [],
      dictList: [],
      isType: '1M',
      form: {
        bAnalCurvetermList: ['SHIBOR1M.IR'],
        date: [date().subtract(1, 'y'), date().now()],
        radioDate: 12
      },
      params: {
        ccid: '5fc2fa869a0447d096a5457963787610',
        ownedModuleid: '708631605142536192'
      },
      downFileUrl: '/web/Shibor/rateChartExport'
    }
  },
  created() {
    this.getDictList()
    this.echartsData()
  },
  methods: {
    // 品种字典
    async getDictList() {
      const data = await GetInfoFn('DICT', 'SHIBOR_YEAR')
      this.dictList = data || []
    },
    // echarts  接口
    async echartsData() {
      this.paramsDatas = {
        ccid: '12ef221ca55f42e396528c6beecce30a', ownedModuleid: '708631605142536192',
        ...this.form
      }
      const chartOptions = await op.getchartData(this.paramsDatas, '/web/Shibor/rateChart', { smooth: true })
      this.chartOptions = chartOptions
    },
    onActive(item) {
      const itemcode = this.dictList.length > 0 ? this.dictList.find(k => k.cnname === item.bAnalCurveterm).itemcode : ''
      this.form.bAnalCurvetermList = itemcode ? [itemcode] : []
      this.echartsData()
    },
    submit() {
      this.echartsData()
    },
    reset() {
      this.echartsData()
      this.$refs.cardList.setCardInd()
    },
    // 发行期限值改变 清除卡片选中效果
    checkChange() {
      this.$nextTick(() => this.$refs.cardList.cardInd = null)
    },
    nameFn() {
      const d = this.form.date.join().replaceAll('-', '').replace(',', '-')
      return `上海银行间同业拆放利率_${d}`
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/assets/styles/market.scss'
</style>
