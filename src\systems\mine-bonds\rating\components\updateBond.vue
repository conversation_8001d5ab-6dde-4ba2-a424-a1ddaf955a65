<template>
  <div class="buyerInfo">
    <collapse-panel title="基本信息">
      <div class="collapseBody">
        <jr-form-item-create :validate-rules="validateRules" :data="buyInfo" :model="data" />
      </div>
    </collapse-panel>
  </div>
</template>

<script>
import { getModalProps, getModalComputed } from '@/systems/mixins'
export default {
  mixins: [getModalProps, getModalComputed],
  props: {
    itemData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      baseFileds: []
    }
  },
  created() {
    this.itemData && this.$emit('setFieldsValue', {
      ...this.itemData
    })

    this.initData()
  },
  methods: {

    initData() {
      this.buyInfo = [
        {
          title: '债券代码',
          prop: 'zqdm',
          required: true
        },
        {
          title: '债券简称',
          prop: 'zqjc',
          required: true
        },
        {
          title: '中俵隐含评级',
          prop: 'zzyhpj',
          type: 'select',
          required: true,
          options: [
            { text: 'AAAA', value: 'AAAA' },
            { text: 'AAA', value: 'AAA' }
          ]
        },
        {
          title: '发行期限',
          prop: 'fxqx',
          type: 'select',
          required: true,
          options: [
            { text: '270D', value: '1' },
            { text: '211D', value: '2' }
          ]
        },
        {
          title: '剩余期限',
          prop: 'syqx',
          type: 'select',
          required: true,
          options: [
            { text: '270D', value: '1' },
            { text: '211D', value: '2' }
          ]
        },
        {
          title: '票面利率',
          prop: 'pmlv',
          type: 'rate',
          required: true
        },
        {
          title: '票面与首次估值利差(BP)',
          prop: 'gzlc',
          type: 'amount',
          required: true
        },
        {
          title: '行权估值',
          prop: 'xqgz',
          type: 'rate',
          required: true
        },
        {
          title: '到期估值',
          prop: 'dqgz',
          type: 'rate',
          required: true
        },
        {
          title: '最新YTM',
          prop: 'zxYTM',
          type: 'rate',
          required: true
        },
        {
          title: '规模(亿)',
          prop: 'gm',
          type: 'hundredMillion',
          required: true
        },
        {
          title: '余额(亿)',
          prop: 'ye',
          type: 'hundredMillion',
          required: true
        }
      ]
    },
    async preSaveHandel() {
      return true
    }

  }
}
</script>

<style lang="scss">
.buyerInfo {
    .textarea {
        width: 100% !important;
        .el-form-item__label {
            width: 9% !important;
        }
        .el-form-item__content {
            width: 100% !important;
        }
    }
}
</style>
