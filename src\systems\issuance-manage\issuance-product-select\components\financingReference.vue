<template>
  <div class="financingReference">
    <div class="financingReference-selectArea public-table-search-container">
      <div class="financingReference-selectArea-select">
        <label>债项评级</label>
        <jr-combobox
          v-model="bond_rating"
          placeholder="请选择债项评级"
          :data="debtRatingOptions"
          :popper-append-to-body="false"
          option-value="itemcode"
          option-label="cnname"
        />
      </div>
      <div class="financingReference-selectArea-select" style="margin-left: 30px">
        <label>发行期限</label>
        <inputSelectUnit ref="inputSelectUnit" v-model="info_term" />
      </div>
      <div class="financingReference-selectArea-select" style="margin-left: 21px">
        <label>债券类型</label>
        <el-cascader
          v-model="sectype"
          :props="{
            multiple: true
          }"
          :show-all-levels="false"
          collapse-tags
          :options="bondTypeOptions"
          placeholder="请选择债券类型"
        />
      </div>
      <div class="financingReference-selectArea-buttons">
        <el-button type="primary" @click.stop="confirmSearch">参考信息</el-button>
        <el-button @click.stop="resetSearch">重置</el-button>
      </div>
    </div>
    <div class="public-title-container" style="padding-left: 16px">
      <span>我司相近期限债券信息</span>
      <el-tooltip placement="right" effect="dark">
        <jr-svg-icon icon-class="info-circle" />
        <div slot="content" style="max-width: 300px">
          展示剩余期限与选择的发行期限差值的绝对值在半年内(180天)最接近的对应的我司债券。
        </div>
      </el-tooltip>
    </div>
    <jr-decorated-table
      :params="{
        ...confirmParams
      }"
      custom-id="86a8a2e5ff8a42099c2579a10d98183d"
      style="height: 474px;"
      :default-page-size="10"
      :initPagination="{
        pageSizeOptions: [10, 20, 50, 100]
      }"
      :custom-render="customRender"
      v-bind="{
        ...$props
      }"
      :menuinfo="{
        ownedModuleid: '1315721394511069184'
      }"
      key="86a8a2e5ff8a42099c2579a10d98183d"
    />
    <div class="public-title-container" style="padding-left: 16px">
      <span>对标企业相近期限债券信息</span>
      <el-tooltip placement="right" effect="dark">
        <jr-svg-icon icon-class="info-circle" />
        <div slot="content" style="max-width: 300px">
          优先展示对标企业近三月内发行的，同选择的发行期限差值在30天内的债券:其次展示同地区、主体性质企业发行债券。
        </div>
      </el-tooltip>
    </div>
    <div class="financingReference-button public-table-search-container">
      <el-button type="primary" @click="goToBenchmark">对标企业配置</el-button>
    </div>
    <jr-decorated-table
      :params="{
        ...confirmParams
      }"
      style="height: 516px; padding-top: 8px; background-color: #fff"
      custom-id="e75ceb4056e54cfd94878b3bf826f0b2"
      :default-page-size="10"
      :initPagination="{
        pageSizeOptions: [10, 20, 50, 100]
      }"
      v-bind="{
        ...$props
      }"
      :menuinfo="{
        ownedModuleid: '1315721394511069184'
      }"
      :custom-render="componenyCustomRender"
      key="e75ceb4056e54cfd94878b3bf826f0b2"
    />
    <div
      style="
        display: flex;
        padding-top: 8px !important;
        padding-bottom: 8px !important;
        background-color: #fff;
        padding-right: 16px;
      "
    >
      <span style="width: 270px">{{ titleData.curvename }}</span>
      <span>{{ titleData.rating }}{{ titleData.adjustInputTerm ? `,${titleData.adjustInputTerm}` : '' }}</span>
      <div class="financingReference-title-date">
        <label>日期区间</label>
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          unlink-panels
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd"
          :clearable="false"
        />
      </div>
    </div>
    <div style="width: 100%; height: 477px; background: #ffffff; padding: 0px 32px">
      <echarts v-if="titleData.showCharts" :options="options" style="width: 100%; height: 100%" />
      <div v-else style="width: 100%; height: 100%"><jr-empty /></div>
    </div>
    <detail ref="detail" />
  </div>
</template>

<script>
import echarts from '@jupiterweb/components/echarts'
import { issuanceYieldCurve } from '@/api/issuance/issuance'
import moment from 'moment'
import inputSelectUnit from '@/components/input-select-unit'
import { GetComboboxList } from '@/api/home'
import { issuanceQueryBondTypeList } from '@/api/issuance/issuance'
import IssuancePopover from '../../components/issuance-popover.vue'
import detail from '@/systems/bond-market-overview/publish-query/dialog/detail.vue'
import { ConvertAmount } from '@jupiterweb/utils/common.js'

const BONDRATING = 'BONDRATING' // 债项评级字典项
export default {
  name: 'FinancingReference',
  components: {
    echarts,
    inputSelectUnit,
    IssuancePopover,
    detail
  },
  data() {
    return {
      // 我司相近期限债券信息参数数据
      ourCompanyRecentBonds: {},
      dateRange: [],
      // 债项评级
      bond_rating: '',
      debtRatingOptions: [],
      // 发行期限
      info_term: '',
      // 债券类型
      sectype: [],
      bondTypeOptions: [],
      confirmParams: {},
      echartDataParmas: {
        tradeDtBegin: '',
        tradeDtEnd: '',
        info_term: '',
        unit: ''
      },
      options: {
        tooltip: {
          trigger: 'axis'
        },
        grid: {
          top: 40,
          left: 20,
          right: 20,
          bottom: 40,
          containLabel: true
        },
        legend: {
          show: false
        },
        toolbox: {
          show: false
        },
        xAxis: [
          {
            type: 'category',
            data: [],
            axisPointer: {
              type: 'shadow'
            },
            axisLine: {
              lineStyle: {
                color: 'rgba(28,31,35,0.8)'
              }
            }
          }
        ],
        yAxis: [
          {
            type: 'value',
            name: '单位 %',
            nameTextStyle: {
              color: '#86909C'
            },
            axisLabel: {
              formatter: '{value}',
              textStyle: {
                color: '#86909C'
              }
            },
            axisLine: {
              lineStyle: {
                color: '#1c1f23'
              }
            },
            splitLine: {
              lineStyle: {
                color: '#e6e8ea',
                type: 'dashed'
              }
            }
          }
        ],
        series: [
          {
            type: 'line',
            smooth: true,
            yAxisIndex: 0,
            color: '#5B8FF9',
            tooltip: {
              valueFormatter: function (value) {
                return ConvertAmount('rate', value / 100, 1, 4)
              }
            },
            data: []
          }
        ]
      },
      titleData: {},
      customRender: {
        sInfoWindcode: (h, { row }) => {
          return (
            <span
              style='color:var(--theme--color);cursor:pointer'
              onClick={() => this.showCodeDetail(row.sInfoWindcode)}
            >
              {row.sInfoWindcode}
            </span>
          )
        }
      },
      componenyCustomRender: {
        sinfowindcode: (h, { row }) => {
          return (
            <span
              style='color:var(--theme--color);cursor:pointer'
              onClick={() => this.showCodeDetail(row.sinfowindcode)}
            >
              {row.sinfowindcode}
            </span>
          )
        },
        exercisevaluation: (h, { row }) => {
          return (
            <span style='color:var(--theme--color);cursor:pointer' onClick={() => this.goToValuationDetails('1', row)}>
              {ConvertAmount('HMU', row.exercisevaluation, 1, 4)}
            </span>
          )
        },
        maturityvaluation: (h, { row }) => {
          return (
            <span style='color:var(--theme--color);cursor:pointer' onClick={() => this.goToValuationDetails('2', row)}>
              {ConvertAmount('HMU', row.maturityvaluation, 1, 4)}
            </span>
          )
        }
      }
    }
  },
  created() {
    this.initDateRange()
    this.getDebtRatingDict()
    this.getBondTypeOptionsApi()
  },
  methods: {
    goToValuationDetails(isRight, row) {
      this.$store.dispatch('tagsView/updateCurrentTab', {
        path: '/1361645930918469632',
        // 跳转需要刷新的话 refresh传true
        meta: {
          refresh: true,
          params: {
            comp_name: row.binfoissuer,
            s_info_windcode: row.sinfowindcode,
            s_info_name: row.sinfoname,
            b_is_right: isRight
          }
        }
      })
    },
    ConvertAmount,
    goToBenchmark() {
      this.$store.dispatch('tagsView/updateCurrentTab', {
        path: '/1352315027146489856',
        // 跳转需要刷新的话 refresh传true
        meta: {
          refresh: true,
          params: {}
        }
      })
    },
    /**
     * 初始化日期区间
     */
    initDateRange() {
      const year = moment(+new Date()).format('YYYY')
      this.dateRange = [year + '-01-01', year + '-12-31']
      this.echartDataParmas.tradeDtBegin = this.dateRange[0]
      this.echartDataParmas.tradeDtEnd = this.dateRange[1]
      this.options.xAxis[0].data = [this.dateRange[0], this.dateRange[1]]
    },
    /**
     * 获取票据收益率曲线数据
     */
    async getEchartDataApi() {
      this.echartDataParmas.tradeDtBegin = this.dateRange[0]
      this.echartDataParmas.tradeDtEnd = this.dateRange[1]
      const data = await issuanceYieldCurve(this.echartDataParmas)
      this.titleData = data?.title || {}
      const length = data?.graphic?.length || 0
      this.titleData.showCharts = length > 0
      if (this.titleData.showCharts) {
        this.options.xAxis[0].data = data.graphic.map((item) => item.tradedt)
        this.options.series[0].data = data.graphic.map((item) => item.ytm)
      } else {
        this.options.xAxis[0].data = []
        this.options.series[0].data = []
      }
    },
    /**
     * 获取债项评级字典项
     */
    async getDebtRatingDict() {
      const data = await GetComboboxList([BONDRATING])
      this.debtRatingOptions = data[BONDRATING]
    },
    /**
     * 获取债券类型下拉项参数
     */
    async getBondTypeOptionsApi() {
      const data = await issuanceQueryBondTypeList()
      this.bondTypeOptions = data.reduce((pre, current) => {
        if (current) {
          const index = pre.findIndex((item) => item.value === current.bondTypeCode)
          if (index === -1) {
            pre.push({
              label: current.bondTypeName,
              value: current.bondTypeCode,
              children: []
            })
          } else {
            pre[index].children.push({
              label: current.bondTypeName2,
              value: current.bondTypeCode2
            })
          }
        }
        return pre
      }, [])
    },
    /**
     * 确认搜索
     */
    confirmSearch() {
      if (!this.bond_rating) {
        this.$message.warning('请选择债项评级')
        return
      }
      if (!this.info_term) {
        this.$message.warning('请输入发行期限')
        return
      }
      if (!this.sectype.length) {
        this.$message.warning('请选择债券类型')
        return
      }
      this.confirmParams = {
        bond_rating: this.bond_rating,
        info_term: this.info_term,
        sectype: this.sectype.map((item) => item[item.length - 1]),
        ownedModuleid: '1315721394511069184',
        webTime: new Date().getTime()
      }
      const match = this.info_term.match(/(\d+)([A-Z])/)
      if (match) {
        // 提取数字部分和字母部分
        const number = match[1]
        const letter = match[2]
        // 返回结果
        this.echartDataParmas.info_term = number
        this.echartDataParmas.unit = letter
      }
      this.getEchartDataApi()
    },
    /**
     * 重置搜索
     */
    resetSearch() {
      this.bond_rating = ''
      this.info_term = ''
      this.sectype = []
      this.$refs.inputSelectUnit.reset()
      this.confirmParams = {
        bond_rating: 'AAAAAA',
        info_term: null,
        sectype: [],
        ownedModuleid: '1315721394511069184',
        webTime: new Date().getTime()
      }
      this.initDateRange()
    },
    showCodeDetail(sInfoWindcode) {
      this.$refs.detail.open({ sinfoWindcode: sInfoWindcode })
    }
  }
}
</script>

<style lang="scss" scoped>
.financingReference {
  &-selectArea {
    width: 100%;
    min-width: 100%;
    background: #ffffff;
    border-radius: 2px;
    display: flex;
    padding: 16px 24px 16px 16px;
    box-sizing: border-box;
    // margin-top: 1px;
    margin-bottom: 1px;
    &-select {
      width: calc(100% - 216px);
      display: flex;
      gap: 8px;
      align-items: center;
      label {
        flex-shrink: 0;
      }
    }
    &-buttons {
      margin-left: 16px;
      display: flex;
      align-items: center;
      button {
        // width: 88px;
        border-radius: 2px;
      }
    }
  }
  &-title {
    width: 100%;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    background-color: #ffffff;
    font-size: var(--el-font-size-base);
    font-weight: 600;
    &-date {
      margin-left: auto;
      display: flex;
      align-items: center;
      gap: 8px;
      label {
        width: 72px;
        height: 22px;
        font-size: var(--el-font-size-base);
        color: rgba(0, 0, 0, 0.85);
        line-height: 22px;
      }
    }
  }
  &-button {
    width: 100%;
    // height: 48px;
    padding-left: 16px;
    background-color: #ffffff;
    padding-bottom: 16px;
    & > button {
      // width: 136px;
      height: 32px;
    }
  }
}
::v-deep .jr-decorated-table--top-search .jr-decorated-table--header {
  height: 0px;
}
::v-deep .jr-decorated-table > div {
  height: 100%;
  padding-left: 16px;
  padding-right: 16px;
}
::v-deep body .el-cascader .el-input--suffix input {
  height: 32px !important;
}
::v-deep .jr-empty {
  margin: 0px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}
</style>
