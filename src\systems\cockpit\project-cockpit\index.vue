<template>
  <div class="cockpit" style="padding: 0px">
    <div class="cockpit-header">
      <div class="cockpit-header-left">
        <img src="@/assets/cockpit/cockpit_pro_logo.png" alt="" />
        <span class="cockpit-header-left-company">
          <img src="~@/assets/cockpit/company_pro_icon.png" alt="公司" />
          <span :title="$store.getters.personInfo.companyName">
            {{ $store.getters.personInfo.companyName | companyFormat }}
          </span>
        </span>
        <el-dropdown @command="changeSysVersion">
          <span class="cockpit-header-left-dropdown">
            <img src="~@/assets/images/home/<USER>" alt="切换" />
            <span>{{ $store.getters.sysVersionName }}</span>
            <img src="~@/assets/images/home/<USER>" alt="切换" />
          </span>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item
              v-for="item in $store.getters.personInfo.companyVerList || []"
              :key="item.id"
              :command="item.id"
            >
              {{ item.text }}
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>
      <div class="cockpit-header-right">
        <span>主体评级：{{ companyLevel }}</span>
        <span>{{ $store.getters.personInfo.username }}</span>
        <jr-svg-icon
          :icon-class="isFullScreen ? 'compress' : 'full-screen'"
          :style="{ width: px2vw(16), height: px2vh(16), color: '#FFFFFF',cursor: 'pointer' }"
          @click="enterFullscreen"
        />
        <img 
          src="@/assets/cockpit/cockpit_pro_close.png" 
          alt="" 
          :style="{ width: px2vw(16), height: px2vh(17), color: '#000000',cursor: 'pointer' }"
          @click="closeMenu"
        >
      </div>
    </div>
    <cockpit-lighter v-if="$store.getters.sysVersion === 'company'" />
    <cockpit-deep v-if="$store.getters.sysVersion === 'group'" />
    <div class="cockpit-footer">
      <div class="cockpit-footer-inner">
        <div class="cockpit-footer-inner-tabs">
          <div class="cockpit-footer-inner-tabs-tabPro"></div>
          <div class="cockpit-footer-inner-tabs-tabRate" @click.stop="redirectRate"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import cockpitLighter from './components/cockpit-lighter.vue'
import cockpitDeep from './components/cockpit-deep.vue'
import router, { createRouter } from '@jupiterweb/router'
import Paths from '@jupiterweb/utils/systopath'
import { toggleFullScreen,isFull } from '@/utils/fullScreen'
import { publicCompanyList } from "@/api/public/public"
import { px2vw, px2vh } from '../utils/portcss'
export default {
  components: {
    cockpitLighter,
    cockpitDeep
  },
  filters: {
    companyFormat(val) {
      if(val){
        return val.length > 9 ? val.substring(0, 9) + '...' : val
      }else{
        return val;
      }
    }
  },
  data() {
    return {
      companyList:[],
      isFullScreen:false
    }
  },
  computed: {
    companyLevel() {
      return this.companyList.find(item  => item.companyName === this.$store.getters.personInfo.companyName)?.rating || ''
    }
  },
  created() {
    this.getPublicCompanyList()
  },
  mounted() {
    this.isFullScreen = isFull()
  },
  methods: {
    px2vw,
    px2vh,
    isFull,
    /**
     * 获取所有企业相关信息
     */
    async getPublicCompanyList() {
      const data = await publicCompanyList()
      this.companyList = data
    },
    changeSysVersion(companyVer) {
      if (this.$store.getters.personInfo?.companyVerList?.length <= 1) return
      this.$store.commit('system/CHANGE_SYS_VERSION', companyVer)
      router.matcher = createRouter().matcher
      this.$store.dispatch('GetMenuAuth').then((res) => {
        if (Object.keys(res).length && JSON.stringify(this.$store.getters.currentSystem) !== '{}') {
          const { sysId, routerflag: sysflag } = this.$store.getters.currentSystem
          const dashboard = Paths()[sysId]
          const plainMenus = this.$store.getters.permissions[sysId] || []
          this.$store.dispatch('GenerateRoutes', { menus: plainMenus, dashboard, sysflag }).then((accessRoutes) => {
            // 根据menus权限生成可访问的路路由表
            router.addRoutes(accessRoutes) // 动态添加可访问路由表
            this.$store.dispatch('tagsView/delOthersViews', this.visitedViews[0])
            this.$store.dispatch('tagsView/updateCurrentTab', this.visitedViews[0].pageKey)
          })
        }
      })
    },
    /**
     * 全屏状态切换
     */
    enterFullscreen(e) {
      e.preventDefault()
      toggleFullScreen(document.documentElement)
      this.isFullScreen = !this.isFullScreen
    },
    /**
     * 退出菜单
     */
    closeMenu() {
      this.$emit('close')
    },
    /**
     * 跳转利率驾驶舱
     */
    redirectRate() {
      this.$store.dispatch('tagsView/updateCurrentTab', {
        path: '/1369319476859256832',
        // 跳转需要刷新的话 refresh传true
        meta: {
          refresh: true,
          params: {}
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~assets/styles/portcss.scss';
.cockpit {
  position: fixed;
  top: 0px;
  left: 0px;
  width: 100%;
  height: 100%;
  z-index: 1003;
  background: radial-gradient( 127% 98% at 52% 38%, #033C72 0%, #003693 100%);
  &-header {
    width: 100%;
    height: vh(69);
    background-image: url('../../../assets/cockpit/cockpit_pro_head.png');
    background-size: vw(1340) 100%;
    background-repeat: no-repeat;
    background-position: center center;
    padding-left: vw(32);
    padding-right: vw(32);
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    color: #ffffff;
    &-left {
      display: flex;
      align-items: center;
      & > img {
        width: vw(263);
        height: vh(33);
      }
      &-company {
        display: flex;
        align-items: center;
        margin-left: vw(32);
        & > img {
          width: vw(15);
          height: vh(13);
        }
        & > span {
          height: vh(22);
          font-family: MicrosoftYaHeiSemibold;
          font-size: vh(14);
          color: #FFFFFF;
          line-height: vh(22);
          margin-left: vw(9);
        }
      }
      &-dropdown {
        margin-left: vw(34);
        cursor: pointer;
        & > img:nth-of-type(1) {
          width: vw(12);
          height: vh(13);
        }
        & > img:nth-of-type(2) {
          width: vw(12);
          height: vh(13);
        }
        & > span {
          height: vh(22);
          font-family: MicrosoftYaHeiSemibold;
          font-size: vh(14);
          color: #ff8e2b;
          line-height: vh(22);
          margin-left: vw(10);
          margin-right: vw(8);
        }
      }
    }
    &-right {
      display: flex;
      gap: vw(32);
      align-items: center;
      span {
        cursor: pointer;
        height: vh(22);
        font-family: MicrosoftYaHeiSemibold;
        font-size: vh(14);
        color: #FFFFFF;
        line-height: vh(22);
      }
    }
  }
  &-footer {
    width: 100%;
    height: vh(71);
    &-inner {
      width: vw(1404);
      height: 100%;
      background-image: url('../../../assets/cockpit/cockpitFootProBac.png');
      background-size: 100% 89%;
      background-position: center bottom;
      background-repeat: no-repeat;
      margin: 0 auto;
      display: flex;
      justify-content: center;
      &-tabs {
        width: vw(635);
        height: vh(50);
        display: flex;
        justify-content: center;
        align-items: center;
        &-tabPro {
          width: vw(337);
          height: vh(49);
          background-image: url('../../../assets/cockpit/cockpitProTabPro.png');
          background-size: 100% 100%;
          position: relative;
          left: vw(20);
          cursor: pointer;
        }
        &-tabRate {
          width: vw(337);
          height: vh(49);
          background-image: url('../../../assets/cockpit/cockpitProTabRate.png');
          background-size: 100% 100%;
          position: relative;
          left: vw(-20);
          cursor: pointer;
        }
      }
    }
  }
}
</style>
