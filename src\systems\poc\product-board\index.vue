<!--
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023-10-27 15:49:45
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2023-11-10 13:37:45
 * @Description: 产品看板
-->
<template>
  <div class="poc-product-board">
    <article>
      <div class="board-header">
        <span class="title">配置需求</span>
        <em>(2)</em>
        <!-- <el-tag effect="dark" size="mini" type="warning">1</el-tag> -->
      </div>
      <div class="board-body">
        <el-collapse value="1">
          <el-collapse-item name="1" title="配置完成的需求">
            <div class="board-body--item">
              <div>
                <label>名称:</label>
                <span>保守配置v1.0</span>
              </div>
              <div>
                <label>风险测评:</label>
                <span>R1</span>
              </div>
              <div>
                <label>目标收益:</label>
                <span>5%~6%</span>
              </div>
              <el-link :underline="false">更多>>></el-link>
            </div>
            <div class="board-body--item">
              <div>
                <label>名称:</label>
                <span>激进配置</span>
              </div>
              <div>
                <label>风险测评:</label>
                <span>R5</span>
              </div>
              <div>
                <label>目标收益:</label>
                <span>10%~13%</span>
              </div>
              <el-link :underline="false">更多>>></el-link>
            </div>
          </el-collapse-item>
        </el-collapse>
        <div class="add">+添加卡片</div>
      </div>
    </article>
    <article>
      <div class="board-header">
        <span class="title">产品创设</span>
        <em>(3)</em>
        <!-- <el-tag effect="dark" size="mini" type="warning">3</el-tag> -->
      </div>
      <div class="board-body">
        <el-collapse value="1">
          <el-collapse-item name="1" title="正在设计的产品">
            <div class="board-body--item">
              <div>
                <label>名称:</label>
                <span>保守配置v2.0</span>
              </div>
              <div>
                <label>风险测评:</label>
                <span>R1</span>
              </div>
              <div>
                <label>目标收益:</label>
                <span>5%~6%</span>
              </div>
            </div>
          </el-collapse-item>
        </el-collapse>
        <el-collapse value="1">
          <el-collapse-item name="1" title="设计完成的产品">
            <div class="board-body--item">
              <div>
                <label>名称:</label>
                <span>投资组合4</span>
              </div>
              <div>
                <label>目标风险:</label>
                <span>R1</span>
              </div>
              <div>
                <label>目标收益:</label>
                <span>5%~6%</span>
              </div>
              <div>
                <label>大类资配:</label>
                <span>均值方差模型</span>
              </div>
              <div>
                <label>市场预期:</label>
                <span>历史值</span>
              </div>
            </div>
            <div class="board-body--item">
              <div>
                <label>名称:</label>
                <span>激进配置</span>
              </div>
              <div>
                <label>目标风险:</label>
                <span>R5</span>
              </div>
              <div>
                <label>目标收益:</label>
                <span>10%~13%</span>
              </div>
              <div>
                <label>大类资配:</label>
                <span>均值方差模型</span>
              </div>
              <div>
                <label>市场预期:</label>
                <span>资产定价</span>
              </div>
            </div>

            <div class="board-body--item">
              <div>
                <label>名称:</label>
                <span>保守配置V1.0</span>
              </div>
              <div>
                <label>目标风险:</label>
                <span>R1</span>
              </div>
              <div>
                <label>目标收益:</label>
                <span>5%~6%</span>
              </div>
              <div>
                <label>大类资配:</label>
                <span>均值方差模型</span>
              </div>
              <div>
                <label>市场预期:</label>
                <span>历史值</span>
              </div>
            </div>
          </el-collapse-item>
        </el-collapse>
        <!-- <div class="add">+添加卡片</div> -->
      </div>
    </article>
    <article>
      <div class="board-header">
        <span class="title">模拟测算</span>
        <em>(2)</em>
        <!-- <el-tag effect="dark" size="mini" type="warning">3</el-tag> -->
      </div>
      <div class="board-body">
        <el-collapse value="1">
          <el-collapse-item name="1" title="正在构建的产品组合">
            <div class="board-body--item">
              <div>
                <label>名称:</label>
                <span>保守配置方案v2.0</span>
              </div>
              <div>
                <label>风险测评:</label>
                <span>R1</span>
              </div>
              <div>
                <label>目标收益:</label>
                <span>5%~6%</span>
              </div>
              <div>
                <label>大类资配:</label>
                <span>均值方差模型</span>
              </div>
              <div>
                <label>市场预期:</label>
                <span>历史值</span>
              </div>
              <div>
                <label>资产选择:</label>
                <span><el-link :underline="false">待选择</el-link></span>
              </div>
              <div class="btn-container">
                <span @click="handleClick('PTL_MANAGEMENT_001_003_001', true)">试算与转指令</span>
              </div>
            </div>
          </el-collapse-item>
        </el-collapse>
        <el-collapse value="1">
          <el-collapse-item name="1" title="构建完成的产品组合">
            <div class="board-body--item">
              <div>
                <label>名称:</label>
                <span>投资组合4</span>
              </div>
              <div>
                <label>风险测评:</label>
                <span>R1</span>
              </div>
              <div>
                <label>目标收益:</label>
                <span>5%~6%</span>
              </div>
              <div>
                <label>大类资配:</label>
                <span>均值方差模型</span>
              </div>
              <div>
                <label>市场预期:</label>
                <span>历史值</span>
              </div>
              <div>
                <label>资产选择:</label>
                <span><el-link :underline="false">已选择</el-link></span>
              </div>
              <div class="btn-container">
                <span @click="handleClick('1')">回测</span>
                <span @click="handleClick('2')">对比</span>
              </div>
            </div>
          </el-collapse-item>
        </el-collapse>
      </div>
    </article>
    <article>
      <div class="board-header">
        <span class="title">组合优化</span>
        <em>(2)</em>
        <!-- <el-tag effect="dark" size="mini" type="warning">4</el-tag> -->
      </div>
      <div class="board-body">
        <el-collapse value="1">
          <el-collapse-item name="1" title="需优化的组合">
            <div class="board-body--item">
              <div>
                <label>名称:</label>
                <span>投资组合4</span>
              </div>
              <div>
                <label>风险测评:</label>
                <span>R1</span>
              </div>
              <div>
                <label>目标收益:</label>
                <span>5%~6%</span>
              </div>
              <div>
                <label>大类资配:</label>
                <span>均值方差模型</span>
              </div>
              <div>
                <label>市场预期:</label>
                <span>历史值</span>
              </div>
              <div>
                <label>资产选择:</label>
                <span><el-link>已选择</el-link></span>
              </div>
              <div class="btn-container">
                <span @click="handleClick('1')">回测</span>
                <span @click="handleClick('2')">对比</span>
                <span @click="handleClick('3')">调优</span>
              </div>
            </div>
          </el-collapse-item>
        </el-collapse>
        <el-collapse value="1">
          <el-collapse-item name="1" title="优化完成的组合">
            <div class="board-body--item">
              <div>
                <label>名称:</label>
                <span>投资组合1218</span>
              </div>
              <div>
                <label>风险测评:</label>
                <span>R1</span>
              </div>
              <div>
                <label>目标收益:</label>
                <span>5%~6%</span>
              </div>
              <div>
                <label>大类资配:</label>
                <span>均值方差模型</span>
              </div>
              <div>
                <label>市场预期:</label>
                <span>历史值</span>
              </div>
              <div>
                <label>资产选择:</label>
                <span><el-link>已选择</el-link></span>
              </div>
              <div class="btn-container">
                <span @click="handleClick('1')">回测</span>
                <span @click="handleClick('2')">对比</span>
                <span>调优记录</span>
              </div>
            </div>
          </el-collapse-item>
        </el-collapse>
      </div>
    </article>
    <!-- <article class="desc">
      <div>
        <div class="desc-title"><el-tag effect="dark" size="mini" type="warning">1</el-tag>看板+Plan:公示产品需求计划</div>
        <p>结合客户收益目标、风险测评情况、市场预期如资表现、市场流动性、赛道盈利难易度、风险因子表现、公募基金整体仓位及风格行业配置情况、以及司资产配置比例中枢和投资限制等，对配置需求进:管理，作为整个资产配置方案的输入</p>
      </div>
      <div>
        <div class="desc-title"><el-tag effect="dark" size="mini" type="warning">2</el-tag>看板+DO:可视化执行节点</div>
        <p>根据配置需求方案，通过设定收益风险目标，运用常见的资产配置模型(均值方差、风险平价、自定义村重)，以大类资产指数、外部服务商自制指数等构廷资产配置方案，并借鉴市场预期
          (上节提到)等对产配置方案进行调优。</p>
      </div>
      <div>
        <div class="desc-title"><el-tag effect="dark" size="mini" type="warning">3</el-tag>看板+Check: 审视结果盘查问题</div>
        <p>根据配置需求、设计资产配置方案后，结合产品投福围，可投池等约束。以产品持仓为出发(或有)筛选可投池的标的资产均建增圳合进行回测，输出绩效分析结果，并可与同类、同略、同比的标品资管产品、持仓组合或其它模拟组进行对比</p>
      </div>
      <div>
        <div class="desc-title"><el-tag effect="dark" size="mini" type="warning">4</el-tag>看板+Act:选代看板优化工作流</div>
        <p>根据配置需求、设计资产配置方案后，结合产品投范围、可投池等约束，以产品持仓为出发，通过资中心，络选可投池的标的资产，经过模拟回测、配比例中枢微调、参考策略研究进行调优后
          (如参考产配置所需的资产表现、市场流动性、赛道盈利难度、风险因子表现、公募基金整体仓位及风格行业面置情况)，确定最终所需配置的组合</p>
      </div>
    </article> -->
    <modal-non-process v-bind="{...modalConfig}" />
  </div>
</template>

<script>
export default {
  data() {
    const self = this
    return {
      modalConfig: {
        visible: false,
        modalType: 'iframe',
        modalTypeName: '组合对比',
        size: 'full',
        menuinfo: {
          componenturl: ''
        },
        closeModal: () => self.modalConfig.visible = false
      }
    }
  },
  methods: {
    handleClick(flag, isMenu) {
      if (flag === '1') {
        Object.assign(this.modalConfig, {
          visible: true,
          modalTypeName: '组合回测',
          menuinfo: {
            componenturl: location.origin + '/backtest.html'
          }
        })
      } else if (flag === '2') {
        Object.assign(this.modalConfig, {
          visible: true,
          modalTypeName: '组合对比',
          menuinfo: {
            componenturl: location.origin + '/compare.html'
          }
        })
      } else if (isMenu) {
        this.$store.dispatch('tagsView/updateCurrentTab', '/' + flag)
      }
    }
  }
}
</script>

<style lang="scss">
$shadow: 1px 2px 9px 0px #c0c0c0;
.poc-product-board {
  height: 100%;
  overflow: auto;
  display: flex;
  width: 100%;
  justify-content: space-between;
  article {
    background: #fff;
    overflow: auto;
    height: 100%;
    width: calc(25% - 10px);
    border-radius: 4px;
    padding: 12px;
    &:first-child {
      margin-left: 0;
    }
    .el-link {
      text-decoration: underline;
    }
    .el-collapse {
      border: 1px solid #e8e8e8;
      border-radius: 4px;
      .el-collapse-item__content {
        padding-bottom: 0;
      }
      .el-collapse-item__header {
        background: var(--theme--color);
        color: #fff;
        padding: 0 12px;
        height: 36px;
        line-height: 36px;
      }
    }
    .el-collapse + .el-collapse {
      margin-top: 20px;
    }
    .el-tag {
      height: 18px;
      width: 18px;
      border-radius: 6px;
      margin-left: 20px;
      line-height: 14px;
      text-align: center;
      vertical-align: text-bottom;
      padding: 0;
    }
    .btn-container {
      width: 100%;
      margin-top: 10px;
      span {
        padding: 0 8px;
        line-height: 18px;
        border: 1px solid #ddd;
        white-space: nowrap;
        text-align: center;
        cursor: pointer;
        height: 20px;
        &:not(:first-child) {
          margin-left: 20px;
        }
        &:hover {
          background: var(--theme--color);
          color: #fff;
        }
      }
    }
    &.desc {
      width: 336px;
      margin-left: 5vw;
      .desc-title {
        color: #333;
        font-weight: bold;
        font-size: 14px;
      }
      .el-tag {
        margin-left: 0;
        margin-right: 6px;
      }
      p {
        color: #409eff;
        line-height: 18px;
        margin-top: 8px;
        margin-bottom: 24px;
      }
    }
    &:not(.desc) .board-header {
      margin-bottom: 10px;
      .title {
        font-size: 14px;
        border-left: 3px solid var(--theme--color);
        padding-left: 6px;
        line-height: 14px;
        height: 14px;
        display: inline-block;
        vertical-align: text-top;

      }
      em {
        // vertical-align: 4px;
        margin-left: 4px;
        font-size: 14px;
        font-style: normal;
        color: red;
        vertical-align: text-bottom;
      }
    }
    .subtitle {
      line-height: 32px;
      box-shadow: $shadow;
      text-align: center;
      font-size: 14px;
      border-radius: 10px;
      margin-bottom: 16px;
    }
    .board-body--item {
      // box-shadow: $shadow;
      padding: 16px 12px;
      // border-radius: 8px;
      // margin-bottom: 16px;
      div {
        line-height: 24px;
        display: flex;
        label {
          padding-right: 4px;
          // width: 60px;
        }
        &>:not(label) {
          color: #000;
        }
      }
      // &>.el-link {
        // margin-top: 10px;
      // }
    }
    .add {
      line-height: 32px;
      // box-shadow: $shadow;
      border: 1px dashed var(--theme--color);
      width: 100%;
      text-align: center;
      cursor: pointer;
      font-size: 14px;
      border-radius: 6px;
      margin-top: 10px;
      &:hover {
        background: var(--theme--color);
        color: #fff;
      }
    }
  }
}
@media screen and (max-width: 1600px) {
  .poc-product-board > div {
    padding-left: 0;
  }
  .poc-product-board > div > article {
    margin-left: 30px;
    min-width: 215px;
  }
  .poc-product-board  > div > article.desc {
    margin-left: 50px;
  }
}
</style>
