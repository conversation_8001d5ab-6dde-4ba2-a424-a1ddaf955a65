<!-- 发行明细 -->
<template>
  <div class="bondsDetail">
    <BondBalanceAnalysis />
    <BondDetailsList v-bind="{...$attrs, ...$props}" />
  </div>
</template>

<script>
import BondBalanceAnalysis from './components/bondBalanceAnalysis/index.vue'
import BondDetailsList from './components/bondDetailsList.vue'
export default {
  components: { BondBalanceAnalysis, BondDetailsList },
  data() {
    return {
      searchForm: {
        mainCaliber: '01',
        backTime: ''
      },
      mainCaliberList: [
        { id: '01', text: '名义发行主体' }
      ]
    }
  },
  methods: {
    queryData() {
      console.log('222222')
    }
  }
}
</script>
<style lang="scss" scoped>
.bondsDetail {
  height: 100%;
  .el-form .el-form-item__label {
    padding-top: 12px;
  }
  .formItem {
    display: inline-block;
    width: 300px
  }
  .btn {
    display: inline-block;
    margin-left: 10px;
  }
  .searchForm {
    padding: 10px 0 10px 0;
    background: #fff;
    margin-bottom: 10px;
  }
}
</style>
