<template>
  <!-- 对外投资详情 -->
  <div class="industrial-business">
    <jr-layout-vertical :height="48" disabled>
      <template v-slot:top>
        <div class="industrial-business-form">
          <el-form inline :model="form" label-width="68">
            <jr-form-item label="关键字">
              <el-input
                v-model="form.keyWord"
                clearable
                placeholder="请输入"
                style="max-width: 285px"
              />
            </jr-form-item>
            <jr-form-item label="变更日期">
              <el-date-picker
                v-model="form.dateRange"
                type="daterange"
                range-separator="~"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                format="yyyy-MM-dd"
                class="date-picker"
                style="max-width: 285px"
              />
            </jr-form-item>
          </el-form>
          <div class="btn-list">
            <el-button type="primary" @click="submit">查询</el-button>
            <el-button @click="reset">重置</el-button>
          </div>
        </div>
      </template>

      <template v-slot:bottom>
        <div class="industrial-business-content">
          <jr-decorated-table
            ref="table"
            stripe
            :menuinfo="{ moduleid: '1352320216532336640' }"
            :params="tableParams"
            custom-id="a57acc4187e4464daf83c00d4bdafec3"
            @refreshed="callFn"
          />
        </div>
      </template>
    </jr-layout-vertical>
  </div>
</template>
<script>
export default {
  props: {
    permitdetail: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      tableParams: {
        ccid: 'a57acc4187e4464daf83c00d4bdafec3',
        ownedModuleid: '1352320216532336640'
      },
      columns: [],
      form: {
        keyWord: '',
        dateRange: []
      }
    }
  },
  methods: {
    callFn(data) {
      this.columns = data.config.columns
    },
    handleClick() {
      //
    },
    submit() {
      this.tableParams = { ...this.tableParams, ...this.form }
    },
    reset() {
      this.form = {
        keyWord: '',
        dateRange: []
      }
    }
  }
}
</script>
<style lang="scss">
.industrial-business {
  height: calc(100% - 56px);

  .vertical-layout {
    background: #fff;
    padding: 0;
    height: 100%;

    &--top-content {
      padding: 0 16px;
    }

    &--resize {
      height: 1px;
      color: #EAE9E9;
    }

    &--bottom {
      margin-top: 16px;
      padding: 0 16px;
    }
  }

  &-form {
    position: relative;

    .el-form {
      display: flex;
      align-items: center;
      padding-top: 16px;
      width: calc(100% - 144px);

      .el-form-item {
        width: 100%;
        max-width: 353px;

        .el-form-item__label {
          padding: 11px 8px 0 0;
        }

        .el-form-item__content {
          width: calc(100% - 68px);
        }

        &.no-label {
          .el-form-item__content {
            width: 100%;
          }
        }
      }
    }

    .btn-list {
      position: absolute;
      top: 8px;
      right: 0;

      .el-button {
        margin-left: 16px;
      }
    }
  }

  &-content {
    height: 100%;

    .jr-decorated-table--header-left {
      display: none;
    }

    .jr-decorated-table--header-right {
      display: none;
    }

    .jr-decorated-table--body {
      padding: 0;
    }
  }
}
</style>
