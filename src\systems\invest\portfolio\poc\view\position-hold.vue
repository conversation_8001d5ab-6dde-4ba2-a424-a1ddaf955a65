<!--
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023-04-20 19:49:22
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2024-06-05 17:35:59
 * @Description: 组合持仓
-->
<template>
  <div class="home-poc-item has-fullscreen with-tab-item">
    <div class="home-poc-item--header">组合持仓
      <el-form :model="queryParams" style="position: absolute;right: 40px;top: 4px;">
        <jr-form-item-create :data="cols" :model="queryParams" :column="1" />
      </el-form>
      <fullscreen v-on="{ ...$listeners }" />
    </div>
    <el-tabs v-model="activeName">
      <el-tab-pane label="债券持仓" name="bond">
        <jr-decorated-table
          custom-id="eb8ff5edf6b34bef96150bb28b4cff29"
          :custom-render="{
            amtChangeChart: amtChangeChartRender
          }"
          v-bind="{ ...$attrs, params: { ...queryParams }, noPagination: true, menuinfo: {pageId: 'BondHold'} }"
          @refreshed="queryEnd"
        />
      </el-tab-pane>
      <el-tab-pane label="股票持仓" name="stock" lazy>
        <jr-decorated-table
          custom-id="370ad7a0fa824842813c1da9a992014c"
          :custom-render="{
            amtChangeChart: amtChangeChartRender
          }"
          v-bind="{ ...$attrs, params: { ...queryParams }, noPagination: true, menuinfo: {pageId: 'StockHold'} }"
          @refreshed="queryEnd"
        />
      </el-tab-pane>
      <el-tab-pane label="基金持仓" name="fund" lazy>
        <jr-decorated-table
          custom-id="f1bc6a5abb51419f80ce78685fbe8935"
          :custom-render="{
            amtChangeChart: amtChangeChartRender
          }"
          v-bind="{ ...$attrs, params: { ...queryParams }, noPagination: true, menuinfo: {pageId: 'FundHold'} }"
          @refreshed="queryEnd"
        />
      </el-tab-pane>
      <el-tab-pane label="其他持仓" name="other" lazy>
        <jr-decorated-table
          custom-id="4924a3e5b66a4b22a3be2d9406f92b7f"
          :custom-render="{
            amtChangeChart: amtChangeChartRender
          }"
          v-bind="{ ...$attrs, params: { ...queryParams }, noPagination: true, menuinfo: {pageId: 'OtherHold'} }"
          @refreshed="queryEnd"
        />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import fullscreen from '../common/fullscreen'
import { FormatDate } from '@jupiterweb/utils/common'
import echarts from '@jupiterweb/components/echarts'
export default {
  components: {
    fullscreen
  },
  props: {
    params: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      activeName: 'bond',
      cols: [{
        type: 'remoteSelect',
        api: '/invest/portfolio/ptloverview/PtlOverview001/getFinprodMarketId',
        prop: 'finprodMarketId',
        optionValue: 'id',
        placeholder: '请选择资产'
      }],
      totalCount: 0,
      queryParams: { portfolioId: '', finprodMarketId: '' },
      platDate: JSON.parse(sessionStorage.getItem('platDate'))
    }
  },
  watch: {
    params: {
      handler(n) {
        if (n) {
          this.queryParams = { ...this.queryParams, ...n }
        }
      },
      deep: true
    }
  },
  methods: {
    // 净值两日变动
    amtChangeChartRender(h, { row }) {
      return (<echarts options={this.getChartOptions(row.amtChangeChartList || [])} />)
    },
    getChartOptions(data) {
      return {
        xAxis: {
          show: false, // 取消显示坐标轴,坐标轴刻度,坐标值(如果是y轴,默认的网格线也会取消显示)
          type: 'category',
          boundaryGap: false,
          splitLine: {
            show: false
          },
          data: data.map(d => FormatDate(d.X, 'yyyy-MM-dd'))
        },
        grid: {
          left: '0',
          top: '5px',
          right: '0',
          bottom: '0',
          containLabel: true
        },
        yAxis: {
          axisLabel: { // 取消显示坐标值
            show: false
          },
          min: Math.min(...data.map(d => d.Y), 1),
          splitLine: { // 取消网格线
            show: false
          },
          type: 'value'
        },
        series: [
          {
            symbol: 'none',
            type: 'line',
            data: data.map(d => d.Y)
          }
        ]
      }
    },
    queryEnd(ins) {
      this.totalCount = ins.pagination.total
    }
  }
}
</script>

<style lang="scss">
</style>

<style lang="scss">
@import "../common/poc.scss";
.home-poc-item.with-tab-item {
  .el-tabs {
    height: calc(100% - 40px);
  }
  .el-tabs__header {
    padding-left: 10px;
    margin-bottom: 8px;
  }
  .el-tabs__content {
    height: calc(100% - 35px);
    .el-tab-pane {
      height: 100%;
    }
  }
  .jr-decorated-table--header {
    display: none;
  }
}
</style>
