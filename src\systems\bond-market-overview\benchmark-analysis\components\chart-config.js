/**
 * 定义柱状图三种线性渐变颜色
 * startColor：0% 处的颜色
 * endColor：100% 处的颜色
 */
const barLinearColor1 = { startColor: '#93C2FF', endColor: '#68A8FF' }
const barLinearColor2 = { startColor: '#8EFAD8', endColor: '#89EAC4' }
const barLinearColor3 = { startColor: '#FFDDC8', endColor: '#FFA57F' }
const BOND_SERIES = {
  AB: '债券余额(协会债)',
  CB: '债券余额(公司债)',
  EB: '债券余额(企业债)',
  WACR: '票面加权利率'
}
const BOND_ISSUE = {
  AB: '债券规模(协会债)',
  CB: '债券规模(公司债)',
  EB: '债券规模(企业债)',
  WACR: '票面加权利率'
}
/**
 * 获取柱状图系列配置
 * @param {Object} params 配置参数
 * @returns 系列配置
 */
export const getBarSeries = ({ name, stack, startColor, endColor }) => {
  const series = {
    type: 'bar',
    barWidth: 32,
    itemStyle: {
      color: {
        type: 'linear',
        x: 0,
        y: 0,
        x2: 0,
        y2: 1,
        colorStops: [
          { offset: 0, color: startColor },
          { offset: 1, color: endColor }
        ]
      }
    },
    emphasis: {
      focus: 'series'
    },
    data: []
  }

  // 如果名称存在，配置中添加进去
  if (name) { series.name = name }

  // 如果数据堆叠存在，配置中添加进去
  if (stack) { series.stack = stack }

  return series
}

export const getXAxis = () => {
  return {
    type: 'category',
    data: []
  }
}

export const getYAxis = (unit) => {
  return {
    type: 'value',
    name: `单位：${unit}`,
    axisLabel: {
      formatter: '{value}'
    }
  }
}

/**
 * 获取反转映射后的键名
 * @param {Object} mappingObj 原始键值映射对象
 * @param {string} targetValue 需要查找的目标值
 * @returns {string|null} 对应的键名或null
 */
function getReversedKey(mappingObj, targetValue) {
  const reverseMap = Object.fromEntries(
    Object.entries(mappingObj).map(([key, val]) => [val, key])
  )
  return reverseMap[targetValue] || null
}

export const bondChartOptions = {
  tooltip: {
    trigger: 'axis',
    backgroundColor: 'rgba(0,0,0,0.6)',
    padding: [12, 16],
    textStyle: {
      color: '#fff',
      fontSize: 14
    },
    formatter: params => {
      // 获取当前激活的系列
      const currentItem = params[0]
      // 提取所有激活系列数据
      const result = {
        abVal: 0,
        cbVal: 0,
        ebVal: 0,
        wacrVal: 0
      }
      params.forEach(series => {
        const seriesKey = getReversedKey(BOND_SERIES, series.seriesName)
        if (seriesKey) result[`${seriesKey.toLowerCase()}Val`] = series.value
      })
      const { abVal, cbVal, ebVal, wacrVal } = result
      const totalVal = abVal + cbVal + ebVal

      const content = `
        <div style="display:flex;flex-direction:column;align-items:flex-start;justify-content:center;">
          <div>${currentItem.name}</div>
          <div>票面加权利率(余额)：${wacrVal}%</div>
          <div>债券余额(总余额)：${totalVal}亿元</div>
          <div style="margin-left:20px">协会债：${abVal}亿元</div>
          <div style="margin-left:20px">公司债：${cbVal}亿元</div>
          <div style="margin-left:20px">企业债：${ebVal}亿元</div>
        </div>
        `
      return content
    }
  },
  toolbox: { show: false },
  legend: {
    orient: 'horizontal',
    x: 'center', // 水平居中
    y: 'bottom', // 垂直底部
    padding: [0, 0, 10, 0], // 下边距10px
    textStyle: {
      color: 'rgba(0,0,0,0.9)'
    },
    data: [
      {
        name: '债券余额(协会债)',
        itemStyle: {
          color: '#5B8FF9',
          width: 8,
          height: 8
        }
      },
      {
        name: '债券余额(公司债)',
        itemStyle: {
          color: '#5AD8A6'
        }
      },
      {
        name: '债券余额(企业债)',
        itemStyle: {
          color: '#E8684A'
        }
      },
      {
        name: '票面加权利率',
        itemStyle: {
          color: '#F5D91F'
        }
      }
    ]
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '11%',
    containLabel: true
  },
  xAxis: [{ ...getXAxis() }],
  yAxis: [
    { ...getYAxis('亿') },
    { ...getYAxis('%') }
  ],
  series: [
    {
      ...getBarSeries({
        name: '债券余额(协会债)',
        stack: 'Ad',
        ...barLinearColor1
      })
    },
    {
      ...getBarSeries({
        name: '债券余额(公司债)',
        stack: 'Ad',
        ...barLinearColor2
      })
    },
    {
      ...getBarSeries({
        name: '债券余额(企业债)',
        stack: 'Ad',
        ...barLinearColor3
      })
    },
    {
      name: '票面加权利率',
      type: 'line',
      yAxisIndex: 1,
      itemStyle: {
        color: '#F5D91F'
      },
      emphasis: {
        focus: 'series'
      },
      data: []
    }
  ]
}

export const getBondChartOptions = (type) => {
  let text = '余额'
  let keyList = BOND_SERIES
  if (type !== 'Exist') {
    text = '规模'
    keyList = BOND_ISSUE
  }
  return {
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(0,0,0,0.6)',
      padding: [12, 16],
      textStyle: {
        color: '#fff',
        fontSize: 14
      },
      formatter: params => {
        // 获取当前激活的系列
        const currentItem = params[0]
        // 提取所有激活系列数据
        const result = {
          abVal: 0,
          cbVal: 0,
          ebVal: 0,
          wacrVal: 0
        }
        params.forEach(series => {
          const seriesKey = getReversedKey(keyList, series.seriesName)
          if (seriesKey) result[`${seriesKey.toLowerCase()}Val`] = series.value
        })
        const { abVal, cbVal, ebVal, wacrVal } = result
        const totalVal = abVal + cbVal + ebVal

        const content = `
          <div style="display:flex;flex-direction:column;align-items:flex-start;justify-content:center;">
            <div>${currentItem.name}</div>
            <div>票面加权利率(${text})：${wacrVal}%</div>
            <div>债券${text}(总${text})：${totalVal}亿元</div>
            <div style="margin-left:20px">协会债：${abVal}亿元</div>
            <div style="margin-left:20px">公司债：${cbVal}亿元</div>
            <div style="margin-left:20px">企业债：${ebVal}亿元</div>
          </div>
          `
        return content
      }
    },
    toolbox: { show: false },
    legend: {
      orient: 'horizontal',
      x: 'center', // 水平居中
      y: 'bottom', // 垂直底部
      padding: [0, 0, 10, 0], // 下边距10px
      textStyle: {
        color: 'rgba(0,0,0,0.9)'
      },
      data: [
        {
          name: '债券' + text + '(协会债)',
          itemStyle: {
            color: '#5B8FF9',
            width: 8,
            height: 8
          }
        },
        {
          name: '债券' + text + '(公司债)',
          itemStyle: {
            color: '#5AD8A6'
          }
        },
        {
          name: '债券' + text + '(企业债)',
          itemStyle: {
            color: '#E8684A'
          }
        },
        {
          name: '票面加权利率',
          itemStyle: {
            color: '#F5D91F'
          }
        }
      ]
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '11%',
      containLabel: true
    },
    xAxis: [{ ...getXAxis() }],
    yAxis: [
      { ...getYAxis('亿') },
      { ...getYAxis('%') }
    ],
    series: [
      {
        ...getBarSeries({
          name: '债券' + text + '(协会债)',
          stack: 'Ad',
          ...barLinearColor1
        })
      },
      {
        ...getBarSeries({
          name: '债券' + text + '(公司债)',
          stack: 'Ad',
          ...barLinearColor2
        })
      },
      {
        ...getBarSeries({
          name: '债券' + text + '(企业债)',
          stack: 'Ad',
          ...barLinearColor3
        })
      },
      {
        name: '票面加权利率',
        type: 'line',
        yAxisIndex: 1,
        itemStyle: {
          color: '#F5D91F'
        },
        emphasis: {
          focus: 'series'
        },
        data: []
      }
    ]
  }
}

/**
 * 获取财务指标对比chart配置信息
 */
export const financialIndicatorsChartOptions = {
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow'
    }
  },
  toolbox: { show: false },
  legend: { show: false },
  grid: {
    left: '3%',
    right: '4%'
  },
  xAxis: [{ ...getXAxis() }],
  yAxis: [{ ...getYAxis('万元') }],
  series: [{ ...getBarSeries({ ...barLinearColor1 }) }]
}

/**
 * 获取财务趋势分析chart配置信息
 */
export const financialTrendChartOptions = {
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow'
    }
  },
  toolbox: { show: false },
  legend: { show: false },
  grid: {
    left: '3%',
    right: '4%'
  },
  xAxis: [{ ...getXAxis() }],
  yAxis: [{ ...getYAxis('万元') }],
  series: [
    { ...getBarSeries({ ...barLinearColor1 }) },
    { ...getBarSeries({ ...barLinearColor2 }) },
    { ...getBarSeries({ ...barLinearColor3 }) }
  ]
}

