<template>
  <div v-loading="loading" class="benchmark-analysis">
    <div class="public-tabs-container ">
      <el-tabs v-model="tabActiveName" @tab-click="changeTabs">
        <el-tab-pane v-for="item in groupList" :key="item.id" :label="item.groupName" :name="item.id" />
      </el-tabs>
    </div>
    <component :is="tabActiveName" v-bind="{ ...$attrs, ...$props, params: { ...tabParams } }" />
  </div>
</template>

<script>
import * as API from '@/api/benchmark-analysis/benchmark-analysis.js'
import { GetComboboxList } from '@/api/home'
import my_benchmarking_group from './my-benchmark.vue'
import industry_benchmarking_group from './industry-benchmark.vue'
import area_benchmarking_group from './area-benchmark.vue'

export default {
  name: 'BenchmarkAnalysis',
  components: { my_benchmarking_group, industry_benchmarking_group, area_benchmarking_group },
  props: {
    // 权限相关
    permitdetail: {
      type: Object,
      default: () => ({})
    },
    menuinfo: {
      type: Object,
      default: () => ({})
    },
    defaultTabActiveName: {
      type: String,
      default: 'my_benchmarking_group'
    },
    defaultTabActiveLabel: {
      type: String,
      default: '我的对标'
    }
  },
  watch: {
    tabActiveName(val) {
      this.tabParams.groupId = val
    }
  },
  data() {
    return {
      tabActiveName: this.defaultTabActiveName,
      tabParams: {
        entId: '',
        entName: '',
        entOrgCode: '',
        groupId: this.defaultTabActiveName,
        groupName: this.defaultTabActiveLabel,
        wprcTermOptions: [],
        reportTypeOptions: []
      },
      loading: false,
      groupList: []
    }
  },
  created() {
    // 查询对标分组信息
    this.queryBenchmarkGroupList()
    // 页面默认值设置
    this.setPageDefaultValue()
    // 查询通用数据字典
    this.getDictOptionsApi()
  },
  methods: {
    /**
     * 页面默认值设置
     */
    setPageDefaultValue() {
      const { companyCode, companyName, orgCode, outCompCode } = this.$store.getters.personInfo || {}
      Object.assign(this.tabParams, {
        entId: outCompCode || companyCode,
        entName: companyName,
        entOrgCode: orgCode
      })
    },
    /**
     * 查询获取数据字典
     */
    async getDictOptionsApi() {
      const data = await GetComboboxList(['WRPC_TERM', 'REPORT_TYPE'])
      this.reportTypeOptions = data['REPORT_TYPE']
      Object.assign(this.tabParams, {
        wprcTermOptions: data['WRPC_TERM'],
        reportTypeOptions: data['REPORT_TYPE']
      })
    },
    /**
     * 查询对标分组信息
     */
    queryBenchmarkGroupList() {
      this.loading = true
      API.getBenchmarkGroupList({})
        .then((data) => {
          this.loading = false
          this.groupList = Array.isArray(data) ? data : []
        })
        .catch(() => {
          this.loading = false
          this.groupList = []
        })
    },
    /**
     * 对标tab切换处理方法
     * @param {Object} tab 标签页对象
     */
    changeTabs(tab) {
      this.tabParams.tabId = tab.name || this.defaultTabActiveName
      this.tabParams.tabName = tab.label || this.defaultTabActiveLabel
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~@jupiterweb/assets/styles/variables.scss';

::v-deep .el-tabs__nav-wrap::after {
  display: none;
}

.benchmark-analysis {
  height: 100%;
  &-tabs {
    width: 100%;
    height: 56px;
    padding: 17px 0px 0px 16px;
    box-sizing: border-box;
    background-color: #ffffff;
    border-bottom: 1.5px solid #eae9e9;

    ::v-deep .el-tabs__header {
      height: 38px;
      line-height: 38px;
      margin: 0px;
    }

    ::v-deep .el-tabs__active-bar {
      height: 3px;
    }

    ::v-deep .el-tabs__item {
      font-size: var(--el-font-size-base) !important;
    }
  }
}
</style>
