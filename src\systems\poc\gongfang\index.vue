<template>
  <div class="poc-gongfang">
    <div class="header">
      <el-tabs v-model="active" type="card">
        <el-tab-pane name="0" label="多资产收益风险图" />
        <el-tab-pane name="1" label="资产历史走势" />
        <el-tab-pane name="2" label="多资产组合策略" />
      </el-tabs>
      <div class="search">
        <label>货币：</label>
        <jr-combobox v-model="form.cny" :data="[{ text: 'CNY', value: 'CNY' }]" />
        <el-radio-group v-model="form.radio" size="mini">
          <el-radio-button
            v-for="r in ['MTD', '1M', '3M', '6M', 'YTD', '1Y', '3Y', '5Y', '10Y', 'MAX']"
            :key="r"
            :label="r"
          >
            {{ r }}
          </el-radio-button>
        </el-radio-group>
        <el-date-picker
          v-model="form.date"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        />
        <el-divider direction="vertical" />
        <jr-svg-icon icon-class="sync" />
        <el-divider direction="vertical" />
        <jr-svg-icon icon-class="fullscreen" />
      </div>
    </div>
    <div class="body">
      <div style="width: calc(100% - 250px)">
        <div class="float-right-tips"><jr-svg-icon icon-class="info-circle" />实际取样时间区间2018-01-18~2023-01-18</div>
        <echarts :options="option" :styles="{ height: '350px' }" />
        <el-tabs type="card">
          <el-tab-pane label="股票">
            <jr-decorated-table custom-id="a66188d874d541cfa993bebfc41f8f68" v-bind="{ ...$attrs, ...$props }" />
          </el-tab-pane>
          <el-tab-pane label="债券" />
          <el-tab-pane label="商品" />
        </el-tabs>
      </div>
      <div class="right" style="width: 250px">
        <div class="search-body">
          <div class="item-title">股票 <el-checkbox>全选</el-checkbox></div>
          <div>
            <el-tag v-for="tag in tagList" :key="tag" size="mini" style="margin: 2px;">{{ tag }}</el-tag>
          </div>
          <div class="item-title">债券 <el-checkbox>全选</el-checkbox></div>
          <div>
            <el-tag v-for="tag in tagListBond" :key="tag" size="mini" style="margin: 2px;">{{ tag }}</el-tag>
          </div>
          <div class="item-title">商品 <el-checkbox>全选</el-checkbox></div>
          <div>
            <el-tag v-for="tag in tagListProd" :key="tag" size="mini" style="margin: 2px;">{{ tag }}</el-tag>
          </div>
          <div class="query-btn-box">
            <el-button-group>
              <el-button type="primary">更新</el-button>
              <el-button type="primary">重置</el-button>
            </el-button-group>
          </div>
          <div class="query2">
            <el-input placeholder="搜索" />
            筛选 <jr-combobox value="核心资产" :data="[]" />
          </div>
          <ul class="query-result">
            <li v-for="tr in treeList" :key="tr.color" :style="'--curr:' + tr.color"><span :style="'background: ' + tr.color" /><i class="el-icon-caret-right" />{{ tr.text }}</li>
          </ul>
        </div>
        <div class="query-btn-box query-btn-box--bottom">
          <el-button-group>
            <el-button type="primary">提交筛选</el-button>
            <el-button type="primary">重置方案</el-button>
          </el-button-group>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import echarts from '@jupiterweb/components/echarts'
export default {
  components: {
    echarts
  },
  data() {
    return {
      active: '0',
      form: {
        cny: 'CNY',
        radio: '5Y',
        date: ['2018-01-18', '2023-01-18']
      },
      tagList: ['沪深300', '中证500', '标普500', '纳斯达克100', '欧洲斯托克50', '富时100', '日经225指数'],
      tagListBond: ['中债总指数', '美国债券'],
      tagListProd: ['道富黄金ETF'],
      treeList: [{ text: '股票', color: '#2486b9' }, { text: '债券', color: '#66c18c' }, { text: '商品', color: '#9fa39a' }, { text: '不动产信托', color: '#513c20' }, { text: '另类投资', color: '#b5aa90' }],
      option: {
        tooltip: {
          show: true
        },
        grid: {
          left: 0,
          top: 20,
          bottom: 20,
          right: 10
        },
        xAxis: {
          type: 'category',
          data: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15],
          axisLabel: {
            //  改变x轴字体颜色和大小
            formatter: '{value} % ', //  给y轴添加单位
            textStyle: {
              fontSize: 12
            }
          },
          splitLine: {
            lineStyle: {
              color: 'rgba(255,255,255,0.2)'
            }
          }
        },
        yAxis: {
          axisLabel: {
            //  改变y轴字体颜色和大小
            formatter: '{value} % ', //  给y轴添加单位
            textStyle: {
              fontSize: 12
            }
          }
        },
        series: [
          {
            type: 'line',
            name: '波动率',
            smooth: true,
            showSymbol: false,
            data: [1.7, 2.1, 3, 4, 5, 6.2, 7.5, 8.5, 9.5, 10.2, 11, 12, 13]
          },
          {
            label: {
              show: true,
              position: 'top',
              formatter: function(params) {
                return params.name
              }
            },
            symbolSize: 22,
            data: [
              {
                name: '纳斯达克',
                value: [12.0, 12.84]
              },
              {
                name: '日经225指数',
                value: [7.0, 4.04]
              },
              {
                name: '标普500',
                value: [8.0, 5.04]
              },
              {
                name: '欧洲斯托克50',
                value: [9.0, 2.04]
              },
              {
                name: '沪深300',
                value: [7.5, 5.04]
              },
              {
                name: '中证500',
                value: [12.0, 4.04]
              }
            ],
            type: 'scatter'
          },
          {
            label: {
              show: true,
              position: 'top',
              formatter: function(params) {
                return params.name
              }
            },
            symbolSize: 22,
            data: [
              {
                name: '中债总指数',
                value: [0.3, 1.54]
              },
              {
                name: '美国债券',
                value: [7.0, 1.04]
              },
              {
                name: '道富黄金ETF',
                value: [12.0, 4.04]
              }
            ],
            type: 'scatter'
          }
        ]
      }
    }
  }
}
</script>

<style lang="scss">
.poc-gongfang {
  height: 100%;
  .header {
    display: flex;
    width: 100%;
    justify-content: space-between;
    background: #fff;
    padding-right: 10px;
    height: 40px;
    align-items: center;
    .el-tabs {
      height: 28px;
      border-top: 1px solid #eee;
    }
    .search {
      display: flex;
      line-height: 28px;
      height: 28px;
      align-items: center;
      .jr-combobox {
        width: 125px;
        margin-right: 10px;
      }
      .el-range-editor {
        margin-left: 10px;
        width: 200px !important;
      }
      .jr-svg-icon {
        cursor: pointer;
      }
    }
  }
  .body {
    height: calc(100% - 50px);
    display: flex;
    width: 100%;
    background: #fff;
    .float-right-tips {
      margin-top: 0px;
      float: right;
      margin-right: 10px;
      color: #999;
    }
    .el-tabs {
      height: calc(100% - 350px);
      margin-top: 20px;
      .el-tabs__content {
        height: calc(100% - 60px);
      }
      .el-tab-pane {
        height: 100%;
        .jr-decorated-table--header {
          display: none;
        }
      }
    }
    .right {
      border: 1px solid #eee;
      position: relative;
      height: 100%;
      overflow: hidden;
      .search-body {
        height: calc(100% - 40px);
        overflow: auto;
      }
      .item-title {
        line-height: 30px;
        background: #eee;
        margin-bottom: 10px;
        margin-top: 10px;
        padding-right: 10px;
        border-left: none;
        &::before {
          content: " ";
          width: 3px;
          height: 14px;
          background: var(--theme--color);
          display: inline-block;
          vertical-align: -2px;
          margin-right: 4px;
        }
        .el-checkbox {
          position: absolute;
          right: 25px;
        }
      }
      .query-btn-box {
        margin-top: 16px;
        width: 100%;
        border-top: 1px solid #eee;
        border-bottom: 1px solid #eee;
        padding: 5px 10px;
        text-align: center;
        .el-button-group {
          width: 100%;
          .el-button {
            width: 50%;
          };
        }
        &--bottom {
          position: absolute;
          bottom: 0;
        }
      }
      .query2 {
        display: flex;
        width: 100%;
        white-space: nowrap;
        height: 40px;
        align-items: center;
        line-height: 28px;
        justify-content: space-between;
        background: #efefef;
        border-bottom: 1px solid #eee;
        &>.el-input {
          width: 100px;
          margin-left: 10px;
        }
        .jr-combobox {
          width: 95px;
          margin-right: 10px;
          margin-left: 4px;
        }
      }
      .query-result {
        li {
          line-height: 28px;
          margin: 5px 0;
          background: linear-gradient(45deg, #c0c8cb, #fff);
          cursor: pointer;
          i {
            font-size: 16px;
            margin: 0 2px;
            vertical-align: -1px;
          }
          span {
            width: 3px;
            display: inline-block;
            height: 28px;
            vertical-align: middle;
            background: var(--curr);
          }
        }
      }
    }
  }
}
</style>
