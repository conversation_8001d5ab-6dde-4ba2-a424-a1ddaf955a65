<!--
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023-10-25 10:54:32
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2023-10-25 17:45:56
 * @Description: 资产监控
-->
<template>
  <article>
    <div class="header">
      <span class="title">资产监控(<font color="red">195</font>)</span>
      <div class="search-list">
        <label>资产类型：</label><jr-combobox v-model="assetType" :data="assetTypeList" />
        <label>指标类型：</label><jr-combobox v-model="indexType" :data="indexTypeList" />
        <label>监控状态：</label><jr-combobox v-model="status" :data="statusList" />
      </div>
    </div>
    <div class="body">
      <jr-table :columns="columns" :data-source="dataList" :height="height" :cell-style="cellStyle" />
    </div>
  </article>
</template>

<script>
export default {
  data() {
    return {
      height: null,
      assetType: '债券',
      assetTypeList: ['全部', '资产包', '基金', '计划类净值产品', '债券', '股票', '其他'].map(t => ({ text: t, value: t })),
      indexType: '信用风险',
      indexTypeList: ['信用风险', '其他'].map(t => ({ text: t, value: t })),
      status: '异常',
      statusList: ['异常', '其他'].map(t => ({ text: t, value: t })),
      dataList: [
        { '资产名称': '22光大银行CD046', '所属池': '债券池-可投池', '指标类型': '信用风险', '指标名称': '外部评级下调', '监控状态': '异常', '指标值': '无数据', '预警线': 'AA-' },
        { '资产名称': '20海峡银行二级01', '所属池': '债券池-可投池', '指标类型': '信用风险', '指标名称': '外部评级下调', '监控状态': '异常', '指标值': '无数据', '预警线': 'AA-' },
        { '资产名称': '18天津农商二级01', '所属池': '债券池-可投池', '指标类型': '信用风险', '指标名称': '外部评级下调', '监控状态': '异常', '指标值': '无数据', '预警线': 'AA-' },
        { '资产名称': '22德源02', '所属池': '债券池-可投池', '指标类型': '信用风险', '指标名称': '外部评级下调', '监控状态': '异常', '指标值': '无数据', '预警线': 'AA-' },
        { '资产名称': '20北湾银行永续债', '所属池': '债券池-可投池', '指标类型': '信用风险', '指标名称': '外部评级下调', '监控状态': '异常', '指标值': '无数据', '预警线': 'AA-' },
        { '资产名称': '20烟台银行永续债', '所属池': '债券池-可投池', '指标类型': '信用风险', '指标名称': '外部评级下调', '监控状态': '异常', '指标值': '无数据', '预警线': 'AA-' },
        { '资产名称': '22郑州银行CD123', '所属池': '债券池-可投池', '指标类型': '信用风险', '指标名称': '外部评级下调', '监控状态': '异常', '指标值': '无数据', '预警线': 'AA-' },
        { '资产名称': '22邯郸银行CD197', '所属池': '债券池-可投池', '指标类型': '信用风险', '指标名称': '外部评级下调', '监控状态': '异常', '指标值': '无数据', '预警线': 'AA-' },
        { '资产名称': '19龙江银行二级02', '所属池': '债券池-可投池', '指标类型': '信用风险', '指标名称': '外部评级下调', '监控状态': '异常', '指标值': '无数据', '预警线': 'AA-' },
        { '资产名称': '19营口银行二级', '所属池': '债券池-可投池', '指标类型': '信用风险', '指标名称': '外部评级下调', '监控状态': '异常', '指标值': '无数据', '预警线': 'AA-' },
        { '资产名称': '21昌阳投资PPN002', '所属池': '债券池-可投池', '指标类型': '信用风险', '指标名称': '外部评级下调', '监控状态': '异常', '指标值': '无数据', '预警线': 'AA-' },
        { '资产名称': '22港兴港投PPN002', '所属池': '债券池-可投池', '指标类型': '信用风险', '指标名称': '外部评级下调', '监控状态': '异常', '指标值': '无数据', '预警线': 'AA-' },
        { '资产名称': '22贵州银行永续债01', '所属池': '债券池-可投池', '指标类型': '信用风险', '指标名称': '外部评级下调', '监控状态': '异常', '指标值': '无数据', '预警线': 'AA-' },
        { '资产名称': '22浦发银行CD054', '所属池': '债券池-可投池', '指标类型': '信用风险', '指标名称': '外部评级下调', '监控状态': '异常', '指标值': '无数据', '预警线': 'AA-' },
        { '资产名称': '22中信银行CD031', '所属池': '债券池-可投池', '指标类型': '信用风险', '指标名称': '外部评级下调', '监控状态': '异常', '指标值': '无数据', '预警线': 'AA-' },
        { '资产名称': '22江津城建MTN001', '所属池': '债券池-可投池', '指标类型': '信用风险', '指标名称': '外部评级下调', '监控状态': '异常', '指标值': '无数据', '预警线': 'AA-' },
        { '资产名称': '21修竹01', '所属池': '债券池-可投池', '指标类型': '信用风险', '指标名称': '外部评级下调', '监控状态': '异常', '指标值': '无数据', '预警线': 'AA-' },
        { '资产名称': '18稠州商行二级01', '所属池': '债券池-可投池', '指标类型': '信用风险', '指标名称': '外部评级下调', '监控状态': '异常', '指标值': '无数据', '预警线': 'AA-' },
        { '资产名称': '18宁夏银行二级01', '所属池': '债券池-可投池', '指标类型': '信用风险', '指标名称': '外部评级下调', '监控状态': '异常', '指标值': '无数据', '预警线': 'AA-' },
        { '资产名称': '17通商银行二级01', '所属池': '债券池-可投池', '指标类型': '信用风险', '指标名称': '外部评级下调', '监控状态': '异常', '指标值': '无数据', '预警线': 'AA-' }

      ]
    }
  },
  computed: {
    columns() {
      return Object.keys(this.dataList[0]).map(item => ({
        prop: item,
        title: item,
        align: typeof this.dataList[0][item] === 'number' ? 'right' : 'left',
        type: typeof this.dataList[0][item] === 'number' ? 'amount' : 'text'
      })).concat({
        prop: 'id',
        title: '操作',
        align: 'center',
        render: () => {
          const btns = [
            <el-tooltip content='出池'><jr-svg-icon icon-class='refine' /></el-tooltip>,
            <el-tooltip content='关闭'><jr-svg-icon icon-class='bell-off' /></el-tooltip>
          ]
          return <span class='table-action-box'>{btns}</span>
        }
      })
    }
  },
  mounted() {
    this.height = this.$el.querySelector('.body').clientHeight - 20
  },
  methods: {
    cellStyle({ column }) {
      if (column.columnKey === '监控状态') {
        return { color: 'red' }
      }
      return {}
    }
  }
}
</script>

<style lang="scss" scoped>
.body {
  padding: 10px 0;
}
</style>
