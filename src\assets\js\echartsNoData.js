const echartNoDataGraphic = function(xAxis){
  if(xAxis && xAxis.length){
    return {}
  }else{
    return {
      graphic: [
        {
          type: 'group',
          left: 'center',
          top: 'center',
          children: [
            // 文档图标背景
            {
              type: 'rect',
              shape: {
                x: -25,
                y: -35,
                width: 50,
                height: 40,
                r: 6
              },
              style: {
                fill: '#f5f5f5',
                stroke: '#e0e0e0',
                lineWidth: 1
              }
            },
            // 文档图标顶部折角
            {
              type: 'polygon',
              shape: {
                points: [
                  [15, -35],
                  [25, -25],
                  [15, -25]
                ]
              },
              style: {
                fill: '#e8e8e8',
                stroke: '#e0e0e0',
                lineWidth: 1
              }
            },
            // 文档内容线条1
            {
              type: 'rect',
              shape: {
                x: -15,
                y: -20,
                width: 20,
                height: 2,
                r: 1
              },
              style: {
                fill: '#d0d0d0'
              }
            },
            // 文档内容线条2
            {
              type: 'rect',
              shape: {
                x: -15,
                y: -15,
                width: 15,
                height: 2,
                r: 1
              },
              style: {
                fill: '#d0d0d0'
              }
            },
            // 文档内容线条3
            {
              type: 'rect',
              shape: {
                x: -15,
                y: -10,
                width: 18,
                height: 2,
                r: 1
              },
              style: {
                fill: '#d0d0d0'
              }
            },
            // 暂无数据文字
            {
              type: 'text',
              style: {
                text: '暂无数据',
                x: 0,
                y: 25,
                fontSize: 14,
                fill: '#999999',
                textAlign: 'center',
                textVerticalAlign: 'middle',
                fontFamily: 'PingFang SC, Microsoft YaHei, sans-serif'
              }
            }
          ]
        }
      ],
      xAxis:{
        show:false
      },
      yAxis:{
        show:false
      },
      legend:{
        show:false
      }
    }
  }
}
export default echartNoDataGraphic