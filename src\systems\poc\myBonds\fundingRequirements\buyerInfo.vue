<template>
  <div class="buyerInfo">
    <collapse-panel title="买方信息">
      <div class="collapseBody">
        <jr-form-item-create :validate-rules="validateRules" :data="buyInfo" :model="data" />
      </div>
    </collapse-panel>
    <collapse-panel title="标的要求">
      <div class="collapseBody">
        <jr-form-item-create :validate-rules="validateRules" :data="objectInfo" :model="data" />
      </div>
    </collapse-panel>
  </div>
</template>

<script>
import { getModalProps, getModalComputed } from '@/systems/mixins'
export default {
  mixins: [getModalProps, getModalComputed],
  props: {
    itemData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      baseFileds: []
    }
  },
  created() {
    this.itemData && this.$emit('setFieldsValue', {
      ...this.itemData
    })

    this.initData()
  },
  methods: {

    initData() {
      this.buyInfo = [
        {
          title: '企业名称',
          prop: 'qymc',
          required: true
        },
        {
          title: '企业评级',
          prop: 'qypj',
          type: 'select',
          required: true,
          options: [
            { text: 'AAAA', value: 'AAAA' },
            { text: 'AAA', value: 'AAA' }
          ]
        },
        {
          title: '企业类型',
          prop: 'qylx',
          type: 'select',
          required: true,
          options: [
            { text: '科技', value: '1' }
          ]
        },
        {
          title: '是否上市公司',
          prop: 'sfssgs',
          type: 'radio',
          required: true,
          options: [
            { text: '是', value: '1' },
            { text: '否', value: '2' }
          ]
        },
        {
          title: '所属区域',
          prop: 'ssqy',
          type: 'select',
          required: true,
          options: [
            { text: '合肥市', value: '1' }
          ]
        },
        {
          title: '所在行业',
          prop: 'szhy',
          type: 'select',
          required: true,
          options: [
            { text: '金融', value: '1' }
          ]
        }
      ]
      this.objectInfo = [
        {
          title: '标的区域',
          prop: 'bdqy',
          type: 'select',
          required: true,
          options: [
            { text: '合肥市', value: '1' }
          ]
        },
        {
          title: '标的行业',
          prop: 'bdhy',
          type: 'select',
          required: true,
          options: [
            { text: '金融', value: '1' }
          ]
        },
        {
          title: '并购预算',
          prop: 'bgys',
          type: 'hundredMillion'
        },
        {
          title: '是否获取控制权',
          prop: 'sfhqkzq',
          type: 'radio',
          required: true,
          options: [
            { text: '是', value: '1' },
            { text: '否', value: '2' }
          ]
        },
        {
          title: '公司简介',
          prop: 'gsjj',
          class: 'textarea',
          required: true,
          type: 'textarea'
        }
      ]
    },
    async preSaveHandel() {
      return true
    }

  }
}
</script>

<style lang="scss">
.buyerInfo {
    .textarea {
        width: 100% !important;
        .el-form-item__label {
            width: 9% !important;
        }
        .el-form-item__content {
            width: 100% !important;
        }
    }
}
</style>
