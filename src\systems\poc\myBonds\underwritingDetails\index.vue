<!-- 承销明细 -->
<template>
  <div class="underwritingDetails">
    <div class="searchForm">
      <el-form :model="searchForm" label-width="90px">
        <jr-form-item label="发行日期" class="formItem">
          <el-date-picker
            v-model="searchForm.fxrq"
            placeholder="请选择"
            type="daterange"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd"
          />
        </jr-form-item>
        <jr-form-item label="债券类型" class="formItem">
          <jr-combobox
            v-model="searchForm.zqlx"
            placeholder="请选择"
            clearable
            filterable
            :data="[]"
            option-value="id"
            option-label="text"
          />
        </jr-form-item>
        <jr-form-item label="机构类型" class="formItem">
          <jr-combobox
            v-model="searchForm.jglx"
            placeholder="请选择"
            clearable
            filterable
            :data="[]"
            option-value="id"
            option-label="text"
          />
        </jr-form-item>
        <el-button type="primary" class="btn" @click="queryData">查询</el-button>
      </el-form>
    </div>
    <MainRanking />
    <UnderwritingTable />
  </div>
</template>

<script>
import MainRanking from './components/mainRanking.vue'
import UnderwritingTable from './components/underwritingTable.vue'
export default {
  components: { MainRanking, UnderwritingTable },
  data() {
    return {
      searchForm: {
        fxrq: [],
        zqlx: '',
        jglx: ''
      }
    }
  },
  methods: {
    queryData() {
      console.log('222222')
    }
  }
}
</script>
<style lang="scss">
.underwritingDetails {
  height: 100%;
  .el-form .el-form-item__label {
    padding-top: 12px;
  }
  .formItem {
    display: inline-block;
    width: 300px
  }
  .btn {
    display: inline-block;
    margin-left: 10px;
  }
  .searchForm {
    padding: 10px 0 10px 0;
    background: #fff;
    margin-bottom: 10px;
  }
}
</style>
