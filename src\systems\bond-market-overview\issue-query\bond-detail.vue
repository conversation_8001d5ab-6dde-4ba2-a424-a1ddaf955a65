<template>
  <!-- POC专用 -->
  <el-descriptions :column="2" size="small" border :label-style="{ width: '180px' }">
    <el-descriptions-item v-for="field in fieldList" :key="field.label" :label="field.label">
      {{ detailData[field.prop] || '--' }}
    </el-descriptions-item>
  </el-descriptions>
</template>

<script>
import { GetInfoFn } from '@jupiterweb/utils/api'

export default {
  props: {
    itemData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      detailData: {},
      fieldList: [
        { label: '债券代码', prop: 'zqdm' },
        { label: '债券简称', prop: 'zqjc' },
        { label: '债券全称', prop: 'zqqc' },
        { label: '债券类型', prop: 'zqlx' },
        { label: '创新专项产品', prop: 'cxzxp' },
        { label: 'SPPI测试', prop: 'sppi' },
        { label: '中债隐含评级', prop: 'zzxypj' },
        { label: '发行价格(元)', prop: 'fxjg' },
        { label: '规模(亿)', prop: 'gm' },
        { label: '发行期限', prop: 'fxqx' },
        { label: '票面利率(%)', prop: 'pmll' },
        { label: '票面与首次估值利差(BP)', prop: 'pmyszclc' },
        { label: '中债估值(行权/到期)', prop: 'zzgz' },
        { label: '剩余期限', prop: 'syqx' },
        { label: '余额(亿)', prop: 'ye' },
        { label: '分层占比(初始)', prop: 'fczbcs' },
        { label: '分层占比(最新)', prop: 'fczbzx' },
        { label: '发行起始日', prop: 'fxcqsr' },
        { label: '发行截止日', prop: 'fxjzr' },
        { label: '发行方式', prop: 'fxff' },
        { label: '公告日', prop: 'ggr' },
        { label: '上市日期', prop: 'ssrq' },
        { label: '主承销商', prop: 'zcxss' },
        { label: '牵头主承销商', prop: 'qtzcxss' },
        { label: '联席主承销商', prop: 'lxzcxss' },
        { label: '牵头承销占比(%)', prop: 'qtzxzb' },
        { label: '承销明细', prop: 'cnmy' },
        { label: '包销总额(亿)', prop: 'bxze' },
        { label: '包销总额占比(%)', prop: 'bxzez' },
        { label: '牵头包销金额(亿)', prop: 'qtbxje' },
        { label: '牵头包销占比(%)', prop: 'qtbxzb' },
        { label: '联席包销金额(亿)', prop: 'lxbxje' },
        { label: '联席包销占比(%)', prop: 'lxbxzb' },
        { label: '包销明细', prop: 'bxmx' },
        { label: '存续期管理机构', prop: 'cxqgljg' },
        { label: '薄记管理人', prop: 'bjglr' },
        { label: '内含特殊条款', prop: 'nhtstk' },
        { label: '是否含权', prop: 'sfhq' },
        { label: '下一行权日', prop: 'xyxqr' },
        { label: '下一债权登记日', prop: 'xyzqdjr' },
        { label: '是否跨市场', prop: 'sfksc' },
        { label: '交易市场', prop: 'jysc' },
        { label: '发行时债项评级', prop: 'fxszxpj' },
        { label: '发行时主体评级', prop: 'fxsztpj' },
        { label: '发行人委托评级机构', prop: 'fxfwrwpjjg' },
        { label: '注册文号', prop: 'zclh' },
        { label: '利率类型', prop: 'lllx' },
        { label: '息票品种', prop: 'xppz' },
        { label: '利率说明', prop: 'llsm' },
        { label: '计息方式', prop: 'jxff' },
        { label: '付息频率', prop: 'fxlv' },
        { label: '付息日说明', prop: 'fxrsm' },
        { label: '计息基准', prop: 'jxz' },
        { label: '起息日期', prop: 'qxrq' },
        { label: '到期日期', prop: 'dqrq' },
        { label: '兑付日期', prop: 'df' },
        { label: '发行人', prop: 'fxf' },
        { label: '主体层级', prop: 'ztcj' },
        { label: '是否城投', prop: 'sfct' },
        { label: '行业', prop: 'hy' },
        { label: '所在地区', prop: 'szdq' },
        { label: '托管机构', prop: 'tgjg' },
        { label: '担保人/企业层级/评级', prop: 'dbrenqycj' },
        { label: '增信情况', prop: 'zxqk' }
      ]
    }
  },
  async created() {
    this.detailData = await GetInfoFn('/poc/getFxcxDetail?zqdm=' + this.itemData.zqdm, {}, 'get')
  }
}
</script>

<style lang="scss" scoped></style>
