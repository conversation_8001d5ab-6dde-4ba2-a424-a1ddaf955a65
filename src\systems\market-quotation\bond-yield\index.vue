<template>
  <div class="bazaar">
    <el-tabs v-model="active" :class="{ 'bazaar-tabs': true, 'bazaar-tabs-three': active === 'four' }">
      <el-tab-pane label="中短期票据" name="first">
        <yield-zdq />
      </el-tab-pane>

      <el-tab-pane label="城投债" name="second" :lazy="true">
        <yield-ct />
      </el-tab-pane>

      <el-tab-pane label="企业债" name="three" :lazy="true">
        <yield-qy />
      </el-tab-pane>

      <el-tab-pane name="four" :lazy="true">
        <template slot="label">
          <div class="interest-rate">
            信用债看板
            <i class="el-icon-s-marketing" />
          </div>
        </template>
        <yield-credit />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import yieldCt from './modules/uib-yield.vue'
import yieldZdq from './modules/stb-yield.vue'
import yieldQy from './modules/cb-yield.vue'
import yieldCredit from './modules/credit-yield.vue'
export default {
  name: 'PolicyRate',
  components: {
    yieldCt,
    yieldZdq,
    yieldQy,
    yieldCredit
  },
  data() {
    return {
      active: 'first',
      url: ''
    }
  },
  props: {
    menuinfo: {
      type: Object,
      default: () => ({})
    }
  },
  created() {
    const params = this.menuinfo.meta.params
    this.active = params.tabName || 'first'
  },
  methods: {}
}
</script>

<style lang="scss" scoped>
@import '@/assets/styles/market.scss';

::v-deep .jr-radio-group .el-radio {
  min-width: 96px;
  margin-right: 16px;
}

::v-deep .jr-checkbox-group .el-checkbox {
  min-width: 80px;
  margin-right: 32px;
}
</style>
<style lang="scss">
.card-title {
  margin-bottom: 16px;
  .jr-svg-icon--exclamation-circle {
    color: #959eb2;
    margin-left: 4px;
    font-size: 12px;
  }
}
</style>
