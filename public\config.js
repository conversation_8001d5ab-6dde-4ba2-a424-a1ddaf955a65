// 前端全局配置文件
window.S_CONFIG = {
  VERSION: 'V4.0.0',
  BASE_URL: '/butler', // 服务端路径, nginx代理时,只需要配置项目名
  DATA_PORTAL_URL: '/', // 数据中台服务路径, 按需配置
  SYSTEM_FLAG: 'M', // 系统标识,可选: SSO(统一登录)、OA(人事系统)、 M(联社)、C(城商行)、-空-
  MODE: 'single', // 系统服务部署标识，支持微服务模式, 默认单体，可选：single、-空-
  CONTAINER: '', // weblogic 或 其他
  TIMEOUT: 300, // 接口超时时间：（单位：秒）
  enableValidateCode: false, // 登录验证码开关
  DEFAULT_THEME: '#FF8E2B', // 拂晓蓝-#409eff,蔚蓝-#00c1de,紫蓝-#7865e3,极客蓝-#3e59f2,明青-#13c2c2,明绿-#79ac43,薄暮-#6384c8,绛紫-#9254de,深橙-#ff7700,金盏花-#fb9c34,日暮-#fb5e28,火山-#ff5456,雅典黑-#4e5777
  MAX_MESSAGE_COUNT: 10, // 右下角消息最大显示条数
  REQUEST_KEY_COMPARISON: false, // 开启参数签名，防越权
  MULTI_ROUTER: false, // 是否多路由模式, 若设置为true, 必须设置服务端接口前缀BASE_URL，并修改vue.config.js中的代理配置
  SIDEBAR_LEFT: true, // 菜单栏是否默认左侧？, 默认在上面, false: 上，true: 左侧
  DEFAULT_FONT_SIZE: 14, // 默认基准字体大小，单位px
  SIDEBAR_TYPE: 'menu', // 菜单栏位置，可选：normal(正常)、menu(往下展开)
  showThemeSwitch: false // 是否显示主题切换
}
