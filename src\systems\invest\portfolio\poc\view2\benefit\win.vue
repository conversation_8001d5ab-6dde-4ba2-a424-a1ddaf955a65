<!-- 组合收益分析 -> 组合胜率分析 -->
<template>
  <div class="home-poc-item has-fullscreen plt-base-info" style="height: 100%;">
    <div class="home-poc-item--header float-left">组合胜率分析 <fullscreen v-on="{ ...$listeners }" /></div>
    <jr-decorated-table
      custom-id="9abf1d2080be4ca9ade24402e9e16dbf"
      style="height: 50%;"
      v-bind="{
        ...$attrs,
        params,
        noPagination: true,
        menuinfo: {
          pageId: 'PtlIncomeAnalysis001',
          btnList: [{
            btnPosition: 'HEAD',
            btnkey: 'export',
            btnnm: '导出',
            componenturl: 'export',
            effectflag: 'E',
            moduleid: 'PtlIncomeAnalysis001_002_001',
            moduletype: 'btn',
            orde: 4,
            parameter: JSON.stringify({ noSelect: true }),
            permitflag: 'C',
            permittag: '02',
            showflag: 'Y',
            tmFlag: 'E'
          }]
        },
        handlequery
      }"
    />

    <echarts :options="options" :styles="{ width: '100%', height: '50%' }" />
  </div>
</template>

<script>
import * as API from '@/api/invest/portfolio/poc/view2'
import echarts from '@jupiterweb/components/echarts'
import fullscreen from '../../common/fullscreen'

export default {
  components: {
    echarts,
    fullscreen
  },
  props: {
    params: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      form: {
        portfolioId: ''
      },
      cols: [{
        title: '起始日期',
        prop: 'startDate',
        type: 'date'
      }, {
        title: '截止日期',
        prop: 'endDate',
        type: 'date'
      }],
      options: {}
    }
  },
  watch: {
    params(v) {
      if (v && Object.keys(v).length) {
        Object.assign(this.form, v)
        this.init()
      }
    }
  },
  methods: {
    async init() {
      const data = await API.getWinRateData(this.form)
      this.options = this.getChartOptions(data)
      // console.log('---原始数据---：', data)
    },
    getChartOptions(data) {
      let x = []
      const series = []

      if (data && Object.keys(data).length) {
        Object.keys(data).forEach((k, i) => {
          const d = data[k]

          !i && (x = d.map(v => v.X))

          series.push({
            type: 'bar',
            barWidth: '30px',
            data: d.map(v => v.Y)
          })
        })
      }

      // console.log('--x--：', x)
      // console.log('--series--：', series)

      return {
        grid: {
          top: '3%',
          left: '5%',
          right: '5%',
          bottom: '10%'
        },
        xAxis: [
          {
            type: 'category',
            data: x,
            axisPointer: {
              type: 'shadow'
            }
          }
        ],
        yAxis: [
          {
            type: 'value',
            alignTicks: true,
            axisLine: {
              show: true
            }
          }
        ],
        series
      }
    },
    handlequery({ startDate, endDate }) {
      Object.assign(this.form, { startDate, endDate })
      this.init()

      return true
    }
  }
}
</script>

<style lang="scss" scoped>
@import "../../common/poc.scss";
</style>
