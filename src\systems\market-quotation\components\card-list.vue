<template>
  <div>
    <div v-if="ratingCartType && ratingCartList.length > 0" class="flex" style="padding-bottom: 8px;">
      <el-radio v-for="item in ratingCartList" :key="item.itemcode" v-model="ratingCartInd" :label="item.itemcode" @change="ratingBtnFn(item)">{{ item.cnname }}</el-radio>
      <!-- <el-tag v-for="item in ratingCartList" :key="item.itemcode" :class="{'is-active': ratingCartInd === item.itemcode}" @click="ratingBtnFn(item)">{{ item.cnname }}</el-tag> -->
    </div>
    <ul>
      <li v-for="item in list" :key="item.bAnalCurveterm" :class="{ 'is-active': item.bAnalCurveterm === cardInd }" @click="cardBtnFn(item)">
        <h5>{{ item.bAnalCurveterm }}</h5>
        <div>
          <p><span>{{ mainTitle }}</span> <span class="num">{{ item.bAnalYield }}</span></p>
          <p>
            <span>{{ subtitle }}</span>
            <span :class="{'ye': item.bAnalYieldAdd == 0, 'bp': item.bAnalYieldAdd > 0, 'gr': item.bAnalYieldAdd < 0 }">
              {{ item.bAnalYieldAdd }}</span>
          </p>
        </div>
      </li>
    </ul>
  </div>
</template>

<script>
import { GetListData } from '@jupiterweb/api/common/active-module-svr'
import { GetViewData } from '@jupiterweb/api/config-platform/params-type'
export default {
  props: {
    ratingCartType: {
      type: String,
      default: ''
    },
    onActive: {
      type: Function,
      default: () => {}
    },
    ratingFn: {
      type: Function,
      default: () => {}
    },
    isType: {
      type: [String, Number],
      default: ''
    },
    ratingInd: {
      type: [String, Number],
      default: ''
    },
    mainTitle: {
      type: String,
      default: '最新估值(%)'
    },
    subtitle: {
      type: String,
      default: '涨跌BP'
    },
    params: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      list: [],
      ratingCartList: [],
      ratingCartInd: '',
      cardInd: ''
    }
  },
  created() {
    this.setCardInd() // 卡片默认值
    this.ratingCartInd = this.ratingInd // 评级默认值
    this.cartData()
    this.ratingCartDict()
  },
  methods: {
    // 卡片评级
    async ratingCartDict() {
      if (!this.ratingCartType) return
      const { dictList } = await GetViewData(this.ratingCartType)
      this.ratingCartList = dictList || []
    },
    // 卡片接口   如果后期参数变动 加一个 空的对象 扩展
    async cartData() {
      const { pageInfo: { list }} = await GetListData({
        params: { ...this.params, main_rating: this.ratingCartInd },
        page: {
          pageNo: 1,
          pageSize: 20 // 卡片不会超过20个 以后有可以根据需求扩展
        }
      })
      list && list.map(k => {
        const obj = {}
        obj.bAnalCurveterm = k.bAnalCurveterm
        obj.bAnalYieldAdd = k.bAnalYieldAdd
        obj.bAnalYield = k.bAnalYield
        return obj
      })
      this.list = list || []
    },
    // 点击卡片上方评级
    async ratingBtnFn(item) {
      // if (this.ratingCartInd === item.itemcode) return // 重复点击已经选的同一个
      // this.ratingCartInd = item.itemcode
      await this.cartData(item.itemcode)
      this.cardInd = this.list.length > 0 ? this.list[0].bAnalCurveterm : ''// 卡片默认选中第一条
      this.ratingFn(item, this.cardInd)
    },
    // 点击卡片
    cardBtnFn(item) {
      this.cardInd = item.bAnalCurveterm
      this.onActive(item)
    },
    setCardInd(val) {
      if (val) {
        this.cardInd = val
        return
      }
      this.cardInd = this.isType
    }
  }
}
</script>
<style lang="scss" scoped>
$w: #ffffff;
$p:#646566;
$bp:#DF4545;
$gr:#2DA641;
$isActive:var(--theme--color);
$ye: #F29600;
::v-deep .el-tag--medium{
    padding: 0 4px;
    margin-right: 8px;
    color: #646566;
    background-color: #ffffff;
    border: 1px solid #E1E1E1;
    cursor: pointer;
    margin-bottom: 16px;
    &:last-child{
        margin-right: 0;
    }
    &.is-active{
        color: #ffffff;
        border: 1px solid var(--theme--color);
        background: var(--theme--color);
    }
}
.ul,li{
  list-style: none;
  padding: 0;
  margin: 0;
}
li{
  background: linear-gradient( 315deg, #FEF6EB 0%, #FFFBF3 100%);
  border-radius: 4px;
  margin-bottom: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  height: 149px;
  position: relative;
  &::after{
    content: '';
    position: absolute;
    left: 113px;
    top: 50%;
    margin-top: -24px;
    width: 1px;
    height: 48px;
    border: 1px solid #EBEDF0;
  }
  div{
    padding: 0 24px;
    flex: 1;
  }
  h5{
    font-size: 20px;
    line-height: 1;
    color: #3D3D3D;
    width: 113px;
    text-align: center;
    margin: 0;
  }
  p{
    font-size: 16px;color: $p;margin: 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .num{
      color: #323233;
      font-weight: bold;
    }
    .bp{
      color: $bp;
      font-weight: bold;
    }
    .gr{
      color: $gr;
      font-weight: bold;
    }
    .ye{
      color: $ye;
      font-weight: bold;
    }
  }
  &.is-active{
    color: $w;
    background: linear-gradient( 315deg, #DDDBFF 0%, #BDBAFF 40%, #8D89FF 100%);
    box-shadow: 0px 4px 10px 0px rgba(0,0,0,0.03);
    h5{
      color: $w;
    }
    p{
      color: $w;
      .num{
        color: $w;
        font-weight: bold;
      }
    }
  }
}
::v-deep .el-radio{
  margin-right: 5px;
}
::v-deep .el-radio__label{
  padding-left: 6px;
}
</style>
