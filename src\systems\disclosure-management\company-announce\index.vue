<template>
  <!-- 公司公告 -->
  <div class="company-announce">
    <jr-layout-vertical :height="48" disabled>
      <template v-slot:top>
        <div class="company-announce-form">
          <el-form inline :model="form" label-width="68">
            <jr-form-item label="标题">
              <el-input
                v-model="form.nInfoTitle"
                clearable
                placeholder="请输入"
                style="max-width: 285px"
              />
            </jr-form-item>
            <jr-form-item label="公告日期">
              <el-date-picker
                v-model="form.annDt"
                type="daterange"
                range-separator="~"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                format="yyyy-MM-dd"
                class="date-picker"
                style="max-width: 285px"
              />
            </jr-form-item>
            <jr-form-item label="类型">
              <jr-combobox
                v-model="form.infoType"
                style="max-width: 285px"
                placeholder="请选择"
                clearable
                multiple
                filterable
                collapse-tags
                :data="infoTypeList"
                option-value="val"
                option-label="text"
              />
            </jr-form-item>
          </el-form>
          <div class="btn-list">
            <el-button type="primary" @click="submit">查询</el-button>
            <el-button @click="reset">重置</el-button>
          </div>
        </div>
      </template>

      <template v-slot:bottom>
        <div class="company-announce-content">
          <jr-decorated-table
            ref="table"
            stripe
            :menuinfo="menuinfo"
            :params="tableParams"
            :custom-render="customRender"
            :handledownload="handleDownload"
            custom-id="48cc1627b224474f8362aedf5a1ca54a"
            v-bind="{...$props}"
            @refreshed="callFn"
          />
        </div>
      </template>
    </jr-layout-vertical>
    <company-dialog ref="dialog" />
  </div>
</template>
<script>
import companyDialog from './dialog/companyDialog.vue'
export default {
  components: {
    companyDialog
  },
  props: {
    permitdetail: {
      type: Object,
      default: () => ({})
    },
    menuinfo: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      tableParams: {
        ccid: '48cc1627b224474f8362aedf5a1ca54a',
        ownedModuleid: '1315724492663078912',
        nInfoTitle: '',
        annDt: [],
        infoType: []
      },
      columns: [],
      customRender: {
        nInfoTitle: (h, { rowIndex, row }) => {
          return (
            <div
              class="table-link"
              onClick={() => {
                this.getDetailDialog(rowIndex, row)
              }}
            >
              {row.nInfoTitle}
            </div>
          )
        }
      },
      form: {
        nInfoTitle: '',
        annDt: []
      },
      infoTypeList: [
        {
          text: '招股',
          val: '01'
        },
        {
          text: '财务报告',
          val: '02'
        },
        {
          text: '重大事项',
          val: '03'
        },
        {
          text: '交易提示',
          val: '04'
        },
        {
          text: '配股',
          val: '05'
        },
        {
          text: '增发',
          val: '06'
        },
        {
          text: '股权股本',
          val: '07'
        },
        {
          text: '其他公告',
          val: '08'
        },
        {
          text: '公司资料变更',
          val: '09'
        },
        {
          text: '发行上市',
          val: '10'
        },
        {
          text: '债券兑付',
          val: '11'
        },
        {
          text: '信用评级',
          val: '12'
        },
        {
          text: '法律意见书',
          val: '13'
        },
        {
          text: '含权债券相关',
          val: '14'
        },
        {
          text: '持有人会议',
          val: '15'
        },
        {
          text: '中介机构变更',
          val: '16'
        },
        {
          text: '受托管理报告',
          val: '17'
        },
        {
          text: '会计事务所专项意见',
          val: '18'
        }
      ]
    }
  },
  mounted() {},
  methods: {
    callFn(data) {
      this.columns = data.config.columns
    },
    handleClick() {
      //
    },
    submit() {
      this.tableParams = { ...this.tableParams, ...this.form }
    },
    reset() {
      this.form = {
        nInfoTitle: '',
        annDt: [],
        infoType: []
      }
    },
    getDetailDialog(rowIndex, row) {
      console.log(rowIndex, row)
      this.$refs.dialog.open('')
    },
    handleDownload(e) {
      console.log(e)
      const url = '#'
      const link = document.createElement('a')
      link.href = url
      link.download = ''
      link.click()
    }
  }
}
</script>
<style lang="scss">
.company-announce {
  height: 100%;

  .vertical-layout {
    background: #fff;
    padding: 0;
    height: 100%;

    &--top-content {
      padding: 0 16px;
    }

    &--resize {
      height: 1px;
      color: #EAE9E9;
    }

    &--bottom {
      margin-top: 16px;
      padding: 0 16px;
    }
  }

  &-form {
    position: relative;

    .el-form {
      display: flex;
      align-items: center;
      padding-top: 16px;
      width: calc(100% - 144px);

      .el-form-item {
        width: 100%;
        max-width: 353px;

        .el-form-item__label {
          padding: 11px 8px 0 0;
        }

        .el-form-item__content {
          width: calc(100% - 68px);
        }

        &.no-label {
          .el-form-item__content {
            width: 100%;
          }
        }
      }
    }

    .btn-list {
      position: absolute;
      top: 8px;
      right: 0;

      .el-button {
        margin-left: 16px;
      }
    }
  }

  &-content {
    height: 100%;

    .jr-decorated-table--header-top {
      display: none;
    }

    .jr-decorated-table--header-left {
      display: none;
    }

    .jr-decorated-table--header-right {
      display: none;
    }

    .jr-decorated-table--body {
      padding: 0;
    }

    .table-link {
      width: 100%;
      color: var(--theme--color);
      overflow: hidden;
      text-overflow: ellipsis;
      cursor: pointer;
    }
  }
}
</style>
