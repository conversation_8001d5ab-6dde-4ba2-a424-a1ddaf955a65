<!-- 组合收益分析 -> 组合风险调整后收益率 -->
<template>
  <div class="home-poc-item has-fullscreen plt-base-info" style="height: 100%;">
    <div class="home-poc-item--header float-left">组合风险调整后收益率 <fullscreen v-on="{ ...$listeners }" /></div>
    <jr-decorated-table
      custom-id="6f11ceff40e241ed918918b346f44958"
      v-bind="{
        ...$attrs,
        params,
        noPagination: true,
        menuinfo: {
          pageId: 'PtlIncomeAnalysis001',
          btnList: [{
            btnPosition: 'HEAD',
            btnkey: 'export',
            btnnm: '导出',
            componenturl: 'export',
            effectflag: 'E',
            moduleid: 'PtlIncomeAnalysis001_002_001',
            moduletype: 'btn',
            orde: 4,
            parameter: JSON.stringify({ noSelect: true }),
            permitflag: 'C',
            permittag: '02',
            showflag: 'Y',
            tmFlag: 'E'
          }]
        }
      }"
    />
  </div>
</template>

<script>
import fullscreen from '../../common/fullscreen'

export default {
  components: {
    fullscreen
  },
  props: {
    params: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      form: {
        portfolioId: ''
      },
      cols: [{
        title: '起始日期',
        prop: 'startDate',
        type: 'date'
      }, {
        title: '截止日期',
        prop: 'endDate',
        type: 'date'
      }],
      options: {}
    }
  },
  methods: {
  }
}
</script>

<style lang="scss" scoped>
@import "../../common/poc.scss";
</style>
