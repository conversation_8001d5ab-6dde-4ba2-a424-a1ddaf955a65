<template>
  <div class="qa-body">
    <div v-if="answerCache.length || answering" ref="list" class="qa-list" :class="{'is-answering': answering}">
      <div v-for="(qa, ix) in answerCache" :key="'answerCache' + qa.q + ix" class="qa-list-item">
        <h3 class="question">
          <jr-svg-icon icon-class="avatar" />
          {{ qa.q }}
        </h3>
        <p v-for="(mg, idx) in qa.a" :key="mg + idx">
          <span v-if="idx === 0" class="logo" />
          {{ mg }}
        </p>
        <el-button v-if="!answering && ix === answerCache.length - 1" type="text" icon="el-icon-refresh-right" @click="restart(ix)">
          重新回答
        </el-button>
        <el-button v-if="!answering && ix === answerCache.length - 1" type="text" style="margin: 0;" icon="el-icon-delete" @click="clear">
          清空
        </el-button>
      </div>
      <div v-if="answering" class="qa-list-item">
        <h3 class="question">
          <jr-svg-icon icon-class="avatar" /> {{ answering.q }}</h3>
        <p v-for="(mg, idx) in answering.a" :key="mg + 'answering' + idx">
          <span v-if="idx === 0" class="logo" />
          {{ mg }}{{ idx === answering.a.length - 1 ? '|' : '' }}
        </p>
        <el-button plain type="primary" @click="stop"><span class="control-square" />停止回答</el-button>
      </div>
    </div>
    <div :class="{collapsed: answerCache.length || answering}" class="qa-tips">
      <h2>猜您想问</h2>
      <ul>
        <li v-for="(qa, idx) in qaList" :key="qa.q">
          <el-link type="primary" :disabled="!!answering" :underline="false" @click="handleQuestion(idx)">{{ qa.q }}</el-link>
        </li>
      </ul>
    </div>
    <div class="qa-footer">
      <el-autocomplete v-model="inputQ" :fetch-suggestions="querySearch" placeholder="在此输入您想了解的内容" @keypress.native.enter="inputStart" />
      <el-button type="primary" icon="el-icon-s-promotion" :disabled="!!answering" @click="inputStart" />
    </div>
  </div>
</template>

<script>
import { GetInfoFn } from '@jupiterweb/utils/api'

// 移出标点符号
const removePunctuation = (str) => {
  return (str || '').replace(/[^\w\s\u4E00-\u9FA5]| /g, '')
}
export default {
  data() {
    return {
      qaList: [],
      defaultText: '',
      inputQ: '',
      answerCache: [],
      answering: null
    }
  },
  created() {
    this.init()
  },
  methods: {
    async init() {
      const { qaList, defaultText } = await GetInfoFn('/data/qa.json', null, 'get')
      this.defaultText = defaultText
      this.qaList = [...qaList]
    },
    querySearch(queryString, cb) {
      const results = this.qaList.filter((qa) => qa.q.indexOf(queryString) !== -1)
      cb(results.map((qa) => ({ value: qa.q })))
    },
    handleQuestion(idx) {
      const qa = this.qaList[idx]
      this.$set(this, 'answering', {
        q: qa.q,
        a: [],
        t: qa.a || ''
      })
      this.$nextTick(this.start.bind(this))
    },
    start() {
      const self = this

      const { answering } = this
      if (!answering || !answering.a) return
      let i = 0
      const msggs = answering.t.split('\r\n')
      this.timer = setInterval(() => {
        if (!msggs[i]) {
          clearInterval(this.timer)
          return
        }
        self.$set(
          self.answering.a,
          i,
          (answering.a[i] || '') + msggs[i].slice((answering.a[i] || '').length, (answering.a[i] || '').length + 1)
        )

        if (!msggs[i].slice((answering.a[i] || '').length, (answering.a[i] || '').length + 1).length) {
          i++
        }
        if (i === msggs.length) {
          self.stop()
        }
        requestAnimationFrame(() => {
          self.$refs.list.scrollTop = self.$refs.list.scrollHeight
        })
      }, 70)
    },
    stop() {
      this.timer && clearInterval(this.timer)
      this.answerCache.push(JSON.parse(JSON.stringify(this.answering)))
      this.answering = null
    },
    clear() {
      this.answerCache = []
    },
    restart(idx) {
      const qa = this.answerCache[idx]
      this.$set(this, 'answering', {
        q: qa.q,
        a: [],
        t: qa.t || ''
      })
      this.answerCache.splice(idx, 1)
      this.$nextTick(this.start.bind(this))
    },
    inputStart() {
      if (this.answering) return
      this.$set(this, 'answering', {
        q: this.inputQ,
        a: [],
        t: { a: this.defaultText, ...this.qaList.find((qa) => removePunctuation(qa.q) === removePunctuation(this.inputQ)) }.a
      })
      this.$nextTick(this.start.bind(this))
      this.inputQ = ''
    }
  }
}
</script>

<style lang="scss">
.qa-body {
  width: 100%;
  margin: auto;
  height: 60vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  .control-square {
    height: 15px;
    width: 15px;
    display: inline-block;
    background: var(--theme--color);
    border-radius: 2px;
    vertical-align: -2px;
    margin-right: 6px;
  }
  .el-button:hover .control-square {
    background: #fff;
  }
  .el-input .el-input__inner,
  .el-button {
    height: 32px !important;
    line-height: 32px !important;
    border-radius: 4px;
  }
  .qa-list {
    flex: 1;
    overflow: auto;
    background: #444654;
    border-radius: 4px;
    color: #f5f5f5;
    padding: 16px;
    will-change: auto;
    transition: all .2 ease-in-out;
    &.is-answering {
      will-change: height;
      &>.qa-list-item:last-child {
        animation: fadeInDown .2s;
      }
    }
    p {
      text-indent: 16px;
      margin-bottom: 12px;
      font-size: 12px;
    }
    &-item {
      // margin-bottom: 30px;
      .question {
        font-weight: 400;
        color: var(--theme--color);
        margin-bottom: 16px;
        .jr-svg-icon {
          font-size: 28px;
          vertical-align: middle;
          margin-right: 6px;
        }
      }
      p {
        padding-left: 40px;
      }
      p .logo {
        background: #fff url("~@/assets/images/ai.png");
        background-repeat: no-repeat;
        background-size: cover;
        vertical-align: middle;
        display: inline-block;
        width: 30px;
        height: 30px;
        padding: 3px;
        margin-right: 6px;
        border-radius: 20px;
        margin-left: -40px;
      }
      .question + p {
        text-indent: 0;
      }
      .el-button {
        border-radius: 16px;
        font-size: 12px;
        margin-left: 40px;
      }
    }
  }
  .el-autocomplete {
    width: 100%;
  }
  .qa-footer {
    width: 100%;
    margin-top: 10px;
    display: flex;
    .el-input__inner {
      border-top-right-radius: 0;
      border-bottom-right-radius: 0;
      border-color: #e7e7e7;
      &:focus {
        border-color: var(--theme--color);
      }
    }
    .el-button {
      border-top-left-radius: 0;
      border-bottom-left-radius: 0;
      font-size: 16px;
    }
  }
  .qa-tips {
    border-radius: 4px;
    background: #e7e7e7;
    padding: 16px;
    &.collapsed {
      display: none;
    }
    h2 {
      font-weight: bold;
    }
    ul {
      line-height: 28px;
      font-size: 14px;
      li {
        margin-left: 20px;
        list-style: disc;
        cursor: pointer;
      }
    }
  }
  .qa-list + .qa-tips {
    margin-top: 30px;
  }
}
</style>
